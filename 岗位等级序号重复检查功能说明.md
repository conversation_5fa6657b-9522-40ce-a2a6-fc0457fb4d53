# 岗位等级序号重复检查功能说明

## 功能概述
在岗位名称的新增和编辑操作中，增加了对选中岗位等级序号（orderNum）重复的检查，防止用户同时选择具有相同序号的不同等级。

## 修改的文件
- `server/src/controllers/organization/positionNameController.js`

## 功能实现

### 1. 新增岗位名称检查
在 `addPositionName` 接口中添加了等级序号重复检查逻辑：

```javascript
// 校验等级序号是否重复
const selectedLevels = await Level.findAll(
  addEnterpriseFilter({
    where: { id: { [Op.in]: levelIds } },
    attributes: ['id', 'name', 'orderNum']
  })
);

// 检查是否有相同的orderNum
const orderNumMap = new Map();
const duplicateNames = [];

for (const level of selectedLevels) {
  if (orderNumMap.has(level.orderNum)) {
    // 找到重复的orderNum
    const existingLevel = orderNumMap.get(level.orderNum);
    duplicateNames.push(`${existingLevel.name}与${level.name}`);
  } else {
    orderNumMap.set(level.orderNum, level);
  }
}

if (duplicateNames.length > 0) {
  await transaction.rollback();
  return res.status(400).json({
    code: 400,
    message: `${duplicateNames.join('、')}为相同序号，不能同时选择`
  });
}
```

### 2. 编辑岗位名称检查
在 `updatePositionName` 接口中添加了相同的等级序号重复检查逻辑。

## 错误提示格式
当用户选择了具有相同序号的等级时，系统会返回错误信息：
- 单个重复：`"A等级与B等级为相同序号，不能同时选择"`
- 多个重复：`"A等级与B等级、C等级与D等级为相同序号，不能同时选择"`

## 检查逻辑
1. 根据用户选择的等级ID数组，查询对应的等级信息（包含orderNum）
2. 使用Map数据结构检查是否有重复的orderNum
3. 如果发现重复，收集重复的等级名称对
4. 如果有重复，返回400错误和相应的提示信息
5. 如果没有重复，继续正常的业务逻辑

## 使用场景
1. **新增岗位名称**：在岗位层级管理页面新增岗位名称时
2. **编辑岗位名称**：在岗位层级管理页面编辑现有岗位名称时

## 测试方法
1. 在数据库中确保有多个等级具有相同的orderNum值
2. 在前端岗位层级管理页面尝试新增或编辑岗位名称
3. 同时选择具有相同orderNum的多个等级
4. 提交表单，应该会收到相应的错误提示

## 注意事项
1. 此检查只在服务器端进行，确保数据一致性
2. 使用了事务处理，确保在检查失败时正确回滚
3. 错误信息采用中文提示，符合系统的国际化要求
4. 检查逻辑不影响正常的业务流程，只在发现重复时才阻止操作

## 相关表结构
- **org_level表**：存储岗位等级信息，包含orderNum字段
- **org_position_name表**：存储岗位名称信息
- **org_position表**：存储岗位与等级的关联关系 