const Employee = require('../../models/Employee');
const Department = require('../../models/Department');
const Position = require('../../models/Position');
const Level = require('../../models/Level');
const PositionName = require('../../models/PositionName');
const PositionType = require('../../models/PositionType');
const EmployeePromotion = require('../../models/EmployeePromotion');
const EmployeePosition = require('../../models/EmployeePosition');
const EmployeeEnterpriseApplication = require('../../models/EmployeeEnterpriseApplication');
const EmployeeCareerRecord = require('../../models/EmployeeCareerRecord');
const PracticeRecord = require('../../models/practice-record');
const PracticeRecordDetail = require('../../models/practice-record-detail');
const ExamRecord = require('../../models/ExamRecord');
const ExamReviewApplication = require('../../models/ExamReviewApplication');
const CertificateRecord = require('../../models/CertificateRecord');
const UserAchievement = require('../../models/UserAchievement');
const KnowledgeBase = require('../../models/knowledge-base');
const { Op } = require('sequelize');
const fs = require('fs');
const path = require('path');
const xlsx = require('xlsx');
const multer = require('multer');
const { addEnterpriseFilter, addEnterpriseId, addEnterpriseIdToArray } = require('../../utils/enterpriseFilter');
// 设置模型关联
Employee.setupEmployeePositionAssociations();
// 配置上传存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 限制文件大小为10MB
  },
  fileFilter: function (req, file, cb) {
    console.log('上传文件信息:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    });

    // 检查文件扩展名
    const extname = path.extname(file.originalname).toLowerCase();
    const allowedExtensions = ['.xlsx', '.xls'];

    // 检查MIME类型（更宽松的检查）
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'application/excel',
      'application/x-excel',
      'application/x-msexcel',
      'application/octet-stream' // 某些情况下Excel文件可能被识别为这种类型
    ];

    if (allowedExtensions.includes(extname) &&
        (allowedMimeTypes.includes(file.mimetype) || file.mimetype.includes('excel') || file.mimetype.includes('spreadsheet'))) {
      return cb(null, true);
    }

    console.error('文件类型验证失败:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      extname: extname
    });

    cb(new Error('只能上传Excel文件(.xlsx, .xls)'));
  }
}).single('file');

/**
 * 递归获取部门及其所有子部门的ID列表
 * @param {Number} departmentId 部门ID
 * @param {Array} allDepartments 所有部门列表
 * @returns {Array} 部门及其所有子部门的ID列表
 */
const getAllChildDepartmentIds = async (departmentId, allDepartments = null) => {
  // 如果没有传入所有部门列表，则先查询所有部门
  if (!allDepartments) {
    allDepartments = await Department.findAll();
  }

  const result = [departmentId];

  // 查找当前部门的直接子部门
  const childDepartments = allDepartments.filter(dept => dept.parentId === departmentId);

  // 递归遍历每个子部门
  for (const childDept of childDepartments) {
    const childIds = await getAllChildDepartmentIds(childDept.id, allDepartments);
    result.push(...childIds);
  }

  return result;
};



/**
 * 获取部门的完整路径名称（包含所有上级部门）
 * @param {Number} departmentId 部门ID
 * @param {Array} allDepartments 所有部门列表
 * @returns {String} 完整的部门路径名称，如"事业部/奥城店/前厅"
 */
const getFullDepartmentPath = async (departmentId, allDepartments = null) => {
  // 如果没有传入所有部门列表，则先查询所有部门
  if (!allDepartments) {
    allDepartments = await Department.findAll(
      addEnterpriseFilter()
    );
  }

  if (!departmentId) {
    return '';
  }

  // 查找当前部门
  const department = allDepartments.find(dept => dept.id === departmentId);
  if (!department) {
    return '';
  }

  // 如果没有父部门，直接返回当前部门名称
  if (!department.parentId) {
    return department.name;
  }

  // 递归获取父部门路径
  const parentPath = await getFullDepartmentPath(department.parentId, allDepartments);

  // 拼接完整路径
  return parentPath ? `${parentPath}/${department.name}` : department.name;
};

/**
 * 获取员工列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeeList = async (req, res) => {
  try {
    const { name, status, departmentId, phone, pageNum = 1, pageSize = 10 } = req.query;

    // 构建查询条件
    const where = {};
    if (name) {
      where.name = {
        [Op.like]: `%${name}%`
      };
    }
    if (status !== undefined) {
      where.status = status;
    }
    if (departmentId) {
      where.departmentId = departmentId;
    }
    if (phone) {
      where.phone = {
        [Op.like]: `%${phone}%`
      };
    }

    // 分页查询
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询员工数据，添加企业ID过滤
    const { count, rows } = await Employee.findAndCountAll(
      addEnterpriseFilter({
        where,
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name']
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name']
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name']
          }
        ],
        offset,
        limit,
        order: [
          ['id', 'DESC']
        ]
      })
    );

    // 处理数据，添加department、position、level字段
    const data = rows.map(employee => {
      const item = employee.toJSON();

      // 处理关联数据
      item.department = item.department ? item.department.name : '';
      item.position = item.positionName ? item.positionName.name : '';
      item.level = item.level ? item.level.name : '';

      return item;
    });

    res.json({
      code: 200,
      data: {
        total: count,
        rows: data,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      },
      message: '获取员工列表成功'
    });
  } catch (error) {
    console.error('获取员工列表失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取员工列表失败',
      error: error.message
    });
  }
};

/**
 * 处理部门ID条件，获取包含子部门的部门ID列表
 * @param {string|number} departmentId 部门ID
 * @param {boolean} includeChildren 是否包含子部门
 * @returns {Promise<Object>} 查询条件
 */
const buildDepartmentWhereClause = async (departmentId, includeChildren) => {
  if (!departmentId || departmentId === 'null') {
    return {};
  }

  if (includeChildren === 'true') {
    // 查询所有部门，添加企业ID过滤
    const allDepartments = await Department.findAll(
      addEnterpriseFilter()
    );

    // 获取部门及其所有子部门的ID
    const departmentIds = await getAllChildDepartmentIds(parseInt(departmentId), allDepartments);

    // 查询这些部门下的员工
    return {
      departmentId: {
        [Op.in]: departmentIds
      }
    };
  } else {
    // 只查询当前部门的员工
    return { departmentId };
  }
};

/**
 * 构建员工查询条件
 * @param {Object} queryParams 查询参数
 * @returns {Promise<Object>} 查询条件
 */
const buildEmployeeWhereClause = async (queryParams) => {
  const { name, status, departmentId, includeChildren } = queryParams;
  let where = {};

  // 添加姓名条件
  if (name) {
    where.name = {
      [Op.like]: `%${name}%`
    };
  }

  // 添加状态条件
  if (status !== undefined) {
    where.status = status;
  }

  // 添加部门条件
  const departmentWhere = await buildDepartmentWhereClause(departmentId, includeChildren);
  where = { ...where, ...departmentWhere };

  return where;
};

/**
 * 获取员工查询的关联模型配置
 * @returns {Array} 关联模型配置
 */
const getEmployeeIncludeOptions = () => {
  return [
    {
      model: Department,
      as: 'department',
      attributes: ['id', 'name']
    },
    {
      model: PositionName,
      as: 'positionName',
      attributes: ['id', 'name']
    },
    {
      model: Level,
      as: 'level',
      attributes: ['id', 'name']
    },
    {
      model: EmployeePosition,
      as: 'employeePositions',
      include: [
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name', 'typeId']
        },
        {
          model: PositionType,
          as: 'positionType',
          attributes: ['id', 'name']
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name', 'code']
        }
      ]
    }
  ];
};

/**
 * 处理员工数据，格式化返回结果
 * @param {Object} employee 员工数据
 * @returns {Object} 格式化后的员工数据
 */
const formatEmployeeData = (employee) => {
  const item = employee.toJSON();

  // 处理关联数据
  item.department = item.department ? item.department.name : '';
  item.position = item.positionName ? item.positionName.name : '';
  item.level = item.level ? item.level.name : '';

  // 处理岗位配置信息
  if (item.employeePositions && item.employeePositions.length > 0) {
    item.positions = item.employeePositions.map(ep => ({
      id: ep.id,
      employeeId: ep.employeeId,
      positionId: ep.positionId,
      positionTypeId: ep.positionTypeId,
      levelId: ep.levelId,
      isDefault: ep.isDefault,
      positionName: ep.positionName?.name || '',
      positionTypeName: ep.positionType?.name || '',
      levelName: ep.level?.name || '',
      createTime: ep.createTime,
      updateTime: ep.updateTime
    }));
    // 添加岗位总数
    item.positionCount = item.employeePositions.length;
  } else {
    item.positions = [];
    item.positionCount = 0;
  }

  // 移除原始employeePositions数据，保持返回数据结构清晰
  delete item.employeePositions;

  return item;
};

/**
 * 统一的错误处理函数
 * @param {Error} error 错误对象
 * @param {Object} res 响应对象
 * @param {string} operation 操作名称
 */
const handleError = (error, res, operation) => {
  console.error(`${operation}失败：`, error);
  res.status(500).json({
    code: 500,
    message: `${operation}失败`,
    error: error.message
  });
};

/**
 * 根据部门ID获取员工
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeeByDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { name, status, pageNum = 1, pageSize = 10, includeChildren = 'true' } = req.query;

    // 构建查询条件
    const where = await buildEmployeeWhereClause({ name, status, departmentId, includeChildren });

    // 分页查询
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询员工数据，添加企业ID过滤
    // 使用 distinct: true 确保 count 计算的是主表记录数，而不是关联表记录数
    const { count, rows } = await Employee.findAndCountAll(
      addEnterpriseFilter({
        where,
        include: getEmployeeIncludeOptions(),
        offset,
        limit,
        order: [
          ['id', 'DESC']
        ],
        distinct: true  // 确保 count 计算主表记录数
      })
    );

    // 处理数据，添加department、position、level字段和岗位配置信息
    const data = rows.map(formatEmployeeData);

    res.json({
      code: 200,
      data: {
        total: count,
        rows: data,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      },
      message: '获取员工列表成功'
    });
  } catch (error) {
    handleError(error, res, '获取员工列表');
  }
};

/**
 * 获取员工详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeeDetail = async (req, res) => {
  try {
    const { id } = req.params;

    // 添加企业ID过滤
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name']
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name']
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name']
          }
        ]
      })
    );

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    res.json({
      code: 200,
      data: employee,
      message: '获取员工详情成功'
    });
  } catch (error) {
    console.error('获取员工详情失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取员工详情失败',
      error: error.message
    });
  }
};

/**
 * 处理员工晋升记录
 * @param {Object} employee 员工对象
 * @param {Number} positionTypeId 岗位类型ID
 * @param {Number} positionId 岗位ID
 * @param {Number} levelId 岗位等级ID
 * @param {Object} user 当前操作用户
 * @param {Boolean} isNewEmployee 是否是新员工
 * @returns {Promise<Object|null>} 创建或更新的晋升记录
 */
const handleEmployeePromotion = async (employee, positionTypeId, positionId, levelId, user, isNewEmployee = false) => {
  try {


    // 检查是否有岗位信息
    const hasPositionInfo = positionTypeId !== undefined || positionId !== undefined || levelId !== undefined;


    // 如果没有岗位信息或没有openId，则不处理晋升记录
    if (!hasPositionInfo) {

      return null;
    }

    if (!employee.id) {

      return null;
    }

    const openId = employee.openId;
    const employeeId = employee.id;
    const currentTime = new Date();
    const username = user ? user.username : 'system';


    const existingPromotion = await EmployeePromotion.findOne(
      addEnterpriseFilter({
        where: {
          employeeId,
          positionTypeId,
          positionId,
          levelId
        }
      })
    );



    let result;

    if (existingPromotion) {
      // 若存在相同的晋升记录，则更新时间

      await existingPromotion.update({
        promotionTime: currentTime,
        updateBy: username,
        openId,
        updateTime: currentTime
      });

      result = existingPromotion;
    } else {
      // 若不存在，则创建新记录

      const createdPromotion = await EmployeePromotion.create(

        addEnterpriseId({
          employeeId,
          openId,
          positionTypeId,
          positionId,
          levelId,
          promotionTime: currentTime,
          createBy: username,
          updateBy: username
        })
      );
      console.log(`创建员工 ${employee.id} 的晋升记录成功，新记录ID: ${createdPromotion.id}`);
      result = createdPromotion;
    }

    return result;
  } catch (error) {
    console.error('处理员工晋升记录时出错：', error);
    console.log('错误堆栈：', error.stack);
    // 不抛出异常，避免影响主要业务流程
    return null;
  }
};

/**
 * 更新员工
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateEmployee = async (req, res) => {
  try {
    console.log('=== 开始更新员工 ===');
    console.log('请求参数:', req.body);

    const {
      id,
      name,
      departmentId,
      positionId,
      levelId,
      phone,
      idCard,
      gender,
      entryTime,
      status,
      isActivated,
      remark,
      typeId,
      positions // 新增：多岗位配置数组 [{ positionId, levelId, isDefault }]
    } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '员工ID不能为空'
      });
    }

    // 查询员工是否存在，添加企业ID过滤
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!employee) {
      console.log(`员工ID ${id} 不存在`);
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    console.log(`查询到员工: ID=${employee.id}, 姓名=${employee.name}, openId=${employee.openId}`);
    console.log(`当前岗位信息: 岗位类型ID=${employee.positionTypeId}, 岗位ID=${employee.positionId}, 岗位等级ID=${employee.levelId}`);

    // 处理岗位配置，确定最终的默认岗位信息
    let finalDefaultPositionId = positionId;
    let finalDefaultLevelId = levelId;
    let finalDefaultTypeId = typeId;

    // 如果有多岗位配置，处理默认岗位
    if (positions && Array.isArray(positions) && positions.length > 0) {
      console.log('处理多岗位配置:', positions);

      // 验证是否有且仅有一个默认岗位
      const defaultPositions = positions.filter(p => p.isDefault);
      if (defaultPositions.length !== 1) {
        return res.status(400).json({
          code: 400,
          message: '必须设置一个且仅有一个默认岗位'
        });
      }

      // 验证岗位和等级是否存在
      for (const pos of positions) {
        // 验证positionTypeId、positionId、levelId都不能为空
        if (!pos.positionTypeId || !pos.positionId || !pos.levelId) {
          return res.status(400).json({
            code: 400,
            message: '岗位类型ID、岗位ID、等级ID不能为空'
          });
        }

        // 验证岗位类型是否存在（从PositionType模型验证）
        const positionType = await PositionType.findOne(
          addEnterpriseFilter({
            where: { id: pos.positionTypeId }
          })
        );
        if (!positionType) {
          return res.status(400).json({
            code: 400,
            message: `岗位类型ID ${pos.positionTypeId} 不存在`
          });
        }

        // 验证岗位是否存在（从PositionName模型验证）
        const positionName = await PositionName.findOne(
          addEnterpriseFilter({
            where: { id: pos.positionId }
          })
        );
        if (!positionName) {
          return res.status(400).json({
            code: 400,
            message: `岗位ID ${pos.positionId} 不存在`
          });
        }

        // 验证岗位是否属于指定的岗位类型
        if (positionName.typeId !== pos.positionTypeId) {
          return res.status(400).json({
            code: 400,
            message: `岗位ID ${pos.positionId} 不属于岗位类型ID ${pos.positionTypeId}`
          });
        }

        const level = await Level.findOne(
          addEnterpriseFilter({
            where: { id: pos.levelId }
          })
        );
        if (!level) {
          return res.status(400).json({
            code: 400,
            message: `等级ID ${pos.levelId} 不存在`
          });
        }

        // 如果是默认岗位，记录信息用于员工表
        if (pos.isDefault) {
          finalDefaultPositionId = pos.positionId;
          finalDefaultLevelId = pos.levelId;
          finalDefaultTypeId = pos.positionTypeId;
        }
      }
    }

    console.log(`最终默认岗位信息: 岗位类型ID=${finalDefaultTypeId}, 岗位ID=${finalDefaultPositionId}, 岗位等级ID=${finalDefaultLevelId}`);

    // 在更新前保存原始值，用于后续判断是否有变更
    const originalPositionTypeId = employee.positionTypeId;
    const originalPositionId = employee.positionId;
    const originalLevelId = employee.levelId;

    // 判断是否修改了岗位相关信息（岗位类型、岗位或岗位等级）
    const isPositionTypeChanged = finalDefaultTypeId !== undefined && originalPositionTypeId !== finalDefaultTypeId;
    const isPositionChanged = finalDefaultPositionId !== undefined && originalPositionId !== finalDefaultPositionId;
    const isLevelChanged = finalDefaultLevelId !== undefined && originalLevelId !== finalDefaultLevelId;

    console.log(`岗位类型是否将变更: ${isPositionTypeChanged}`);
    console.log(`岗位是否将变更: ${isPositionChanged}`);
    console.log(`岗位等级是否将变更: ${isLevelChanged}`);

    // 校验手机号是否已存在（排除当前员工），添加企业ID过滤
    if (phone && phone !== employee.phone) {
      const existEmployee = await Employee.findOne(
        addEnterpriseFilter({
          where: {
            phone,
            id: {
              [Op.ne]: id
            }
          }
        })
      );

      if (existEmployee) {
        return res.status(400).json({
          code: 400,
          message: '手机号已存在'
        });
      }
    }

    // 校验身份证号是否已存在（排除当前员工），添加企业ID过滤
    if (idCard && idCard !== employee.idCard) {
      const existEmployee = await Employee.findOne(
        addEnterpriseFilter({
          where: {
            idCard,
            id: {
              [Op.ne]: id
            }
          }
        })
      );

      if (existEmployee) {
        return res.status(400).json({
          code: 400,
          message: '身份证号已存在'
        });
      }
    }

    // 检查是否是离职操作
    const isResignation = status !== undefined && status === '0' && employee.status === '1';

    // 检查是否是在职操作（任务3）
    const isReemployment = status !== undefined && status === '1' && employee.status === '0';

    // 更新员工
    console.log('更新员工信息...');
    await employee.update({
      name: name || employee.name,
      departmentId: departmentId === undefined ? employee.departmentId : departmentId,
      positionId: finalDefaultPositionId === undefined ? employee.positionId : finalDefaultPositionId,
      levelId: finalDefaultLevelId === undefined ? employee.levelId : finalDefaultLevelId,
      phone: phone === undefined ? employee.phone : phone,
      idCard: idCard === undefined ? employee.idCard : idCard,
      gender: gender === undefined ? employee.gender : gender,
      entryTime: entryTime === undefined ? employee.entryTime : entryTime,
      status: status === undefined ? employee.status : status,
      isActivated: isActivated === undefined ? employee.isActivated : isActivated,
      remark: remark === undefined ? employee.remark : remark,
      positionTypeId: finalDefaultTypeId === undefined ? employee.positionTypeId : finalDefaultTypeId,
      updateBy: req.user ? req.user.username : 'admin',
      updateTime: new Date()
    });

    console.log('员工信息更新成功');

    // 如果是离职操作，执行离职相关处理
    if (isResignation) {
      console.log('=== 开始处理员工离职 ===');
      await handleEmployeeResignation(employee, req.user);
      console.log('=== 员工离职处理完成 ===');
    }

    // 如果是在职操作，执行在职相关处理（任务3）
    if (isReemployment) {
      console.log('=== 开始处理员工在职 ===');
      await handleEmployeeReemployment(employee, req.user);
      console.log('=== 员工在职处理完成 ===');
    }

    // 如果有多岗位配置，更新岗位配置记录
    if (positions && Array.isArray(positions) && positions.length > 0) {
      console.log('更新员工岗位配置记录...');

      // 开始事务处理
      const transaction = await Employee.sequelize.transaction();

      try {
        console.log(`准备删除员工ID ${id} 的岗位配置...`);

        // 删除员工现有的岗位配置
        const deleteCondition = addEnterpriseFilter({ where: { employeeId: id } }).where;
        console.log('删除条件:', JSON.stringify(deleteCondition, null, 2));

        const deletedCount = await EmployeePosition.destroy({
          where: deleteCondition,
          transaction
        });

        console.log(`成功删除 ${deletedCount} 条岗位配置记录`);

        // 创建新的岗位配置
        const newPositions = positions.map(pos =>
          addEnterpriseId({
            employeeId: parseInt(id),
            positionId: pos.positionId,
            positionTypeId: pos.positionTypeId,
            levelId: pos.levelId,
            isDefault: pos.isDefault || false,
            createBy: req.user ? req.user.username : 'admin',
            updateBy: req.user ? req.user.username : 'admin'
          })
        );

        await EmployeePosition.bulkCreate(newPositions, { transaction });
        await transaction.commit();
        console.log('员工岗位配置记录更新成功');
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    }

    // 重新加载员工数据，确保获取最新信息
    await employee.reload();
    console.log(`更新后员工信息: 岗位类型ID=${employee.positionTypeId}, 岗位ID=${employee.positionId}, 岗位等级ID=${employee.levelId}`);

    // 处理员工晋升记录
    // 如果岗位相关信息有变更，则创建晋升记录
    if (isPositionTypeChanged || isPositionChanged || isLevelChanged) {
      console.log('开始处理员工晋升记录...');

      // 使用更新后的岗位信息
      const newPositionTypeId = finalDefaultTypeId !== undefined ? finalDefaultTypeId : originalPositionTypeId;
      const newPositionId = finalDefaultPositionId !== undefined ? finalDefaultPositionId : originalPositionId;
      const newLevelId = finalDefaultLevelId !== undefined ? finalDefaultLevelId : originalLevelId;

      await handleEmployeePromotion(
        employee,
        newPositionTypeId,
        newPositionId,
        newLevelId,
        req.user,
        false // 不是新员工
      );
    } else {
      console.log('岗位相关信息没有变更，不处理晋升记录');
    }

    console.log('=== 员工更新完成 ===');
    res.json({
      code: 200,
      data: employee,
      message: '更新员工成功'
    });
  } catch (error) {
    console.error('更新员工失败：', error);
    console.log('错误堆栈：', error.stack);
    res.status(500).json({
      code: 500,
      message: '更新员工失败',
      error: error.message
    });
  }
};

/**
 * 新增员工
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.addEmployee = async (req, res) => {
  try {
    console.log('=== 开始新增员工 ===');
    console.log('请求参数:', req.body);

    const {
      name,
      departmentId,
      positionId,
      levelId,
      phone,
      idCard,
      gender,
      entryTime,
      status,
      isActivated,
      remark,
      typeId,
      positions // 新增：多岗位配置数组 [{ positionId, levelId, isDefault }]
    } = req.body;

    // 校验必填字段
    if (!name) {
      return res.status(400).json({
        code: 400,
        message: '姓名不能为空'
      });
    }

    // 校验手机号是否已存在，添加企业ID过滤
    if (phone) {
      const existEmployee = await Employee.findOne(
        addEnterpriseFilter({
          where: {
            phone
          }
        })
      );

      if (existEmployee) {
        return res.status(400).json({
          code: 400,
          message: '手机号已存在'
        });
      }
    }

    // 校验身份证号是否已存在，添加企业ID过滤
    if (idCard) {
      const existEmployee = await Employee.findOne(
        addEnterpriseFilter({
          where: {
            idCard
          }
        })
      );

      if (existEmployee) {
        return res.status(400).json({
          code: 400,
          message: '身份证号已存在'
        });
      }
    }

    // 处理岗位配置
    let finalPositions = [];
    let defaultPositionId = null;
    let defaultLevelId = null;
    let defaultTypeId = null;

    if (positions && Array.isArray(positions) && positions.length > 0) {
      // 使用新的多岗位配置
      console.log('使用多岗位配置:', positions);

      // 验证是否有且仅有一个默认岗位
      const defaultPositions = positions.filter(p => p.isDefault);
      if (defaultPositions.length !== 1) {
        return res.status(400).json({
          code: 400,
          message: '必须设置一个且仅有一个默认岗位'
        });
      }

      // 验证岗位和等级是否存在
      for (const pos of positions) {
        // 验证positionTypeId、positionId、levelId都不能为空
        if (!pos.positionTypeId || !pos.positionId || !pos.levelId) {
          return res.status(400).json({
            code: 400,
            message: '岗位类型ID、岗位ID、等级ID不能为空'
          });
        }

        // 验证岗位类型是否存在（从PositionType模型验证）
        const positionType = await PositionType.findOne(
          addEnterpriseFilter({
            where: { id: pos.positionTypeId }
          })
        );
        if (!positionType) {
          return res.status(400).json({
            code: 400,
            message: `岗位类型ID ${pos.positionTypeId} 不存在`
          });
        }

        // 验证岗位是否存在（从PositionName模型验证）
        const positionName = await PositionName.findOne(
          addEnterpriseFilter({
            where: { id: pos.positionId }
          })
        );
        if (!positionName) {
          return res.status(400).json({
            code: 400,
            message: `岗位ID ${pos.positionId} 不存在`
          });
        }

        // 验证岗位是否属于指定的岗位类型
        if (positionName.typeId !== pos.positionTypeId) {
          return res.status(400).json({
            code: 400,
            message: `岗位ID ${pos.positionId} 不属于岗位类型ID ${pos.positionTypeId}`
          });
        }

        const level = await Level.findOne(
          addEnterpriseFilter({
            where: { id: pos.levelId }
          })
        );
        if (!level) {
          return res.status(400).json({
            code: 400,
            message: `等级ID ${pos.levelId} 不存在`
          });
        }

        // 如果是默认岗位，记录信息用于员工表
        if (pos.isDefault) {
          defaultPositionId = pos.positionId;
          defaultLevelId = pos.levelId;
          defaultTypeId = pos.positionTypeId;
        }
      }

      finalPositions = positions;
    } else if (positionId && levelId) {
      // 使用传统的单岗位配置（向后兼容）
      console.log('使用传统单岗位配置');
      finalPositions = [{
        positionId: positionId,
        positionTypeId: typeId,
        levelId: levelId,
        isDefault: true
      }];
      defaultPositionId = positionId;
      defaultLevelId = levelId;
      defaultTypeId = typeId;
    }

    // 检查是否有岗位相关信息
    const hasPositionInfo = finalPositions.length > 0;
    console.log(`是否有岗位相关信息: ${hasPositionInfo}`);
    console.log(`最终岗位配置:`, finalPositions);

    // 开始事务处理
    const transaction = await Employee.sequelize.transaction();

    try {
      // 创建员工，添加企业ID
      console.log('创建员工...');
      const employee = await Employee.create(
        addEnterpriseId({
          name,
          departmentId,
          positionId: defaultPositionId,
          levelId: defaultLevelId,
          phone,
          positionTypeId: defaultTypeId,
          idCard,
          gender,
          entryTime,
          status: status || '1',
          isActivated: isActivated || false,
          remark,
          createBy: req.user ? req.user.username : 'admin',
          createTime: new Date()
        }),
        { transaction }
      );

      console.log(`员工创建成功，ID=${employee.id}`);

      // 创建岗位配置记录
      if (hasPositionInfo) {
        console.log('创建员工岗位配置记录...');

        const positionRecords = finalPositions.map(pos =>
          addEnterpriseId({
            employeeId: employee.id,
            positionId: pos.positionId,
            positionTypeId: pos.positionTypeId,
            levelId: pos.levelId,
            isDefault: pos.isDefault || false,
            createBy: req.user ? req.user.username : 'admin',
            updateBy: req.user ? req.user.username : 'admin'
          })
        );

        await EmployeePosition.bulkCreate(positionRecords, { transaction });
        console.log('员工岗位配置记录创建成功');

        // 为新员工添加初始晋升记录（保持原有逻辑）
        console.log('开始处理新员工晋升记录...');

        await handleEmployeePromotion(
          employee,
          defaultTypeId,
          defaultPositionId,
          defaultLevelId,
          req.user,
          true // 是新员工
        );
      } else {
        console.log('没有岗位相关信息，不创建岗位配置和晋升记录');
      }

      await transaction.commit();
      console.log('=== 新增员工完成 ===');

      res.json({
        code: 200,
        data: employee,
        message: '新增员工成功'
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('新增员工失败：', error);
    console.log('错误堆栈：', error.stack);
    res.status(500).json({
      code: 500,
      message: '新增员工失败',
      error: error.message
    });
  }
};

/**
 * 删除员工
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deleteEmployee = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询员工是否存在，添加企业ID过滤
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    // 删除员工
    await employee.destroy();

    res.json({
      code: 200,
      message: '删除员工成功'
    });
  } catch (error) {
    console.error('删除员工失败：', error);
    res.status(500).json({
      code: 500,
      message: '删除员工失败',
      error: error.message
    });
  }
};

/**
 * 更新员工状态
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateEmployeeStatus = async (req, res) => {
  try {
    const { id, status } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '员工ID不能为空'
      });
    }

    if (status === undefined) {
      return res.status(400).json({
        code: 400,
        message: '状态不能为空'
      });
    }

    // 查询员工是否存在，添加企业ID过滤
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    // 更新状态
    await employee.update({
      status,
      updateBy: req.user ? req.user.username : 'admin', // 假设请求中有用户信息
      updateTime: new Date()
    });

    res.json({
      code: 200,
      data: employee,
      message: '更新员工状态成功'
    });
  } catch (error) {
    console.error('更新员工状态失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新员工状态失败',
      error: error.message
    });
  }
};

/**
 * 更新员工小程序激活状态
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateEmployeeActivation = async (req, res) => {
  try {
    const { id, isActivated } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '员工ID不能为空'
      });
    }

    if (isActivated === undefined) {
      return res.status(400).json({
        code: 400,
        message: '激活状态不能为空'
      });
    }

    // 查询员工是否存在，添加企业ID过滤
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    // 更新激活状态
    await employee.update({
      isActivated,
      updateBy: req.user ? req.user.username : 'admin', // 假设请求中有用户信息
      updateTime: new Date()
    });

    res.json({
      code: 200,
      data: employee,
      message: '更新员工小程序激活状态成功'
    });
  } catch (error) {
    console.error('更新员工小程序激活状态失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新员工小程序激活状态失败',
      error: error.message
    });
  }
};

/**
 * 导入员工
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.importEmployees = async (req, res) => {
  upload(req, res, async function (err) {
    if (err) {
      return res.status(400).json({
        code: 400,
        message: err.message
      });
    }

    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请选择要上传的文件'
      });
    }

    try {
      const filePath = req.file.path;

      // 读取Excel文件
      const workbook = xlsx.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      // 将Excel数据转换为JSON，跳过第1行的说明文字，从第2行开始读取
      const excelData = xlsx.utils.sheet_to_json(sheet, {
        range: 1 // 从第2行开始读取（0-based索引，1表示第2行）
      });

      if (excelData.length === 0) {
        // 删除临时文件
        fs.unlinkSync(filePath);
        return res.status(400).json({
          code: 400,
          message: 'Excel文件中没有数据'
        });
      }

      // 添加调试信息
      console.log('Excel数据总行数:', excelData.length);
      console.log('Excel第一行数据:', JSON.stringify(excelData[0], null, 2));
      console.log('Excel列名:', Object.keys(excelData[0] || {}));

      // 检查前几行的姓名字段
      for (let i = 0; i < Math.min(3, excelData.length); i++) {
        const row = excelData[i];
        console.log(`第${i + 2}行数据:`, JSON.stringify(row, null, 2));
        console.log(`第${i + 2}行姓名字段值:`, row['姓名']);
        console.log(`第${i + 2}行姓名字段类型:`, typeof row['姓名']);
        console.log(`第${i + 2}行姓名字段是否为空:`, !row['姓名'] || row['姓名'].toString().trim() === '');
      }

      // 预加载所有需要的基础数据，用于验证
      console.log('开始预加载基础数据用于验证...');
      const [allDepartments, allPositionTypes, allPositionNames, allLevels] = await Promise.all([
        Department.findAll(addEnterpriseFilter()),
        PositionType.findAll(addEnterpriseFilter()),
        PositionName.findAll(addEnterpriseFilter()),
        Level.findAll(addEnterpriseFilter())
      ]);

      console.log(`加载完成: 部门${allDepartments.length}个, 岗位类型${allPositionTypes.length}个, 岗位名称${allPositionNames.length}个, 岗位等级${allLevels.length}个`);

      // 数据验证函数
      const validateRowData = (row, rowIndex) => {
        const errors = [];
        const rowNum = rowIndex + 2; // Excel行号（从第2行开始是数据）

        // 1. 验证必填字段
        if (!row['姓名'] || row['姓名'].toString().trim() === '') {
          errors.push(`第${rowNum}行：姓名不能为空`);
        }

        // 2. 验证归属部门层级
        if (row['归属部门']) {
          const departmentPath = row['归属部门'].toString().split('/');
          let currentDepartment = null;
          let foundPath = [];

          for (let i = 0; i < departmentPath.length; i++) {
            const deptName = departmentPath[i].trim();
            if (!deptName) {
              errors.push(`第${rowNum}行：归属部门格式错误，部门名称不能为空`);
              break;
            }

            const department = allDepartments.find(dept =>
              dept.name === deptName &&
              (currentDepartment ? dept.parentId === currentDepartment.id : dept.parentId === null || dept.parentId === 0)
            );

            if (!department) {
              const pathStr = foundPath.length > 0 ? foundPath.join('/') + '/' + deptName : deptName;
              errors.push(`第${rowNum}行：归属部门"${pathStr}"不存在或层级关系错误`);
              break;
            }

            currentDepartment = department;
            foundPath.push(deptName);
          }
        }

        // 3. 验证岗位级别格式（岗位类型-岗位名称-岗位等级，多岗位用/分隔）
        if (row['岗位级别']) {
          const positionLevelStr = row['岗位级别'].toString();
          const positionGroups = positionLevelStr.split('/'); // 按/分隔多岗位

          for (let j = 0; j < positionGroups.length; j++) {
            const positionGroup = positionGroups[j].trim();
            if (!positionGroup) {
              errors.push(`第${rowNum}行：岗位级别格式错误，岗位信息不能为空`);
              continue;
            }

            const parts = positionGroup.split('-');

            if (parts.length !== 3) {
              errors.push(`第${rowNum}行：岗位级别格式错误，每个岗位应为"岗位类型-岗位名称-岗位等级"，当前格式："${positionGroup}"`);
              continue;
            }

            const [typeName, positionName, levelName] = parts.map(p => p.trim());

            // 验证岗位类型
            const positionType = allPositionTypes.find(type => type.name === typeName);
            if (!positionType) {
              errors.push(`第${rowNum}行：岗位类型"${typeName}"不存在`);
            }

            // 验证岗位名称
            const positionNameRecord = allPositionNames.find(pos => pos.name === positionName);
            if (!positionNameRecord) {
              errors.push(`第${rowNum}行：岗位名称"${positionName}"不存在`);
            } else if (positionType && positionNameRecord.typeId !== positionType.id) {
              errors.push(`第${rowNum}行：岗位名称"${positionName}"不属于岗位类型"${typeName}"`);
            }

            // 验证岗位等级
            const level = allLevels.find(lvl => lvl.name === levelName);
            if (!level) {
              errors.push(`第${rowNum}行：岗位等级"${levelName}"不存在`);
            }
          }
        }

        // 4. 验证性别
        if (row['性别']) {
          const gender = row['性别'].toString().trim();
          if (gender !== '男' && gender !== '女') {
            errors.push(`第${rowNum}行：性别只能填写"男"或"女"，当前填写："${gender}"`);
          }
        }

        // 5. 验证状态
        if (row['状态']) {
          const status = row['状态'].toString().trim();
          if (status !== '在职' && status !== '离职') {
            errors.push(`第${rowNum}行：状态只能填写"在职"或"离职"，当前填写："${status}"`);
          }
        }

        // 6. 验证手机号格式（如果填写了）
        if (row['手机号']) {
          const phone = row['手机号'].toString().trim();
          if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
            errors.push(`第${rowNum}行：手机号格式错误，应为11位数字，当前填写："${phone}"`);
          }
        }

        // 7. 验证身份证号格式（如果填写了）
        if (row['身份证号']) {
          const idCard = row['身份证号'].toString().trim();
          if (idCard && !/^(^\d{17}(\d|X)$)/.test(idCard)) {
            errors.push(`第${rowNum}行：身份证号格式错误，应为18位数字或最后一位为大写X，当前填写："${idCard}"`);
          }
        }

        return errors;
      };

      // 对所有数据进行验证
      console.log('开始验证Excel数据...');
      let allErrors = [];

      for (let i = 0; i < excelData.length; i++) {
        const row = excelData[i];
        const errors = validateRowData(row, i);
        allErrors.push(...errors);
      }

      // 如果有任何验证错误，直接返回错误信息
      if (allErrors.length > 0) {
        // 删除临时文件
        fs.unlinkSync(filePath);
        return res.status(400).json({
          code: 400,
          message: '数据验证失败，请修正以下错误：',
          errors: allErrors
        });
      }

      console.log('数据验证通过，开始导入...');

      // 导入的员工列表
      const employees = [];

      // 遍历Excel数据，创建员工
      for (let i = 0; i < excelData.length; i++) {
        const row = excelData[i];
        console.log(`开始处理第${i + 2}行数据:`, row);

        // 校验必填字段（与验证阶段保持一致）
        if (!row['姓名'] || row['姓名'].toString().trim() === '') {
          console.log(`第${i + 2}行：姓名为空，跳过处理`);
          continue;
        }

        // 解析部门层级
        let departmentId = null;
        if (row['归属部门']) {
          const departmentPath = row['归属部门'].toString().split('/');
          let currentDepartment = null;

          for (const deptName of departmentPath) {
            const department = allDepartments.find(dept =>
              dept.name === deptName.trim() &&
              (currentDepartment ? dept.parentId === currentDepartment.id : dept.parentId === null || dept.parentId === 0)
            );

            if (department) {
              currentDepartment = department;
            } else {
              break;
            }
          }

          if (currentDepartment) {
            departmentId = currentDepartment.id;
          }
        }

        // 解析岗位级别
        let positionId = null;
        let levelId = null;
        let positionTypeId = null;

        if (row['岗位级别']) {
          const positionLevelStr = row['岗位级别'].toString();
          const positionGroups = positionLevelStr.split('/'); // 按/分隔多岗位

          // 取第一个岗位作为主岗位
          if (positionGroups.length > 0) {
            const firstPositionGroup = positionGroups[0].trim();
            const parts = firstPositionGroup.split('-');

            if (parts.length === 3) {
              const [typeName, positionName, levelName] = parts.map(p => p.trim());

              // 查找岗位类型
              const positionType = allPositionTypes.find(type => type.name === typeName);
              if (positionType) {
                positionTypeId = positionType.id;
              }

              // 查找岗位名称 - 直接使用PositionName的id作为positionId
              const positionNameRecord = allPositionNames.find(pos => pos.name === positionName);
              if (positionNameRecord) {
                positionId = positionNameRecord.id; // 直接使用PositionName的id
              }

              // 查找岗位等级
              const level = allLevels.find(lvl => lvl.name === levelName);
              if (level) {
                levelId = level.id;
              }
            }
          }
        }

        // 处理入职时间
        let entryTime = null;
        if (row['入职时间']) {
          console.log(`第${i + 2}行入职时间原始值:`, row['入职时间']);
          console.log(`第${i + 2}行入职时间类型:`, typeof row['入职时间']);

          try {
            // Excel中的日期可能是数字格式（Excel日期序列号）
            if (typeof row['入职时间'] === 'number') {
              // Excel日期序列号转换为JavaScript日期
              // Excel的日期基准是1899年12月30日，UTC时间是1970年1月1日
              // 25569是从1899年12月30日到1970年1月1日的天数差
              // 86400000是一天的毫秒数（24 * 60 * 60 * 1000）

              console.log(`第${i + 2}行Excel序列号:`, row['入职时间']);

              // 先转换为UTC时间戳
              const utcTime = (row['入职时间'] - 25569) * 86400000;

              // 创建Date对象
              entryTime = new Date(utcTime);

              // 验证日期是否有效且合理
              if (isNaN(entryTime.getTime()) || entryTime.getFullYear() < 1900 || entryTime.getFullYear() > 2100) {
                console.log(`第${i + 2}行Excel序列号转换的日期无效，尝试调整算法`);
                // Excel的1900年闰年错误处理
                // 对于1900年3月1日之后的日期，Excel多算了一天
                let adjustedSerialNumber = row['入职时间'];
                if (adjustedSerialNumber > 59) { // 大于1900年2月28日
                  adjustedSerialNumber = adjustedSerialNumber - 1;
                }

                // 重新计算
                const adjustedUtcTime = (adjustedSerialNumber - 25569) * 86400000;
                entryTime = new Date(adjustedUtcTime);

                if (isNaN(entryTime.getTime()) || entryTime.getFullYear() < 1900 || entryTime.getFullYear() > 2100) {
                  console.log(`第${i + 2}行调整后仍无效，设为null`);
                  entryTime = null;
                }
              }

              console.log(`第${i + 2}行Excel序列号转换后的日期:`, entryTime);
            } else if (typeof row['入职时间'] === 'string') {
              // 如果是字符串，尝试解析
              const dateStr = row['入职时间'].toString().trim();
              if (dateStr) {
                entryTime = new Date(dateStr);
                if (isNaN(entryTime.getTime())) {
                  // 尝试解析常见的中文日期格式
                  const matches = dateStr.match(/(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日]?/);
                  if (matches) {
                    const [, year, month, day] = matches;
                    entryTime = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
                  }
                }
              }
              console.log(`第${i + 2}行字符串转换后的日期:`, entryTime);
            } else {
              // 其他格式，尝试直接转换
              entryTime = new Date(row['入职时间']);
              console.log(`第${i + 2}行直接转换后的日期:`, entryTime);
            }

            if (!entryTime || isNaN(entryTime.getTime())) {
              console.log(`第${i + 2}行日期转换失败，设为null`);
              entryTime = null;
            } else {
              console.log(`第${i + 2}行最终日期:`, entryTime.toISOString().split('T')[0]);
            }
          } catch (e) {
            console.log(`第${i + 2}行日期转换异常:`, e.message);
            entryTime = null;
          }
        }

        // 创建员工数据
        const employee = {
          name: row['姓名'].toString().trim(),
          departmentId,
          positionId,
          levelId,
          positionTypeId,
          phone: row['手机号'] ? row['手机号'].toString().trim() : null,
          idCard: row['身份证号'] ? row['身份证号'].toString().trim() : null,
          gender: row['性别'] === '女' ? 'female' : 'male',
          entryTime,
          status: row['状态'] === '离职' ? '0' : '1',
          isActivated: false,
          remark: row['备注'] ? row['备注'].toString().trim() : null,
          createBy: req.user ? req.user.username : 'admin',
          createTime: new Date()
        };

        employees.push(employee);
      }

      // 批量创建员工，添加企业ID
      if (employees.length > 0) {
        await Employee.bulkCreate(
          addEnterpriseIdToArray(employees)
        );
      }

      // 删除临时文件
      fs.unlinkSync(filePath);

      console.log(`成功导入 ${employees.length} 名员工`);

      res.json({
        code: 200,
        data: {
          count: employees.length
        },
        message: `成功导入 ${employees.length} 名员工`
      });
    } catch (error) {
      console.error('导入员工失败：', error);

      // 删除临时文件
      if (req.file && req.file.path) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (e) {
          console.error('删除临时文件失败:', e);
        }
      }

      res.status(500).json({
        code: 500,
        message: '导入员工失败',
        error: error.message
      });
    }
  });
};

/**
 * 导出员工
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.exportEmployees = async (req, res) => {
  try {
    // 直接查询所有员工数据，添加企业ID过滤
    const employees = await Employee.findAll(
      addEnterpriseFilter({
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name'],
            required: false
          }
        ],
        order: [
          ['id', 'DESC']
        ]
      })
    );

    console.log(`查询到${employees.length}名员工`);

    // 处理数据，添加department、position、level字段
    const data = employees.map(employee => {
      const item = employee.toJSON();

      return {
        '姓名': item.name || '',
        '部门': item.department ? item.department.name : '',
        '岗位': item.positionName ? item.positionName.name : '',
        '岗位等级': item.level ? item.level.name : '',
        '手机号': item.phone || '',
        '身份证号': item.idCard || '',
        '性别': item.gender === 'male' ? '男' : (item.gender === 'female' ? '女' : ''),
        '入职时间': item.entryTime || '',
        '状态': item.status === '1' ? '在职' : '离职',
        '小程序激活': item.isActivated ? '已激活' : '未激活',
        '备注': item.remark || ''
      };
    });

    // 创建工作簿
    const workbook = xlsx.utils.book_new();

    // 创建工作表
    const worksheet = xlsx.utils.json_to_sheet(data.length > 0 ? data : [{
      '姓名': '',
      '部门': '',
      '岗位': '',
      '岗位等级': '',
      '手机号': '',
      '身份证号': '',
      '性别': '',
      '入职时间': '',
      '状态': '',
      '小程序激活': '',
      '备注': ''
    }], { header: [
      '姓名', '部门', '岗位', '岗位等级', '手机号', '身份证号',
      '性别', '入职时间', '状态', '小程序激活', '备注'
    ]});

    // 设置列宽
    const colWidths = [
      { wpx: 100 }, // 姓名
      { wpx: 120 }, // 部门
      { wpx: 120 }, // 岗位
      { wpx: 100 }, // 岗位等级
      { wpx: 120 }, // 手机号
      { wpx: 180 }, // 身份证号
      { wpx: 80 },  // 性别
      { wpx: 100 }, // 入职时间
      { wpx: 80 },  // 状态
      { wpx: 100 }, // 小程序激活
      { wpx: 200 }  // 备注
    ];

    worksheet['!cols'] = colWidths;

    // 添加工作表到工作簿
    xlsx.utils.book_append_sheet(workbook, worksheet, '员工数据');

    // 生成Excel文件
    const excelBuffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=员工数据.xlsx');

    // 发送Excel文件
    res.send(excelBuffer);
  } catch (error) {
    console.error('导出员工失败：', error);
    res.status(500).json({
      code: 500,
      message: '导出员工失败',
      error: error.message
    });
  }
};

/**
 * 下载员工导入模板
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.downloadEmployeeTemplate = async (req, res) => {
  try {
    const path = require('path');
    const fs = require('fs');

    // 模板文件路径
    const templatePath = path.join(__dirname, '../../../uploads/员工导入模板.xlsx');

    // 检查文件是否存在
    if (!fs.existsSync(templatePath)) {
      return res.status(404).json({
        code: 404,
        message: '模板文件不存在'
      });
    }

    // 读取文件
    const fileBuffer = fs.readFileSync(templatePath);

    // 使用纯英文文件名，避免HTTP头部中文字符问题
    const filename = 'employee-import-template.xlsx';

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', fileBuffer.length);

    // 发送文件
    res.send(fileBuffer);
  } catch (error) {
    console.error('下载模板失败：', error);
    res.status(500).json({
      code: 500,
      message: '下载模板失败',
      error: error.message
    });
  }
};

/**
 * 获取员工企业申请列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeeApplicationList = async (req, res) => {
  try {
    const {
      realName,
      phone,
      auditStatus,
      pageNum = 1,
      pageSize = 10
    } = req.query;

    // 构建查询条件
    const where = {};
    if (realName) {
      where.realName = {
        [Op.like]: `%${realName}%`
      };
    }
    if (phone) {
      where.phone = {
        [Op.like]: `%${phone}%`
      };
    }
    if (auditStatus) {
      where.auditStatus = auditStatus;
    }

    // 分页查询
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询申请数据，添加企业ID过滤
    const { count, rows } = await EmployeeEnterpriseApplication.findAndCountAll(
      addEnterpriseFilter({
        where,
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name'],
            required: false
          }
        ],
        offset,
        limit,
        order: [
          ['createTime', 'DESC']
        ]
      })
    );

    // 预加载所有部门数据，用于构建完整的部门路径
    const allDepartments = await Department.findAll(
      addEnterpriseFilter()
    );

    // 处理数据
    const data = await Promise.all(rows.map(async application => {
      const item = application.toJSON();

      // 处理关联数据
      // 获取完整的部门路径
      item.departmentName = item.department ?
        await getFullDepartmentPath(item.department.id, allDepartments) : '';
      item.positionName = item.positionName ? item.positionName.name : '';
      item.levelName = item.level ? item.level.name : '';
      item.positionTypeName = item.positionType ? item.positionType.name : '';

      // 隐藏敏感信息（身份证号和手机号部分隐藏）
      if (item.idCard) {
        item.idCardMasked = item.idCard.substring(0, 4) + '**********' + item.idCard.substring(14);
      }
      if (item.phone) {
        item.phoneMasked = item.phone.substring(0, 3) + '****' + item.phone.substring(7);
      }

      return item;
    }));

    res.json({
      code: 200,
      data: {
        total: count,
        rows: data,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      },
      message: '获取申请列表成功'
    });
  } catch (error) {
    console.error('获取申请列表失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取申请列表失败',
      error: error.message
    });
  }
};

/**
 * 获取员工企业申请详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeeApplicationDetail = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询申请详情，添加企业ID过滤
    const application = await EmployeeEnterpriseApplication.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name'],
            required: false
          }
        ]
      })
    );

    if (!application) {
      return res.status(404).json({
        code: 404,
        message: '申请记录不存在'
      });
    }

    const data = application.toJSON();

    // 处理关联数据
    // 获取完整的部门路径
    if (data.department) {
      // 获取所有部门数据，用于构建完整的部门路径
      const allDepartments = await Department.findAll(
        addEnterpriseFilter()
      );
      data.departmentName = await getFullDepartmentPath(data.department.id, allDepartments);
    } else {
      data.departmentName = '';
    }

    data.positionName = data.positionName ? data.positionName.name : '';
    data.levelName = data.level ? data.level.name : '';
    data.positionTypeName = data.positionType ? data.positionType.name : '';

    res.json({
      code: 200,
      data,
      message: '获取申请详情成功'
    });
  } catch (error) {
    console.error('获取申请详情失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取申请详情失败',
      error: error.message
    });
  }
};

/**
 * 审核员工企业申请
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.auditEmployeeApplication = async (req, res) => {
  try {
    const { id, auditStatus, auditRemark, currentUser } = req.body;

    console.log('收到审核请求参数:', { id, auditStatus, auditRemark, currentUser });

    // 验证当前用户信息
    if (!currentUser || !currentUser.id || !currentUser.username) {
      return res.status(400).json({
        code: 400,
        message: '当前用户信息不完整'
      });
    }

    // 验证审核状态
    if (!['通过', '驳回'].includes(auditStatus)) {
      return res.status(400).json({
        code: 400,
        message: '审核状态只能是"通过"或"驳回"'
      });
    }

    // 查询申请记录，添加企业ID过滤
    const application = await EmployeeEnterpriseApplication.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!application) {
      return res.status(404).json({
        code: 404,
        message: '申请记录不存在'
      });
    }

    // 检查申请状态
    if (application.auditStatus !== '审核中') {
      return res.status(400).json({
        code: 400,
        message: '该申请已审核，无法重复操作'
      });
    }

    // 如果审核通过，需要创建员工记录
    if (auditStatus === '通过') {
      // 检查是否已存在该openId的员工（优先通过openId查找）
      let existingEmployee = await Employee.findOne(
        addEnterpriseFilter({
          where: {
            openId: application.openId
          }
        })
      );

      // 如果通过openId没找到，再通过身份证号或手机号查找
      if (!existingEmployee) {
        existingEmployee = await Employee.findOne(
          addEnterpriseFilter({
            where: {
              [Op.or]: [
                { idCard: application.idCard },
                { phone: application.phone }
              ]
            }
          })
        );
      }

      if (existingEmployee) {
        // 如果员工已存在，更新openId绑定和状态，同时更新岗位信息
        // 处理入职时间：支持字符串转换为日期
        let finalEntryTime = null;
        if (application.entryTime) {
          if (typeof application.entryTime === 'string') {
            finalEntryTime = new Date(application.entryTime);
            // 验证日期是否有效
            if (isNaN(finalEntryTime.getTime())) {
              finalEntryTime = null;
            }
          } else {
            finalEntryTime = application.entryTime;
          }
        }

        await existingEmployee.update({
          name: application.realName,     // 更新姓名
          phone: application.phone,       // 更新手机号
          idCard: application.idCard,     // 更新身份证号
          openId: application.openId,
          departmentId: application.departmentId,  // 更新归属部门
          positionId: application.positionId,      // 更新默认岗位
          levelId: application.levelId,            // 更新默认等级
          positionTypeId: application.positionTypeId,  // 更新默认的归属type_belong
          gender: application.gender,              // 更新性别
          entryTime: finalEntryTime,               // 更新入职时间
          isActivated: true,  // 小程序已激活状态
          status: '1',        // 状态为在职
          updateBy: currentUser.id,
          updateTime: new Date()
        });

        // 创建或更新员工岗位关联记录
        if (application.positionId && application.levelId) {
          // 查找是否已存在相同岗位配置
          const existingPosition = await EmployeePosition.findOne(
            addEnterpriseFilter({
              where: {
                employeeId: existingEmployee.id,
                positionTypeId: application.positionTypeId,
                positionId: application.positionId,
                levelId: application.levelId
              }
            })
          );

          if (existingPosition) {
            // 如果已存在相同岗位配置，先将所有现有岗位设置为非默认，然后更新该岗位为默认
            await EmployeePosition.update(
              { isDefault: false },
              addEnterpriseFilter({
                where: {
                  employeeId: existingEmployee.id,
                  isDefault: true
                }
              })
            );

            await existingPosition.update({
              isDefault: true,
              updateBy: currentUser.id,
              updateTime: new Date()
            });
          } else {
            // 如果不存在相同岗位配置，先删除该员工的全部岗位关联，再创建新的默认岗位关联
            await EmployeePosition.destroy(
              addEnterpriseFilter({
                where: {
                  employeeId: existingEmployee.id
                }
              })
            );

            await EmployeePosition.create(
              addEnterpriseId({
                employeeId: existingEmployee.id,
                positionTypeId: application.positionTypeId,
                positionId: application.positionId,
                levelId: application.levelId,
                isDefault: true,
                createBy: currentUser.id,
                updateBy: currentUser.id
              })
            );
          }
        }
      } else {
        // 创建新员工记录，信息从EmployeeEnterpriseApplication表获取
        // 处理入职时间：支持字符串转换为日期
        let finalEntryTime = null;
        if (application.entryTime) {
          if (typeof application.entryTime === 'string') {
            finalEntryTime = new Date(application.entryTime);
            // 验证日期是否有效
            if (isNaN(finalEntryTime.getTime())) {
              finalEntryTime = null;
            }
          } else {
            finalEntryTime = application.entryTime;
          }
        }

        const newEmployee = await Employee.create(
          addEnterpriseId({
            name: application.realName,
            phone: application.phone,
            idCard: application.idCard,
            departmentId: application.departmentId,
            positionId: application.positionId,
            levelId: application.levelId,
            positionTypeId: application.positionTypeId,
            gender: application.gender,
            entryTime: finalEntryTime,
            openId: application.openId,
            isActivated: true,  // 小程序已激活状态
            status: '1',        // 未离职状态
            createBy: currentUser.id,
            updateBy: currentUser.id,
            createTime: new Date(),
            updateTime: new Date()
          })
        );

        // 创建员工履历记录（任务4）
        console.log('创建新员工的履历记录...');
        await EmployeeCareerRecord.create({
          employeeId: newEmployee.id,
          openId: newEmployee.openId,
          enterpriseId: newEmployee.enterpriseId,
          status: 1, // 在职状态
          entryTime: finalEntryTime || new Date(),
          departureTime: null,
          departureReason: null,
          createBy: currentUser.username,
          updateBy: currentUser.username
        });
        console.log('新员工履历记录创建成功');

        // 创建员工岗位关联记录
        if (application.positionId && application.levelId) {
          // 查找是否已存在相同岗位配置（这里是新员工，理论上不会存在，但仍做检查）
          const existingPosition = await EmployeePosition.findOne(
            addEnterpriseFilter({
              where: {
                employeeId: newEmployee.id,
                positionTypeId: application.positionTypeId,
                positionId: application.positionId,
                levelId: application.levelId
              }
            })
          );

          if (existingPosition) {
            // 如果已存在相同岗位配置，则更新为默认
            await existingPosition.update({
              isDefault: true,
              updateBy: currentUser.id,
              updateTime: new Date()
            });
          } else {
            // 如果不存在相同岗位配置，先删除该员工的全部岗位关联，再创建新的默认岗位关联
            await EmployeePosition.destroy(
              addEnterpriseFilter({
                where: {
                  employeeId: newEmployee.id
                }
              })
            );

            await EmployeePosition.create(
              addEnterpriseId({
                employeeId: newEmployee.id,
                positionTypeId: application.positionTypeId,
                positionId: application.positionId,
                levelId: application.levelId,
                isDefault: true,
                createBy: currentUser.id,
                updateBy: currentUser.id
              })
            );
          }
        }
      }
    }

    // 更新申请记录
    await application.update({
      auditStatus,
      auditRemark,
      auditTime: new Date(),
      auditorId: currentUser.id,
      updatedBy: currentUser.id
    });

    res.json({
      code: 200,
      data: {
        id: application.id,
        auditStatus,
        auditTime: new Date()
      },
      message: `审核${auditStatus === '通过' ? '通过' : '驳回'}成功`
    });
  } catch (error) {
    console.error('审核申请失败：', error);
    res.status(500).json({
      code: 500,
      message: '审核申请失败',
      error: error.message
    });
  }
};

/**
 * 批量审核员工企业申请
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.batchAuditEmployeeApplication = async (req, res) => {
  try {
    const { ids, auditStatus, auditRemark, currentUser } = req.body;

    console.log('收到批量审核请求参数:', { ids, auditStatus, auditRemark, currentUser });

    // 验证当前用户信息
    if (!currentUser || !currentUser.id || !currentUser.username) {
      return res.status(400).json({
        code: 400,
        message: '当前用户信息不完整'
      });
    }

    // 验证参数
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请选择要审核的申请'
      });
    }

    if (!['通过', '驳回'].includes(auditStatus)) {
      return res.status(400).json({
        code: 400,
        message: '审核状态只能是"通过"或"驳回"'
      });
    }

    // 查询所有待审核的申请，添加企业ID过滤
    const applications = await EmployeeEnterpriseApplication.findAll(
      addEnterpriseFilter({
        where: {
          id: {
            [Op.in]: ids
          },
          auditStatus: '审核中'
        }
      })
    );

    if (applications.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '没有找到可审核的申请'
      });
    }

    let successCount = 0;
    const errors = [];

    // 批量处理审核
    for (const application of applications) {
      try {
        // 如果审核通过，需要创建员工记录
        if (auditStatus === '通过') {
          // 检查是否已存在该openId的员工（优先通过openId查找）
          let existingEmployee = await Employee.findOne(
            addEnterpriseFilter({
              where: {
                openId: application.openId
              }
            })
          );

          // 如果通过openId没找到，再通过身份证号或手机号查找
          if (!existingEmployee) {
            existingEmployee = await Employee.findOne(
              addEnterpriseFilter({
                where: {
                  [Op.or]: [
                    { idCard: application.idCard },
                    { phone: application.phone }
                  ]
                }
              })
            );
          }

          if (existingEmployee) {
            // 如果员工已存在，更新openId绑定和状态，同时更新岗位信息
            // 处理入职时间：支持字符串转换为日期
            let finalEntryTime = null;
            if (application.entryTime) {
              if (typeof application.entryTime === 'string') {
                finalEntryTime = new Date(application.entryTime);
                // 验证日期是否有效
                if (isNaN(finalEntryTime.getTime())) {
                  finalEntryTime = null;
                }
              } else {
                finalEntryTime = application.entryTime;
              }
            }

            await existingEmployee.update({
              name: application.realName,     // 更新姓名
              phone: application.phone,       // 更新手机号
              idCard: application.idCard,     // 更新身份证号
              openId: application.openId,
              departmentId: application.departmentId,  // 更新归属部门
              positionId: application.positionId,      // 更新默认岗位
              levelId: application.levelId,            // 更新默认等级
              positionTypeId: application.positionTypeId,  // 更新默认的归属type_belong
              gender: application.gender,              // 更新性别
              entryTime: finalEntryTime,               // 更新入职时间
              isActivated: true,  // 小程序已激活状态
              status: '1',        // 状态为在职
              updateBy: currentUser.id,
              updateTime: new Date()
            });

            // 创建或更新员工岗位关联记录
            if (application.positionId && application.levelId) {
              // 查找是否已存在相同岗位配置
              const existingPosition = await EmployeePosition.findOne(
                addEnterpriseFilter({
                  where: {
                    employeeId: existingEmployee.id,
                    positionTypeId: application.positionTypeId,
                    positionId: application.positionId,
                    levelId: application.levelId
                  }
                })
              );

              if (existingPosition) {
                // 如果已存在相同岗位配置，先将所有现有岗位设置为非默认，然后更新该岗位为默认
                await EmployeePosition.update(
                  { isDefault: false },
                  addEnterpriseFilter({
                    where: {
                      employeeId: existingEmployee.id,
                      isDefault: true
                    }
                  })
                );

                await existingPosition.update({
                  isDefault: true,
                  updateBy: currentUser.id,
                  updateTime: new Date()
                });
              } else {
                // 如果不存在相同岗位配置，先删除该员工的全部岗位关联，再创建新的默认岗位关联
                await EmployeePosition.destroy(
                  addEnterpriseFilter({
                    where: {
                      employeeId: existingEmployee.id
                    }
                  })
                );

                await EmployeePosition.create(
                  addEnterpriseId({
                    employeeId: existingEmployee.id,
                    positionTypeId: application.positionTypeId,
                    positionId: application.positionId,
                    levelId: application.levelId,
                    isDefault: true,
                    createBy: currentUser.id,
                    updateBy: currentUser.id
                  })
                );
              }
            }
          } else {
            // 创建新员工记录，信息从EmployeeEnterpriseApplication表获取
            // 处理入职时间：支持字符串转换为日期
            let finalEntryTime = null;
            if (application.entryTime) {
              if (typeof application.entryTime === 'string') {
                finalEntryTime = new Date(application.entryTime);
                // 验证日期是否有效
                if (isNaN(finalEntryTime.getTime())) {
                  finalEntryTime = null;
                }
              } else {
                finalEntryTime = application.entryTime;
              }
            }

            const newEmployee = await Employee.create(
              addEnterpriseId({
                name: application.realName,
                phone: application.phone,
                idCard: application.idCard,
                departmentId: application.departmentId,
                positionId: application.positionId,
                levelId: application.levelId,
                positionTypeId: application.positionTypeId,
                gender: application.gender,
                entryTime: finalEntryTime,
                openId: application.openId,
                isActivated: true,  // 小程序已激活状态
                status: '1',        // 未离职状态
                createBy: currentUser.id,
                updateBy: currentUser.id,
                createTime: new Date(),
                updateTime: new Date()
              })
            );

            // 创建员工履历记录（任务4）
            console.log('创建新员工的履历记录...');
            await EmployeeCareerRecord.create({
              employeeId: newEmployee.id,
              openId: newEmployee.openId,
              enterpriseId: newEmployee.enterpriseId,
              status: 1, // 在职状态
              entryTime: finalEntryTime || new Date(),
              departureTime: null,
              departureReason: null,
              createBy: currentUser.username,
              updateBy: currentUser.username
            });
            console.log('新员工履历记录创建成功');

            // 创建员工岗位关联记录
            if (application.positionId && application.levelId) {
              // 查找是否已存在相同岗位配置（这里是新员工，理论上不会存在，但仍做检查）
              const existingPosition = await EmployeePosition.findOne(
                addEnterpriseFilter({
                  where: {
                    employeeId: newEmployee.id,
                    positionTypeId: application.positionTypeId,
                    positionId: application.positionId,
                    levelId: application.levelId
                  }
                })
              );

              if (existingPosition) {
                // 如果已存在相同岗位配置，则更新为默认
                await existingPosition.update({
                  isDefault: true,
                  updateBy: currentUser.id,
                  updateTime: new Date()
                });
              } else {
                // 如果不存在相同岗位配置，先删除该员工的全部岗位关联，再创建新的默认岗位关联
                await EmployeePosition.destroy(
                  addEnterpriseFilter({
                    where: {
                      employeeId: newEmployee.id
                    }
                  })
                );

                await EmployeePosition.create(
                  addEnterpriseId({
                    employeeId: newEmployee.id,
                    positionTypeId: application.positionTypeId,
                    positionId: application.positionId,
                    levelId: application.levelId,
                    isDefault: true,
                    createBy: currentUser.id,
                    updateBy: currentUser.id
                  })
                );
              }
            }
          }
        }

        // 更新申请记录
        await application.update({
          auditStatus,
          auditRemark,
          auditTime: new Date(),
          auditorId: currentUser.id,
          updateBy: currentUser.id
        });

        successCount++;
      } catch (error) {
        console.error(`审核申请${application.id}失败:`, error);
        errors.push(`申请${application.id}审核失败: ${error.message}`);
      }
    }

    res.json({
      code: 200,
      data: {
        successCount,
        totalCount: applications.length,
        errors
      },
      message: `批量审核完成，成功${successCount}条${errors.length > 0 ? `，失败${errors.length}条` : ''}`
    });
  } catch (error) {
    console.error('批量审核申请失败：', error);
    res.status(500).json({
      code: 500,
      message: '批量审核申请失败',
      error: error.message
    });
  }
};

/**
 * 处理员工离职
 * @param {Object} employee 员工对象
 * @param {Object} currentUser 当前用户
 */
async function handleEmployeeResignation(employee, currentUser) {
  const transaction = await Employee.sequelize.transaction();

  try {
    console.log(`开始处理员工 ${employee.name}(ID: ${employee.id}) 的离职操作`);

    // 1. 检查员工是否有任何履历记录
    const existingCareerRecords = await EmployeeCareerRecord.findAll(
      addEnterpriseFilter({
        where: {
          employeeId: employee.id
        },
        order: [['createTime', 'DESC']]
      }),
      { transaction }
    );

    let careerRecord;

    if (existingCareerRecords.length === 0) {
      // 新增规则：如果该员工一条都没有employee_career_record记录，则新建一条
      console.log(`员工 ${employee.id} 没有任何履历记录，创建新的离职履历记录`);
      careerRecord = await EmployeeCareerRecord.create(
        addEnterpriseId({
          employeeId: employee.id,
          openId: employee.openId,
          status: 0, // 离职状态
          entryTime: employee.entryTime || new Date(), // 使用员工入职时间，如果没有则使用当前时间
          departureTime: new Date(),
          departureReason: '离职', // 可以根据需要调整
          createBy: currentUser ? currentUser.username : 'admin',
          updateBy: currentUser ? currentUser.username : 'admin'
        }),
        { transaction }
      );
      console.log(`为员工 ${employee.id} 创建新的离职履历记录，ID: ${careerRecord.id}`);
    } else {
      // 查找当前在职状态的履历记录
      careerRecord = existingCareerRecords.find(record => record.status === 1);

      if (careerRecord) {
        // 更新现有的在职记录为离职状态
        await careerRecord.update({
          status: 0, // 离职
          departureTime: new Date(),
          departureReason: '离职', // 可以根据需要调整
          updateBy: currentUser ? currentUser.username : 'admin'
        }, { transaction });
        console.log(`更新履历记录 ${careerRecord.id} 为离职状态`);
      } else {
        // 如果有履历记录但都不是在职状态，创建一个新的离职记录
        console.log(`员工 ${employee.id} 有履历记录但无在职状态记录，创建新的离职履历记录`);
        careerRecord = await EmployeeCareerRecord.create(
          addEnterpriseId({
            employeeId: employee.id,
            openId: employee.openId,
            status: 0, // 离职状态
            entryTime: employee.entryTime || new Date(),
            departureTime: new Date(),
            departureReason: '离职',
            createBy: currentUser ? currentUser.username : 'admin',
            updateBy: currentUser ? currentUser.username : 'admin'
          }),
          { transaction }
        );
        console.log(`为员工 ${employee.id} 创建新的离职履历记录，ID: ${careerRecord.id}`);
      }
    }

    const careerRecordId = careerRecord.id;
    console.log(`当前履历记录ID: ${careerRecordId}`);

    // 2. 将所有相关表的 is_active 设置为 0
    const employeeId = employee.id;
    const openId = employee.openId;


    // 更新考试记录表
    await ExamRecord.update(
      {
        isActive: 0,
        careerRecordId: careerRecordId
      },
      addEnterpriseFilter({
        where: {
          openId: openId, 
          careerRecordId: null
        }
      }),
      { transaction }
    );
    console.log('更新考试记录表 is_active 为 0 并设置 career_record_id');

    // 更新考试审核申请表
    await ExamReviewApplication.update(
      {
        isActive: 0,
        careerRecordId: careerRecordId
      },
      addEnterpriseFilter({
        where: {
          openId: openId ,
          careerRecordId: null
        }
      }),
      { transaction }
    );
    console.log('更新考试审核申请表 is_active 为 0 并设置 career_record_id');

    // 更新证书记录表
    await CertificateRecord.update(
      {
        isActive: 0,
        careerRecordId: careerRecordId
      },
      addEnterpriseFilter({
        where: {
          openId: openId ,
          careerRecordId: null
        }
      }),
      { transaction }
    );
    console.log('更新证书记录表 is_active 为 0 并设置 career_record_id');

    // 更新用户成就表
    await UserAchievement.update(
      {
        isActive: 0,
        careerRecordId: careerRecordId
      },
      addEnterpriseFilter({
        where: {
          openId: openId ,
          careerRecordId: null
        }
      }),
      { transaction }
    );
    console.log('更新用户成就表 is_active 为 0 并设置 career_record_id');

    // 更新员工岗位关联表
    await EmployeePosition.update(
      {
        isActive: 0,
        careerRecordId: careerRecordId
      },
      addEnterpriseFilter({
        where: {
          openId: openId ,
          careerRecordId: null
        }
      }),
      { transaction }
    );
    console.log('更新员工岗位关联表 is_active 为 0 并设置 career_record_id');

    // 更新员工晋升表
    await EmployeePromotion.update(
      {
        isActive: 0,
        careerRecordId: careerRecordId
      },
      addEnterpriseFilter({
        where: {
          openId: openId ,
          careerRecordId: null
        }
      }),
      { transaction }
    );
    console.log('更新员工晋升表 is_active 为 0 并设置 career_record_id');

    // 3. 填充任务1中的所有名称字段
    await fillNameFieldsForEmployee(employeeId, openId, transaction);

    await transaction.commit();
    console.log(`员工 ${employee.name} 离职处理完成`);

  } catch (error) {
    await transaction.rollback();
    console.error('处理员工离职失败:', error);
    throw error;
  }
}

/**
 * 填充员工相关记录的名称字段
 * @param {Number} employeeId 员工ID
 * @param {String} openId 员工openId
 * @param {Object} transaction 事务对象
 */
async function fillNameFieldsForEmployee(employeeId, openId, transaction) {
  console.log('开始填充员工相关记录的名称字段...');

  try {
    // 1. 更新练习记录表的名称字段
    const practiceRecords = await PracticeRecord.findAll(
      addEnterpriseFilter({
        where: {
          openId: openId ,
        }
      }),
      { transaction }
    );

    for (const record of practiceRecords) {
      const updateData = {};

      // 获取岗位归属名称
      if (record.positionBelong) {
        const positionType = await PositionType.findOne(
          addEnterpriseFilter({ where: { id: record.positionBelong } }),
          { transaction }
        );
        if (positionType) {
          updateData.positionBelongName = positionType.name;
        }
      }

      // 获取岗位名称
      if (record.positionName) {
        const positionName = await PositionName.findOne(
          addEnterpriseFilter({ where: { id: record.positionName } }),
          { transaction }
        );
        if (positionName) {
          updateData.positionNameCn = positionName.name;
        }
      }

      // 获取等级名称
      if (record.positionLevel) {
        const level = await Level.findOne(
          addEnterpriseFilter({ where: { id: record.positionLevel } }),
          { transaction }
        );
        if (level) {
          updateData.positionLevelName = level.name;
        }
      }

      // 获取科目名称
      if (record.examSubject) {
        const knowledgeBase = await KnowledgeBase.findOne(
          addEnterpriseFilter({ where: { id: record.examSubject } }),
          { transaction }
        );
        if (knowledgeBase) {
          updateData.examSubjectName = knowledgeBase.name;
        }
      }

      if (Object.keys(updateData).length > 0) {
        await record.update(updateData, { transaction });
      }
    }
    console.log(`更新了 ${practiceRecords.length} 条练习记录的名称字段`);

    // 2. 更新考试记录表的名称字段
    const examRecords = await ExamRecord.findAll(
      addEnterpriseFilter({
        where: {
         openId: openId ,
        }
      }),
      { transaction }
    );

    for (const record of examRecords) {
      const updateData = {};

      // 获取岗位归属名称 (通过categoryId)
      if (record.categoryId) {
        const positionType = await PositionType.findOne(
          addEnterpriseFilter({ where: { id: record.categoryId } }),
          { transaction }
        );
        if (positionType) {
          updateData.positionBelongName = positionType.name;
        }
      }

      // 获取岗位名称
      if (record.positionId) {
        const positionName = await PositionName.findOne(
          addEnterpriseFilter({ where: { id: record.positionId } }),
          { transaction }
        );
        if (positionName) {
          updateData.positionName = positionName.name;
        }
      }

      // 获取等级名称
      if (record.levelId) {
        const level = await Level.findOne(
          addEnterpriseFilter({ where: { id: record.levelId } }),
          { transaction }
        );
        if (level) {
          updateData.levelName = level.name;
        }
      }

      if (Object.keys(updateData).length > 0) {
        await record.update(updateData, { transaction });
      }
    }
    console.log(`更新了 ${examRecords.length} 条考试记录的名称字段`);

    // 3. 更新证书记录表的名称字段
    const certificateRecords = await CertificateRecord.findAll(
      addEnterpriseFilter({
        where: {
          openId: openId ,
        }
      }),
      { transaction }
    );

    for (const record of certificateRecords) {
      const updateData = {};

      // 获取岗位归属名称
      if (record.positionBelong) {
        const positionType = await PositionType.findOne(
          addEnterpriseFilter({ where: { id: record.positionBelong } }),
          { transaction }
        );
        if (positionType) {
          updateData.positionBelongName = positionType.name;
        }
      }

      // 获取岗位名称
      if (record.positionName) {
        const positionName = await PositionName.findOne(
          addEnterpriseFilter({ where: { id: record.positionName } }),
          { transaction }
        );
        if (positionName) {
          updateData.positionNameCn = positionName.name;
        }
      }

      // 获取等级名称
      if (record.positionLevel) {
        const level = await Level.findOne(
          addEnterpriseFilter({ where: { id: record.positionLevel } }),
          { transaction }
        );
        if (level) {
          updateData.positionLevelName = level.name;
        }
      }

      // 获取科目名称
      if (record.kbId) {
        const knowledgeBase = await KnowledgeBase.findOne(
          addEnterpriseFilter({ where: { id: record.kbId } }),
          { transaction }
        );
        if (knowledgeBase) {
          updateData.examSubjectName = knowledgeBase.name;
        }
      }

      if (Object.keys(updateData).length > 0) {
        await record.update(updateData, { transaction });
      }
    }
    console.log(`更新了 ${certificateRecords.length} 条证书记录的名称字段`);

    // 4. 更新员工岗位关联表的名称字段
    const employeePositions = await EmployeePosition.findAll(
      addEnterpriseFilter({
        where: { employeeId: employeeId }
      }),
      { transaction }
    );

    for (const record of employeePositions) {
      const updateData = {};

      // 获取岗位归属名称
      if (record.positionTypeId) {
        const positionType = await PositionType.findOne(
          addEnterpriseFilter({ where: { id: record.positionTypeId } }),
          { transaction }
        );
        if (positionType) {
          updateData.positionBelongName = positionType.name;
        }
      }

      // 获取岗位名称
      if (record.positionId) {
        const positionName = await PositionName.findOne(
          addEnterpriseFilter({ where: { id: record.positionId } }),
          { transaction }
        );
        if (positionName) {
          updateData.positionNameCn = positionName.name;
        }
      }

      // 获取等级名称
      if (record.levelId) {
        const level = await Level.findOne(
          addEnterpriseFilter({ where: { id: record.levelId } }),
          { transaction }
        );
        if (level) {
          updateData.positionLevelName = level.name;
        }
      }

      if (Object.keys(updateData).length > 0) {
        await record.update(updateData, { transaction });
      }
    }
    console.log(`更新了 ${employeePositions.length} 条员工岗位关联记录的名称字段`);

    // 5. 更新员工晋升表的名称字段
    const employeePromotions = await EmployeePromotion.findAll(
      addEnterpriseFilter({
        where: { employeeId: employeeId }
      }),
      { transaction }
    );

    for (const record of employeePromotions) {
      const updateData = {};

      // 获取岗位归属名称
      if (record.positionTypeId) {
        const positionType = await PositionType.findOne(
          addEnterpriseFilter({ where: { id: record.positionTypeId } }),
          { transaction }
        );
        if (positionType) {
          updateData.positionBelongName = positionType.name;
        }
      }

      // 获取岗位名称
      if (record.positionId) {
        const positionName = await PositionName.findOne(
          addEnterpriseFilter({ where: { id: record.positionId } }),
          { transaction }
        );
        if (positionName) {
          updateData.positionNameCn = positionName.name;
        }
      }

      // 获取等级名称
      if (record.levelId) {
        const level = await Level.findOne(
          addEnterpriseFilter({ where: { id: record.levelId } }),
          { transaction }
        );
        if (level) {
          updateData.positionLevelName = level.name;
        }
      }

      if (Object.keys(updateData).length > 0) {
        await record.update(updateData, { transaction });
      }
    }
    console.log(`更新了 ${employeePromotions.length} 条员工晋升记录的名称字段`);

    console.log('员工相关记录的名称字段填充完成');

  } catch (error) {
    console.error('填充名称字段失败:', error);
    throw error;
  }
}

/**
 * 处理员工在职（任务3）
 * @param {Object} employee 员工对象
 * @param {Object} currentUser 当前用户
 */
async function handleEmployeeReemployment(employee, currentUser) {
  const transaction = await Employee.sequelize.transaction();

  try {
    console.log(`开始处理员工在职，员工ID: ${employee.id}, 姓名: ${employee.name}`);

    // 1. 创建新的员工履历记录
    console.log('创建新的员工履历记录...');
    const careerRecord = await EmployeeCareerRecord.create({
      employeeId: employee.id,
      openId: employee.openId,
      enterpriseId: employee.enterpriseId,
      status: 1, // 在职状态
      entryTime: employee.entryTime || new Date(), // 使用员工表的入职时间或当前时间
      departureTime: null,
      departureReason: null,
      createBy: currentUser ? currentUser.username : 'system',
      updateBy: currentUser ? currentUser.username : 'system'
    }, { transaction });

    console.log(`员工履历记录创建成功，履历ID: ${careerRecord.id}`);

    // 提交事务
    await transaction.commit();
    console.log('员工在职处理事务提交成功');

  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('员工在职处理失败，事务已回滚:', error);
    throw error;
  }
}
