const express = require('express');
const router = express.Router();
const systemSettingController = require('../controllers/systemSettingController');
const { requireLogin } = require('../middleware/auth');

// 获取系统设置列表
router.get('/', requireLogin, systemSettingController.getSystemSettings);

// 获取系统设置详情
router.get('/:id', requireLogin, systemSettingController.getSystemSettingById);

// 通过code获取系统设置
router.get('/code/:code', requireLogin, systemSettingController.getSystemSettingByCode);

// 创建系统设置
router.post('/', requireLogin, systemSettingController.createSystemSetting);

// 更新系统设置
router.put('/:id', requireLogin, systemSettingController.updateSystemSetting);

// 删除系统设置
router.delete('/:id', requireLogin, systemSettingController.deleteSystemSetting);

module.exports = router; 