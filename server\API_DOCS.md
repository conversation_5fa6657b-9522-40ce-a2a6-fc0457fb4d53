# 微信小程序API文档

## 获取员工可选择的剩余岗位名称

### 接口描述
获取员工可选择的剩余岗位名称，从全部岗位中排除员工已配置的晋升岗位。

### 请求信息
- **URL**: `/api/wechat/employee/available-position-names`
- **方法**: `GET`
- **请求头**: 
  - `openid`: 用户的微信openId（必填）

### 请求参数
无

### 返回值结构
返回值结构与 `getPositionNamesByUserType` 方法完全一致：

```json
{
  "code": 200,
  "data": {
    "rows": [
      {
        "id": "岗位ID",
        "name": "岗位名称",
        "code": "岗位编码",
        "typeId": "岗位类型ID",
        "type": {
          "id": "岗位类型ID",
          "name": "岗位类型名称",
          "code": "岗位类型编码"
        }
      }
    ],
    "total": "可选择岗位总数",
    "higherLevels": [
      {
        "id": "等级ID",
        "name": "等级名称",
        "code": "等级编码",
        "orderNum": "等级排序号"
      }
    ]
  },
  "message": "获取可选择岗位名称成功"
}
```

### 业务逻辑
1. 根据openId查找员工信息
2. 获取该员工岗位类型下的全部岗位（调用 `getPositionNamesByUserType` 的逻辑）
3. 获取员工已配置的晋升岗位（调用 `getEmployeePromotionPositions` 的逻辑）
4. 从全部岗位中排除员工已配置的岗位
5. 返回剩余可选择的岗位列表

### 错误码
- `400`: openId不能为空
- `404`: 未找到员工信息或员工未激活
- `500`: 获取全部岗位失败 / 获取可选择岗位名称失败

### 使用场景
用于员工选择新的晋升岗位时，显示可选择的岗位列表，避免重复配置已有的岗位。 