const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 员工岗位关联模型
 */
const EmployeePosition = sequelize.define('EmployeePosition', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  employeeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'employee_id',
    comment: '员工ID'
  },
  positionId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'position_id',
    comment: '岗位ID'
  },
  positionTypeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'position_type_id',
    comment: '岗位类型ID'
  },
  levelId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'level_id',
    comment: '岗位等级ID'
  },
  isDefault: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_default',
    comment: '是否默认岗位(1是 0否)'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active',
    comment: '是否有效(1有效 0无效)'
  },
  positionBelongName: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_belong_name',
    comment: '岗位归属名称'
  },
  positionNameCn: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_name_cn',
    comment: '岗位名称'
  },
  positionLevelName: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_level_name',
    comment: '岗位等级名称'
  },
  careerRecordId: {
    type: DataTypes.INTEGER,
    field: 'career_record_id',
    allowNull: true,
    comment: '员工履历记录ID'
  },
  enterpriseId: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  createBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'create_by',
    comment: '创建者'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'update_by',
    comment: '更新者'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'org_employee_position',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

module.exports = EmployeePosition; 