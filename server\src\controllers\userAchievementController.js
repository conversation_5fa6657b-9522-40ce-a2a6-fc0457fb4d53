const { Op } = require('sequelize');
const { addEnterpriseId, addEnterpriseFilter, createResponse } = require('../utils/responseHelper');
const UserAchievement = require('../models/UserAchievement');
const AchievementProgress = require('../models/AchievementProgress');
const AchievementTemplate = require('../models/AchievementTemplate');
const { User } = require('../models');

/**
 * 获取用户成就列表
 */
const getUserAchievements = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, category, userId } = req.query;
    const openId = req.headers.openid; // 从请求头获取openId
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    console.log('用户成就查询参数:', { page, pageSize, category, userId, openId });

    // 构建查询条件
    const whereCondition = { enterpriseId };

    if (userId) {
      whereCondition.userId = userId;
    }
    if (openId) {
      whereCondition.openId = openId;
    }
    if (category) {
      whereCondition.category = category;
    }

    const pageNum = parseInt(page);
    const size = parseInt(pageSize);
    const offset = (pageNum - 1) * size;

    const { rows: achievements, count: total } = await UserAchievement.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: AchievementTemplate,
          as: 'template',
          attributes: ['id', 'name', 'description', 'icon', 'category', 'ruleType'],
          required: false
        }
      ],
      order: [['achievedAt', 'DESC'], ['id', 'DESC']],
      offset: offset,
      limit: size
    });

    console.log('用户成就查询结果:', { total, listLength: achievements.length });

    res.json(createResponse(200, '获取用户成就列表成功', {
      list: achievements,
      total,
      page: pageNum,
      pageSize: size
    }));
  } catch (error) {
    console.error('获取用户成就列表失败:', error);
    res.status(500).json(createResponse(500, '获取用户成就列表失败'));
  }
};

/**
 * 获取用户成就进度列表
 */
const getUserAchievementProgress = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, status, userId } = req.query;
    const openId = req.headers.openid; // 从请求头获取openId
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    console.log('用户成就进度查询参数:', { page, pageSize, status, userId, openId });

    // 构建查询条件
    const whereCondition = { enterpriseId };

    if (userId) {
      whereCondition.userId = userId;
    }
    if (openId) {
      whereCondition.openId = openId;
    }
    if (status) {
      whereCondition.status = status;
    }

    const pageNum = parseInt(page);
    const size = parseInt(pageSize);
    const offset = (pageNum - 1) * size;

    const { rows: progressList, count: total } = await AchievementProgress.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: AchievementTemplate,
          as: 'template',
          attributes: ['id', 'name', 'description', 'icon', 'category', 'ruleType'],
          required: false
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'nickname'],
          required: false
        }
      ],
      order: [['progressPercentage', 'DESC'], ['updateTime', 'DESC']],
      offset: offset,
      limit: size
    });

    console.log('用户成就进度查询结果:', { total, listLength: progressList.length });

    res.json(createResponse(200, '获取用户成就进度成功', {
      list: progressList,
      total,
      page: pageNum,
      pageSize: size
    }));
  } catch (error) {
    console.error('获取用户成就进度失败:', error);
    res.status(500).json(createResponse(500, '获取用户成就进度失败'));
  }
};

/**
 * 获取用户成就统计
 */
const getUserAchievementStats = async (req, res) => {
  try {
    const { userId } = req.query;
    const openId = req.headers.openid; // 从请求头获取openId
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 后台管理系统允许不传userId和openId，获取所有用户的统计数据
    // if (!userId && !openId) {
    //   return res.status(400).json(createResponse(400, '请提供userId或openId'));
    // }

    // 构建查询条件
    const whereCondition = { enterpriseId };
    if (userId) whereCondition.userId = userId;
    if (openId) whereCondition.openId = openId;

    // 统计已获得成就
    const achievedCount = await UserAchievement.count({
      where: { ...whereCondition, status: 'achieved' }
    });

    // 按类别统计已获得成就
    const achievedByCategory = await UserAchievement.findAll({
      where: { ...whereCondition, status: 'achieved' },
      attributes: [
        'category',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['category']
    });

    // 统计进行中的成就
    const inProgressCount = await AchievementProgress.count({
      where: { ...whereCondition, status: 'in_progress' }
    });

    // 统计总积分
    const totalRewardPoints = await UserAchievement.sum('rewardPoints', {
      where: { ...whereCondition, status: 'achieved' }
    }) || 0;

    // 最近获得的成就
    const recentAchievements = await UserAchievement.findAll({
      where: { ...whereCondition, status: 'achieved' },
      order: [['achievedAt', 'DESC']],
      limit: 5,
      attributes: ['id', 'achievementName', 'achievementIcon', 'rewardPoints', 'achievedAt']
    });

    // 进度最高的未完成成就
    const topProgress = await AchievementProgress.findAll({
      where: { ...whereCondition, status: 'in_progress' },
      order: [['progressPercentage', 'DESC']],
      limit: 5,
      attributes: ['id', 'achievementName', 'currentValue', 'targetValue', 'progressPercentage']
    });

    const stats = {
      achievedCount,
      inProgressCount,
      totalRewardPoints,
      achievedByCategory: achievedByCategory.reduce((acc, item) => {
        acc[item.category] = parseInt(item.dataValues.count);
        return acc;
      }, {}),
      recentAchievements,
      topProgress
    };

    console.log('用户成就统计:', stats);

    res.json(createResponse(200, '获取用户成就统计成功', stats));
  } catch (error) {
    console.error('获取用户成就统计失败:', error);
    res.status(500).json(createResponse(500, '获取用户成就统计失败'));
  }
};

/**
 * 获取成就详情（包含进度信息）
 */
const getAchievementDetail = async (req, res) => {
  try {
    const { templateId } = req.params;
    const { userId } = req.query;
    const openId = req.headers.openid; // 从请求头获取openId
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    if (!userId && !openId) {
      return res.status(400).json(createResponse(400, '请提供userId或openId'));
    }

    // 获取成就模板信息
    const template = await AchievementTemplate.findOne({
      where: { id: templateId, enterpriseId }
    });

    if (!template) {
      return res.status(404).json(createResponse(404, '成就模板不存在'));
    }

    // 构建查询条件
    const whereCondition = { enterpriseId, templateId };
    if (userId) whereCondition.userId = userId;
    if (openId) whereCondition.openId = openId;

    // 查找用户是否已获得该成就
    const userAchievement = await UserAchievement.findOne({
      where: whereCondition
    });

    // 查找成就进度
    const progress = await AchievementProgress.findOne({
      where: whereCondition
    });

    // 解析触发条件
    let triggerCondition = null;
    try {
      triggerCondition = JSON.parse(template.triggerCondition || '{}');
    } catch (error) {
      console.warn('解析触发条件失败:', error);
    }

    const result = {
      template: {
        ...template.toJSON(),
        triggerCondition
      },
      isAchieved: !!userAchievement,
      achievedAt: userAchievement?.achievedAt || null,
      rewardPoints: userAchievement?.rewardPoints || template.rewardPoints || 0,
      progress: progress ? {
        currentValue: progress.currentValue,
        targetValue: progress.targetValue,
        progressPercentage: progress.progressPercentage,
        status: progress.status,
        lastActivityDate: progress.lastActivityDate,
        lastUpdatedData: progress.lastUpdatedData
      } : null
    };

    res.json(createResponse(200, '获取成就详情成功', result));
  } catch (error) {
    console.error('获取成就详情失败:', error);
    res.status(500).json(createResponse(500, '获取成就详情失败'));
  }
};

/**
 * 获取可用成就列表（包含进度）
 */
const getAvailableAchievements = async (req, res) => {
  try {
    const { userId, category } = req.query;
    const openId = req.headers.openid; // 从请求头获取openId
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 后台管理系统允许不传userId和openId，获取所有成就模板
    // if (!userId && !openId) {
    //   return res.status(400).json(createResponse(400, '请提供userId或openId'));
    // }

    // 构建模板查询条件
    const templateWhere = { enterpriseId, isActive: true };
    if (category) {
      templateWhere.category = category;
    }

    // 获取所有激活的成就模板
    const templates = await AchievementTemplate.findAll({
      where: templateWhere,
      order: [['sort', 'ASC'], ['id', 'ASC']]
    });

    // 构建用户查询条件 - 如果没有userId和openId，则获取所有用户数据的统计
    const userWhere = { enterpriseId };
    if (userId) userWhere.userId = userId;
    if (openId) userWhere.openId = openId;

    // 获取用户已获得的成就（如果没有用户限制，获取所有成就数据用于统计）
    const userAchievements = await UserAchievement.findAll({
      where: userWhere,
      attributes: ['templateId', 'achievedAt', 'rewardPoints', 'userId', 'openId']
    });

    // 获取用户成就进度（如果没有用户限制，获取所有进度数据用于统计）
    const userProgress = await AchievementProgress.findAll({
      where: userWhere,
      attributes: ['templateId', 'currentValue', 'targetValue', 'progressPercentage', 'status', 'userId', 'openId']
    });

    // 组织数据
    const achievementMap = userAchievements.reduce((acc, achievement) => {
      acc[achievement.templateId] = achievement;
      return acc;
    }, {});

    const progressMap = userProgress.reduce((acc, progress) => {
      acc[progress.templateId] = progress;
      return acc;
    }, {});

    const result = templates.map(template => {
      const achievement = achievementMap[template.id];
      const progress = progressMap[template.id];

      // 解析触发条件
      let triggerCondition = null;
      try {
        triggerCondition = JSON.parse(template.triggerCondition || '{}');
      } catch (error) {
        console.warn('解析触发条件失败:', error);
      }

      return {
        id: template.id,
        name: template.name,
        description: template.description,
        icon: template.icon,
        category: template.category,
        ruleType: template.ruleType,
        rewardPoints: template.rewardPoints || 0,
        triggerCondition,
        isAchieved: !!achievement,
        achievedAt: achievement?.achievedAt || null,
        progress: progress ? {
          currentValue: progress.currentValue,
          targetValue: progress.targetValue,
          progressPercentage: progress.progressPercentage,
          status: progress.status
        } : null
      };
    });

    res.json(createResponse(200, '获取可用成就列表成功', {
      list: result,
      total: result.length
    }));
  } catch (error) {
    console.error('获取可用成就列表失败:', error);
    res.status(500).json(createResponse(500, '获取可用成就列表失败'));
  }
};

/**
 * 手动触发成就检测（调试用）
 */
const triggerAchievementCheck = async (req, res) => {
  try {
    const { userId, eventType, eventData } = req.body;
    const openId = req.headers.openid; // 从请求头获取openId
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    if (!userId && !openId) {
      return res.status(400).json(createResponse(400, '请提供userId或openId'));
    }

    if (!eventType || !eventData) {
      return res.status(400).json(createResponse(400, '请提供eventType和eventData'));
    }

    const { emitAchievementEvent, ACHIEVEMENT_EVENTS } = require('../utils/achievementEventListener');

    // 获取用户信息
    let user;
    if (userId) {
      user = await User.findByPk(userId);
    } else {
      user = await User.findOne({ where: { openId } });
    }

    if (!user) {
      return res.status(404).json(createResponse(404, '用户不存在'));
    }

    // 发射成就事件
    const fullEventData = {
      userId: user.id,
      openId: user.openId,
      enterpriseId,
      ...eventData
    };

    emitAchievementEvent(eventType, fullEventData);

    console.log(`[手动触发] 成就检测事件: ${eventType}`, fullEventData);

    res.json(createResponse(200, '成就检测已触发', {
      eventType,
      eventData: fullEventData
    }));
  } catch (error) {
    console.error('手动触发成就检测失败:', error);
    res.status(500).json(createResponse(500, '手动触发成就检测失败'));
  }
};

module.exports = {
  getUserAchievements,
  getUserAchievementProgress,
  getUserAchievementStats,
  getAchievementDetail,
  getAvailableAchievements,
  triggerAchievementCheck
}; 