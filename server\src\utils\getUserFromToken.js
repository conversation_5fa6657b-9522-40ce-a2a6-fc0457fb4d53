const { verifyToken } = require('./jwt');
const { User, Role } = require('../models');

/**
 * 从请求头的Authorization中获取用户信息
 * @param {Object} req 请求对象
 * @returns {Object|null} 用户信息或null
 */
const getUserFromToken = async (req) => {
  try {
    // 获取请求头中的Authorization
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return null;
    }
    
    // 解析token
    const token = authHeader.split(' ')[1]; // Bearer {token}
    const result = verifyToken(token);
    
    if (!result.valid) {
      return null;
    }
    
    // 查询用户信息
    const user = await User.findByPk(result.data.userId, {
      include: [
        {
          model: Role,
          through: { attributes: [] } // 不包含中间表字段
        }
      ],
      attributes: { exclude: ['password'] }
    });
    
    return user;
  } catch (error) {
    console.error('从token获取用户信息失败:', error);
    return null;
  }
};

module.exports = {
  getUserFromToken
}; 