/**
 * 测试控制器
 * 用于创建测试数据，仅用于开发和测试环境
 */
const { Op } = require('sequelize');
const ExamReviewApplication = require('../../models/ExamReviewApplication');
const ExamRecord = require('../../models/ExamRecord');
const User = require('../../models/user');
const { DictionaryData } = require('../../models/dictionary');
const Level = require('../../models/Level');
const { handleError } = require('../../utils/errorHandler');
const { handleResponse } = require('../../utils/response');

/**
 * 创建测试考试审核申请
 * @param {*} req - 请求对象
 * @param {*} res - 响应对象
 */
exports.createTestApplication = async (req, res) => {
  try {
    // 处理未登录的情况
    let user = req.user;
    let enterpriseId = 1; // 默认企业ID为1
    
    if (!user) {
      // 如果未登录，获取admin用户
      user = await User.findOne({
        where: {
          username: 'admin',
          enterpriseId: 1
        }
      });
      
      if (!user) {
        return res.status(404).json({
          code: 404,
          message: '无法找到默认用户，请先登录'
        });
      }
    } else {
      enterpriseId = user.enterpriseId;
    }
    
    // 生成申请编号：企业代码+当前日期+4位随机数
    const date = new Date();
    const dateStr = date.getFullYear().toString().slice(2) + 
                   (date.getMonth() + 1).toString().padStart(2, '0') + 
                   date.getDate().toString().padStart(2, '0');
    const randomNum = Math.floor(1000 + Math.random() * 9000);
    const applicationNo = `EX${enterpriseId.toString().padStart(2, '0')}${dateStr}${randomNum}`;
    
    // 创建测试考试审核申请
    const application = await ExamReviewApplication.create({
      applicationNo,
      enterpriseId,
      applicantName: user.nickname || user.username || '测试申请人',
      contactPhone: user.phone || '13800138000',
      examType: '理论考试',
      examTitle: '测试考试申请-' + new Date().toLocaleString(),
      positionLevel: '初级',
      examDate: new Date(),
      reasonForApplication: '测试用途，自动创建的考试申请',
      status: '通过', // 默认为已通过状态
      scoreConfirmStatus: '1', // 待审核
      delFlag: 0,
      createdBy: user.id,
      updatedBy: user.id
    });
    
    res.json({
      code: 200,
      message: '成功创建测试考试申请',
      data: application
    });
  } catch (error) {
    console.error('创建测试考试申请失败:', error);
    handleError(res, error);
  }
};

/**
 * 根据考试申请创建测试考试记录
 * @param {*} req - 请求对象，包含applicationId等参数
 * @param {*} res - 响应对象
 */
exports.createTestExamRecords = async (req, res) => {
  try {
    // 支持GET和POST请求，从不同位置获取参数
    const params = req.method === 'GET' ? req.query : req.body;
    const applicationId = params.applicationId;
    const recordCount = parseInt(params.recordCount || 1);
    
    // 处理未登录的情况
    let user = req.user;
    let enterpriseId = 1; // 默认企业ID为1
    
    if (!user) {
      // 如果未登录，获取admin用户
      user = await User.findOne({
        where: {
          username: 'admin',
          enterpriseId
        }
      });
      
      if (!user) {
        return res.status(404).json({
          code: 404,
          message: '无法找到默认用户，请先登录'
        });
      }
    } else {
      enterpriseId = user.enterpriseId;
    }

    // 如果没有提供applicationId，则自动创建一个考试申请
    let application;
    if (!applicationId) {
      // 生成申请编号
      const date = new Date();
      const dateStr = date.getFullYear().toString().slice(2) + 
                     (date.getMonth() + 1).toString().padStart(2, '0') + 
                     date.getDate().toString().padStart(2, '0');
      const randomNum = Math.floor(1000 + Math.random() * 9000);
      const applicationNo = `EX${enterpriseId.toString().padStart(2, '0')}${dateStr}${randomNum}`;
      
      // 创建测试考试审核申请
      application = await ExamReviewApplication.create({
        applicationNo,
        enterpriseId,
        applicantName: user.nickname || user.username || '测试申请人',
        contactPhone: user.phone || '13800138000',
        examType: '理论考试',
        examTitle: '测试考试申请-' + new Date().toLocaleString(),
        positionLevel: '初级',
        examDate: new Date(),
        reasonForApplication: '测试用途，自动创建的考试申请',
        status: '通过', // 默认为已通过状态
        scoreConfirmStatus: '1', // 待审核
        delFlag: 0,
        createdBy: user.id,
        updatedBy: user.id
      });
    } else {
      // 验证考试申请是否存在
      application = await ExamReviewApplication.findOne({
        where: {
          id: applicationId,
          enterpriseId,
          delFlag: 0
        }
      });

      if (!application) {
        return res.status(404).json({
          code: 404,
          message: '考试申请不存在'
        });
      }
    }

    // 获取真实的岗位类别数据
    const positionTypes = await DictionaryData.findAll({
      where: {
        typeId: 9, // 岗位归属类型ID
        status: true,
        enterpriseId
      }
    });

    if (!positionTypes || positionTypes.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '岗位归属数据不存在'
      });
    }

    // 获取真实的岗位名称数据
    const positionNames = await DictionaryData.findAll({
      where: {
        typeId: 10, // 岗位名称类型ID
        status: true,
        enterpriseId
      }
    });

    if (!positionNames || positionNames.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '岗位名称数据不存在'
      });
    }

    // 获取真实的岗位等级数据
    const levels = await Level.findAll({
      where: {
        enterpriseId
      }
    });

    if (!levels || levels.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '岗位等级数据不存在'
      });
    }

    // 获取管理员用户作为参考人
    const adminUser = await User.findOne({
      where: {
        username: 'admin',
        enterpriseId
      }
    });

    if (!adminUser) {
      return res.status(404).json({
        code: 404,
        message: '管理员用户不存在'
      });
    }

    // 创建测试考试记录
    const createdRecords = [];
    for (let i = 0; i < recordCount; i++) {
      // 随机选择岗位归属
      const randomPositionBelong = positionTypes[Math.floor(Math.random() * positionTypes.length)];
      
      // 随机选择岗位名称
      const randomPositionName = positionNames[Math.floor(Math.random() * positionNames.length)];
      
      // 随机选择岗位等级
      const randomLevel = levels[Math.floor(Math.random() * levels.length)];
      
      // 随机生成考试成绩（60-100之间）
      const randomScore = Math.floor(Math.random() * 41) + 60;
      
      // 随机生成考试用时（30-120分钟）
      const randomDuration = Math.floor(Math.random() * 91) + 30;

      // 创建考试记录数据
      const examRecord = await ExamRecord.create({
        enterpriseId,
        examSubject: application.examTitle,
        examTime: new Date(),
        positionBelongId: randomPositionBelong.id,
        positionId: randomPositionName.id,
        levelId: randomLevel.id,
        categoryId: randomPositionBelong.id, // 使用岗位归属作为分类ID
        examineeId: adminUser.id,
        examinee: adminUser.nickname || adminUser.username,
        score: randomScore,
        usedDuration: randomDuration,
        confirmStatus: '1',
        reviewApplicationId: application.id,
        examContent: {
          questions: [
            {
              title: "测试题目1",
              questionType: "单选题",
              userAnswer: "A",
              correctAnswer: "A",
              isCorrect: true,
              score: 10,
              userScore: 10
            },
            {
              title: "测试题目2",
              questionType: "多选题",
              userAnswer: "ABC",
              correctAnswer: "ABCD",
              isCorrect: false,
              score: 10,
              userScore: 8
            }
          ]
        },
        delFlag: 0,
        createdBy: user.id,
        updatedBy: user.id
      });

      createdRecords.push(examRecord);
    }

    res.json({
      code: 200,
      message: `成功创建${recordCount}条测试考试记录`,
      data: {
        application,
        records: createdRecords
      }
    });
  } catch (error) {
    console.error('创建测试考试记录失败:', error);
    handleError(res, error);
  }
}; 