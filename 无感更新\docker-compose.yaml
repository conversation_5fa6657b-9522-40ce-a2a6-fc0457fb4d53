version: '3.8'

services:
  admin-api:
    image: registry.cn-wulanchabu.aliyuncs.com/ydy-lowcode/cankao-admin-server:${ADMIN_DOCKER_VERSION}
    restart: always
    environment:
      TZ: Asia/Shanghai
      PORT: 3000
      DB_HOST: ${ADMIN_API_DB_HOST}
      DB_PORT: ${ADMIN_API_DB_PORT}
      DB_NAME: ${ADMIN_API_DB_NAME}
      DB_USER: ${ADMIN_API_DB_USER}
      DB_PASSWORD: ${ADMIN_API_DB_PASSWORD}
      DB_DIALECT: ${ADMIN_API_DB_DIALECT}
      JWT_SECRET: ${ADMIN_API_JWT_SECRET}
      JWT_EXPIRES_IN: ${ADMIN_API_JWT_EXPIRES_IN}
      API_PREFIX: ${ADMIN_API_API_PREFIX}
      REDIS_HOST: ${ADMIN_API_REDIS_HOST}
      REDIS_PORT: ${ADMIN_API_REDIS_PORT}
      REDIS_USERNAME: ${ADMIN_API_REDIS_USERNAME}
      REDIS_PASSWORD: ${ADMIN_API_REDIS_PASSWORD}
      REDIS_DB: ${ADMIN_API_REDIS_DB}
      WECHAT_APPID: ${ADMIN_API_WECHAT_APPID}
      WECHAT_SECRET: ${ADMIN_API_WECHAT_SECRET}
      DEFAULT_ENTERPRISE_ID: ${ADMIN_API_DEFAULT_ENTERPRISE_ID}
      DIFY_URL: ${ADMIN_API_DIFY_URL}
      DATASET_ID: ${ADMIN_API_DATASET_ID}
    volumes:
      - ./api-uploads:/app/uploads
    ports:
      - "${ENT_ADMIN_API_PORT}:3000"
    networks:
      - cankao-dev

  admin-web:
    image: registry.cn-wulanchabu.aliyuncs.com/ydy-lowcode/cankao-admin-web:${ADMIN_WEB_DOCKER_VERSION}
    restart: always
    depends_on:
      - admin-api
    environment:
      TZ: Asia/Shanghai
      VITE_APP_API_BASE_URL: ${ADMIN_WEB_API_BASE_URL}
      VITE_ALLOWED_HOSTS: ${ADMIN_WEB_ALLOWED_HOSTS}
      VITE_APP_API_BASE_IMG_URL: ${ADMIN_WEB_API_BASE_IMG_URL}
      VITE_APP_API_TIMEOUT: ${ADMIN_WEB_APP_API_TIMEOUT}
    ports:
      - "${ENT_ADMIN_WEB_PORT}:5173"
    networks:
      - cankao-dev

networks:
  cankao-dev:
    driver: bridge
