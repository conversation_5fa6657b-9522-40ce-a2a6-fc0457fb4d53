const express = require('express');
const router = express.Router();
const announcementController = require('../controllers/announcementController');
const { requireLogin } = require('../middleware/auth');

// 创建公告
router.post('/', requireLogin, announcementController.createAnnouncement);

// 更新公告
router.put('/:id', requireLogin, announcementController.updateAnnouncement);

// 删除公告
router.delete('/:id', requireLogin, announcementController.deleteAnnouncement);

// 获取公告详情
router.get('/:id', requireLogin, announcementController.getAnnouncement);

// 获取公告列表
router.get('/', requireLogin, announcementController.getAnnouncementList);

module.exports = router; 