SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
--     初始化企业的id，如果你想新增初始化企业的数据，修改这个id就行
SET @ENTERPRISE_ID =  '72ed5378-4d6d-40cf-af83-8ab801d379c2';

ALTER TABLE `users`
DROP INDEX `username`,
DROP INDEX `username_2`,
DROP INDEX `username_3`,
ADD UNIQUE INDEX `username`(`username`, `enterprise_id`) USING BTREE;

ALTER TABLE `roles`
DROP INDEX `role_code`,
DROP INDEX `role_code_2`,
DROP INDEX `role_code_3`,
ADD UNIQUE INDEX `role_code`(`role_code`, `enterprise_id`) USING BTREE;

ALTER TABLE `dictionary_type`
DROP INDEX `uk_type_code`,
DROP INDEX `type_code`,
DROP INDEX `type_code_2`,
ADD UNIQUE INDEX `uk_type_code`(`type_code`, `enterprise_id`) USING BTREE;

ALTER TABLE `system_settings`
DROP INDEX `uk_code`,
DROP INDEX `code`,
DROP INDEX `code_2`,
ADD UNIQUE INDEX `uk_code`(`code`, `enterprise_id`) USING BTREE;

ALTER TABLE `org_level`
DROP INDEX `idx_name`,
ADD UNIQUE INDEX `idx_name`(`code`, `enterprise_id`) USING BTREE;

-- ----------------------------
-- Records of users (先创建用户)
-- ----------------------------
INSERT INTO `users`( `username`, `password`, `nickname`, `email`, `phone`, `avatar`, `status`, `create_time`, `update_time`, `enterprise_id`, `open_id`, `real_name`, `real_phone`, `id_number`, `platform`, `current_position`, `current_level`) VALUES ( 'admin', '$2b$10$ox0I2bhiiUxlo5bKNN.7Nu7LaDOSg5sLV9j7IArahPqiwNrLtyA36', '管理员', '<EMAIL>', '15852522121', NULL, 1, NOW(), NOW(), @ENTERPRISE_ID, NULL, NULL, NULL, NULL, 'admin', NULL, NULL);

-- 获取admin用户ID
SET @ADMIN_USER_ID = (SELECT id FROM `users` WHERE `username` = 'admin' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles`( `role_name`, `role_code`, `sort`, `status`, `remark`, `create_time`, `update_time`, `enterprise_id`) VALUES ( '超级管理员', 'ROLE_ADMIN', 1, 1, '系统超级管理员角色', NOW(), NOW(), @ENTERPRISE_ID);

-- 获取超级管理员角色ID
SET @ADMIN_ROLE_ID = (SELECT id FROM `roles` WHERE `role_code` = 'ROLE_ADMIN' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);

-- ----------------------------
-- Records of menus
-- ----------------------------
-- 一级菜单（父菜单）
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (0, '控制台', '/dashboard', 'dashboard/index', NULL, 'dashboard', 0, 0, 1, 'dashboard', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (0, '组织管理', '/organization', NULL, NULL, 'apartment', 1, 0, 0, NULL, 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (0, '练考管理', '/exam-manage', NULL, NULL, 'form', 3, 0, 0, 'exam.manage', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (0, '餐考师', '/catering-examiner', NULL, NULL, 'customer-service', 4, 0, 0, NULL, 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (0, '系统设置', '/system', NULL, NULL, 'setting', 6, 0, 0, NULL, 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (0, '初始化管理', '/position', NULL, NULL, 'user', 2, 0, 0, NULL, 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (0, '信息管理', '/config', NULL, NULL, 'file', 5, 0, 0, NULL, 1, NOW(), NOW(), @ENTERPRISE_ID);

-- 获取一级菜单ID（父菜单ID）
SET @ENTERPRISE_MENU_ID = (SELECT id FROM `menus` WHERE `path` = '/enterprise' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @ORGANIZATION_MENU_ID = (SELECT id FROM `menus` WHERE `path` = '/organization' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @EXAM_MANAGE_MENU_ID = (SELECT id FROM `menus` WHERE `path` = '/exam-manage' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @CATERING_EXAMINER_MENU_ID = (SELECT id FROM `menus` WHERE `path` = '/catering-examiner' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @SYSTEM_MENU_ID = (SELECT id FROM `menus` WHERE `path` = '/system' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @POSITION_MENU_ID = (SELECT id FROM `menus` WHERE `path` = '/position' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @CONFIG_MENU_ID = (SELECT id FROM `menus` WHERE `path` = '/config' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);

-- 二级菜单
-- 企业管理子菜单
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@ENTERPRISE_MENU_ID, '企业列表', '/enterprise/list', 'enterprise/list/index', NULL, NULL, 1, 0, 1, 'enterprise.list', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@ENTERPRISE_MENU_ID, '智能体列表', '/enterprise/agent', 'enterprise/agent/index', NULL, NULL, 2, 0, 1, 'enterprise.agent', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@ENTERPRISE_MENU_ID, '知识库列表', '/enterprise/knowledge', 'enterprise/knowledge/index', NULL, NULL, 3, 0, 1, 'enterprise.knowledge', 1, NOW(), NOW(), @ENTERPRISE_ID);

-- 组织管理子菜单
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@ORGANIZATION_MENU_ID, '组织架构', '/organization/structure', 'organization/structure/index', NULL, NULL, 1, 0, 1, 'organization.structure', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@ORGANIZATION_MENU_ID, '员工列表', '/organization/employee', 'organization/employee/index', NULL, '', 2, 1, 1, 'organization.employee', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@ORGANIZATION_MENU_ID, '员工统计', '/organization/statistics', 'organization/statistics/index', NULL, NULL, 3, 0, 1, 'organization.statistics', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@ORGANIZATION_MENU_ID, '员工审核', '/organization/employee-application', 'organization/employee/application', NULL, '', 4, 0, 1, 'organization.employee.application', 1, NOW(), NOW(), @ENTERPRISE_ID);

-- 练考管理子菜单
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@EXAM_MANAGE_MENU_ID, '练考配置', '/exam-manage/config', 'exam-manage/config/index', NULL, '', 1, 0, 1, 'exam.manage.config', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@EXAM_MANAGE_MENU_ID, '练习记录', '/exam-manage/practice', 'exam-manage/practice/index', NULL, '', 2, 0, 1, 'exam.manage.practice', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@EXAM_MANAGE_MENU_ID, '考试记录', '/exam-manage/exam', 'exam-manage/exam/index', NULL, '', 3, 0, 1, 'exam.manage.exam', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@EXAM_MANAGE_MENU_ID, '考试审核', '/exam-manage/review', 'exam-manage/review/index', NULL, '', 4, 0, 1, 'exam.manage.review', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@EXAM_MANAGE_MENU_ID, '证书记录', '/exam-manage/certificate', 'exam-manage/certificate/index', NULL, '', 5, 0, 1, 'exam.manage.certificate', 1, NOW(), NOW(), @ENTERPRISE_ID);

-- 餐考师子菜单
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@CATERING_EXAMINER_MENU_ID, '餐考师', '/catering-examiner/front-hall', 'catering-examiner/front-hall/index', NULL, NULL, 1, 0, 1, 'catering.examiner.front', 1, NOW(), NOW(), @ENTERPRISE_ID);

-- 系统设置子菜单
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@SYSTEM_MENU_ID, '系统设置', '/system/setting', 'system/setting/index', NULL, '', 0, 0, 1, 'system.setting', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@SYSTEM_MENU_ID, '用户管理', '/system/user', 'system/user/index', NULL, NULL, 1, 0, 1, 'system.user', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@SYSTEM_MENU_ID, '角色管理', '/system/role', 'system/role/index', NULL, NULL, 2, 0, 1, 'system.role', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@SYSTEM_MENU_ID, '字典管理', '/system/dictionary', 'organization/dictionary/index', NULL, '', 3, 0, 1, 'organization.dictionary', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@SYSTEM_MENU_ID, '菜单管理', '/system/menu', 'system/menu/index', NULL, '', 4, 0, 1, 'system.menu', 1, NOW(), NOW(), @ENTERPRISE_ID);

-- 初始化管理子菜单
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@POSITION_MENU_ID, '企业专属配置', '/config/info', 'config/info/index', NULL, '', 1, 0, 1, 'config.info', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@POSITION_MENU_ID, '等级列表', '/position/level', 'position/level/index', NULL, '', 2, 0, 1, 'position.level', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@POSITION_MENU_ID, '岗位列表', '/position/hierarchy', 'position/hierarchy/index', NULL, '', 3, 0, 1, 'position.hierarchy', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@POSITION_MENU_ID, '知识库管理', '/knowledge-base', 'knowledge-base/index', NULL, 'book', 4, 0, 1, 'knowledge.base', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@POSITION_MENU_ID, '题库管理', '/kb-questions', '/kb-questions/index', NULL, '', 5, 0, 1, 'knowledge.question', 1, NOW(), NOW(), @ENTERPRISE_ID);


-- 信息管理子菜单
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@CONFIG_MENU_ID, '意见反馈', '/feedback', 'feedback/index', NULL, 'message', 1, 0, 1, 'feedback', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@CONFIG_MENU_ID, '通知公告', '/config/announcement', 'config/announcement/index', NULL, '', 2, 0, 1, 'config.announcement', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@CONFIG_MENU_ID, '餐烤师配置', '/config/restaurant', 'config/restaurant/index', NULL, '', 3, 0, 1, 'config.restaurant', 1, NOW(), NOW(), @ENTERPRISE_ID);

-- 获取考试审核菜单ID，用于三级菜单
SET @EXAM_REVIEW_MENU_ID = (SELECT id FROM `menus` WHERE `path` = '/exam-manage/review' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);

-- 三级菜单（按钮权限）
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@EXAM_REVIEW_MENU_ID, '资格审核', '', NULL, NULL, '', 0, 0, 2, 'exam.review.qualification', 1, NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `menus`(`parent_id`, `name`, `path`, `component`, `redirect`, `icon`, `sort`, `hidden`, `type`, `perms`, `status`, `create_time`, `update_time`, `enterprise_id`) VALUES (@EXAM_REVIEW_MENU_ID, '成绩审核', '', NULL, NULL, '', 0, 0, 2, 'exam.review.score', 1, NOW(), NOW(), @ENTERPRISE_ID);

-- ----------------------------
-- Records of user_roles (用户角色关联)
-- 给admin用户分配超级管理员角色
-- ----------------------------
INSERT INTO `user_roles` (`user_id`, `role_id`, `enterprise_id`)
VALUES (@ADMIN_USER_ID, @ADMIN_ROLE_ID, @ENTERPRISE_ID);

-- ----------------------------
-- Records of menu_role (角色菜单关联)
-- 给超级管理员角色分配所有菜单权限
-- ----------------------------
INSERT INTO `role_menus` (`menu_id`, `role_id`, `enterprise_id`)
SELECT id, @ADMIN_ROLE_ID, @ENTERPRISE_ID
FROM `menus`
WHERE `enterprise_id` = @ENTERPRISE_ID;

-- ----------------------------
-- Records of dictionary_type (字典类型)
-- ----------------------------
INSERT INTO `dictionary_type` (`type_code`, `type_name`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES ('sys_user_sex', '用户性别1', 0, '用户性别列表', @ADMIN_USER_ID, NOW(), @ADMIN_USER_ID, NOW(), @ENTERPRISE_ID);
INSERT INTO `dictionary_type` (`type_code`, `type_name`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES ('sys_yes_no', '系统是否', 1, '系统是否列表', @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_type` (`type_code`, `type_name`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES ('sys_normal_disable', '系统开关', 1, '系统开关列表', @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_type` (`type_code`, `type_name`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES ('sys_show_hide', '显示状态', 1, '显示状态', @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_type` (`type_code`, `type_name`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES ('position_type', '岗位类别', 1, '岗位类别字典', @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_type` (`type_code`, `type_name`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES ('position_name', '岗位名称', 1, '岗位名称字典', @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_type` (`type_code`, `type_name`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES ('feedback_type', '反馈类型', 1, '意见反馈类型字典', @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);

-- ----------------------------
-- Records of dictionary_data (字典数据)
-- ----------------------------
-- 设置字典类型ID变量
SET @DICT_TYPE_SEX_ID = (SELECT id FROM `dictionary_type` WHERE `type_code` = 'sys_user_sex' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @DICT_TYPE_YES_NO_ID = (SELECT id FROM `dictionary_type` WHERE `type_code` = 'sys_yes_no' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @DICT_TYPE_NORMAL_DISABLE_ID = (SELECT id FROM `dictionary_type` WHERE `type_code` = 'sys_normal_disable' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @DICT_TYPE_SHOW_HIDE_ID = (SELECT id FROM `dictionary_type` WHERE `type_code` = 'sys_show_hide' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @DICT_TYPE_POSITION_TYPE_ID = (SELECT id FROM `dictionary_type` WHERE `type_code` = 'position_type' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @DICT_TYPE_POSITION_NAME_ID = (SELECT id FROM `dictionary_type` WHERE `type_code` = 'position_name' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);
SET @DICT_TYPE_FEEDBACK_TYPE_ID = (SELECT id FROM `dictionary_type` WHERE `type_code` = 'feedback_type' AND `enterprise_id` = @ENTERPRISE_ID LIMIT 1);

-- 用户性别字典数据
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_SEX_ID, '男', '0', 1, NULL, NULL, 1, 0, NULL, @ADMIN_USER_ID, NOW(), @ADMIN_USER_ID, NOW(), @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_SEX_ID, '女', '1', 2, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_SEX_ID, '未知', '2', 3, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);

-- 系统是否字典数据
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_YES_NO_ID, '是', 'Y', 1, NULL, NULL, 1, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_YES_NO_ID, '否', 'N', 2, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);

-- 系统开关字典数据
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_NORMAL_DISABLE_ID, '正常', '0', 1, NULL, NULL, 1, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_NORMAL_DISABLE_ID, '停用', '1', 2, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);

-- 显示状态字典数据
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_SHOW_HIDE_ID, '显示', '0', 1, NULL, NULL, 1, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_SHOW_HIDE_ID, '隐藏', '1', 2, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);

-- 岗位类别字典数据
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_TYPE_ID, '管理岗位', '1', 1, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_TYPE_ID, '技术岗位', '2', 2, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_TYPE_ID, '营销岗位', '3', 3, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_TYPE_ID, '行政岗位', '4', 4, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_TYPE_ID, '财务岗位', '5', 5, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_TYPE_ID, '人力资源岗位', '6', 6, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_TYPE_ID, '客服岗位', '7', 7, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);

-- 岗位名称字典数据
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '总经理', '1', 1, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '部门经理', '2', 2, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '高级工程师', '3', 3, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '中级工程师', '4', 4, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '初级工程师', '5', 5, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '销售经理', '6', 6, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '销售顾问', '7', 7, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '人事专员', '8', 8, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '行政专员', '9', 9, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '财务专员', '10', 10, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_POSITION_NAME_ID, '客服专员', '11', 11, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);

-- 反馈类型字典数据
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_FEEDBACK_TYPE_ID, '功能建议', '1', 1, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_FEEDBACK_TYPE_ID, '内容问题', '2', 2, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_FEEDBACK_TYPE_ID, '系统故障', '3', 3, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_FEEDBACK_TYPE_ID, '体验问题', '4', 4, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);
INSERT INTO `dictionary_data` (`type_id`, `dict_label`, `dict_value`, `dict_sort`, `css_class`, `list_class`, `is_default`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `enterprise_id`) VALUES (@DICT_TYPE_FEEDBACK_TYPE_ID, '其他', '5', 5, NULL, NULL, 0, 1, NULL, @ADMIN_USER_ID, NOW(), NULL, NULL, @ENTERPRISE_ID);

-- ----------------------------
-- Records of system_settings (系统设置)
-- ----------------------------
INSERT INTO `system_settings` (`code`, `name`, `value`, `description`, `created_at`, `updated_at`, `enterprise_id`) VALUES ('company_name', '餐烤餐考', '餐烤餐考', '公司名称', NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `system_settings` (`code`, `name`, `value`, `description`, `created_at`, `updated_at`, `enterprise_id`) VALUES ('auto_pass', '考试自动通过申请', 'true', '考试自动通过申请', NOW(), NOW(), @ENTERPRISE_ID);
INSERT INTO `system_settings`(`code`, `name`, `value`, `description`, `created_at`, `updated_at`, `enterprise_id`) VALUES ('achieve', '成就标志', 'true', '', NOW(), NOW(), @ENTERPRISE_ID);

INSERT INTO `restaurant_config`( `enterprise_id`, `position_belong_id`, `avatar`, `name`, `init_message`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES (1,  @ENTERPRISE_ID, 5, null, '张磊磊', '我是你们的大管家【张磊】，人称行走的菜单百科全书，还是问题解决小能手，有问题快来问我吧！', @ADMIN_USER_ID, @ADMIN_USER_ID, '2025-05-23 18:55:58', '2025-06-13 11:16:58');


SET FOREIGN_KEY_CHECKS = 1;
