const { Op } = require('sequelize');
const ExamReviewApplication = require('../../models/ExamReviewApplication');
const User = require('../../models/user');
const { handleError } = require('../../utils/errorHandler');
const { handleResponse } = require('../../utils/response');
const ExamRecord = require('../../models/ExamRecord');
const Employee = require('../../models/Employee');
const Position = require('../../models/Position');
const PositionType = require('../../models/PositionType');
const Level = require('../../models/Level');
const KnowledgeBase = require('../../models/knowledge-base');
const CertificateRecord = require('../../models/CertificateRecord');
const ExamConfig = require('../../models/ExamConfigModel');
const { SystemSetting } = require('../../models');

/**
 * 根据申请信息查询匹配的考试配置
 * @param {string} enterpriseId 企业ID
 * @param {object} application 申请信息对象
 * @returns {object|null} 考试配置信息
 */
async function getExamConfigInfo(enterpriseId, application) {
  let examConfigInfo = null;
  try {
    // 查询匹配的ExamConfigModel数据
    const examConfig = await ExamConfig.findOne({
      where: {
        enterpriseId: String(enterpriseId),
        positionName: application.positionName,
        positionLevel: application.positionLevel,
        examSubject: application.kbId,
        status: '必考'
      }
    });

    if (examConfig) {
      // 将查询到的ExamConfigModel数据保存到examConfigInfo字段
      examConfigInfo = examConfig.get({ plain: true });
      console.log('找到匹配的练考配置信息:', examConfigInfo);
    } else {
      console.log('未找到匹配的练考配置信息');
    }
  } catch (error) {
    console.error('查询练考配置信息出错:', error);
  }
  return examConfigInfo;
}

/**
 * 获取申请状态
 * 根据系统设置中的auto_pass决定申请状态
 * @param {string} enterpriseId 企业ID
 * @returns {Promise<string>} 返回状态值："1"表示待审核，"2"表示已通过
 */
async function getApplicationStatus(enterpriseId) {
  try {
    const autoPassSetting = await SystemSetting.findOne({
      where: {
        code: 'auto_pass',
        enterpriseId
      }
    });
    
    if (autoPassSetting && autoPassSetting.value === 'true') {
      return '2'; // 自动通过
    }
  } catch (error) {
    console.error('获取自动通过设置失败:', error);
  }
  
  return '1'; // 默认为待审核
}

/**
 * 考试审核申请控制器
 * 用于处理考试申请的增删改查和审核流程
 */
class ReviewController {
  /**
   * 创建证书记录
   * @param {Object} application - 考试申请记录
   * @param {Object} passingRecord - 合格的考试记录
   * @param {Object} employee - 员工信息
   * @param {string} enterpriseId - 企业ID
   * @returns {Promise<Object>} 创建的证书记录
   * @private
   */
  static async _createCertificateRecord(application, passingRecord, enterpriseId) {
    try {
      if (passingRecord) {
        const knowledgeBase = await KnowledgeBase.findOne({
          where: {
            id: application.kbId,
            deleted: false,
            enterpriseId: String(enterpriseId)
          }
        });

        if (knowledgeBase) {
          // 查询员工信息
          const employee = await Employee.findOne({
            where: {
              openId: application.openId ,
              enterpriseId,
              status: '1'
            }
          });

          // 获取岗位信息
          const position = await Position.findOne({
            where: {
              nameId: application.positionName,
              levelId: application.positionLevel,
              enterpriseId
            },
            include: [
              {
                model: PositionType,
                as: 'positionType',
                attributes: ['id', 'name'],
                required: false
              }
            ]
          });

          // 获取练考配置中的证书有效期
          const examConfig = await ExamConfig.findOne({
            where: {
              positionName: application.positionName,
              positionLevel: application.positionLevel,
              examSubject: application.kbId,
              status: '必考',
              enterpriseId
            }
          });

          if (employee) {
            // 生成证书编号
            const date = new Date();
            const yearMonth = date.getFullYear().toString().slice(2) +
                          (date.getMonth() + 1).toString().padStart(2, '0');
            const randomNum = Math.floor(100000 + Math.random() * 900000);
            const certificateNo = `C${yearMonth}${randomNum}`;

            const certificateRecord = await CertificateRecord.create({
              certificateNo,
              certificateName: knowledgeBase.name,
              obtainTime: new Date(),
              positionName: application.positionName,
              positionLevel: application.positionLevel,
              positionBelong: position?.positionType?.id || null, // 设置岗位归属
              employeeName: employee.name,
              employeeId: employee.id,
              openId: employee.openId,
              examRecordId: passingRecord.id,
              enterpriseId,
              kbId: knowledgeBase.id,
              applicationId: application.id,
              validUntil: examConfig ? new Date(new Date().setDate(new Date().getDate() + examConfig.certificateValidDays)) : new Date(new Date().setFullYear(new Date().getFullYear() + 1)), // 如果有配置使用配置的有效期，否则默认1年
              delFlag: 0,
              createTime: new Date(),
              updateTime: new Date()
            });
            console.log(`创建证书记录成功, ID: ${certificateRecord.id}`);
            return certificateRecord;
          }
        }
      }
    } catch (error) {
      console.error('创建证书记录失败:', error);
      throw error;
    }
  }
  /**
   * 创建考试审核申请
   */
  async createApplication(req, res) {
    try {
      const { user } = req;
      const enterpriseId = user.enterpriseId;

      // 检查是否自动通过
      const status = await getApplicationStatus(enterpriseId);

      // 生成申请编号：企业代码+当前日期+4位随机数
      const date = new Date();
      const dateStr = date.getFullYear().toString().slice(2) +
                     (date.getMonth() + 1).toString().padStart(2, '0') +
                     date.getDate().toString().padStart(2, '0');
      const randomNum = Math.floor(1000 + Math.random() * 9000);
      const applicationNo = `EX${enterpriseId.toString().padStart(2, '0')}${dateStr}${randomNum}`;

      const application = await ExamReviewApplication.create({
        ...req.body,
        applicationNo,
        enterpriseId,
        createdBy: user.id,
        updatedBy: user.id,
        status // 使用根据系统设置决定的状态
      });

      handleResponse(res, {
        data: application,
        message: '考试审核申请创建成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 获取考试审核申请列表（分页）
   */
  async getApplicationList(req, res) {
    try {
      const { user } = req;
      const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
      const {
        pageNum = 1,
        limit = 10,
        status,
        examType,
        startDate,
        endDate,
        applicantName,
        applicationNo,
        examTitle,
        positionName,
        positionLevel,
        scoreConfirmStatus
      } = req.query;

      const offset = (parseInt(pageNum) - 1) * parseInt(limit);

      // 构建查询条件
      const where = {
        enterpriseId,
        delFlag: false
      };

      if (status) where.status = status;
      if (examType) where.examType = examType;
      if (applicantName) where.applicantName = { [Op.like]: `%${applicantName}%` };
      if (applicationNo) where.applicationNo = { [Op.like]: `%${applicationNo}%` };

      // 添加新的查询条件
      if (examTitle) where.examTitle = { [Op.like]: `%${examTitle}%` };

      // 根据岗位名称查找考试记录
      if (positionName) {
        console.log('按岗位名称查询:', positionName);

        // 由于字段变更，现在positionName字段存储岗位名称
        where.positionName = { [Op.like]: `%${positionName}%` };
      }

      // 处理岗位等级查询
      if (positionLevel) {
        console.log('按岗位等级ID查询:', positionLevel);
        where.positionLevel = positionLevel; // 直接匹配ID
      }

      if (scoreConfirmStatus) where.scoreConfirmStatus = scoreConfirmStatus;

      // 日期范围查询
      if (startDate || endDate) {
        where.examDate = {};
        if (startDate) where.examDate[Op.gte] = new Date(startDate);
        if (endDate) where.examDate[Op.lte] = new Date(endDate);
      }

      console.log('查询条件构建完成:', JSON.stringify(where, null, 2));
      console.log('查询字段映射说明:');
      console.log('- examTitle 直接对应考试标题');
      console.log('- positionName 存储岗位名称');
      console.log('- scoreConfirmStatus 直接对应数据库字段');
      console.log('- positionLevel 直接对应数据库字段');

      // 执行查询
      const { count, rows } = await ExamReviewApplication.findAndCountAll({
        where,
        order: [['created_at', 'DESC']],
        offset,
        limit: parseInt(limit),
        logging: console.log // 输出SQL语句
      });

      // 处理返回数据，添加confirmInfo字段
      const enhancedRows = rows.map(row => {
        const plainRow = row.get({ plain: true });

        // 如果没有confirmInfo字段，则添加
        if (!plainRow.confirmInfo) {
          plainRow.confirmInfo = {
            confirmedBy: null,
            confirmedAt: null
          };

          // 根据实际逻辑填充有意义的默认值
          if (plainRow.status === '已完成') {
            plainRow.confirmInfo = {
              confirmedBy: plainRow.updatedBy || user.id,
              confirmedAt: plainRow.updatedAt || new Date().toISOString()
            };
          }
        }

        // // 添加scoreConfirmStatus字段
        // if (!plainRow.scoreConfirmStatus) {
        //   plainRow.scoreConfirmStatus = plainRow.status === '已完成' ? '2' : '1';
        // }

        return plainRow;
      });

      handleResponse(res, {
        data: {
          list: enhancedRows,
          // pagination: {
            total: count,
            pageNum: parseInt(pageNum),
            limit: parseInt(limit),
            pages: Math.ceil(count / parseInt(limit))
          // }
        },
        message: '获取考试审核申请列表成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 获取考试审核申请详情
   */
  async getApplicationDetail(req, res) {
    try {
      const { id } = req.params;
      const { user } = req;
      const enterpriseId = user.enterpriseId;

      const application = await ExamReviewApplication.findOne({
        where: {
          id,
          enterpriseId,
          delFlag: false
        },
        include: [
          {
            model: ExamRecord,
            as: 'examRecords',
            where: {
              delFlag: false
            },
            required: false
          }
        ]
      });

      if (!application) {
        return handleResponse(res, {
          code: 404,
          message: '考试审核申请不存在'
        });
      }

      handleResponse(res, {
        data: application,
        message: '获取考试审核申请详情成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 更新考试审核申请
   */
  async updateApplication(req, res) {
    try {
      const { id } = req.params;
      const { user } = req;
      const enterpriseId = user.enterpriseId;

      // 只能更新待审核状态的申请
      const application = await ExamReviewApplication.findOne({
        where: {
          id,
          enterpriseId,
          delFlag: false
        }
      });

      if (!application) {
        return handleResponse(res, {
          code: 404,
          message: '考试审核申请不存在'
        });
      }

      if (application.status !== '待审核') {
        return handleResponse(res, {
          code: 400,
          message: '只能修改待审核状态的申请'
        });
      }

      // 更新申请信息
      await application.update({
        ...req.body,
        updatedBy: user.id
      });

      handleResponse(res, {
        data: application,
        message: '考试审核申请更新成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 删除考试审核申请（逻辑删除）
   */
  async deleteApplication(req, res) {
    try {
      const { id } = req.params;
      const { user } = req;
      const enterpriseId = user.enterpriseId;

      const application = await ExamReviewApplication.findOne({
        where: {
          id,
          enterpriseId,
          delFlag: false
        }
      });

      if (!application) {
        return handleResponse(res, {
          code: 404,
          message: '考试审核申请不存在'
        });
      }

      // 只能删除待审核状态的申请
      if (application.status !== '待审核') {
        return handleResponse(res, {
          code: 400,
          message: '只能删除待审核状态的申请'
        });
      }

      // 逻辑删除
      await application.update({
        delFlag: true,
        updatedBy: user.id
      });

      handleResponse(res, {
        message: '考试审核申请删除成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 审核考试申请
   */
  async reviewApplication(req, res) {
    try {
      const { id } = req.params;
      const { user } = req;
      const enterpriseId = user.enterpriseId;
      const { status, reviewInfo, examTime } = req.body;
      console.log('审核信息:', reviewInfo);
      console.log('考试时间:', examTime);

      if (!['2', '3'].includes(status)) {
        return handleResponse(res, {
          code: 400,
          message: '审核状态无效'
        });
      }

      const application = await ExamReviewApplication.findOne({
        where: {
          id,
          enterpriseId,
          delFlag: false
        }
      });

      if (!application) {
        return handleResponse(res, {
          code: 404,
          message: '考试审核申请不存在'
        });
      }

      // 只能审核待审核状态的申请
      if (application.status !== '1') {
        return handleResponse(res, {
          code: 400,
          message: '该申请已审核'
        });
      }

      // 获取当前时间并格式化为yyyy-MM-dd HH:mm:ss格式
      const now = new Date();
      const reviewedAt = now.getFullYear() + '-' +
                        String(now.getMonth() + 1).padStart(2, '0') + '-' +
                        String(now.getDate()).padStart(2, '0') + ' ' +
                        String(now.getHours()).padStart(2, '0') + ':' +
                        String(now.getMinutes()).padStart(2, '0') + ':' +
                        String(now.getSeconds()).padStart(2, '0');

      // 审核通过时，查询并保存ExamConfigModel数据
      let examConfigInfo = null;
      if (status === '2') {
        try {
          // 查询匹配的ExamConfigModel数据
          examConfigInfo = await getExamConfigInfo(enterpriseId, application);
        } catch (error) {
          console.error('查询练考配置信息出错:', error);
        }
      }

      // 更新审核信息
      await application.update({
        status,
        examDate: examTime, // 添加考试时间
        reviewInfo: {
          reviewedBy: user.id,
          reviewedByName: user.username,
          reviewedAt: reviewedAt,
          reviewComments: reviewInfo.reviewComments || ''
        },
        examConfigInfo, // 保存练考配置信息
        updatedBy: user.id
      });

      handleResponse(res, {
        data: application,
        message: `考试审核${status === '2' ? '通过' : '不通过'}`
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 添加考试成绩
   */
  async addExamScore(req, res) {
    try {
      const { applicationId } = req.params;
      const { user } = req;
      const enterpriseId = user.enterpriseId;
      const scoreData = req.body;

      // 检查申请是否存在并且已通过审核
      const application = await ExamReviewApplication.findOne({
        where: {
          id: applicationId,
          enterpriseId,
          status: '通过',
          delFlag: false
        }
      });

      if (!application) {
        return handleResponse(res, {
          code: 404,
          message: '申请不存在或未通过审核'
        });
      }

      // 获取现有成绩数据
      let examScores = application.examScores || [];

      // 检查是否已存在相同学生的成绩
      const studentExists = examScores.some(score =>
        score.studentId === scoreData.studentId
      );

      if (studentExists) {
        return handleResponse(res, {
          code: 400,
          message: '该学生的成绩已存在'
        });
      }

      // 添加新的成绩数据
      examScores.push({
        ...scoreData,
        gradedBy: user.id,
        gradedAt: new Date()
      });

      // 更新申请记录
      await application.update({
        examScores,
        updatedBy: user.id
      });

      handleResponse(res, {
        data: application,
        message: '考试成绩添加成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 获取考试成绩列表
   */
  async getExamScoreList(req, res) {
    try {
      const { user } = req;
      const enterpriseId = user.enterpriseId;
      const {
        pageNum = 1,
        limit = 10,
        applicationId,
        studentName,
        examType,
        startDate,
        endDate,
        grade
      } = req.query;

      // 构建基础查询条件
      const where = {
        enterpriseId,
        delFlag: false,
        status: '通过' // 只查找已通过的申请
      };

      if (applicationId) where.id = applicationId;
      if (examType) where.examType = examType;

      // 日期范围查询
      if (startDate || endDate) {
        where.examDate = {};
        if (startDate) where.examDate[Op.gte] = new Date(startDate);
        if (endDate) where.examDate[Op.lte] = new Date(endDate);
      }

      // 查询所有符合条件的应用
      const applications = await ExamReviewApplication.findAll({
        where,
        order: [['examDate', 'DESC']]
      });

      // 提取并处理成绩数据
      let allScores = [];
      applications.forEach(app => {
        if (app.examScores && app.examScores.length > 0) {
          // 过滤成绩数据
          const filteredScores = app.examScores.filter(score => {
            let match = true;
            if (studentName && !score.studentName.includes(studentName)) {
              match = false;
            }
            if (grade && score.grade !== grade) {
              match = false;
            }
            return match;
          });

          // 将符合条件的成绩添加到结果中，附加申请信息
          filteredScores.forEach(score => {
            allScores.push({
              applicationId: app.id,
              applicationNo: app.applicationNo,
              examTitle: app.examTitle,
              examType: app.examType,
              examDate: app.examDate,
              ...score
            });
          });
        }
      });

      // 手动分页
      const total = allScores.length;
      const offset = (parseInt(pageNum) - 1) * parseInt(limit);
      const pagedScores = allScores.slice(offset, offset + parseInt(limit));

      handleResponse(res, {
        data: {
          list: pagedScores,
          // pagination: {
            total,
            pageNum: parseInt(pageNum),
            limit: parseInt(limit),
            pages: Math.ceil(total / parseInt(limit))
          // }
        },
        message: '获取考试成绩列表成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 统计考试通过率
   */
  async getExamStatistics(req, res) {
    try {
      const { user } = req;
      const enterpriseId = user.enterpriseId;
      const { examType, startDate, endDate } = req.query;

      // 构建基础查询条件
      const where = {
        enterpriseId,
        delFlag: false,
        status: '通过'
      };

      if (examType) where.examType = examType;

      // 日期范围查询
      if (startDate || endDate) {
        where.examDate = {};
        if (startDate) where.examDate[Op.gte] = new Date(startDate);
        if (endDate) where.examDate[Op.lte] = new Date(endDate);
      }

      // 查询所有符合条件的应用
      const applications = await ExamReviewApplication.findAll({
        where,
        attributes: ['examScores']
      });

      // 统计对象
      const statistics = {
        total: 0,
        excellent: 0,
        good: 0,
        pass: 0,
        fail: 0
      };

      // 统计成绩
      applications.forEach(app => {
        if (app.examScores && app.examScores.length > 0) {
          app.examScores.forEach(score => {
            statistics.total++;
            if (score.grade === '优秀') statistics.excellent++;
            else if (score.grade === '良好') statistics.good++;
            else if (score.grade === '及格') statistics.pass++;
            else if (score.grade === '不及格') statistics.fail++;
          });
        }
      });

      // 计算通过率
      statistics.passRate = statistics.total > 0
        ? ((statistics.excellent + statistics.good + statistics.pass) / statistics.total * 100).toFixed(2)
        : 0;

      handleResponse(res, {
        data: statistics,
        message: '获取考试统计信息成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 添加考试题目信息
   */
  async addExamQuestions(req, res) {
    try {
      const { applicationId } = req.params;
      const { user } = req;
      const enterpriseId = user.enterpriseId;
      const questionsData = req.body;

      // 检查申请是否存在并且已通过审核
      const application = await ExamReviewApplication.findOne({
        where: {
          id: applicationId,
          enterpriseId,
          status: '通过',
          delFlag: false
        }
      });

      if (!application) {
        return handleResponse(res, {
          code: 404,
          message: '申请不存在或未通过审核'
        });
      }

      // 检查是否已存在题目数据
      if (application.examQuestions && application.examQuestions.length > 0) {
        return handleResponse(res, {
          code: 400,
          message: '该申请已存在考试题目数据'
        });
      }

      // 添加题目数据
      await application.update({
        examQuestions: questionsData,
        updatedBy: user.id
      });

      handleResponse(res, {
        data: application,
        message: '考试题目数据添加成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 更新考试题目信息
   */
  async updateExamQuestions(req, res) {
    try {
      const { applicationId } = req.params;
      const { user } = req;
      const enterpriseId = user.enterpriseId;
      const questionsData = req.body;

      // 检查申请是否存在并且已通过审核
      const application = await ExamReviewApplication.findOne({
        where: {
          id: applicationId,
          enterpriseId,
          status: '通过',
          delFlag: false
        }
      });

      if (!application) {
        return handleResponse(res, {
          code: 404,
          message: '申请不存在或未通过审核'
        });
      }

      // 更新题目数据
      await application.update({
        examQuestions: questionsData,
        updatedBy: user.id
      });

      handleResponse(res, {
        data: application,
        message: '考试题目数据更新成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 获取考试题目统计信息
   */
  async getExamQuestionsStatistics(req, res) {
    try {
      const { applicationId } = req.params;
      const { user } = req;
      const enterpriseId = user.enterpriseId;

      // 检查申请是否存在
      const application = await ExamReviewApplication.findOne({
        where: {
          id: applicationId,
          enterpriseId,
          delFlag: false
        }
      });

      if (!application) {
        return handleResponse(res, {
          code: 404,
          message: '申请不存在'
        });
      }

      if (!application.examQuestions || application.examQuestions.length === 0) {
        return handleResponse(res, {
          code: 404,
          message: '该申请没有考试题目数据'
        });
      }

      // 计算各类题目的数量和正确率
      const statistics = {
        total: application.examQuestions.length,
        correctCount: 0,
        incorrectCount: 0,
        correctRate: 0,
        byType: {}
      };

      // 按题型分类统计
      application.examQuestions.forEach(question => {
        // 计算总体正确率
        if (question.isCorrect) {
          statistics.correctCount++;
        } else {
          statistics.incorrectCount++;
        }

        // 按题型统计
        if (!statistics.byType[question.questionType]) {
          statistics.byType[question.questionType] = {
            total: 0,
            correct: 0,
            correctRate: 0
          };
        }

        statistics.byType[question.questionType].total++;
        if (question.isCorrect) {
          statistics.byType[question.questionType].correct++;
        }
      });

      // 计算整体正确率
      statistics.correctRate = statistics.total > 0
        ? (statistics.correctCount / statistics.total * 100).toFixed(2)
        : 0;

      // 计算各题型正确率
      Object.keys(statistics.byType).forEach(type => {
        const typeStats = statistics.byType[type];
        typeStats.correctRate = typeStats.total > 0
          ? (typeStats.correct / typeStats.total * 100).toFixed(2)
          : 0;
      });

      handleResponse(res, {
        data: statistics,
        message: '获取考试题目统计信息成功'
      });
    } catch (error) {
      handleError(res, error);
    }
  }

  /**
   * 更新成绩确认状态
   */
  async updateScoreConfirmStatus(req, res) {
    try {
      const { id, scoreConfirmStatus, confirmComments, confirmedBy,reviewInfo } = req.body;
      const { user } = req;
      const enterpriseId = user.enterpriseId;

      // 检查申请是否存在
      const application = await ExamReviewApplication.findOne({
        where: {
          id,
          enterpriseId,
          delFlag: false
        }
      });

      if (!application) {
        console.log('申请不存在, id:', id);
        return handleResponse(res, {
          code: 404,
          message: '申请不存在'
        });
      }

      console.log('申请状态:', application.status);
      // 只能更新已通过审核的申请
      if (application.status !== '2') {
        console.log('申请未通过审核, status:', application.status);
        return handleResponse(res, {
          code: 400,
          message: '只能更新已通过审核的申请'
        });
      }

      // 如果要确认为通过状态，需要检查是否有至少一个考试成绩达到合格
      if (scoreConfirmStatus === '2') {
        // 查询与此申请关联的所有考试记录
        const examRecords = await ExamRecord.findAll({
          where: {
            reviewApplicationId: id,
            enterpriseId,
            delFlag: false
          }
        });

        console.log(`找到关联考试记录 ${examRecords.length} 条`);

        // 如果有考试记录，检查并更新状态
        if (examRecords.length > 0) {
          // 检查是否有至少一个考试记录分数达到合格分数
          const hasPassingRecord = examRecords.some(record => {
            const score = record.score !== undefined ? Number(record.score) : 0;
            const passScore = record.passScore !== undefined ? Number(record.passScore) : 60;
            console.log(`考试记录ID: ${record.id}, 分数: ${score}, 合格分: ${passScore}`);
            return score >= passScore;
          });

          if (!hasPassingRecord) {
            console.log('所有考试记录均未达到合格分数线，但允许管理员强制通过');
            // 不再阻止操作，允许管理员强制通过
          }

          // 更新所有考试记录的confirmStatus为2（管理员确认通过）
          for (const record of examRecords) {
            await record.update({
              confirmStatus: 2
            });
            console.log(`已更新考试记录ID: ${record.id} 的confirmStatus为2`);
          }

          // 获取知识库证书名称
          if (application.kbId) {
            try {
              // 优先获取合格的考试记录，如果没有则使用第一条记录
              const passingRecord = examRecords.find(record => {
                const score = record.score !== undefined ? Number(record.score) : 0;
                const passScore = record.passScore !== undefined ? Number(record.passScore) : 60;
                return score >= passScore;
              }) || examRecords[0]; // 如果没有合格记录，使用第一条记录

              // 调用独立的创建证书记录方法
              const certificateRecord = await ReviewController._createCertificateRecord(
                application,
                passingRecord,
                enterpriseId
              );
              // 将证书记录ID保存到application中
              application.certificateRecordId = certificateRecord.id;
            } catch (error) {
              console.error('获取知识库证书名称或创建证书记录出错:', error);
            }
          }
        } else {
          console.log('没有关联的考试记录，但允许管理员强制通过审核');
          // 没有考试记录也允许通过，可能是管理员手动审核通过
        }
      }

      // 如果是驳回不通过，将所有考试记录的confirmStatus更新为3
      if (scoreConfirmStatus === '3') {
        // 查询与此申请关联的所有考试记录
        const examRecords = await ExamRecord.findAll({
          where: {
            reviewApplicationId: id,
            enterpriseId,
            delFlag: false
          }
        });

        console.log(`找到关联考试记录 ${examRecords.length} 条，标记为不通过`);

        // 更新所有考试记录的confirmStatus为3
        for (const record of examRecords) {
          await record.update({
            confirmStatus: 3
          });
          console.log(`已更新考试记录ID: ${record.id} 的confirmStatus为3（不通过）`);
        }
      }

      // 获取当前时间并格式化为yyyy-MM-dd HH:mm:ss格式
      const now = new Date();
      const confirmedAt = now.getFullYear() + '-' +
                        String(now.getMonth() + 1).padStart(2, '0') + '-' +
                        String(now.getDate()).padStart(2, '0') + ' ' +
                        String(now.getHours()).padStart(2, '0') + ':' +
                        String(now.getMinutes()).padStart(2, '0') + ':' +
                        String(now.getSeconds()).padStart(2, '0');

      // 更新确认状态和信息
      const confirmInfo = {
        confirmedBy:  user.id,
        confirmedByName:  user.username,
        confirmedAt: confirmedAt,
        confirmComments: reviewInfo.confirmInfo.confirmComments || ''
      };

      console.log('确认信息:', confirmInfo);


      await application.update({
        scoreConfirmStatus,
        confirmInfo: confirmInfo,
        updatedBy: user.id,
        certificateRecordId: application.certificateRecordId // 将证书记录ID保存到字段中
      });

      handleResponse(res, {
        data: application,
        message: '成绩确认状态更新成功'
      });
    } catch (error) {
      console.error('更新成绩确认状态出错:', error);
      handleError(res, error);
    }
  }

  /**
   * 批量审核考试申请
   */
  async batchReviewApplication(req, res) {
    try {
      const { ids, status, reviewInfo, examTime } = req.body;
      const { user } = req;
      const enterpriseId = user.enterpriseId || process.env.DEFAULT_ENTERPRISE_ID;

      console.log('批量审核请求参数:', { ids, status, reviewInfo, examTime });
      console.log('用户信息:', { userId: user.id, enterpriseId });

      if (!Array.isArray(ids) || ids.length === 0) {
        return handleResponse(res, {
          code: 400,
          message: '请选择要审核的申请'
        });
      }

      if (!['2', '3'].includes(status)) {
        return handleResponse(res, {
          code: 400,
          message: '审核状态无效'
        });
      }

      // 查找所有要审核的申请
      const applications = await ExamReviewApplication.findAll({
        where: {
          id: ids,
          enterpriseId,
          delFlag: false,
          status: '1' // 只能审核待审核状态的申请
        }
      });

      console.log(`找到 ${applications.length} 条可审核的申请，期望 ${ids.length} 条`);

      if (applications.length === 0) {
        return handleResponse(res, {
          code: 404,
          message: '没有找到可审核的申请'
        });
      }

      if (applications.length !== ids.length) {
        return handleResponse(res, {
          code: 400,
          message: '部分申请不存在或已审核，请重新选择'
        });
      }

      // 获取当前时间并格式化
      const now = new Date();
      const reviewedAt = now.getFullYear() + '-' +
                        String(now.getMonth() + 1).padStart(2, '0') + '-' +
                        String(now.getDate()).padStart(2, '0') + ' ' +
                        String(now.getHours()).padStart(2, '0') + ':' +
                        String(now.getMinutes()).padStart(2, '0') + ':' +
                        String(now.getSeconds()).padStart(2, '0');

      const successCount = [];
      const failedCount = [];

      // 批量处理每个申请
      for (const application of applications) {
        try {
          console.log(`处理申请ID: ${application.id}`);
          
          // 审核通过时，查询并保存ExamConfigModel数据
          let examConfigInfo = null;
          if (status === '2') {
            try {
              examConfigInfo = await getExamConfigInfo(enterpriseId, application);
              console.log(`申请ID ${application.id} 获取考试配置信息:`, examConfigInfo ? '成功' : '未找到');
            } catch (error) {
              console.error('查询练考配置信息出错:', error);
            }
          }

          // 更新审核信息
          await application.update({
            status,
            examDate: examTime,
            reviewInfo: {
              reviewedBy: user.id,
              reviewedByName: user.username,
              reviewedAt: reviewedAt,
              reviewComments: reviewInfo.reviewComments || ''
            },
            examConfigInfo,
            updatedBy: user.id
          });

          console.log(`申请ID ${application.id} 审核成功`);
          successCount.push(application.id);
        } catch (error) {
          console.error(`审核申请ID ${application.id} 失败:`, error);
          failedCount.push(application.id);
        }
      }

      const message = `批量审核完成，成功 ${successCount.length} 条，失败 ${failedCount.length} 条`;

      console.log('批量审核结果:', { successCount: successCount.length, failedCount: failedCount.length });

      handleResponse(res, {
        data: {
          successCount: successCount.length,
          failedCount: failedCount.length,
          successIds: successCount,
          failedIds: failedCount
        },
        message
      });
    } catch (error) {
      console.error('批量审核出错:', error);
      handleError(res, error);
    }
  }

  /**
   * 批量更新成绩确认状态
   */
  async batchUpdateScoreConfirmStatus(req, res) {
    try {
      const { ids, scoreConfirmStatus, reviewInfo } = req.body;
      const { user } = req;
      const enterpriseId = user.enterpriseId || process.env.DEFAULT_ENTERPRISE_ID;

      console.log('批量成绩确认请求参数:', { ids, scoreConfirmStatus, reviewInfo });
      console.log('用户信息:', { userId: user.id, enterpriseId });

      if (!Array.isArray(ids) || ids.length === 0) {
        return handleResponse(res, {
          code: 400,
          message: '请选择要确认的申请'
        });
      }

      if (!['2', '3'].includes(scoreConfirmStatus)) {
        return handleResponse(res, {
          code: 400,
          message: '确认状态无效'
        });
      }

      // 查找所有要确认的申请
      const applications = await ExamReviewApplication.findAll({
        where: {
          id: ids,
          enterpriseId,
          delFlag: false,
          status: '2', // 只能确认已通过审核的申请
          scoreConfirmStatus: '1' // 只能确认待审核状态的申请
        }
      });

      console.log(`找到 ${applications.length} 条可确认的申请，期望 ${ids.length} 条`);

      if (applications.length === 0) {
        return handleResponse(res, {
          code: 404,
          message: '没有找到可确认的申请'
        });
      }

      if (applications.length !== ids.length) {
        return handleResponse(res, {
          code: 400,
          message: '部分申请不存在或状态不符合要求，请重新选择'
        });
      }

      // 获取当前时间并格式化
      const now = new Date();
      const confirmedAt = now.getFullYear() + '-' +
                        String(now.getMonth() + 1).padStart(2, '0') + '-' +
                        String(now.getDate()).padStart(2, '0') + ' ' +
                        String(now.getHours()).padStart(2, '0') + ':' +
                        String(now.getMinutes()).padStart(2, '0') + ':' +
                        String(now.getSeconds()).padStart(2, '0');

      const successCount = [];
      const failedCount = [];

      // 批量处理每个申请
      for (const application of applications) {
        try {
          console.log(`处理申请ID: ${application.id}`);
          
          // 如果要确认为通过状态，需要检查是否有至少一个考试成绩达到合格
          if (scoreConfirmStatus === '2') {
            const examRecords = await ExamRecord.findAll({
              where: {
                reviewApplicationId: application.id,
                enterpriseId,
                delFlag: false
              }
            });

            console.log(`申请ID ${application.id} 找到 ${examRecords.length} 条考试记录`);

            // 如果有考试记录，检查是否有合格的记录
            if (examRecords.length > 0) {
              const hasPassingRecord = examRecords.some(record => {
                const score = record.score !== undefined ? Number(record.score) : 0;
                const passScore = record.passScore !== undefined ? Number(record.passScore) : 60;
                console.log(`考试记录ID: ${record.id}, 分数: ${score}, 合格分: ${passScore}`);
                return score >= passScore;
              });

              if (!hasPassingRecord) {
                console.log(`申请ID ${application.id} 所有考试记录均未达到合格分数线，但仍允许通过审核`);
                // 没有考试记录也允许通过，可能是管理员手动审核通过
              }

              // 更新所有达到合格分数的考试记录的confirmStatus为2
              for (const record of examRecords) {
                const score = record.score !== undefined ? Number(record.score) : 0;
                const passScore = record.passScore !== undefined ? Number(record.passScore) : 60;

                // 对于通过审核的情况，更新所有考试记录的confirmStatus为2
                await record.update({
                  confirmStatus: 2
                });
                console.log(`已更新考试记录ID: ${record.id} 的confirmStatus为2`);
              }

              // 获取知识库证书名称并创建证书记录
              if (application.kbId) {
                try {
                  // 优先选择合格的考试记录，如果没有则选择第一条记录
                  const passingRecord = examRecords.find(record => {
                    const score = record.score !== undefined ? Number(record.score) : 0;
                    const passScore = record.passScore !== undefined ? Number(record.passScore) : 60;
                    return score >= passScore;
                  }) || examRecords[0]; // 如果没有合格记录，使用第一条记录

                  const certificateRecord = await ReviewController._createCertificateRecord(
                    application,
                    passingRecord,
                    enterpriseId
                  );
                  application.certificateRecordId = certificateRecord.id;
                  console.log(`申请ID ${application.id} 创建证书记录成功`);
                } catch (error) {
                  console.error(`申请ID ${application.id} 创建证书记录出错:`, error);
                }
              }
            } else {
              console.log(`申请ID ${application.id} 没有关联的考试记录，但仍允许通过审核`);
              // 没有考试记录也允许通过，可能是管理员手动审核通过
            }
          }

          // 如果是驳回不通过，将所有考试记录的confirmStatus更新为3
          if (scoreConfirmStatus === '3') {
            const examRecords = await ExamRecord.findAll({
              where: {
                reviewApplicationId: application.id,
                enterpriseId,
                delFlag: false
              }
            });

            console.log(`申请ID ${application.id} 找到 ${examRecords.length} 条考试记录，标记为不通过`);

            for (const record of examRecords) {
              await record.update({
                confirmStatus: 3
              });
              console.log(`已更新考试记录ID: ${record.id} 的confirmStatus为3（不通过）`);
            }
          }

          // 更新确认状态和信息
          const confirmInfo = {
            confirmedBy: user.id,
            confirmedByName: user.username,
            confirmedAt: confirmedAt,
            confirmComments: reviewInfo.confirmInfo.confirmComments || ''
          };

          console.log(`申请ID ${application.id} 确认信息:`, confirmInfo);

          await application.update({
            scoreConfirmStatus,
            confirmInfo: confirmInfo,
            updatedBy: user.id,
            certificateRecordId: application.certificateRecordId
          });

          console.log(`申请ID ${application.id} 更新成功`);
          successCount.push(application.id);
        } catch (error) {
          console.error(`确认申请ID ${application.id} 失败:`, error);
          failedCount.push(application.id);
        }
      }

      const message = `批量成绩确认完成，成功 ${successCount.length} 条，失败 ${failedCount.length} 条`;

      console.log('批量成绩确认结果:', { successCount: successCount.length, failedCount: failedCount.length });

      handleResponse(res, {
        data: {
          successCount: successCount.length,
          failedCount: failedCount.length,
          successIds: successCount,
          failedIds: failedCount
        },
        message
      });
    } catch (error) {
      console.error('批量成绩确认出错:', error);
      handleError(res, error);
    }
  }

  /**
   * 小程序申请考试接口
   * 根据openId查找用户和员工信息，并创建考试申请
   */
  async createApplicationFromMiniapp(req, res) {
    try {
      const { knowledgeBaseId,positionName,positionLevel } = req.body;
      const openId = req.headers.openid; // 从请求头获取openId

      if (!openId || !knowledgeBaseId || !positionName || !positionLevel) {
        return handleResponse(res, {
          code: 400,
          message: '参数不完整'
        });
      }

      // 1. 根据openId查找用户
      const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
      const user = await User.findOne({
        where: { 
          openId, 
          status: true,
          enterpriseId
        }
      });

      if (!user) {
        return handleResponse(res, {
          code: 404,
          message: '用户不存在'
        });
      }

      // 检查是否自动通过
      const status = await getApplicationStatus(enterpriseId);
      // 获取考试配置信息
      const examConfigInfo = await getExamConfigInfo(enterpriseId, {
        positionName,
        positionLevel,
        kbId: knowledgeBaseId
      });

      // 检查是否已存在未完成的相同科目申请
      const existingApplication = await ExamReviewApplication.findOne({
        where: {
          createdBy: user.id,
          kbId: knowledgeBaseId,
          [Op.or]: [
            { status: '1' },
            {
              [Op.and]: [
                { status: '2' },
                { scoreConfirmStatus: '1' }
              ]
            },
            {
              [Op.and]: [
                { status: '2' },
                { scoreConfirmStatus: '2' }
              ]
            }
          ],
          delFlag: false,
          enterpriseId
        }
      });

      if (existingApplication) {
        return handleResponse(res, {
          code: 400,
          message: '已有相同科目申请，请勿重复申请'
        });
      }

      // 2. 查找员工信息
      const employee = await Employee.findOne({
        where: {
          openId,
          status: '1', // 在职状态
          enterpriseId
        },
      });

      if (!employee) {
        return handleResponse(res, {
          code: 404,
          message: '员工信息不存在'
        });
      }

      // 3. 查询知识库数据
      const knowledgeBase = await require('../../models/knowledge-base').findOne({
        where: {
          id: knowledgeBaseId,
          deleted: false,
          enterpriseId: String(enterpriseId)
        }
      });

      if (!knowledgeBase) {
        return handleResponse(res, {
          code: 404,
          message: '知识库数据不存在'
        });
      }

      // 4. 生成申请编号
      const date = new Date();
      const dateStr = date.getFullYear().toString().slice(2) +
                     (date.getMonth() + 1).toString().padStart(2, '0') +
                     date.getDate().toString().padStart(2, '0');
      const randomNum = Math.floor(1000 + Math.random() * 9000);
      const applicationNo = `EX${enterpriseId.toString().padStart(2, '0')}${dateStr}${randomNum}`;

      // 5. 格式化申请时间
      const now = date;
      const applyTime = now.getFullYear() + '-' +
                      String(now.getMonth() + 1).padStart(2, '0') + '-' +
                      String(now.getDate()).padStart(2, '0') + ' ' +
                      String(now.getHours()).padStart(2, '0') + ':' +
                      String(now.getMinutes()).padStart(2, '0') + ':' +
                      String(now.getSeconds()).padStart(2, '0');

      // 6. 创建考试申请
      
      const application = await ExamReviewApplication.create({
        applicationNo,
        enterpriseId,
        openId,
        applicantName: employee.name,
        contactPhone: employee.phone || '',
        examType: knowledgeBase.certificateType || '证书考试',  // 使用证书类型作为考试类型
        examTitle: knowledgeBase.name || '考试',
        positionLevel: positionLevel,
        kbId: knowledgeBaseId,// 存储关联的知识库ID
        reasonForApplication: `通过小程序申请考试，知识库：${knowledgeBase.name}`,
        status: status, // 使用根据系统设置决定的状态
        createdBy: user.id,
        updatedBy: user.id,
        delFlag: false,
        scoreConfirmStatus:null,
        applyTime: applyTime, // 额外记录申请时间
        positionName: positionName,// 存储关联的岗位ID
        examConfigInfo: examConfigInfo
      });

      handleResponse(res, {
        data: application,
        message: '考试申请提交成功'
      });
    } catch (error) {
      console.error('小程序考试申请出错:', error);
      handleError(res, error);
    }
  }
}

module.exports = new ReviewController();
module.exports.getExamConfigInfo = getExamConfigInfo;
module.exports.getApplicationStatus = getApplicationStatus;
