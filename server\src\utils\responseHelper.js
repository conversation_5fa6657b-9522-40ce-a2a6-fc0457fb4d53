const axios = require('axios');
const bcrypt = require('bcryptjs');
const { generateToken } = require('./jwt');
const crypto = require('crypto');
const { addEnterpriseId, addEnterpriseFilter } = require('./enterpriseFilter');
const { User, Employee, ExamConfig, PracticeRecord, PracticeRecordDetail, sequelize, KnowledgeBase, Level, InfoConfig, PositionType, Position,PositionName,SystemSetting,RestaurantConfig,RestaurantRecord,RestaurantRecordDetail } = require('../models');
const EmployeeEnterpriseApplication = require('../models/EmployeeEnterpriseApplication');
const { Op } = require('sequelize');

/**
 * 统一返回结构
 * @param {number} code - 状态码
 * @param {string} message - 消息
 * @param {object} data - 数据对象
 */
const createResponse = (code, message, data = null) => {
  return {
    code,
    message,
    data
  };
};

module.exports = {
  axios,
  bcrypt,
  generateToken,
  crypto,
  addEnterpriseId,
  addEnterpriseFilter,
  User,
  Employee,
  EmployeeEnterpriseApplication,
  ExamConfig,
  PracticeRecord,
  PracticeRecordDetail,
  sequelize,
  KnowledgeBase,
  Op,
  Level,
  InfoConfig,
  PositionType,
  Position,
  PositionName,
  SystemSetting,
  RestaurantConfig,
  RestaurantRecord,
  RestaurantRecordDetail,
  createResponse
};