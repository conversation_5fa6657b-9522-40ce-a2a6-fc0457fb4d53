# 生产环境WebSocket问题分析

## 问题发现

通过分析你的生产环境nginx配置，我发现了几个关键问题：

### 1. **域名不匹配问题** ⚠️
- **Nginx配置域名**: `cankao-admin-api.dev.lingmiaoai.com`
- **前端连接域名**: `cankao-admin.dev.lingmiaoai.com`
- **结果**: 请求根本没有到达正确的nginx服务器！

### 2. **协议不匹配问题** ⚠️
- **Nginx配置**: HTTPS (443端口) + SSL证书
- **前端连接**: `ws://` (非加密WebSocket)
- **结果**: 应该使用 `wss://` (加密WebSocket)

### 3. **路径配置问题** ⚠️
- **Nginx配置**: `/ws/` 路径代理
- **可能的前端连接**: 直接连接根路径或其他路径

## 解决方案

### 方案1：修正域名和协议（推荐）

**正确的前端WebSocket连接地址应该是：**
```javascript
const wsUrl = 'wss://cankao-admin-api.dev.lingmiaoai.com/ws/';
```

### 方案2：如果要使用 `cankao-admin.dev.lingmiaoai.com`

需要为这个域名单独配置nginx：

```nginx
server {
    listen 80;
    server_name cankao-admin.dev.lingmiaoai.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name cankao-admin.dev.lingmiaoai.com;

    ssl_certificate /etc/nginx/ssl/dev.lingmiaoai.crt;
    ssl_certificate_key /etc/nginx/ssl/dev.lingmiaoai.key;

    # WebSocket代理配置
    location /ws/ {
        proxy_pass http://***********:31490/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 如果需要直接根路径访问WebSocket
    location / {
        # 检查是否为WebSocket升级请求
        if ($http_upgrade = "websocket") {
            proxy_pass http://***********:31490;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
            break;
        }
        
        # 普通HTTP请求
        proxy_pass http://***********:31490/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 方案3：优化现有配置

如果继续使用 `cankao-admin-api.dev.lingmiaoai.com`，优化配置：

```nginx
server {
    listen 443 ssl;
    server_name cankao-admin-api.dev.lingmiaoai.com;

    ssl_certificate /etc/nginx/ssl/dev.lingmiaoai.crt;
    ssl_certificate_key /etc/nginx/ssl/dev.lingmiaoai.key;

    # WebSocket专用路径
    location /ws/ {
        proxy_pass http://***********:31490/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 支持根路径的WebSocket连接
    location / {
        # 判断是否为WebSocket请求
        set $is_websocket 0;
        if ($http_upgrade ~* "websocket") {
            set $is_websocket 1;
        }
        if ($http_connection ~* "upgrade") {
            set $is_websocket "${is_websocket}1";
        }
        
        # WebSocket请求
        if ($is_websocket = "11") {
            proxy_pass http://***********:31490;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }
        
        # 普通HTTP请求
        if ($is_websocket != "11") {
            proxy_pass http://***********:31490/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## 调试步骤

### 1. 确认域名解析
```bash
nslookup cankao-admin-api.dev.lingmiaoai.com
nslookup cankao-admin.dev.lingmiaoai.com
```

### 2. 测试HTTPS连接
```bash
curl -I https://cankao-admin-api.dev.lingmiaoai.com
```

### 3. 测试WebSocket连接
使用浏览器开发者工具或在线WebSocket测试工具：
- 测试地址：`wss://cankao-admin-api.dev.lingmiaoai.com/ws/`

### 4. 查看nginx日志
```bash
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

## 前端代码修改

### 当前可能的错误代码：
```javascript
// ❌ 错误的连接方式
const url = 'ws://cankao-admin.dev.lingmiaoai.com';
```

### 正确的连接代码：
```javascript
// ✅ 正确的连接方式
const getWebSocketUrl = () => {
  if (process.env.NODE_ENV === 'production') {
    // 使用正确的域名、协议和路径
    return 'wss://cankao-admin-api.dev.lingmiaoai.com/ws/';
  } else {
    // 开发环境
    return 'ws://localhost:3000';
  }
};

const ws = new WebSocket(getWebSocketUrl());
```

### 或者支持多种连接方式：
```javascript
const WebSocketUrls = [
  'wss://cankao-admin-api.dev.lingmiaoai.com/ws/',
  'wss://cankao-admin-api.dev.lingmiaoai.com/',
  'ws://**************:31490'  // 备用直连
];

function tryConnect(urls, index = 0) {
  if (index >= urls.length) {
    console.error('所有WebSocket连接尝试都失败了');
    return;
  }
  
  const ws = new WebSocket(urls[index]);
  
  ws.onopen = function() {
    console.log(`WebSocket连接成功: ${urls[index]}`);
  };
  
  ws.onerror = function() {
    console.log(`WebSocket连接失败: ${urls[index]}，尝试下一个...`);
    tryConnect(urls, index + 1);
  };
}

tryConnect(WebSocketUrls);
```

## 总结

主要问题是：
1. **域名不匹配**：nginx配置的是 `cankao-admin-api` 但前端连接的是 `cankao-admin`
2. **协议不匹配**：nginx配置HTTPS但前端使用ws://而不是wss://
3. **路径可能不匹配**：nginx配置/ws/路径但前端可能连接根路径

**推荐解决方案**：
使用正确的WebSocket连接地址：`wss://cankao-admin-api.dev.lingmiaoai.com/ws/` 