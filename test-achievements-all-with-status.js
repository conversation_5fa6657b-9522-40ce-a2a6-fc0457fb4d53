/**
 * 测试获取所有徽章并标记用户获得状态的接口
 * 验证徽章列表显示和高亮功能
 */
require('dotenv').config();

console.log('获取所有徽章并标记用户状态接口说明');
console.log('='.repeat(60));

console.log('\n接口功能：');
console.log('1. 获取 achievement_templates 表的全部启用徽章');
console.log('2. 查询 user_achievements 表获取用户已获得的徽章');
console.log('3. 为每个徽章模板添加用户获得状态字段');
console.log('4. 提供统计信息和分类统计');

console.log('\n接口信息：');
console.log('路由：GET /api/wechat/achievements/all');
console.log('参数：openId (query parameter)');
console.log('方法：wechatController.getAllAchievementsWithUserStatus');

console.log('\n查询逻辑：');
console.log('1. 验证 openId 参数和用户存在性');
console.log('2. 查询所有启用的成就模板（isActive = true）');
console.log('3. 查询用户已获得的成就（status = "achieved"）');
console.log('4. 使用 Map 创建用户成就映射表');
console.log('5. 为每个模板添加用户获得状态');
console.log('6. 计算统计信息和分类统计');

console.log('\n返回数据结构：');
console.log(`{
  "code": 200,
  "data": {
    "achievements": [
      {
        "id": 1,
        "name": "初入宝殿",
        "description": "完成第一次练习",
        "icon": "/uploads/achievements/1.png",
        "category": "learning",
        "ruleType": "progress",
        "triggerCondition": "{\\"type\\":\\"first_complete\\"}",
        "rewardPoints": 10,
        "sort": 1,
        "isAchieved": true,              // 用户是否获得该徽章
        "achievedAt": "2024-01-01T10:00:00.000Z",  // 获得时间
        "actualRewardPoints": 10         // 实际获得积分
      },
      {
        "id": 2,
        "name": "学无止境",
        "description": "累计学习时长超过50小时",
        "icon": "/uploads/achievements/2.png",
        "category": "time",
        "ruleType": "time",
        "triggerCondition": "{\\"type\\":\\"study_time\\"}",
        "rewardPoints": 40,
        "sort": 2,
        "isAchieved": false,             // 用户未获得该徽章
        "achievedAt": null,              // 未获得时间为 null
        "actualRewardPoints": null       // 未获得积分为 null
      }
    ],
    "statistics": {
      "totalAchievements": 10,           // 总徽章数
      "achievedCount": 5,                // 已获得数量
      "achievementRate": 50,             // 获得率（百分比）
      "categoryStats": {                 // 分类统计
        "learning": {
          "total": 4,
          "achieved": 3
        },
        "time": {
          "total": 3,
          "achieved": 1
        },
        "exam": {
          "total": 2,
          "achieved": 1
        },
        "practice": {
          "total": 1,
          "achieved": 0
        }
      }
    }
  },
  "message": "获取徽章列表成功"
}`);

console.log('\n关键字段说明：');
console.log('- isAchieved: boolean - 用户是否获得该徽章（前端高亮显示用）');
console.log('- achievedAt: string|null - 获得时间，未获得为 null');
console.log('- actualRewardPoints: number|null - 实际获得的积分，未获得为 null');

console.log('\n排序规则：');
console.log('1. 按 category（类别）升序');
console.log('2. 按 sort（排序号）升序');
console.log('3. 按 id 升序');

console.log('\n前端使用建议：');
console.log('1. 根据 isAchieved 字段决定徽章是否高亮显示');
console.log('2. 已获得的徽章可以显示彩色图标');
console.log('3. 未获得的徽章显示灰色或半透明');
console.log('4. 可以显示获得时间和实际积分');
console.log('5. 使用统计信息显示进度条和完成率');

console.log('\n统计信息用途：');
console.log('- totalAchievements: 显示总徽章数');
console.log('- achievedCount: 显示已获得数量');
console.log('- achievementRate: 显示完成百分比');
console.log('- categoryStats: 按类别显示进度');

console.log('\n数据安全：');
console.log('- 使用 addEnterpriseFilter 确保企业数据隔离');
console.log('- 只返回启用的成就模板（isActive = true）');
console.log('- 只统计已获得状态的成就（status = "achieved"）');

console.log('\n性能优化：');
console.log('- 使用 Map 数据结构快速查找用户成就');
console.log('- 单次查询获取所有模板和用户成就');
console.log('- 在内存中进行数据组合，减少数据库查询');

console.log('\n测试场景：');
console.log('1. 测试有成就的用户');
console.log('2. 测试没有成就的用户');
console.log('3. 测试部分获得成就的用户');
console.log('4. 验证不同类别的徽章显示');
console.log('5. 验证统计信息的准确性');
console.log('6. 测试企业隔离功能');

console.log('\n错误处理：');
console.log('- openId 参数验证');
console.log('- 用户存在性验证');
console.log('- 数据库查询异常处理');
console.log('- 详细的错误日志记录');
