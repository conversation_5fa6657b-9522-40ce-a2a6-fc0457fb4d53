#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL Binlog 快速分析工具
用于快速分析binlog文件的基本信息和操作统计
"""

import os
import sys
import subprocess
import re
from collections import defaultdict, Counter
from datetime import datetime
import argparse

class BinlogQuickAnalyzer:
    def __init__(self, binlog_file, mysqlbinlog_path="mysqlbinlog"):
        self.binlog_file = binlog_file
        self.mysqlbinlog_path = mysqlbinlog_path
        self.file_info = {}
        self.table_operations = defaultdict(int)
        self.time_operations = defaultdict(int)
        self.operation_types = Counter()
        
    def get_file_info(self):
        """获取binlog文件基本信息"""
        if os.path.exists(self.binlog_file):
            stat = os.stat(self.binlog_file)
            self.file_info = {
                'size': stat.st_size,
                'size_mb': round(stat.st_size / 1024 / 1024, 2),
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime)
            }
        
    def run_mysqlbinlog(self, extra_args=""):
        """执行mysqlbinlog命令"""
        cmd = f'"{self.mysqlbinlog_path}" --no-defaults {extra_args} "{self.binlog_file}"'
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, 
                                  encoding='utf-8', errors='ignore')
            return result.stdout if result.stdout else ""
        except Exception as e:
            print(f"执行mysqlbinlog命令失败: {e}")
            return ""
    
    def analyze_tables(self):
        """分析涉及的表"""
        output = self.run_mysqlbinlog()
        if not output:
            return {}
        
        # 提取Table_map信息
        table_pattern = r'Table_map: `([^`]+)`\.`([^`]+)` mapped to number (\d+)'
        tables = re.findall(table_pattern, output)
        
        table_stats = defaultdict(lambda: {'count': 0, 'table_id': None, 'database': None})
        
        for database, table, table_id in tables:
            key = f"{database}.{table}"
            table_stats[key]['count'] += 1
            table_stats[key]['table_id'] = table_id
            table_stats[key]['database'] = database
            
        return dict(table_stats)
    
    def analyze_operations(self):
        """分析操作类型"""
        output = self.run_mysqlbinlog()
        if not output:
            return
        
        # 提取操作类型
        operation_patterns = [
            (r'Update_rows:', 'UPDATE'),
            (r'Write_rows:', 'INSERT'),
            (r'Delete_rows:', 'DELETE'),
            (r'Query_event.*CREATE', 'CREATE'),
            (r'Query_event.*DROP', 'DROP'),
            (r'Query_event.*ALTER', 'ALTER')
        ]
        
        for pattern, op_type in operation_patterns:
            matches = re.findall(pattern, output, re.IGNORECASE)
            self.operation_types[op_type] = len(matches)
    
    def analyze_timeline(self):
        """分析时间线"""
        output = self.run_mysqlbinlog()
        if not output:
            return {}
        
        # 提取时间戳
        time_pattern = r'#(\d{6})\s+(\d{2}:\d{2}:\d{2})'
        times = re.findall(time_pattern, output)
        
        timeline = defaultdict(int)
        for date_str, time_str in times:
            try:
                # 转换日期格式 YYMMDD -> YYYY-MM-DD
                year = "20" + date_str[:2]
                month = date_str[2:4]
                day = date_str[4:6]
                
                # 按小时统计
                hour_key = f"{year}-{month}-{day} {time_str[:2]}:00"
                timeline[hour_key] += 1
            except:
                continue
                
        return dict(timeline)
    
    def get_mysql_version(self):
        """获取MySQL版本信息"""
        output = self.run_mysqlbinlog("--hexdump")
        if not output:
            return "未知"
        
        # 查找版本信息
        version_pattern = r'server v ([\d\.]+)'
        match = re.search(version_pattern, output)
        return match.group(1) if match else "未知"
    
    def generate_report(self):
        """生成分析报告"""
        print("=" * 60)
        print(f"📊 Binlog文件分析报告: {os.path.basename(self.binlog_file)}")
        print("=" * 60)
        
        # 基本信息
        self.get_file_info()
        print(f"\n🔍 基本信息:")
        print(f"  文件大小: {self.file_info.get('size_mb', 0)} MB")
        print(f"  创建时间: {self.file_info.get('created', '未知')}")
        print(f"  修改时间: {self.file_info.get('modified', '未知')}")
        print(f"  MySQL版本: {self.get_mysql_version()}")
        
        # 表统计
        tables = self.analyze_tables()
        print(f"\n📋 涉及的表 (共{len(tables)}个):")
        sorted_tables = sorted(tables.items(), key=lambda x: x[1]['count'], reverse=True)
        for table_name, info in sorted_tables[:10]:  # 显示前10个最频繁的表
            print(f"  {table_name}: {info['count']}次操作 (表ID: {info['table_id']})")
        
        if len(sorted_tables) > 10:
            print(f"  ... 还有{len(sorted_tables) - 10}个表")
        
        # 操作类型统计
        self.analyze_operations()
        print(f"\n⚡ 操作类型统计:")
        for op_type, count in self.operation_types.most_common():
            print(f"  {op_type}: {count}次")
        
        # 时间线分析
        timeline = self.analyze_timeline()
        print(f"\n⏰ 操作时间分布:")
        sorted_timeline = sorted(timeline.items())
        for time_key, count in sorted_timeline[-10:]:  # 显示最近10个时间段
            print(f"  {time_key}: {count}次操作")
        
        print("\n" + "=" * 60)
        print("✅ 分析完成！")
        
        # 生成建议
        self.generate_recommendations(tables, timeline)
    
    def generate_recommendations(self, tables, timeline):
        """生成建议"""
        print(f"\n💡 分析建议:")
        
        # 找出最频繁操作的表
        if tables:
            most_active_table = max(tables.items(), key=lambda x: x[1]['count'])
            print(f"  🔴 重点关注: {most_active_table[0]} (操作次数最多: {most_active_table[1]['count']}次)")
        
        # 找出操作最集中的时间段
        if timeline:
            peak_time = max(timeline.items(), key=lambda x: x[1])
            print(f"  ⏰ 高峰时段: {peak_time[0]} (操作次数: {peak_time[1]}次)")
        
        # 性能建议
        total_ops = sum(self.operation_types.values())
        if total_ops > 1000:
            print(f"  ⚠️  大量操作检测: 共{total_ops}次操作，建议检查性能影响")
        
        if self.operation_types.get('UPDATE', 0) > 500:
            print(f"  🔄 大量UPDATE操作: {self.operation_types['UPDATE']}次，注意锁等待风险")

def main():
    parser = argparse.ArgumentParser(description='MySQL Binlog 快速分析工具')
    parser.add_argument('binlog_file', help='binlog文件路径')
    parser.add_argument('--mysqlbinlog-path', default='mysqlbinlog', 
                       help='mysqlbinlog工具路径 (默认: mysqlbinlog)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.binlog_file):
        print(f"错误: 文件 {args.binlog_file} 不存在")
        sys.exit(1)
    
    analyzer = BinlogQuickAnalyzer(args.binlog_file, args.mysqlbinlog_path)
    analyzer.generate_report()

if __name__ == "__main__":
    main() 