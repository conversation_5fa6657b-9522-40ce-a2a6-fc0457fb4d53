// 智能体数据模型
let agentData = [
  {
    id: 1,
    name: '客服助手',
    code: 'CSA001',
    type: '客服',
    description: '处理客户常见问题的智能体',
    enterpriseId: 1,
    enterpriseName: '阿依来科技有限公司',
    status: true,
    createTime: '2024-01-18 09:30:00'
  },
  {
    id: 2,
    name: '销售顾问',
    code: 'SLS002',
    type: '销售',
    description: '为潜在客户提供产品咨询和建议',
    enterpriseId: 1,
    enterpriseName: '阿依来科技有限公司',
    status: true,
    createTime: '2024-01-20 14:15:00'
  },
  {
    id: 3,
    name: '智能文档分析师',
    code: 'DOC003',
    type: '数据分析',
    description: '自动分析和提取文档中的关键信息',
    enterpriseId: 2,
    enterpriseName: '智慧未来科技有限公司',
    status: true,
    createTime: '2024-02-05 10:45:00'
  },
  {
    id: 4,
    name: '市场数据分析师',
    code: 'MKT004',
    type: '数据分析',
    description: '分析市场趋势和竞争对手数据',
    enterpriseId: 3,
    enterpriseName: '数智云科技有限公司',
    status: false,
    createTime: '2024-02-10 16:20:00'
  }
];

// 获取智能体列表
exports.getAgentList = (query = {}) => {
  let result = [...agentData];
  
  // 支持按名称过滤
  if (query.name) {
    result = result.filter(item => 
      item.name.toLowerCase().includes(query.name.toLowerCase())
    );
  }
  
  // 支持按编码过滤
  if (query.code) {
    result = result.filter(item => 
      item.code.toLowerCase().includes(query.code.toLowerCase())
    );
  }
  
  // 支持按类型过滤
  if (query.type) {
    result = result.filter(item => 
      item.type === query.type
    );
  }
  
  // 支持按企业ID过滤
  if (query.enterpriseId) {
    result = result.filter(item => 
      item.enterpriseId === parseInt(query.enterpriseId)
    );
  }
  
  // 支持按状态过滤
  if (query.status !== undefined) {
    const status = query.status === 'true' || query.status === true || query.status === 1;
    result = result.filter(item => item.status === status);
  }
  
  // 计算总数
  const total = result.length;
  
  // 分页处理
  const pageNum = parseInt(query.pageNum) || 1;
  const pageSize = parseInt(query.pageSize) || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = pageNum * pageSize;
  const list = result.slice(startIndex, endIndex);
  
  return {
    list,
    total
  };
};

// 获取智能体详情
exports.getAgentDetail = (id) => {
  const agent = agentData.find(item => item.id === id);
  return agent || null;
};

// 创建智能体
exports.createAgent = (data) => {
  const newAgent = {
    ...data,
    id: Date.now(),
    createTime: new Date().toLocaleString()
  };
  
  agentData.push(newAgent);
  return newAgent;
};

// 更新智能体
exports.updateAgent = (data) => {
  const index = agentData.findIndex(item => item.id === data.id);
  if (index > -1) {
    agentData[index] = { ...agentData[index], ...data };
    return agentData[index];
  }
  return null;
};

// 删除智能体
exports.deleteAgent = (id) => {
  const index = agentData.findIndex(item => item.id === id);
  if (index > -1) {
    const deleted = agentData.splice(index, 1)[0];
    return deleted;
  }
  return null;
}; 