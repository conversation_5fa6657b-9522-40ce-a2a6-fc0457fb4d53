const { PracticeRecord, PracticeRecordDetail, ExamRecord, CertificateRecord, Employee, ExamConfig } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const { addEnterpriseFilter } = require('../utils/enterpriseFilter');
const redisClient = require('../utils/redisClient');

/**
 * 统一错误处理
 * @param {Object} res - 响应对象
 * @param {Error} error - 错误对象
 */
const handleError = (res, error) => {
  console.error('操作失败:', error);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'production' ? undefined : error.message
  });
};

/**
 * 获取最高练习题数（带缓存）
 * @returns {Promise<number>} 最高练习题数
 */
const getMaxPracticeQuestions = async () => {
  const cacheKey = 'max_practice_questions';
  
  try {
    // 尝试从缓存获取
    const cachedValue = await redisClient.get(cacheKey, false);
    if (cachedValue) {
      console.log('从缓存获取最高练习题数:', cachedValue);
      return parseInt(cachedValue);
    }
    
    // 缓存不存在，查询数据库
    // 使用子查询来正确获取每个用户的总题数，然后取最大值
    const result = await sequelize.query(`
      SELECT MAX(user_total) as maxQuestions 
      FROM (
        SELECT open_id, SUM(question_num) as user_total 
        FROM practice_record 
        WHERE enterprise_id = :enterpriseId
        GROUP BY open_id
      ) as user_totals
    `, {
      replacements: { enterpriseId: process.env.DEFAULT_ENTERPRISE_ID || 1 },
      type: sequelize.QueryTypes.SELECT,
      raw: true
    });
    
    const maxQuestions = result && result[0] ? parseInt(result[0].maxQuestions) || 0 : 0;
    console.log('查询到的最高练习题数:', maxQuestions);
    
    // 如果查询结果为0，设置一个默认的最小值，避免除零错误
    const finalMaxQuestions = maxQuestions > 0 ? maxQuestions : 100;
    
    // 缓存10分钟
    await redisClient.set(cacheKey, finalMaxQuestions.toString(), 600);
    
    return finalMaxQuestions;
  } catch (error) {
    console.error('获取最高练习题数失败:', error);
    // 返回一个默认值，避免除零错误
    return 100;
  }
};

/**
 * 计算维度分数
 * @param {number} current - 当前值
 * @param {number} max - 最大值
 * @returns {number} 维度分数 (0-100)
 */
const calculateDimensionScore = (current, max) => {
  console.log(`计算维度分数: current=${current}, max=${max}`);
  if (max === 0 || current === 0) return 0;
  const ratio = current / max;
  const score = Math.min(Math.floor(ratio * 100), 100);
  console.log(`计算结果: ratio=${ratio}, score=${score}`);
  return score;
};

/**
 * 获取维度数据
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getDimensionData = async (req, res) => {
  try {
    const openId = req.headers.openid;
    
    if (!openId) {
      return res.status(400).json({
        code: 400,
        message: '缺少openId参数',
        data: null
      });
    }

    // 并行查询所有需要的数据
    const [
      practiceRecords,
      practiceDetails,
      examRecords,
      certificateRecords,
      employee,
      examConfigs
    ] = await Promise.all([
      // 1. 获取当前用户的练习记录
      PracticeRecord.findAll(
        addEnterpriseFilter({
          where: { openId },
          attributes: ['questionNum', 'createTime']
        })
      ),
      
      // 2. 获取当前用户的练习详情（用于计算正确率）
      PracticeRecordDetail.findAll({
        include: [{
          model: PracticeRecord,
          as: 'practiceRecord',
          where: {
            open_id: openId,
            enterpriseId: process.env.DEFAULT_ENTERPRISE_ID || 1
          },
          attributes: ['id', 'open_id'], // 添加这些字段用于调试
          required: true // 确保必须有关联的练习记录
        }],
        where: {
          type: 'analysis'
        },
        attributes: ['result', 'practiceRecordId'] // 添加practiceRecordId用于调试
      }),
      
      // 3. 获取当前用户的考试记录
      ExamRecord.findAll(
        addEnterpriseFilter({
          where: { openId },
          attributes: ['score', 'passScore']
        })
      ),
      
      // 4. 获取当前用户的证书记录
      CertificateRecord.findAll(
        addEnterpriseFilter({
          where: { openId },
          attributes: ['id']
        })
      ),
      
      // 5. 获取用户信息（创建时间）
      Employee.findOne({
        where: { openId },
        attributes: ['createTime']
      }),
      
      // 6. 获取所有必考和必练科目配置
      ExamConfig.findAll(
        addEnterpriseFilter({
          where: { status: '必考' },
          attributes: ['examSubject']
        })
      )
    ]);

    // 获取最高练习题数（带缓存）
    const maxPracticeQuestions = await getMaxPracticeQuestions();
    console.log('当前用户练习记录数量:', practiceRecords.length);
    console.log('当前用户openId:', openId);
    console.log('练习记录样例:', practiceRecords.slice(0, 2));

    // 1. 累计答题数量
    const totalQuestions = practiceRecords.reduce((sum, record) => sum + (record.questionNum || 0), 0);
    console.log('当前用户累计答题数量:', totalQuestions);
    console.log('系统最高练习题数:', maxPracticeQuestions);
    const questionCountScore = calculateDimensionScore(totalQuestions, maxPracticeQuestions);

    // 2. 累计答题数量（与第1个维度相同）
    const questionCountScore2 = questionCountScore;

    // 3. 答题平均正确率
    console.log('练习详情查询结果:', practiceDetails.length, '条记录');
    console.log('练习详情数据样例:', practiceDetails.slice(0, 3).map(detail => ({
      result: detail.result,
      practiceRecordId: detail.practiceRecordId,
      practiceRecordOpenId: detail.practiceRecord?.openId
    })));
    
    const totalAnswers = practiceDetails.length;
    const correctAnswers = practiceDetails.filter(detail => 
      detail.result === 'true' || detail.result === '1'
    ).length;
    
    console.log(`正确率计算: 总答题=${totalAnswers}, 正确答题=${correctAnswers}`);
    
    const correctRate = totalAnswers > 0 ? (correctAnswers / totalAnswers) * 100 : 0;
    const correctRateScore = Math.floor(correctRate);
    
    console.log(`正确率: ${correctRate}%, 得分: ${correctRateScore}`);

    // 4. 考试通过率
    const totalExams = examRecords.length;
    const passedExams = examRecords.filter(exam => 
      exam.score && exam.passScore && exam.score >= exam.passScore
    ).length;
    const examPassRate = totalExams > 0 ? (passedExams / totalExams) * 100 : 0;
    const examPassRateScore = Math.floor(examPassRate);

    // 5. 证书完成率
    const totalCertificates = certificateRecords.length;
    const totalRequiredSubjects = examConfigs.length;
    const certificateCompletionRate = totalRequiredSubjects > 0 ? 
      (totalCertificates / totalRequiredSubjects) * 100 : 0;
    const certificateCompletionScore = Math.floor(certificateCompletionRate);

    // 6. 连续学习天数
    let continuousLearningScore = 0;
    if (employee && employee.createTime) {
      // 获取用户练习的所有日期
      const practiceDates = practiceRecords.map(record => {
        const date = new Date(record.createTime);
        return date.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
      });
      
      // 去重并排序
      const uniqueDates = [...new Set(practiceDates)].sort();
      
      // 计算从用户创建到现在的总天数
      const createDate = new Date(employee.createTime);
      const currentDate = new Date();
      const totalDays = Math.ceil((currentDate - createDate) / (1000 * 60 * 60 * 24));
      
      // 计算连续学习天数比例
      const learningDays = uniqueDates.length;
      const continuousRate = totalDays > 0 ? (learningDays / totalDays) * 100 : 0;
      continuousLearningScore = Math.floor(continuousRate);
    }

    // 构建返回数据
    const dimensionData = {
      // 维度1: 累计答题数量
      questionCount: {
        score: questionCountScore,
        current: totalQuestions,
        max: maxPracticeQuestions,
        description: '努力程度'
      },
      // 维度2: 累计答题数量（相同）
      questionCount2: {
        score: questionCountScore2,
        current: totalQuestions,
        max: maxPracticeQuestions,
        description: '成长意愿'
      },
      // 维度3: 答题平均正确率
      correctRate: {
        score: correctRateScore,
        current: correctAnswers,
        total: totalAnswers,
        rate: correctRate,
        description: '掌握程度'
      },
      // 维度4: 考试通过率
      examPassRate: {
        score: examPassRateScore,
        passed: passedExams,
        total: totalExams,
        rate: examPassRate,
        description: '目标能力'
      },
      // 维度5: 证书完成率
      certificateCompletion: {
        score: certificateCompletionScore,
        obtained: totalCertificates,
        required: totalRequiredSubjects,
        rate: certificateCompletionRate,
        description: '晋升轨迹'
      },
      // 维度6: 连续学习天数
      continuousLearning: {
        score: continuousLearningScore,
        learningDays: practiceRecords.length > 0 ? [...new Set(practiceRecords.map(record => {
          const date = new Date(record.createTime);
          return date.toISOString().split('T')[0];
        }))].length : 0,
        totalDays: employee && employee.createTime ? 
          Math.ceil((new Date() - new Date(employee.createTime)) / (1000 * 60 * 60 * 24)) : 0,
        description: '学习稳定'
      }
    };

    res.json({
      code: 200,
      data: dimensionData,
      message: '获取维度数据成功'
    });

  } catch (error) {
    console.error('获取维度数据失败：', error);
    handleError(res, error);
  }
};

module.exports = {
  getDimensionData: exports.getDimensionData
};