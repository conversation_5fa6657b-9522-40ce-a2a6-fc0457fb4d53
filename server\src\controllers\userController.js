const { User, Role, UserRole, <PERSON><PERSON><PERSON>u, Menu } = require('../models');
const { Op } = require('sequelize');
const { generateToken } = require('../utils/jwt');
const bcrypt = require('bcryptjs');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

// 用户登录
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空'
      });
    }

    // 查询用户，添加企业ID过滤
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { username },
        include: [
          {
            model: Role,
            through: { attributes: [] } // 不包含中间表字段
          }
        ]
      })
    );

    if (!user) {
      return res.status(400).json({
        code: 400,
        message: '用户名或密码错误'
      });
    }

    // 使用bcrypt比较密码
    const passwordMatch = bcrypt.compareSync(password, user.password);

    if (!passwordMatch) {
      return res.status(400).json({
        code: 400,
        message: '用户名或密码错误'
      });
    }

    // 转换为普通对象
    const userObj = user.get({ plain: true });
    // 移除密码
    delete userObj.password;

    // 生成token，包含企业ID
    const token = generateToken({
      userId: user.id,
      username: user.username,
      enterpriseId: user.enterpriseId
    });

    res.json({
      code: 200,
      message: '登录成功',
      data: {
        token,
        user: userObj
      }
    });
  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      code: 500,
      message: '登录失败',
      error: error.message
    });
  }
};

// 获取用户信息
exports.getUserInfo = async (req, res) => {
  try {
    // 通常从token中获取用户ID，这里简化处理
    const userId = req.query.userId || 1;

    // 添加企业ID过滤
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { id: userId },
        include: [
          {
            model: Role,
            through: { attributes: [] } // 不包含中间表字段
          }
        ],
        attributes: { exclude: ['password'] }
      })
    );

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    res.json({
      code: 200,
      message: '获取用户信息成功',
      data: user
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户信息失败',
      error: error.message
    });
  }
};

// 获取用户列表
exports.getUserList = async (req, res) => {
  try {
    const { username, nickname, status, platform, pageNum = 1, pageSize = 10 } = req.query;

    // 构建查询条件
    const where = {};
    if (username) {
      where.username = { [Op.like]: `%${username}%` };
    }
    if (nickname) {
      where.nickname = { [Op.like]: `%${nickname}%` };
    }
    if (status !== undefined) {
      where.status = status === 'true' || status === '1' || status === 1;
    }
    if (platform !== undefined) {
      where.platform = platform;
    }

    // 分页查询，添加企业ID过滤
    const { count, rows } = await User.findAndCountAll(
      addEnterpriseFilter({
        where,
        include: [
          {
            model: Role,
            through: { attributes: [] } // 不包含中间表字段
          }
        ],
        order: [['id', 'ASC']],
        limit: parseInt(pageSize),
        offset: (parseInt(pageNum) - 1) * parseInt(pageSize),
        attributes: { exclude: ['password'] }
      })
    );

    res.json({
      code: 200,
      message: '获取用户列表成功',
      data: {
        list: rows,
        total: count
      }
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户列表失败',
      error: error.message
    });
  }
};

// 创建用户
exports.createUser = async (req, res) => {
  try {
    const { username, password, nickname, email, phone, roleIds, status, realName, realPhone, idNumber } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空'
      });
    }

    // 检查用户名是否已存在，添加企业ID过滤
    const existUser = await User.findOne(
      addEnterpriseFilter({
        where: { username }
      })
    );

    if (existUser) {
      return res.status(400).json({
        code: 400,
        message: '用户名已存在'
      });
    }

    // 使用bcrypt加密密码
    const hashedPassword = bcrypt.hashSync(password, 10);

    // 创建用户，添加企业ID
    const userData = addEnterpriseId({
      username,
      password: hashedPassword,
      nickname,
      email,
      phone,
      realName,
      realPhone,
      idNumber,
      status: status === undefined ? true : status
    });

    const user = await User.create(userData);

    // 如果有角色ID，创建用户角色关系
    if (roleIds && roleIds.length > 0) {
      const userRoles = roleIds.map(roleId => ({
        userId: user.id,
        roleId,
        enterpriseId: userData.enterpriseId
      }));
      await UserRole.bulkCreate(userRoles);
    }

    // 转换为普通对象
    const userObj = user.get({ plain: true });
    // 移除密码
    delete userObj.password;

    res.json({
      code: 200,
      message: '创建用户成功',
      data: userObj
    });
  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建用户失败',
      error: error.message
    });
  }
};

// 更新用户
exports.updateUser = async (req, res) => {
  try {
    const { id, username, nickname, email, phone, roleIds, status, password, realName, realPhone, idNumber } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '用户ID不能为空'
      });
    }

    // 查询用户，添加企业ID过滤
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 检查用户平台权限 - 只有admin平台的用户可以被编辑
    if (user.platform !== 'admin') {
      return res.status(403).json({
        code: 403,
        message: '小程序用户不允许编辑'
      });
    }

    // 如果要更新用户名，先检查用户名是否已存在
    if (username && username !== user.username) {
      const existingUser = await User.findOne(
        addEnterpriseFilter({
          where: { username }
        })
      );

      if (existingUser) {
        return res.status(400).json({
          code: 400,
          message: '用户名已存在'
        });
      }
    }

    // 准备更新数据
    const updateData = {};
    if (username !== undefined) updateData.username = username; // 添加对用户名的更新支持
    if (nickname !== undefined) updateData.nickname = nickname;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (status !== undefined) updateData.status = status;
    if (realName !== undefined) updateData.realName = realName;
    if (realPhone !== undefined) updateData.realPhone = realPhone;
    if (idNumber !== undefined) updateData.idNumber = idNumber;

    // 如果有新密码，使用bcrypt加密
    if (password) {
      updateData.password = bcrypt.hashSync(password, 10);
    }

    // 更新用户，添加企业ID
    await user.update(
      addEnterpriseId(updateData)
    );

    // 如果有角色ID，更新用户角色关系
    if (roleIds) {
      // 先删除旧的用户角色关系 - 使用原始SQL查询避免enterpriseId问题
      await UserRole.sequelize.query(
        'DELETE FROM `user_roles` WHERE `user_id` = ?',
        {
          replacements: [id]
        }
      );

      // 如果有新角色，创建用户角色关系
      if (roleIds.length > 0) {
        const userRoles = roleIds.map(roleId => ({
          userId: id,
          roleId,
          enterpriseId: user.enterpriseId || 1
        }));
        await UserRole.bulkCreate(userRoles);
      }
    }

    // 重新查询用户
    const updatedUser = await User.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [
          {
            model: Role,
            through: { attributes: [] } // 不包含中间表字段
          }
        ],
        attributes: { exclude: ['password'] }
      })
    );

    res.json({
      code: 200,
      message: '更新用户成功',
      data: updatedUser
    });
  } catch (error) {
    console.error('更新用户失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新用户失败',
      error: error.message
    });
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在，添加企业ID过滤
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 检查用户平台权限 - 只有admin平台的用户可以被删除
    if (user.platform !== 'admin') {
      return res.status(403).json({
        code: 403,
        message: '小程序用户不允许删除'
      });
    }

    // 删除用户角色关联，添加企业ID过滤
    await UserRole.destroy(
      addEnterpriseFilter({
        where: { userId: id }
      })
    );

    // 删除用户
    await user.destroy();

    res.json({
      code: 200,
      message: '删除用户成功'
    });
  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除用户失败',
      error: error.message
    });
  }
};

// 获取当前用户信息
exports.getCurrentUser = async (req, res) => {
  try {
    // 从Auth中间件获取用户信息
    const currentUser = req.user;

    if (!currentUser) {
      return res.status(401).json({
        code: 401,
        message: '用户未登录或登录已过期'
      });
    }

    // 查询用户详细信息
    const user = await User.findByPk(currentUser.id, {
      include: [
        {
          model: Role,
          through: { attributes: [] }
        }
      ],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    res.json({
      code: 200,
      message: '获取当前用户信息成功',
      data: user
    });
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取当前用户信息失败',
      error: error.message
    });
  }
};

// 获取当前用户的菜单权限
exports.getUserMenuPermissions = async (req, res) => {
  try {
    // 从请求中获取用户信息（通过auth中间件添加）
    const currentUser = req.user;


    if (!currentUser) {
      return res.status(401).json({
        code: 401,
        message: '用户未登录或登录已过期'
      });
    }

    // 在查询用户及其角色信息之前，先直接查询关联表

    const userRoles = await UserRole.findAll({
      where: { userId: currentUser.id }
    });


    // 手动查询该用户的所有角色
    const directRoles = await Role.findAll({
      include: [
        {
          model: User,
          where: { id: currentUser.id },
          through: { attributes: [] }
        }
      ]
    });


    // 查询用户及其角色信息
    const user = await User.findByPk(currentUser.id, {
      include: [
        {
          model: Role,
          through: { attributes: [] }
        }
      ],
      attributes: { exclude: ['password'] }
    });



    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 获取用户的所有角色ID - 修改这里，使用前面成功查询到的直接角色
    // const roleIds = user.Roles && Array.isArray(user.Roles) ? user.Roles.map(role => role && role.id ? role.id : null).filter(id => id !== null) : [];

    // 从directRoles中获取角色ID，因为前面的查询显示它是正确的
    const roleIds = directRoles && Array.isArray(directRoles) ?
      directRoles.map(role => role && role.id ? role.id : null).filter(id => id !== null) :
      [];



    // 如果用户没有角色，返回空菜单
    if (roleIds.length === 0) {
      console.log('用户没有任何角色，返回空菜单');
      return res.json({
        code: 200,
        message: '获取用户菜单权限成功',
        data: {
          menus: [],
          permissions: []
        }
      });
    }

    // 查询角色拥有的菜单权限
    const roleMenus = await RoleMenu.findAll({
      where: {
        roleId: {
          [Op.in]: roleIds
        }
      }
    });



    // 获取菜单ID列表
    const menuIds = roleMenus && Array.isArray(roleMenus) ?
      [...new Set(roleMenus.map(rm => rm && rm.menuId ? rm.menuId : null).filter(id => id !== null))] :
      [];



    // 如果没有菜单权限，返回空菜单
    if (menuIds.length === 0) {
      console.log('没有菜单权限，返回空菜单');
      return res.json({
        code: 200,
        message: '获取用户菜单权限成功',
        data: {
          menus: [],
          permissions: []
        }
      });
    }

    // 查询菜单信息（包含所有类型菜单，用于提取权限）
    const allMenus = await Menu.findAll({
      where: {
        id: {
          [Op.in]: menuIds
        },
        status: true, // 只获取状态为启用的菜单
        hidden: false // 只获取非隐藏的菜单
      },
      order: [
        ['parentId', 'ASC'],
        ['sort', 'ASC']
      ]
    });

    // 查询菜单信息（排除type为2的菜单，用于构建菜单树）
    const displayMenus = await Menu.findAll({
      where: {
        id: {
          [Op.in]: menuIds
        },
        status: true, // 只获取状态为启用的菜单
        hidden: false, // 只获取非隐藏的菜单
        type: {
          [Op.ne]: 2 // 排除type为2的按钮菜单
        }
      },
      order: [
        ['parentId', 'ASC'],
        ['sort', 'ASC']
      ]
    });



    // 构建菜单树（使用过滤后的菜单）
    const menuList = displayMenus && Array.isArray(displayMenus) ?
      displayMenus.map(menu => menu ? menu.get({ plain: true }) : null).filter(m => m !== null) :
      [];
    const menuTree = buildMenuTree(menuList);



    // 提取权限标识（使用所有菜单，包括按钮权限）
    const permissions = [];
    if (allMenus && Array.isArray(allMenus)) {
      for (const menu of allMenus) {
        if (menu && menu.perms) {
          permissions.push(menu.perms);
        }
      }
    }



    res.json({
      code: 200,
      message: '获取用户菜单权限成功',
      data: {
        menus: menuTree,
        permissions
      }
    });
  } catch (error) {
    console.error('获取用户菜单权限失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户菜单权限失败',
      error: error.message
    });
  }
};

// 构建菜单树 - 辅助函数
function buildMenuTree(menus, parentId = 0) {
  const result = [];

  menus.forEach(menu => {
    if (menu.parentId === parentId) {
      const children = buildMenuTree(menus, menu.id);
      if (children.length > 0) {
        menu.children = children;
      }
      result.push(menu);
    }
  });

  return result;
}

// 更新用户个人资料
exports.updateUserProfile = async (req, res) => {
  try {
    const { id, nickname, email, phone, realName } = req.body;
    const currentUser = req.user;

    // 验证是否为当前用户修改个人资料
    if (id !== currentUser.id) {
      return res.status(403).json({
        code: 403,
        message: '只能修改自己的个人资料'
      });
    }

    // 查询用户，添加企业ID过滤
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 准备更新数据
    const updateData = {};
    if (nickname !== undefined) updateData.nickname = nickname;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (realName !== undefined) updateData.realName = realName;

    // 更新用户信息
    await user.update(
      addEnterpriseId(updateData)
    );

    // 查询更新后的用户信息
    const updatedUser = await User.findOne(
      addEnterpriseFilter({
        where: { id },
        attributes: { exclude: ['password'] }
      })
    );

    res.json({
      code: 200,
      message: '个人资料更新成功',
      data: updatedUser
    });
  } catch (error) {
    console.error('更新个人资料失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新个人资料失败',
      error: error.message
    });
  }
};

// 修改用户密码
exports.updateUserPassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const currentUser = req.user;

    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        code: 400,
        message: '旧密码和新密码不能为空'
      });
    }

    // 查询用户完整信息（包含密码）
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { id: currentUser.id }
      })
    );

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 验证旧密码
    const passwordMatch = bcrypt.compareSync(oldPassword, user.password);

    if (!passwordMatch) {
      return res.status(400).json({
        code: 400,
        message: '旧密码不正确'
      });
    }

    // 更新密码
    const hashedPassword = bcrypt.hashSync(newPassword, 10);
    await user.update({ password: hashedPassword });

    console.log(`用户 ${user.username}(ID:${user.id}) 密码已更新`);

    res.json({
      code: 200,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({
      code: 500,
      message: '修改密码失败',
      error: error.message
    });
  }
};


