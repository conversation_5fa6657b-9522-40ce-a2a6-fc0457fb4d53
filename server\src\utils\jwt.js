const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');

// 确保环境变量已加载
dotenv.config();

// 从环境变量中获取JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'ayilai-jwt-secret';
// token有效期（单位：秒）
const EXPIRES_IN = process.env.JWT_EXPIRES_IN ? parseInt(process.env.JWT_EXPIRES_IN.replace('h', '') * 3600) : 60 * 60 * 24; // 默认1天

/**
 * 生成token
 * @param {Object} payload 载荷数据
 * @param {Number} expiresIn 过期时间（单位：秒）
 * @returns {String} token
 */
const generateToken = (payload, expiresIn = EXPIRES_IN) => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn });
};

/**
 * 验证token
 * @param {String} token 
 * @returns {Object} 解析后的数据
 */
const verifyToken = (token) => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return {
      valid: true,
      data: decoded
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message
    };
  }
};

module.exports = {
  generateToken,
  verifyToken
}; 