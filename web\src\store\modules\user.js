import { defineStore } from 'pinia'
import { 
  getUserList, 
  createUser, 
  updateUser, 
  deleteUser, 
  getRoleList,
  login,
  getCurrentUser,
  getUserMenuPermissions
} from '@/api/system/user'
import { setToken, setUserInfo, clearAuth, getUserInfo } from '@/utils/auth'
import { useMenuStore } from './menu'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: getUserInfo(), // 从localStorage初始化
    permissions: [],
    userMenus: [] // 保存用户可访问的菜单
  }),

  actions: {
    // 登录
    async login(loginData) {
      try {
        const response = await login(loginData)
        if (!response) {
          console.error('登录响应为空')
          return { success: false, error: '登录失败，响应为空' }
        }
        
        const { token, user } = response
        
        if (!token || !user) {
          console.error('登录响应数据不完整:', response)
          return { success: false, error: '登录失败，响应数据不完整' }
        }
        
        // 保存token和用户信息
        setToken(token)
        setUserInfo(user)
        
        // 更新状态
        this.userInfo = user
        
        // 从用户角色中提取权限
        this.permissions = []
        if (user.Roles && Array.isArray(user.Roles) && user.Roles.length > 0) {
          const allPermissions = []
          for (const role of user.Roles) {
            if (role.permissions && Array.isArray(role.permissions)) {
              allPermissions.push(...role.permissions)
            }
          }
          this.permissions = allPermissions
        }
        
        // 获取用户菜单
        try {
          await this.getUserMenus()
        } catch (menuError) {
          console.error('获取用户菜单失败:', menuError)
          // 不中断登录流程
        }
        
        return { success: true }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, error: error.message || '登录失败' }
      }
    },
    
    // 退出登录
    logout() {
      this.userInfo = null
      this.permissions = []
      this.userMenus = []
      clearAuth()
    },
    
    // 获取当前用户信息
    async getCurrentUserInfo() {
      try {
        const user = await getCurrentUser()
        if (!user) {
          throw new Error('用户信息为空')
        }
        
        setUserInfo(user)
        this.userInfo = user
        
        // 从用户角色中提取权限
        this.permissions = []
        if (user.Roles && Array.isArray(user.Roles) && user.Roles.length > 0) {
          const allPermissions = []
          for (const role of user.Roles) {
            if (role.permissions && Array.isArray(role.permissions)) {
              allPermissions.push(...role.permissions)
            }
          }
          this.permissions = allPermissions
        }
        
        // 获取用户菜单
        try {
          await this.getUserMenus()
        } catch (menuError) {
          console.error('获取用户菜单失败:', menuError)
          // 不中断获取用户信息流程
        }
        
        return { success: true }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return { success: false, error: error.message || '获取用户信息失败' }
      }
    },

    // 获取用户列表
    async getUserList(params) {
      try {
        const response = await getUserList(params)
        return response.data || { list: [], total: 0 }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        return { list: [], total: 0 }
      }
    },

    // 创建用户
    async createUser(data) {
      try {
        const response = await createUser(data)
        return response.data
      } catch (error) {
        console.error('创建用户失败:', error)
        throw error
      }
    },

    // 更新用户
    async updateUser(data) {
      try {
        const response = await updateUser(data)
        return response.data
      } catch (error) {
        console.error('更新用户失败:', error)
        throw error
      }
    },

    // 删除用户
    async deleteUser(id) {
      try {
        const response = await deleteUser(id)
        return response.data
      } catch (error) {
        console.error('删除用户失败:', error)
        throw error
      }
    },

    // 获取角色选项列表
    async getRoleOptions() {
      try {
        const response = await getRoleList();
        
        if (!response || !response.data) {
          console.error('获取角色列表返回数据为空');
          return [];
        }
        
        const roles = Array.isArray(response.data) ? response.data : [];
        
        return roles;
      } catch (error) {
        console.error('获取角色列表失败:', error);
        return [];
      }
    },

    // 获取用户可访问的菜单
    async getUserMenus() {
      try {
        console.log('[UserStore] 开始获取用户菜单');
        
        // 使用新的后端接口获取用户菜单和权限
        const response = await getUserMenuPermissions();
        console.log('[UserStore] 菜单接口响应:', response);
        
        // 检查响应是否有效
        if (!response) {
          console.error('[UserStore] 获取用户菜单返回为空');
          this.userMenus = [];
          return [];
        }
        
        // 后端可能直接返回data，或者把data包在response里
        const responseData = response.data || response;
        console.log('[UserStore] 解析的响应数据:', responseData);
        
        if (!responseData) {
          console.error('[UserStore] 无法解析菜单数据');
          this.userMenus = [];
          return [];
        }
        
        // 获取菜单数据，兼容多种数据结构
        const menus = responseData.menus || responseData.data?.menus || responseData || [];
        const permissions = responseData.permissions || responseData.data?.permissions || [];
        
        console.log('[UserStore] 提取的菜单数据:', menus);
        console.log('[UserStore] 提取的权限数据:', permissions);
        
        // 设置用户菜单
        if (Array.isArray(menus) && menus.length > 0) {
          this.userMenus = menus;
          console.log(`[UserStore] 成功设置${menus.length}个菜单项`);
        } else {
          console.warn('[UserStore] 提取的菜单数据为空或无效:', menus);
          this.userMenus = [];
        }
        
        // 设置用户权限
        if (Array.isArray(permissions) && permissions.length > 0) {
          this.permissions = permissions;
          console.log(`[UserStore] 成功设置${permissions.length}个权限`);
        } else {
          console.warn('[UserStore] 提取的权限数据为空或无效:', permissions);
          // 不清空现有权限，可能是从其他地方获取的
        }
        
        return this.userMenus;
      } catch (error) {
        console.error('[UserStore] 获取用户菜单失败:', error);
        
        // 根据错误类型提供更具体的信息
        if (error.response?.status === 401) {
          console.error('[UserStore] 认证失败，token可能已过期');
        } else if (error.response?.status === 403) {
          console.error('[UserStore] 权限不足，无法获取菜单');
        } else if (error.response?.status === 404) {
          console.error('[UserStore] 菜单接口不存在');
        } else if (error.code === 'ERR_NETWORK') {
          console.error('[UserStore] 网络错误，无法连接到服务器');
        } else {
          console.error('[UserStore] 未知错误:', error.message);
        }
        
        this.userMenus = [];
        throw error; // 重新抛出错误，让调用者处理
      }
    }
  }
}) 