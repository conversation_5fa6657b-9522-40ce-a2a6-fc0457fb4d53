const { setupAchievementEventListeners } = require('./server/src/utils/achievementEventListener');
const { processFirstPerfectScoreAchievement } = require('./server/src/utils/achievementUtils');

/**
 * 测试旗开得胜成就
 */
async function testFirstPerfectScoreAchievement() {
  try {
    console.log('🏆 开始测试旗开得胜成就...\n');

    // 1. 初始化成就事件监听器
    console.log('1️⃣ 初始化成就事件监听器...');
    setupAchievementEventListeners();

    // 2. 设置测试数据
    const testData = {
      userId: 1,
      openId: 'test_openid_perfect_score',
      enterpriseId: '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32',
      examRecordId: 999,
      score: 10,  // 满分
      questionCount: 10  // 总题数
    };

    console.log('2️⃣ 测试数据准备完成:');
    console.log(`   - 用户ID: ${testData.userId}`);
    console.log(`   - OpenID: ${testData.openId}`);
    console.log(`   - 企业ID: ${testData.enterpriseId}`);
    console.log(`   - 考试记录ID: ${testData.examRecordId}`);
    console.log(`   - 考试得分: ${testData.score}`);
    console.log(`   - 题目总数: ${testData.questionCount}`);
    console.log('');

    // 3. 直接调用旗开得胜成就检测函数
    console.log('3️⃣ 直接调用旗开得胜成就检测函数...');
    await processFirstPerfectScoreAchievement(
      testData.userId,
      testData.openId,
      testData.enterpriseId,
      testData.examRecordId,
      testData.score,
      testData.questionCount
    );

    console.log('\n✅ 旗开得胜成就测试完成！');
    
    // 4. 测试非满分情况
    console.log('\n4️⃣ 测试非满分情况...');
    await processFirstPerfectScoreAchievement(
      testData.userId,
      testData.openId,
      testData.enterpriseId,
      testData.examRecordId + 1,
      8,  // 非满分
      10  // 总题数
    );

    console.log('\n🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testFirstPerfectScoreAchievement().catch(console.error);
}

module.exports = { testFirstPerfectScoreAchievement }; 