import request from '@/utils/request'

// 获取成就模板列表
export function getAchievementTemplates(params) {
  return request({
    url: '/achievement/templates',
    method: 'get',
    params
  })
}

// 创建成就模板
export function createAchievementTemplate(data) {
  return request({
    url: '/achievement/templates',
    method: 'post',
    data
  })
}

// 更新成就模板
export function updateAchievementTemplate(id, data) {
  return request({
    url: `/achievement/templates/${id}`,
    method: 'put',
    data
  })
}

// 删除成就模板
export function deleteAchievementTemplate(id) {
  return request({
    url: `/achievement/templates/${id}`,
    method: 'delete'
  })
}

// 获取成就模板详情
export function getAchievementTemplateDetail(id) {
  return request({
    url: `/achievement/templates/${id}`,
    method: 'get'
  })
}

// 初始化默认成就模板
export function initDefaultTemplates() {
  return request({
    url: '/achievement/init-default',
    method: 'post'
  })
}

// 上传成就模板图标
export function uploadAchievementIcon(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/achievement/upload-icon',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}