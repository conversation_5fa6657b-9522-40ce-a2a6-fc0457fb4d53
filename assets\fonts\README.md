# 中文字体配置指南

## 问题说明
在 Linux 系统中生成证书时，可能会出现中文字符乱码的问题。这是因为 Linux 系统缺少中文字体导致的。

## 快速解决方案

### 方案一：使用自动安装脚本（推荐）

运行项目提供的字体安装脚本：
```bash
# 进入项目根目录
cd /path/to/your/project

# 运行字体安装脚本
bash scripts/install-fonts.sh
```

该脚本会自动检测你的 Linux 发行版并安装相应的中文字体包。

### 方案二：手动安装系统字体包

根据你的 Linux 发行版选择对应的命令：

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install fonts-noto-cjk fonts-wqy-microhei

# CentOS/RHEL/Fedora
sudo yum install google-noto-sans-cjk-fonts wqy-microhei-fonts
# 或者使用 dnf (较新版本)
sudo dnf install google-noto-sans-cjk-fonts wqy-microhei-fonts

# Arch Linux
sudo pacman -S noto-fonts-cjk wqy-microhei

# Alpine Linux
sudo apk add font-noto-cjk font-wqy-microhei
```

### 方案三：下载字体文件（备用方案）

如果无法通过包管理器安装，可以手动下载字体文件：

1. 下载文泉驿微米黑字体（较小，约2MB）：
   ```bash
   wget https://github.com/anthonyfok/fonts-wqy-microhei/raw/master/wqy-microhei.ttc
   mkdir -p assets/fonts
   mv wqy-microhei.ttc assets/fonts/
   ```

2. 或下载 Noto Sans CJK 字体：
   - [GitHub 下载地址](https://github.com/googlefonts/noto-cjk/releases)
   - 选择 `Sans` 版本下载

### 方案二：使用系统已有字体

如果系统已安装中文字体，可以修改代码中的字体路径：

```javascript
// 查看系统中可用的中文字体
fc-list :lang=zh

// 常见的 Linux 中文字体路径：
// Ubuntu/Debian: /usr/share/fonts/truetype/
// CentOS/RHEL: /usr/share/fonts/
// 文泉驿字体: /usr/share/fonts/wenquanyi/
```

### 方案三：安装系统字体包

在 Linux 系统中安装中文字体包：

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install fonts-noto-cjk

# CentOS/RHEL
sudo yum install google-noto-sans-cjk-fonts

# 或者安装文泉驿字体
sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei
```

## 字体文件要求

- 文件名：`NotoSansCJK-Regular.ttc`
- 路径：`assets/fonts/NotoSansCJK-Regular.ttc`
- 格式：TTC 或 TTF 格式
- 大小：约 110MB

## 测试验证

部署后可以通过以下方式验证字体是否正常工作：

1. 查看服务器日志，确认字体注册成功
2. 生成一张测试证书，检查中文显示是否正常
3. 如果仍有问题，检查字体文件路径和权限

## 备用字体

代码中已配置多个备用字体，优先级如下：
1. NotoSansCJK (自定义注册)
2. Microsoft YaHei (Windows)
3. SimHei (Windows)
4. WenQuanYi Micro Hei (Linux)
5. sans-serif (系统默认)

## 注意事项

- 字体文件较大（~110MB），请确保服务器有足够存储空间
- 首次加载字体可能需要额外时间
- 建议在生产环境部署前测试字体效果 