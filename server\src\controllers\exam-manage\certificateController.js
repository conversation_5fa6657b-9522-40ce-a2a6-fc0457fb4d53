const { CertificateR<PERSON><PERSON>, <PERSON>ployee, ExamRecord, PositionName, Level, PositionType } = require('../../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, DEFAULT_ENTERPRISE_ID } = require('../../utils/enterpriseFilter');
const logger = require('../../utils/logger');
const path = require('path');
const fs = require('fs');
const { generateCertificatePDF } = require('../../utils/pdfGenerator');
const moment = require('moment');

/**
 * 获取证书记录列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCertificateList = async (req, res) => {
  try {
    const { 
      searchText, startDate, endDate, positionFilter, positionId,
      pageNum = 1, pageSize = 10 
    } = req.query;
    
    // 构建查询条件
    let where = {
      delFlag: 0
    };
    
    
    // 根据参数构建过滤条件
    if (searchText) {
      where[Op.or] = [
        { certificateName: { [Op.like]: `%${searchText}%` } },
        { employeeName: { [Op.like]: `%${searchText}%` } }
      ];
    }
    
    if (startDate && endDate) {
      where.obtainTime = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }
    
    if (positionFilter) {
      where.positionBelong = positionFilter;
    }
    if (positionId) {
      where.positionName = positionId;
    }
    
    // 使用addEnterpriseFilter添加企业ID过滤
    const options = addEnterpriseFilter({ where });
    
    // 查询总数
    const total = await CertificateRecord.count(options);
    
    // 分页查询数据
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);
    
    const certificates = await CertificateRecord.findAll({
      ...options,
      attributes: { exclude: ['delFlag'] },
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'name', 'phone'],
          required: false
        },
        {
          model: ExamRecord,
          as: 'examRecord',
          attributes: ['id', 'examSubject', 'score'],
          required: false
        },
        {
          model: PositionName,
          foreignKey: 'positionName', // 使用 positionName 字段作为外键
          as: 'positionNameInfo',
          attributes: ['id', 'name', 'code'],
          required: false,
          where: {
            status: true
          }
        },
        {
          model: Level,
          foreignKey: 'positionLevel', // 使用 positionLevel 字段作为外键
          as: 'positionLevelInfo',
          attributes: ['id', 'name', 'code'],
          required: false
        },
        {
          model: PositionType,
          foreignKey: 'positionBelong', // 使用 positionBelong 字段作为外键
          as: 'positionBelongInfo',
          attributes: ['id', 'name', 'code'],
          required: false,
          where: {
            status: true
          }
        }
      ],
      order: [['obtainTime', 'DESC']],
      offset,
      limit
    });
    
    // 返回结果
    return res.json({
      code: 200,
      message: '获取证书记录列表成功',
      data: {
        total,
        records: certificates,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      }
    });
    
  } catch (error) {
    logger.error('获取证书记录列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取证书记录列表失败',
      error: error.message
    });
  }
};

/**
 * 获取证书详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCertificateDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 构建查询条件并添加企业ID过滤
    let where = {
      id,
      delFlag: 0
    };
    const options = addEnterpriseFilter({ where });
    
    // 查询数据
    const certificate = await CertificateRecord.findOne({
      ...options,
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'name', 'phone'], // 移除了 email 字段
          required: false
        },
        {
          model: ExamRecord,
          as: 'examRecord',
          attributes: ['id', 'examSubject', 'score', 'examTime'],
          required: false
        }
      ]
    });
    
    if (!certificate) {
      return res.status(404).json({
        code: 404,
        message: '证书记录不存在'
      });
    }
    
    // 返回结果
    return res.json({
      code: 200,
      message: '获取证书详情成功',
      data: certificate
    });
    
  } catch (error) {
    logger.error('获取证书详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取证书详情失败',
      error: error.message
    });
  }
};

/**
 * 下载证书
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.downloadCertificate = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 构建查询条件并添加企业ID过滤
    let where = {
      id,
      delFlag: 0
    };
    const options = addEnterpriseFilter({ where });
    
    // 查询数据
    const certificate = await CertificateRecord.findOne({
      ...options,
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'name', 'phone', 'email', 'avatar'],
          required: false
        }
      ]
    });
    
    if (!certificate) {
      return res.status(404).json({
        code: 404,
        message: '证书记录不存在'
      });
    }
    
    // 检查是否已有证书文件
    if (certificate.certificateFileUrl) {
      const filePath = path.join(__dirname, '../../../', certificate.certificateFileUrl);
      
      // 检查文件是否存在
      if (fs.existsSync(filePath)) {
        // 设置响应头
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(certificate.certificateName)}_${encodeURIComponent(certificate.employeeName)}.pdf`);
        
        // 发送文件
        return res.download(filePath);
      }
    }
    
    // 如果没有文件或文件不存在，则生成新的证书PDF
    const pdfBuffer = await generateCertificatePDF(certificate);
    
    // 确保uploads目录存在
    const uploadsDir = path.join(__dirname, '../../../uploads/certificates');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // 保存文件
    const fileName = `certificate_${certificate.id}_${Date.now()}.pdf`;
    const filePath = path.join(uploadsDir, fileName);
    fs.writeFileSync(filePath, pdfBuffer);
    
    // 更新证书文件URL
    const fileUrl = `uploads/certificates/${fileName}`;
    await certificate.update({ certificateFileUrl: fileUrl });
    
    // 设置响应头
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(certificate.certificateName)}_${encodeURIComponent(certificate.employeeName)}.pdf`);
    
    // 发送文件
    return res.download(filePath);
    
  } catch (error) {
    logger.error('下载证书失败:', error);
    return res.status(500).json({
      code: 500,
      message: '下载证书失败',
      error: error.message
    });
  }
};

/**
 * 创建证书记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createCertificate = async (req, res) => {
  try {
    const { 
      certificateName, obtainTime, positionName, positionBelong,
      positionLevel, employeeName, employeeId, examRecordId
    } = req.body;
    
    // 验证参数
    if (!certificateName || !obtainTime || !positionName || !positionBelong || !positionLevel || !employeeName || !employeeId) {
      return res.status(400).json({
        code: 400,
        message: '参数不完整，请检查必填字段'
      });
    }
    
    // 计算有效期（默认为获取日期后一年）
    const validUntil = moment(obtainTime).add(1, 'year').toDate();
    
    // 创建数据
    const certificate = await CertificateRecord.create({
      certificateName,
      obtainTime,
      positionName,
      positionBelong,
      positionLevel,
      employeeName,
      employeeId,
      examRecordId,
      validUntil,
      enterpriseId: DEFAULT_ENTERPRISE_ID
    });
    
    // 返回结果
    return res.status(201).json({
      code: 201,
      message: '创建证书记录成功',
      data: certificate
    });
    
  } catch (error) {
    logger.error('创建证书记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建证书记录失败',
      error: error.message
    });
  }
};

/**
 * 更新证书记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateCertificate = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      certificateName, obtainTime, positionName, positionBelong,
      positionLevel, employeeName, employeeId, validUntil
    } = req.body;
    
    // 构建查询条件并添加企业ID过滤
    let where = {
      id,
      delFlag: 0
    };
    const options = addEnterpriseFilter({ where });
    
    // 查询数据是否存在
    const certificate = await CertificateRecord.findOne(options);
    
    if (!certificate) {
      return res.status(404).json({
        code: 404,
        message: '证书记录不存在'
      });
    }
    
    // 更新数据
    const updateData = {};
    if (certificateName) updateData.certificateName = certificateName;
    if (obtainTime) {
      updateData.obtainTime = obtainTime;
      // 如果修改了获取时间，同时更新有效期（一年后）
      updateData.validUntil = moment(obtainTime).add(1, 'year').toDate();
    }
    if (positionName) updateData.positionName = positionName;
    if (positionBelong) updateData.positionBelong = positionBelong;
    if (positionLevel) updateData.positionLevel = positionLevel;
    if (employeeName) updateData.employeeName = employeeName;
    if (employeeId) updateData.employeeId = employeeId;
    if (validUntil) updateData.validUntil = validUntil;
    
    await certificate.update(updateData);
    
    // 返回结果
    return res.json({
      code: 200,
      message: '更新证书记录成功',
      data: certificate
    });
    
  } catch (error) {
    logger.error('更新证书记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新证书记录失败',
      error: error.message
    });
  }
};

/**
 * 删除证书记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteCertificate = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 构建查询条件并添加企业ID过滤
    let where = {
      id,
      delFlag: 0
    };
    const options = addEnterpriseFilter({ where });
    
    // 查询数据是否存在
    const certificate = await CertificateRecord.findOne(options);
    
    if (!certificate) {
      return res.status(404).json({
        code: 404,
        message: '证书记录不存在'
      });
    }
    
    // 软删除数据
    await certificate.update({ delFlag: 1 });
    
    // 返回结果
    return res.json({
      code: 200,
      message: '删除证书记录成功'
    });
    
  } catch (error) {
    logger.error('删除证书记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除证书记录失败',
      error: error.message
    });
  }
}; 