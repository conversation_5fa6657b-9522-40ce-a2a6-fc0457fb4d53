const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Role = sequelize.define('roles', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  roleName: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'role_name',
    comment: '角色名称'
  },
  roleCode: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    field: 'role_code',
    comment: '角色编码'
  },
  sort: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '排序'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态：0禁用，1正常'
  },
  remark: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '备注'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'roles'
});

module.exports = Role; 