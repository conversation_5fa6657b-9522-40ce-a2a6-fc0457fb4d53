/**
 * 测试员工离职API的脚本
 * 用于验证任务2的实现
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000'; // 根据实际情况修改
const API_ENDPOINT = '/api/organization/employee';

/**
 * 测试员工离职功能
 */
async function testEmployeeResignation() {
  console.log('🧪 开始测试员工离职功能...\n');

  try {
    // 测试数据
    const testEmployeeId = 69; // 根据实际情况修改员工ID
    
    console.log(`📋 测试参数:`);
    console.log(`- 员工ID: ${testEmployeeId}`);
    console.log(`- 操作: 离职 (status: "0")`);
    console.log(`- 接口: PUT ${API_ENDPOINT}\n`);

    // 发送离职请求
    console.log('🚀 发送离职请求...');
    const response = await axios.put(`${BASE_URL}${API_ENDPOINT}`, {
      id: testEmployeeId,
      status: '0'
    }, {
      headers: {
        'Content-Type': 'application/json',
        // 如果需要认证，添加认证头
        // 'Authorization': 'Bearer your-token-here'
      }
    });

    console.log('✅ 请求成功!');
    console.log('📊 响应数据:');
    console.log(JSON.stringify(response.data, null, 2));

    // 验证响应
    if (response.data.code === 200) {
      console.log('\n🎉 员工离职处理成功!');
      
      console.log('\n📝 预期的数据变更:');
      console.log('1. ✅ employee_career_record 表:');
      console.log('   - 创建或更新离职记录');
      console.log('   - status = 0 (离职)');
      console.log('   - departure_time = 当前时间');
      console.log('   - entry_time = 从 org_employee 获取');

      console.log('\n2. ✅ 相关表 is_active 字段更新为 0 并设置 career_record_id:');
      console.log('   - practice_record_detail');
      console.log('   - practice_record');
      console.log('   - exam_records');
      console.log('   - exam_review_applications');
      console.log('   - certificate_records');
      console.log('   - user_achievements');
      console.log('   - org_employee_position');
      console.log('   - org_employee_promotion');

      console.log('\n3. ✅ 名称字段填充:');
      console.log('   - position_belong_name (从 org_position_type)');
      console.log('   - position_name_cn (从 org_position_name)');
      console.log('   - position_level_name (从 org_level)');
      console.log('   - exam_subject_name (从 kb_knowledge_base)');

      console.log('\n🔍 建议验证步骤:');
      console.log('1. 检查数据库中 employee_career_record 表的记录');
      console.log('2. 验证相关表的 is_active 字段是否为 0');
      console.log('3. 验证相关表的 career_record_id 字段是否正确设置');
      console.log('4. 确认名称字段是否正确填充');
      console.log('5. 检查事务是否正确提交');

    } else {
      console.log('\n❌ 员工离职处理失败');
      console.log('错误信息:', response.data.message);
    }

  } catch (error) {
    console.log('\n❌ 请求失败');
    
    if (error.response) {
      console.log('📊 错误响应:');
      console.log('状态码:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('📡 网络错误: 无法连接到服务器');
      console.log('请确保服务器正在运行在', BASE_URL);
    } else {
      console.log('⚠️ 请求配置错误:', error.message);
    }
  }
}

/**
 * 测试数据准备建议
 */
function printTestDataPreparation() {
  console.log('\n📋 测试数据准备建议:');
  console.log('='.repeat(50));
  
  console.log('\n1. 创建测试员工:');
  console.log(`
INSERT INTO org_employee (
  name, department_id, position_id, level_id, position_type_id,
  phone, status, entry_time, enterprise_id, create_by
) VALUES (
  '测试员工', 1, 1, 1, 1,
  '13800138000', '1', NOW(), 1, 'admin'
);
  `);

  console.log('\n2. 为测试员工创建相关记录:');
  console.log(`
-- 练习记录
INSERT INTO practice_record (employee_id, open_id, exam_subject, enterprise_id, is_active) 
VALUES (69, 'test_open_id', 1, 1, 1);

-- 考试记录
INSERT INTO exam_records (examinee_id, open_id, enterprise_id, is_active) 
VALUES (69, 'test_open_id', 1, 1);

-- 员工岗位关联
INSERT INTO org_employee_position (employee_id, position_id, position_type_id, level_id, enterprise_id, is_active) 
VALUES (69, 1, 1, 1, 1, 1);
  `);

  console.log('\n3. 验证查询:');
  console.log(`
-- 检查离职后的履历记录
SELECT * FROM employee_career_record WHERE employee_id = 69;

-- 检查相关表的 is_active 状态
SELECT COUNT(*) as active_records FROM practice_record WHERE employee_id = 69 AND is_active = 1;
SELECT COUNT(*) as active_records FROM exam_records WHERE examinee_id = 69 AND is_active = 1;
SELECT COUNT(*) as active_records FROM org_employee_position WHERE employee_id = 69 AND is_active = 1;

-- 检查 career_record_id 字段设置
SELECT career_record_id FROM practice_record WHERE employee_id = 69;
SELECT career_record_id FROM exam_records WHERE examinee_id = 69;
SELECT career_record_id FROM org_employee_position WHERE employee_id = 69;

-- 检查名称字段填充
SELECT position_belong_name, position_name_cn, position_level_name, exam_subject_name
FROM practice_record WHERE employee_id = 69;
  `);
}

/**
 * 主函数
 */
async function main() {
  console.log('🎯 员工离职功能测试工具');
  console.log('='.repeat(50));
  
  // 打印测试准备建议
  printTestDataPreparation();
  
  // 询问是否继续测试
  console.log('\n⚠️  请确保:');
  console.log('1. 服务器正在运行');
  console.log('2. 数据库连接正常');
  console.log('3. 测试员工数据已准备');
  console.log('4. 已备份重要数据');
  
  // 等待用户确认（在实际使用时可以添加交互式确认）
  console.log('\n🚀 开始执行测试...');
  
  await testEmployeeResignation();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testEmployeeResignation,
  printTestDataPreparation
};
