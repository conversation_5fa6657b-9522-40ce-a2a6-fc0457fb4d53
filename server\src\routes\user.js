const express = require('express');
const router = express.Router();

// 这里是临时的路由处理函数，实际项目中应该引用用户控制器
// 用户登录
router.post('/login', (req, res) => {
  // 临时返回成功响应
  res.json({
    code: 200,
    message: '登录成功',
    data: {
      token: 'temp-token-123456',
      user: {
        id: 1,
        username: 'admin',
        name: '管理员'
      }
    }
  });
});

// 获取用户信息
router.get('/info', (req, res) => {
  res.json({
    code: 200,
    message: '获取成功',
    data: {
      id: 1,
      username: 'admin',
      name: '管理员',
      avatar: '',
      roles: ['admin']
    }
  });
});

// 获取用户列表
router.get('/list', (req, res) => {
  res.json({
    code: 200,
    message: '获取成功',
    data: {
      list: [
        {
          id: 1,
          username: 'admin',
          name: '管理员',
          status: true,
          createdTime: '2023-01-01 00:00:00'
        }
      ],
      total: 1
    }
  });
});

// 用户登出
router.post('/logout', (req, res) => {
  res.json({
    code: 200,
    message: '登出成功'
  });
});

module.exports = router; 