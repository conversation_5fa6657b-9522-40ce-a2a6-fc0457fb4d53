/* 全局样式定义 */
@import './variables.css';

/**
 * 页面根容器统一样式
 * 
 * 所有页面根容器统一使用 page-container 类名
 * 特殊页面（如登录页）可以使用额外的修饰类：page-container login-page
 * 页面特有的样式应该保留在各自的组件中，公共样式统一放在这里
 */
.page-container {
  height: 100%; /* 减去顶部导航栏高度 */
  overflow: hidden;
}
  /* 表格卡片样式 */
    
    .title-text {
      color: var(--primary-color);
      font-weight: 500;
    }

    .action-buttons {
      display: flex;
      flex-direction: row;
    }

    /* 操作按钮样式 */
    .action-button {
      margin: 0 4px;
      color: var(--primary-color);
    }

    :deep(.ant-table-container) {
      overflow-x: auto;
    }

    :deep(.ant-table-fixed-right) {
      background-color: white;
    }

  /* 自定义按钮样式 */
  .custom-button {
    float: left;
    border: none;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    line-height: 32px;
    
    &[type="primary"] {
      background-color: var(--primary-color);
      color: white;
    }
  }
  
.ant-table-wrapper .ant-table-cell-ellipsis{
  cursor: pointer !important;
}
/* 确保操作列按钮不会挤出固定列 */
:deep(.ant-table-tbody > tr > td.ant-table-cell-fix-right) {
  padding: 12px 8px;
}

.ant-pagination-item-active:hover {
  border-color: var(--primary-color) !important;

}

.ant-pagination-item-active, 
.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{
  border-color: var(--primary-color) !important;
}

.ant-pagination-item-active a {
  color: var(--primary-color) !important;
}
.ant-table-row-selected:hover>td, .ant-table-row-selected >td {
  background-color: var(--primary-td-active-color) !important;
}
.ant-switch-checked,.ant-switch-checked:hover:not(.ant-switch-disabled) {
  background: var(--primary-color) !important;
}
.ant-tooltip .ant-tooltip-inner, .ant-tooltip-arrow::after,.ant-tooltip-arrow::before {
  background-color: var(--primary-color) !important;
}
.ant-btn-link:not(:disabled):hover {
  color: var(--primary-active-color) !important;
}


// .ant-tree .ant-tree-switcher-noop{
//   width:0px !important;
// }

.ant-menu-light .ant-menu-submenu-selected >.ant-menu-submenu-title{
  color:var(--primary-color)
}
.ant-menu-light .ant-menu-item-selected, a:active{
  background-color: var(--primary-td-active-color) !important;
  color:var(--primary-color)
}
.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner,.ant-checkbox-wrapper-checked:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner, .ant-checkbox-checked:not(.ant-checkbox-disabled):hover .ant-checkbox-inner{
  background:var(--primary-color) !important;
}
.ant-checkbox-checked:after{
  border-color:var(--primary-color) !important;
}
.ant-menu-light .ant-menu-item:not(.ant-menu-item-disabled):focus-visible, .ant-menu-light .ant-menu-submenu-title:not(.ant-menu-item-disabled):focus-visible{
  outline-color: var(--primary-td-active-color) !important;
}
.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active{
  background-color: var(--primary-td-active-color) !important;
}
.import-tips {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f6f8fa;
  border-radius: 6px;

  h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-weight: 600;
  }

  ul {
    margin: 0;
    list-style:none;
    padding:0px;
    li {
      margin-bottom: 8px;
      color: #666;
      line-height: 1.5;
    }
  }
}
