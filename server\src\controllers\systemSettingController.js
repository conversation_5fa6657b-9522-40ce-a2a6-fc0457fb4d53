const { SystemSetting } = require('../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

// 获取系统设置列表
exports.getSystemSettings = async (req, res) => {
  try {
    const { keyword, pageNum = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (keyword) {
      where[Op.or] = [
        { code: { [Op.like]: `%${keyword}%` } },
        { name: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ];
    }
    
    // 分页查询，添加企业ID过滤
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);
    
    const { count, rows } = await SystemSetting.findAndCountAll(
      addEnterpriseFilter({
        where,
        order: [['createdAt', 'DESC']],
        offset,
        limit
      })
    );
    
    res.status(200).json({
      code: 200,
      message: '获取系统设置列表成功',
      data: {
        total: count,
        list: rows,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取系统设置列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取系统设置列表失败',
      error: error.message
    });
  }
};

// 获取系统设置详情
exports.getSystemSettingById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 添加企业ID过滤
    const systemSetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!systemSetting) {
      return res.status(404).json({
        code: 404,
        message: '系统设置不存在'
      });
    }
    
    res.status(200).json({
      code: 200,
      message: '获取系统设置详情成功',
      data: systemSetting
    });
  } catch (error) {
    console.error('获取系统设置详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取系统设置详情失败',
      error: error.message
    });
  }
};

// 通过code获取系统设置
exports.getSystemSettingByCode = async (req, res) => {
  try {
    const { code } = req.params;
    
    // 添加企业ID过滤
    const systemSetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: { code }
      })
    );
    
    if (!systemSetting) {
      return res.status(404).json({
        code: 404,
        message: `未找到代码为 ${code} 的系统设置`
      });
    }
    
    res.status(200).json({
      code: 200,
      message: '获取系统设置成功',
      data: systemSetting
    });
  } catch (error) {
    console.error('通过code获取系统设置失败:', error);
    res.status(500).json({
      code: 500,
      message: '通过code获取系统设置失败',
      error: error.message
    });
  }
};

// 获取设置值（工具方法）
exports.getSettingValue = async (code) => {
  try {
    // 添加企业ID过滤
    const setting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: { code }
      })
    );
    
    return setting ? setting.value : null;
  } catch (error) {
    console.error(`获取设置值 ${code} 失败:`, error);
    return null;
  }
};

// 创建系统设置
exports.createSystemSetting = async (req, res) => {
  try {
    const { code, name, value, description } = req.body;
    
    // 验证必填字段
    if (!code || !name) {
      return res.status(400).json({
        code: 400,
        message: '代码和名称为必填项'
      });
    }
    
    // 检查代码是否已存在，添加企业ID过滤
    const existingSetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: { code }
      })
    );
    
    if (existingSetting) {
      return res.status(400).json({
        code: 400,
        message: `代码 ${code} 已存在`
      });
    }
    
    // 创建系统设置，添加企业ID
    const systemSetting = await SystemSetting.create(
      addEnterpriseId({
        code,
        name,
        value,
        description
      })
    );
    
    res.status(200).json({
      code: 200,
      message: '创建系统设置成功',
      data: systemSetting
    });
  } catch (error) {
    console.error('创建系统设置失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建系统设置失败',
      error: error.message
    });
  }
};

// 更新系统设置
exports.updateSystemSetting = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, value, description } = req.body;
    
    // 查找系统设置，添加企业ID过滤
    const systemSetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!systemSetting) {
      return res.status(404).json({
        code: 404,
        message: '系统设置不存在'
      });
    }
    
    // 更新系统设置
    await systemSetting.update({
      name: name !== undefined ? name : systemSetting.name,
      value: value !== undefined ? value : systemSetting.value,
      description: description !== undefined ? description : systemSetting.description
    });
    
    res.status(200).json({
      code: 200,
      message: '更新系统设置成功',
      data: systemSetting
    });
  } catch (error) {
    console.error('更新系统设置失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新系统设置失败',
      error: error.message
    });
  }
};

// 删除系统设置
exports.deleteSystemSetting = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找系统设置，添加企业ID过滤
    const systemSetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!systemSetting) {
      return res.status(404).json({
        code: 404,
        message: '系统设置不存在'
      });
    }
    
    // 删除系统设置
    await systemSetting.destroy();
    
    res.status(200).json({
      code: 200,
      message: '删除系统设置成功'
    });
  } catch (error) {
    console.error('删除系统设置失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除系统设置失败',
      error: error.message
    });
  }
}; 