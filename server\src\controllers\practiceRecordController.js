const { PracticeRecord, PracticeRecordDetail, PositionType, PositionName, Level, KnowledgeBase, User, Employee } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');  // 添加这行，引入sequelize实例
const { addEnterpriseFilter } = require('../utils/enterpriseFilter');

/**
 * 统一错误处理
 * @param {Object} res - 响应对象
 * @param {Error} error - 错误对象
 */
const handleError = (res, error) => {
  console.error('操作失败:', error);

  if (error.name === 'SequelizeValidationError' || error.name === 'SequelizeUniqueConstraintError') {
    return res.status(400).json({
      code: 400,
      message: '数据验证错误',
      errors: error.errors.map(err => ({
        field: err.path,
        message: err.message
      }))
    });
  }

  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'production' ? undefined : error.message
  });
};

/**
 * 获取练习记录列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPracticeRecordList = async (req, res) => {
  try {
    const {
      positionBelong,
      positionName,
      positionLevel,
      examSubject,
      userName,
      minDuration,
      maxDuration,
      startTime,
      endTime,
      pageNum = 1,
      pageSize = 10,
      sortField,
      sortOrder
    } = req.query;

    // 构建查询条件
    const where = {};

    if (positionBelong) {
      where.positionBelong = positionBelong;
    }
    if (positionName) {
      where.positionName = positionName;
    }
    if (positionLevel) {
      where.positionLevel = positionLevel;
    }
    if (examSubject) {
      // 不能直接在 examSubject 字段上进行模糊查询，因为它存储的是 ID
      // 需要通过关联表查询，但这会导致 count 查询出错
      // 我们将在后面的查询中处理这个筛选条件
    }
    if (userName) {
      // 由于员工信息是单独查询的，这里先移除关联查询条件
      // 后面会单独处理用户名筛选
    }
    
    // 练习时长范围筛选
    if (minDuration || maxDuration) {
      // 由于total_duration存储的是mm:ss格式，需要转换为分钟数进行比较
      where[Op.and] = where[Op.and] || [];
      
      // 构建时间筛选条件
      const timeCondition = [];
      
      if (minDuration) {
        // 将mm:ss格式转换为分钟数，并与最小时长比较
        timeCondition.push(
          sequelize.where(
            sequelize.literal(`
              CASE 
                WHEN total_duration REGEXP '^[0-9]+:[0-9]+$' 
                THEN 
                  CAST(SUBSTRING_INDEX(total_duration, ':', 1) AS UNSIGNED) + 
                  CAST(SUBSTRING_INDEX(total_duration, ':', -1) AS UNSIGNED) / 60.0
                ELSE 0 
              END
            `),
            { [Op.gte]: parseInt(minDuration) }
          )
        );
      }
      
      if (maxDuration) {
        // 将mm:ss格式转换为分钟数，并与最大时长比较
        timeCondition.push(
          sequelize.where(
            sequelize.literal(`
              CASE 
                WHEN total_duration REGEXP '^[0-9]+:[0-9]+$' 
                THEN 
                  CAST(SUBSTRING_INDEX(total_duration, ':', 1) AS UNSIGNED) + 
                  CAST(SUBSTRING_INDEX(total_duration, ':', -1) AS UNSIGNED) / 60.0
                ELSE 0 
              END
            `),
            { [Op.lte]: parseInt(maxDuration) }
          )
        );
      }
      
      // 将时间条件添加到where子句中
      where[Op.and].push(...timeCondition);
    }

    if (startTime && endTime) {
      where.createTime = {
        [Op.between]: [startTime, endTime]
      };
    }

    // 分页查询
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 添加企业ID到查询条件
    where.enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 如果有 examSubject 筛选，需要先查询匹配的知识库 ID
    let knowledgeBaseIds = null;
    if (examSubject) {
      // 查询名称包含 examSubject 的知识库记录
      const matchedKnowledgeBases = await KnowledgeBase.findAll({
        where: {
          name: { [Op.like]: `%${examSubject}%` },
          enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
        },
        attributes: ['id']
      });
      
      if (matchedKnowledgeBases.length > 0) {
        // 获取这些知识库的 ID
        knowledgeBaseIds = matchedKnowledgeBases.map(kb => kb.id);
        
        // 将知识库 ID 添加到查询条件中
        where.examSubject = { [Op.in]: knowledgeBaseIds };
      } else {
        // 如果没有匹配的知识库，则返回空结果
        return res.json({
          code: 200,
          data: {
            total: 0,
            rows: [],
            pageNum: parseInt(pageNum),
            pageSize: parseInt(pageSize)
          },
          message: '获取练习记录列表成功'
        });
      }
    }

    // 查询练习记录总数（不包含关联）
    let count = await PracticeRecord.count({ where });

    // 如果有用户名筛选，需要单独处理
    let filteredIds = null;
    if (userName) {
      // 先查询匹配用户名的员工
      const matchedEmployees = await Employee.findAll({
        where: {
          name: { [Op.like]: `%${userName}%` },
          enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
        },
        attributes: ['openId']
      });
      
      if (matchedEmployees.length > 0) {
        // 获取这些员工的openId
        const matchedOpenIds = matchedEmployees.map(emp => emp.openId);
        
        // 查询这些openId对应的练习记录ID
        const matchedRecords = await PracticeRecord.findAll({
          where: {
            ...where,
            openId: { [Op.in]: matchedOpenIds }
          },
          attributes: ['id']
        });
        
        // 获取匹配的记录ID列表
        filteredIds = matchedRecords.map(record => record.id);
        
        // 更新总数
        count = filteredIds.length;
      } else {
        // 如果没有匹配的用户名，则返回空结果
        return res.json({
          code: 200,
          data: {
            total: 0,
            rows: [],
            pageNum: parseInt(pageNum),
            pageSize: parseInt(pageSize)
          },
          message: '获取练习记录列表成功'
        });
      }
    }

    // 构建排序条件
    let orderBy = [['createTime', 'DESC']]; // 默认排序

    // 如果提供了排序字段和排序方向，使用它们
    if (sortField && sortOrder) {
      const direction = sortOrder === 'ascend' ? 'ASC' : 'DESC';
      
      // 特殊处理练习时长字段，因为它存储为字符串格式
      if (sortField === 'totalDuration') {
        // 使用数据库函数将时长转换为数值进行排序
        orderBy = [
          [
            sequelize.literal(`
              CASE 
                WHEN total_duration REGEXP '^[0-9]+:[0-9]+$' 
                THEN 
                  CAST(SUBSTRING_INDEX(total_duration, ':', 1) AS UNSIGNED) + 
                  CAST(SUBSTRING_INDEX(total_duration, ':', -1) AS UNSIGNED) / 60.0
                ELSE 0 
              END
            `),
            direction
          ]
        ];
      } else {
        // 对于普通字段，直接使用字段名排序
        orderBy = [[sortField, direction]];
      }
    }

    // 查询练习记录数据，添加关联查询
    const rows = await PracticeRecord.findAll({
      where: filteredIds ? { id: { [Op.in]: filteredIds } } : where,
      offset,
      limit,
      order: orderBy,
      include: [
        {
          model: PositionType,
          as: 'positionTypeData',
          attributes: ['id', 'name', 'code'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionNameData',
          attributes: ['id', 'name', 'code'],
          required: false
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: KnowledgeBase,
          as: 'knowledge',
          attributes: ['id', 'name'],
          required: false
        }
      ]
    });

    // 单独查询员工信息以避免关联重复
    const openIds = [...new Set(rows.map(record => record.openId).filter(Boolean))];
    let employeeMap = {};
    if (openIds.length > 0) {
      const employees = await Employee.findAll({
        where: {
          openId: { [Op.in]: openIds },
          enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
        },
        attributes: ['openId', 'name']
      });
      
      employees.forEach(emp => {
        employeeMap[emp.openId] = emp.name;
      });
    }

    // 处理返回数据，添加关联信息
    const formattedRecords = rows.map(record => {
      const data = record.toJSON();
      return {
        ...data,
        userName: employeeMap[data.openId] || '未知用户',
        departmentName: data.positionTypeData?.name || data.positionBelong,
        positionFullName: data.positionNameData?.name || data.positionName,
        levelFullName: data.level?.name || data.positionLevel,
        subjectName: data.knowledge?.name || data.examSubject
      };
    });

    res.json({
      code: 200,
      data: {
        total: count,
        rows: formattedRecords,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      },
      message: '获取练习记录列表成功'
    });
  } catch (error) {
    console.error('获取练习记录列表失败：', error);
    handleError(res, error);
  }
};

/**
 * 获取练习记录详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPracticeRecordDetail = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('获取练习记录详情，ID:', id);

    // 查询练习记录详情，添加关联查询
    const practiceRecord = await PracticeRecord.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['openId', 'realName', 'nickname'],
          required: false,
        },
        {
          model: PositionType,
          as: 'positionTypeData',
          attributes: ['id', 'name', 'code'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionNameData',
          attributes: ['id', 'name', 'code'],
          required: false
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: KnowledgeBase,
          as: 'knowledge',
          attributes: ['id', 'name'],
          required: false
        }
      ]
    });

    if (!practiceRecord) {
      return res.status(404).json({
        code: 404,
        message: '练习记录不存在',
        data: null
      });
    }

    // 查询练习记录的详细对话内容
    const practiceRecordDetails = await PracticeRecordDetail.findAll({
      where: {
        practiceRecordId: id
      },
      order: [['id', 'ASC']]
    });

    // 处理返回数据，添加关联信息的中文名称
    const formattedRecord = practiceRecord.toJSON();
    const data = {
      ...formattedRecord,
      userName: formattedRecord.user?.realName || formattedRecord.user?.nickname || '未知用户',
      departmentName: formattedRecord.positionTypeData?.name || formattedRecord.positionBelong,
      positionFullName: formattedRecord.positionNameData?.name || formattedRecord.positionName,
      levelFullName: formattedRecord.level?.name || formattedRecord.positionLevel,
      subjectName: formattedRecord.knowledge?.name || formattedRecord.examSubject
    };

    res.json({
      code: 200,
      data: {
        practiceRecord: data,
        details: practiceRecordDetails
      },
      message: '获取练习记录详情成功'
    });
  } catch (error) {
    console.error('获取练习记录详情失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取练习记录详情失败',
      error: error.message
    });
  }
};

/**
 * 获取用户练习词汇统计数据
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getUserPracticeWordStats = async (req, res) => {
  try {
    // 从请求头中获取openid
    const openId = req.headers.openid;

    if (!openId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：openid',
        data: null
      });
    }

    // 获取企业ID
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 构建查询条件
    const where = {
      enterpriseId: enterpriseId
    };

    // 查询该用户下的所有练习记录
    const practiceRecords = await PracticeRecord.findAll(
      addEnterpriseFilter({
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['openId', 'realName', 'nickname'],
            where: { openId: openId }, // 根据openId过滤用户
            required: true
          }
        ]
      })
    );

    // 计算统计数据
    const totalPractices = practiceRecords.length; // 总练习次数

    // 计算累计时长（将mm:ss格式转换为小时）
    let totalDurationInSeconds = 0;

    practiceRecords.forEach(record => {
      if (record.totalDuration) {
        const [minutes, seconds] = record.totalDuration.split(':').map(Number);
        totalDurationInSeconds += minutes * 60 + seconds;
      }
    });

    // 将秒转换为小时，并保留2位小数
    const totalDurationInHours = (totalDurationInSeconds / 3600).toFixed(2);

    res.json({
      code: 200,
      data: {
        totalPractices: totalPractices,
        totalDuration: parseFloat(totalDurationInHours) // 转为数字类型返回
      },
      message: '获取用户练习词汇统计数据成功'
    });
  } catch (error) {
    console.error('获取用户练习词汇统计数据失败：', error);
    handleError(res, error);
  }
};

/**
 * 获取用户练习词汇列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getUserPracticeWordList = async (req, res) => {
  try {
    // 从请求头中获取openid
    const openId = req.headers.openid;
    
    if (!openId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：openid',
        data: null
      });
    }

    // 从URL获取分页参数
    const pageNum = parseInt(req.query.pageNum || 1);
    const pageSize = parseInt(req.query.pageSize || 10);

    // 从请求体获取筛选参数
    const { position, level, timeRange } = req.body || {};
    
    // 根据openid查询员工信息，获取岗位和等级ID
    const employee = await Employee.findOne({
      where: { openId: openId }
    });
    
    // 构建查询条件
    const where = {};
    
    // 如果找到员工信息，使用员工的岗位和等级作为默认筛选条件
    if (employee) {
      // 优先使用请求体中传来的筛选条件，如果没有则使用员工的岗位和等级
      where.positionName = position || employee.positionId?.toString();
      where.positionLevel = level || employee.levelId?.toString();
    } else {
      // 如果没找到员工信息，则使用请求体中的筛选条件
      if (position) {
        where.positionName = position;
      }
      
      if (level) {
        where.positionLevel = level;
      }
    }
    
    if (timeRange) {
      const { startTime, endTime } = timeRange;
      if (startTime && endTime) {
        where.createTime = {
          [Op.between]: [startTime, endTime]
        };
      }
    }

    // 分页参数
    const offset = (pageNum - 1) * pageSize;
    const limit = pageSize;
    
    // 查询该用户的所有练习记录
    const { count, rows: practiceRecords } = await PracticeRecord.findAndCountAll(
      addEnterpriseFilter({
        where,
        offset,
        limit,
        order: [['createTime', 'DESC']],
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['openId', 'realName', 'nickname'],
            where: { openId: openId },
            required: true
          },
          {
            model: PositionType,
            as: 'positionTypeData',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionNameData',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: KnowledgeBase,
            as: 'knowledge',
            attributes: ['id', 'name'],
            required: false
          }
        ]
      })
    );

    // 处理返回数据
    const formattedRecords = practiceRecords.map(record => {
      const data = record.toJSON();
      
      // 将 mm:ss 格式转换为 XX分钟
      let durationInMinutes = '';
      if (data.totalDuration) {
        const [minutes, seconds] = data.totalDuration.split(':').map(Number);
        // 如果秒数超过30秒，则向上取整分钟数
        durationInMinutes = seconds >= 30 ? `${minutes + 1}分钟` : `${minutes}分钟`;
      }
      
      // 创建新对象，只包含所需字段，不包含关联表数据
      return {
        id: data.id,
        openId: data.openId,
        enterpriseId: data.enterpriseId,
        questionNum: data.questionNum,
        totalDuration: durationInMinutes,
        examSubject: data.examSubject,
        subjectName: data.knowledge?.name || data.examSubject,
        departmentName: data.positionTypeData?.name || data.positionBelong,
        positionFullName: data.positionNameData?.name || data.positionName,
        levelFullName: data.level?.name || data.positionLevel,
        createTime: data.createTime,
        user: data.user
      };
    });

    res.json({
      code: 200,
      data: {
        total: count,
        rows: formattedRecords,
        pageNum: pageNum,
        pageSize: pageSize
      },
      message: '获取用户练习词汇列表成功'
    });
  } catch (error) {
    console.error('获取用户练习词汇列表失败：', error);
    handleError(res, error);
  }
};
