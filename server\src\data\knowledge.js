// 知识库数据模型
let knowledgeData = [
  {
    id: 1,
    name: '产品知识库',
    code: 'PDK001',
    description: '包含公司所有产品信息和常见问题解答',
    type: '产品',
    documentCount: 125,
    enterpriseId: 1,
    enterpriseName: '阿依来科技有限公司',
    status: true,
    createTime: '2024-01-19 11:20:00'
  },
  {
    id: 2,
    name: '技术支持知识库',
    code: 'TSK002',
    description: '技术支持相关的问题和解决方案',
    type: '技术支持',
    documentCount: 87,
    enterpriseId: 1,
    enterpriseName: '阿依来科技有限公司',
    status: true,
    createTime: '2024-01-22 09:45:00'
  },
  {
    id: 3,
    name: '市场营销资料库',
    code: 'MKT003',
    description: '市场营销相关的材料和案例',
    type: '市场营销',
    documentCount: 64,
    enterpriseId: 2,
    enterpriseName: '智慧未来科技有限公司',
    status: true,
    createTime: '2024-02-08 14:30:00'
  },
  {
    id: 4,
    name: '行业研究报告',
    code: 'IRR004',
    description: '各种行业研究报告和分析',
    type: '研究报告',
    documentCount: 32,
    enterpriseId: 3,
    enterpriseName: '数智云科技有限公司',
    status: false,
    createTime: '2024-02-15 16:10:00'
  }
];

// 获取知识库列表
exports.getKnowledgeList = (query = {}) => {
  let result = [...knowledgeData];
  
  // 支持按名称过滤
  if (query.name) {
    result = result.filter(item => 
      item.name.toLowerCase().includes(query.name.toLowerCase())
    );
  }
  
  // 支持按编码过滤
  if (query.code) {
    result = result.filter(item => 
      item.code.toLowerCase().includes(query.code.toLowerCase())
    );
  }
  
  // 支持按类型过滤
  if (query.type) {
    result = result.filter(item => 
      item.type === query.type
    );
  }
  
  // 支持按企业ID过滤
  if (query.enterpriseId) {
    result = result.filter(item => 
      item.enterpriseId === parseInt(query.enterpriseId)
    );
  }
  
  // 支持按状态过滤
  if (query.status !== undefined) {
    const status = query.status === 'true' || query.status === true || query.status === 1;
    result = result.filter(item => item.status === status);
  }
  
  // 计算总数
  const total = result.length;
  
  // 分页处理
  const pageNum = parseInt(query.pageNum) || 1;
  const pageSize = parseInt(query.pageSize) || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = pageNum * pageSize;
  const list = result.slice(startIndex, endIndex);
  
  return {
    list,
    total
  };
};

// 获取知识库详情
exports.getKnowledgeDetail = (id) => {
  const knowledge = knowledgeData.find(item => item.id === id);
  return knowledge || null;
};

// 创建知识库
exports.createKnowledge = (data) => {
  const newKnowledge = {
    ...data,
    id: Date.now(),
    documentCount: 0,
    createTime: new Date().toLocaleString()
  };
  
  knowledgeData.push(newKnowledge);
  return newKnowledge;
};

// 更新知识库
exports.updateKnowledge = (data) => {
  const index = knowledgeData.findIndex(item => item.id === data.id);
  if (index > -1) {
    knowledgeData[index] = { ...knowledgeData[index], ...data };
    return knowledgeData[index];
  }
  return null;
};

// 删除知识库
exports.deleteKnowledge = (id) => {
  const index = knowledgeData.findIndex(item => item.id === id);
  if (index > -1) {
    const deleted = knowledgeData.splice(index, 1)[0];
    return deleted;
  }
  return null;
}; 