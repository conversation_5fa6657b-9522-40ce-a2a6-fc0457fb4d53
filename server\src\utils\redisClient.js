const redis = require('redis');
const dotenv = require('dotenv');

dotenv.config();

// Redis配置 - 使用v4正确的格式
const redisHost = process.env.REDIS_HOST || 'localhost';
const redisPort = parseInt(process.env.REDIS_PORT) || 6379;
const redisDB = parseInt(process.env.REDIS_DB) || 0;
const redisPassword = process.env.REDIS_PASSWORD?.trim() || '';

// Redis v4配置格式
const redisConfig = {
  socket: {
    host: redisHost,
    port: redisPort,
    reconnectStrategy: (retries) => {
      console.log(`Redis重连尝试 ${retries}`);
      if (retries > 5) {
        return new Error('Redis重连失败，超过最大重试次数');
      }
      return Math.min(retries * 50, 500); // 递增延迟，最大500ms
    }
  },
  database: redisDB
};

// 只有当密码实际存在且不为空时，才添加密码配置
if (redisPassword) {
  redisConfig.password = redisPassword;
}

console.log('Redis配置:', {
  host: redisHost,
  port: redisPort,
  database: redisDB,
  hasPassword: !!redisPassword
});

// 创建Redis客户端 - 使用v4正确语法
const client = redis.createClient(redisConfig);

// 连接监听
client.on('connect', () => {
  console.log(`✅ Redis客户端已连接到 ${redisHost}:${redisPort}`);
});

client.on('ready', () => {
  console.log('✅ Redis客户端就绪，可以执行命令');
});

client.on('error', (err) => {
  console.error('❌ Redis连接错误:', {
    message: err.message,
    code: err.code,
    errno: err.errno,
    syscall: err.syscall,
    address: err.address,
    port: err.port,
    config: {
      host: redisHost,
      port: redisPort,
      database: redisDB
    }
  });
});

client.on('reconnecting', () => {
  console.log('🔄 Redis正在重新连接...');
});

client.on('end', () => {
  console.log('🔚 Redis连接已断开');
});

// 连接到Redis - v4会自动重连，无需手动重试逻辑
client.connect().catch((error) => {
  console.error('💀 Redis初始连接失败:', error.message);
  // 不抛出错误，让应用启动，依赖自动重连机制
});

/**
 * 设置键值对，支持过期时间
 * @param {string} key - 键
 * @param {string|object} value - 值，对象会被JSON.stringify
 * @param {number} [expireSeconds] - 过期时间(秒)，可选
 * @returns {Promise<string>} - 设置结果
 */
const set = async (key, value, expireSeconds = null) => {
  // 如果值是对象，进行JSON序列化
  const stringValue = typeof value === 'object' ? JSON.stringify(value) : value;
  
  if (expireSeconds !== null) {
    // 设置带过期时间的键值对
    const result = await client.setEx(key, expireSeconds, stringValue);
    return result;
  } else {
    // 设置不带过期时间的键值对
    const result = await client.set(key, stringValue);
    return result;
  }
};

/**
 * 获取键对应的值
 * @param {string} key - 键
 * @param {boolean} [parseJson=true] - 是否将值解析为JSON对象
 * @returns {Promise<any>} - 获取到的值
 */
const get = async (key, parseJson = true) => {
  const value = await client.get(key);
  
  if (value && parseJson) {
    try {
      return JSON.parse(value);
    } catch (error) {
      return value;
    }
  }
  
  return value;
};

/**
 * 删除键
 * @param {string} key - 要删除的键
 * @returns {Promise<number>} - 删除的键数量
 */
const del = async (key) => {
  return await client.del(key);
};

/**
 * 向集合中添加元素
 * @param {string} key - 集合键名
 * @param {...string} members - 要添加的集合成员
 * @returns {Promise<number>} - 添加的成员数量
 */
const sadd = async (key, ...members) => {
  return await client.sAdd(key, members);
};

/**
 * 获取集合的所有成员
 * @param {string} key - 集合键名
 * @returns {Promise<string[]>} - 集合成员数组
 */
const smembers = async (key) => {
  return await client.sMembers(key);
};

/**
 * 从集合中移除指定成员
 * @param {string} key - 集合键名
 * @param {...string} members - 要移除的集合成员
 * @returns {Promise<number>} - 移除的成员数量
 */
const srem = async (key, ...members) => {
  return await client.sRem(key, members);
};

/**
 * 检查键是否存在
 * @param {string} key - 键
 * @returns {Promise<number>} - 1表示存在，0表示不存在
 */
const exists = async (key) => {
  return await client.exists(key);
};

/**
 * 递增操作
 * @param {string} key - 键
 * @returns {Promise<number>} - 递增后的值
 */
const incr = async (key) => {
  return await client.incr(key);
};

/**
 * 设置键的过期时间
 * @param {string} key - 键
 * @param {number} seconds - 过期时间（秒）
 * @returns {Promise<boolean>} - 设置结果
 */
const expire = async (key, seconds) => {
  return await client.expire(key, seconds);
};

module.exports = {
  client,
  set,
  get,
  del,
  sadd,
  smembers,
  srem,
  exists,
  incr,
  expire
}; 