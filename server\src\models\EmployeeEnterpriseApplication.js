const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const Department = require('./Department');
const PositionName = require('./PositionName');
const Level = require('./Level');
const PositionType = require('./PositionType');

/**
 * 员工企业申请模型
 */
const EmployeeEnterpriseApplication = sequelize.define('EmployeeEnterpriseApplication', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  openId: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'open_id',
    comment: '微信openid'
  },
  enterpriseInviteCode: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'enterprise_invite_code',
    comment: '企业邀请码'
  },
  realName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'real_name',
    comment: '真实姓名'
  },
  idCard: {
    type: DataTypes.STRING(20),
    allowNull: false,
    field: 'id_card',
    comment: '身份证号'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '手机号'
  },
  departmentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'department_id',
    comment: '部门ID'
  },
  positionId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'position_id',
    comment: '岗位ID'
  },
  levelId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'level_id',
    comment: '岗位等级ID'
  },
  positionTypeId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'position_type_id',
    comment: '岗位类型ID'
  },
  gender: {
    type: DataTypes.STRING(10),
    allowNull: true,
    comment: '性别（male男 female女）'
  },
  entryTime: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    field: 'entry_time',
    comment: '入职时间'
  },
  enterpriseId: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  auditStatus: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: '审核中',
    field: 'audit_status',
    comment: '审核状态：审核中、通过、驳回'
  },
  auditorId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'auditor_id',
    comment: '审核人ID'
  },
  auditTime: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'audit_time',
    comment: '审核时间'
  },
  auditRemark: {
    type: DataTypes.STRING(500),
    allowNull: true,
    field: 'audit_remark',
    comment: '审核备注'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by',
    comment: '创建人ID'
  },
  updatedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'updated_by',
    comment: '更新人ID'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'employee_enterprise_application',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

// 关联部门
EmployeeEnterpriseApplication.belongsTo(Department, {
  foreignKey: 'departmentId',
  as: 'department'
});

// 关联岗位名称
EmployeeEnterpriseApplication.belongsTo(PositionName, {
  foreignKey: 'positionId',
  as: 'positionName'
});

// 关联岗位等级
EmployeeEnterpriseApplication.belongsTo(Level, {
  foreignKey: 'levelId',
  as: 'level'
});

// 关联岗位类型
EmployeeEnterpriseApplication.belongsTo(PositionType, {
  foreignKey: 'positionTypeId',
  as: 'positionType'
});

module.exports = EmployeeEnterpriseApplication; 