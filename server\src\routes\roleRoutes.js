const express = require('express');
const router = express.Router();
const roleController = require('../controllers/roleController');

// 获取角色列表
router.get('/list', roleController.getRoleList);

// 获取角色权限
router.get('/permissions/:id', roleController.getRolePermissions);

// 获取角色用户列表
router.get('/users/:id', roleController.getRoleUsers);

// 创建角色
router.post('/', roleController.createRole);

// 更新角色
router.put('/', roleController.updateRole);

// 删除角色
router.delete('/:id', roleController.deleteRole);

// 更新角色权限
router.put('/permissions', roleController.updateRolePermissions);

module.exports = router; 