# 员工晋升岗位接口文档

## 接口概述

根据微信openId获取员工的所有岗位配置信息，包括默认岗位和晋升岗位。

## 接口信息

- **接口路径**: `/api/wechat/employee/promotion-positions`
- **请求方法**: `GET`
- **接口描述**: 获取员工的所有岗位配置信息

## 请求参数

### Headers
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| openid | string | 是 | 微信用户的openId |

## 返回数据

### 成功响应
```json
{
  "code": 200,
  "message": "获取员工晋升岗位信息成功",
  "data": {
    "rows": [
      {
        "id": 9,
        "name": "切菜师",
        "code": "T1747218648544_1747218682036",
        "typeId": 6,
        "type": {
          "id": 6,
          "name": "后厨",
          "code": "T1747218648544"
        },
        "level": {
          "id": 1,
          "name": "初级",
          "code": "L001",
          "orderNum": 1
        },
        "isDefault": true,
        "employeeId": 123,
        "enterpriseId": "1",
        "createTime": "2024-01-15T08:30:00.000Z",
        "updateTime": "2024-01-15T08:30:00.000Z"
      }
    ],
    "total": 1,
    "employee": {
      "id": 123,
      "name": "张三",
      "openId": "wx_openid_123456",
      "departmentId": 1,
      "entryTime": "2024-01-01",
      "status": "1"
    }
  }
}
```

### 无岗位配置响应
```json
{
  "code": 200,
  "message": "员工暂无岗位配置",
  "data": {
    "rows": [],
    "total": 0,
    "employee": {
      "id": 123,
      "name": "张三",
      "openId": "wx_openid_123456",
      "departmentId": 1,
      "entryTime": "2024-01-01",
      "status": "1"
    }
  }
}
```

### 错误响应

#### 缺少openId
```json
{
  "code": 400,
  "message": "参数错误：缺少openId"
}
```

#### 员工不存在
```json
{
  "code": 404,
  "message": "未找到员工信息或员工未激活"
}
```

#### 服务器错误
```json
{
  "code": 500,
  "message": "服务器内部错误",
  "error": "具体错误信息"
}
```

## 数据字段说明

### rows数组中的岗位对象
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 岗位配置记录ID |
| name | string | 岗位名称 |
| code | string | 岗位编码 |
| typeId | number | 岗位类型ID |
| type | object | 岗位类型信息 |
| type.id | number | 岗位类型ID |
| type.name | string | 岗位类型名称 |
| type.code | string | 岗位类型编码 |
| level | object | 岗位等级信息 |
| level.id | number | 等级ID |
| level.name | string | 等级名称 |
| level.code | string | 等级编码 |
| level.orderNum | number | 等级排序号 |
| isDefault | boolean | 是否为默认岗位 |
| employeeId | number | 员工ID |
| enterpriseId | string | 企业ID |
| createTime | string | 创建时间 |
| updateTime | string | 更新时间 |

### employee对象
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 员工ID |
| name | string | 员工姓名 |
| openId | string | 微信openId |
| departmentId | number | 部门ID |
| entryTime | string | 入职时间 |
| status | string | 员工状态（1在职 0离职） |

## 使用示例

### JavaScript/小程序调用示例
```javascript
// 小程序中调用
wx.request({
  url: 'https://your-domain.com/api/wechat/employee/promotion-positions',
  method: 'GET',
  header: {
    'openid': 'wx_user_openid_here'
  },
  success: function(res) {
    if (res.data.code === 200) {
      const positions = res.data.data.rows;
      const employee = res.data.data.employee;
      
      // 处理岗位数据
      positions.forEach(position => {
        console.log(`岗位: ${position.name}, 类型: ${position.type.name}, 等级: ${position.level.name}`);
        if (position.isDefault) {
          console.log('这是默认岗位');
        }
      });
    }
  }
});
```

## 注意事项

1. **企业隔离**: 接口会自动根据环境变量 `DEFAULT_ENTERPRISE_ID` 进行企业数据隔离
2. **员工状态**: 只返回在职状态（status='1'）的员工信息
3. **数据排序**: 岗位列表按默认岗位优先，然后按创建时间排序
4. **关联查询**: 自动关联查询岗位名称、岗位类型、岗位等级等相关信息
5. **错误处理**: 完善的错误处理机制，包含详细的错误信息

## 业务逻辑

1. 根据openId和企业ID查找员工信息
2. 验证员工是否存在且为在职状态
3. 查询员工的所有岗位配置记录
4. 关联查询岗位相关的详细信息（岗位名称、类型、等级）
5. 按指定格式返回数据，默认岗位排在前面 