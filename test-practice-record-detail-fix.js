/**
 * 测试 practice_record_detail 表更新修复
 * 问题：该表没有 employee_id 和 open_id 字段，需要通过关联查询
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000';
const API_URL = `${BASE_URL}/api/organization/employee`;

/**
 * 测试员工离职API - 重点验证 practice_record_detail 表更新
 */
async function testPracticeRecordDetailFix() {
  console.log('🧪 测试 practice_record_detail 表更新修复');
  console.log('=' .repeat(60));

  // 测试数据
  const testEmployeeId = 69; // 请根据实际情况修改员工ID

  try {
    console.log(`\n📋 测试员工ID: ${testEmployeeId}`);
    console.log('🔄 发送离职请求...');

    const response = await axios.put(API_URL, {
      id: testEmployeeId,
      status: "0"  // 设置为离职状态
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30秒超时
    });

    // 验证响应
    if (response.data.code === 200) {
      console.log('\n🎉 员工离职处理成功!');
      console.log('✅ 响应数据:', JSON.stringify(response.data, null, 2));
      
      console.log('\n📝 practice_record_detail 表修复验证:');
      console.log('1. ✅ 不再直接使用 employee_id 和 open_id 字段');
      console.log('2. ✅ 通过关联查询 practice_record 表获取记录ID');
      console.log('3. ✅ 使用 practice_record_id 字段进行更新');
      console.log('4. ✅ 避免 "Unknown column" 错误');

      console.log('\n🔍 修复逻辑说明:');
      console.log('步骤1: 查询员工的所有 practice_record 记录');
      console.log('步骤2: 提取 practice_record 的 ID 列表');
      console.log('步骤3: 使用 practice_record_id IN (...) 更新 practice_record_detail');

    } else {
      console.log('\n❌ 员工离职处理失败');
      console.log('响应:', JSON.stringify(response.data, null, 2));
    }

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
      
      // 检查是否还有字段错误
      if (error.response.data.message && error.response.data.message.includes('Unknown column')) {
        console.error('\n💡 仍然存在字段错误，需要进一步检查表结构');
      }
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 请确保服务器正在运行在', BASE_URL);
    }
  }
}

/**
 * 显示修复前后的对比
 */
function showFixComparison() {
  console.log('\n📋 修复前后对比:');
  console.log('=' .repeat(60));
  
  console.log('\n❌ 修复前 (错误的方式):');
  console.log(`await PracticeRecordDetail.update(
  { isActive: 0, careerRecordId: careerRecordId },
  {
    where: {
      [Op.or]: [
        { employeeId: employeeId },  // ❌ 表中不存在此字段
        { openId: openId }           // ❌ 表中不存在此字段
      ],
      careerRecordId: null
    }
  }
);`);

  console.log('\n✅ 修复后 (正确的方式):');
  console.log(`// 1. 先获取员工的练习记录ID
const practiceRecords = await PracticeRecord.findAll({
  where: {
    [Op.or]: [
      { employeeId: employeeId },
      { openId: openId }
    ]
  },
  attributes: ['id']
});

const practiceRecordIds = practiceRecords.map(record => record.id);

// 2. 通过练习记录ID更新详情表
if (practiceRecordIds.length > 0) {
  await PracticeRecordDetail.update(
    { isActive: 0, careerRecordId: careerRecordId },
    {
      where: {
        practiceRecordId: { [Op.in]: practiceRecordIds }, // ✅ 使用存在的字段
        careerRecordId: null
      }
    }
  );
}`);

  console.log('\n🎯 修复要点:');
  console.log('1. practice_record_detail 表只有 practice_record_id 字段');
  console.log('2. 需要通过 practice_record 表关联查询');
  console.log('3. 使用 IN 操作符批量更新相关记录');
  console.log('4. 避免直接使用不存在的字段');
}

/**
 * 数据库验证查询
 */
function showVerificationQueries() {
  console.log('\n🔍 数据库验证查询:');
  console.log('=' .repeat(60));
  
  console.log('\n-- 1. 检查 practice_record_detail 表结构');
  console.log(`DESCRIBE practice_record_detail;`);

  console.log('\n-- 2. 验证更新结果');
  console.log(`SELECT 
  prd.id,
  prd.practice_record_id,
  prd.is_active,
  prd.career_record_id,
  pr.employee_id,
  pr.open_id
FROM practice_record_detail prd
JOIN practice_record pr ON prd.practice_record_id = pr.id
WHERE pr.employee_id = 69 
   OR pr.open_id = 'oxiSG65bMvpNcF9TORr5mvW-HXo4'
ORDER BY prd.id DESC
LIMIT 10;`);

  console.log('\n-- 3. 检查关联更新的一致性');
  console.log(`SELECT 
  COUNT(*) as total_practice_records,
  COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_records
FROM practice_record 
WHERE employee_id = 69 
   OR open_id = 'oxiSG65bMvpNcF9TORr5mvW-HXo4';`);
}

// 执行测试
async function runTest() {
  showFixComparison();
  await testPracticeRecordDetailFix();
  showVerificationQueries();
}

// 运行测试
runTest().catch(console.error);
