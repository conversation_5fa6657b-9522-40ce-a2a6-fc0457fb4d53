const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const EmployeeCareerRecord = sequelize.define('EmployeeCareerRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '履历ID'
  },
  employeeId: {
    type: DataTypes.INTEGER,
    field: 'employee_id',
    allowNull: false,
    comment: '员工ID'
  },
  openId: {
    type: DataTypes.STRING(255),
    field: 'open_id',
    allowNull: true,
    comment: 'open_id'
  },
  enterpriseId: {
    type: DataTypes.STRING(255),
    field: 'enterprise_id',
    allowNull: false,
    comment: '企业ID'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1在职 0离职）'
  },
  entryTime: {
    type: DataTypes.DATEONLY,
    field: 'entry_time',
    allowNull: true,
    comment: '入职时间'
  },
  departureTime: {
    type: DataTypes.DATE,
    field: 'departure_time',
    allowNull: true,
    comment: '离职时间'
  },
  departureReason: {
    type: DataTypes.STRING(500),
    field: 'departure_reason',
    allowNull: true,
    comment: '离职原因'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  },
  createBy: {
    type: DataTypes.STRING(64),
    field: 'create_by',
    allowNull: true,
    comment: '创建者'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    field: 'update_by',
    allowNull: true,
    comment: '更新者'
  }
}, {
  tableName: 'employee_career_record',
  timestamps: false, // 使用自定义时间字段
  underscored: false, // 已经在字段定义中指定了field名称
  comment: '员工履历记录表'
});

module.exports = EmployeeCareerRecord;
