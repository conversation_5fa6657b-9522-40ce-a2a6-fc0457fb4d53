#!/bin/bash
PROJECT_NAME=cankao-admin-server
PROJECT_DESC=餐烤餐考企业后端

echo '当前项目名称为:' "$PROJECT_NAME" - "$PROJECT_DESC"

# 从命令行参数中获取密码和推送模式
password=$1
push_mode=${2:-"yes"}  # 默认为yes，表示需要推送

# 检查是否提供了密码
if [ -z "$password" ]; then
  echo "Error: 请提供Docker密码作为第一个参数。"
  echo "用法: $0 <password> [push_mode]"
  echo "push_mode: yes(默认)|no - 是否推送到远程仓库"
  exit 1
fi

cd server
echo '当前工作目录为:' "$PWD"

# 获取开始时间
start_time=$(date +%s)

version=$(grep '"version"' ../web/package.json | sed -E 's/.*"version": "([^"]+)".*/\1/')-$(date '+%Y%m%d%H%M%S')
echo '待构建的镜像版本为:' "$version"

registry=registry.cn-wulanchabu.aliyuncs.com
imagePro=registry.cn-wulanchabu.aliyuncs.com/ydy-lowcode/$PROJECT_NAME

docker build -f Dockerfile -t $imagePro:$version -t $imagePro .

if [ "$push_mode" = "yes" ]; then
  echo "开始推送镜像到远程仓库..."
  docker login --username=candycloud $registry --password=$password
  docker push $imagePro:$version
  docker rmi $imagePro:$version
  docker rmi $imagePro:latest
  echo "镜像推送完成并清理本地镜像"
else
  echo "跳过推送步骤，镜像已在本地构建完成"
fi

# 获取结束时间
end_time=$(date +%s)

# 计算时间差
elapsed_time=$((end_time - start_time))

echo "耗时: $elapsed_time 秒"


# 更新deployment
#kubectl set image deploy/cankao-admin-server cankao-admin-server=$imagePro:$version
