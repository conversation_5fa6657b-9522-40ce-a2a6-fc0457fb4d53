const KnowledgeBase = require('../models/knowledge-base');
const { getFileType } = require('../utils/fileUpload');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const { Op } = require('sequelize');
const axios = require('axios');
const FormData = require('form-data');
const dotenv = require('dotenv');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');
const KBQuestions = require('../models/kb-questions');
const { DictionaryData } = require('../models/dictionary');
const redisClient = require('../utils/redisClient');
const { safeJsonParse } = require('../utils/jsonHelper');

// 加载环境变量
dotenv.config();

// 文档状态常量
const DOC_STATUS = {
  PREPARING: 'preparing',
  EMBEDDING: 'embedding',
  INDEXING: 'indexing',
  GENERATING: 'indexing',
  COMPLETED: 'completed',
  FAILED: 'failed'
};



// 设置文档状态到Redis
const setDocumentStatus = async (docId, status, progress = 0, message = '') => {
  try {
    const statusData = {
      id: docId,
      status,
      progress,
      message,
      updatedAt: new Date().toISOString()
    };

    // 保存状态到Redis
    await redisClient.set(`doc:status:${docId}`, statusData, 86400); // 保存24小时

    // 同步更新数据库中的状态
    try {
      await KnowledgeBase.update({
        processStatus: status,
        processProgress: progress,
        processMessage: message,
        updatedTime: new Date()
      }, {
        where: { id: docId }
      });
    } catch (dbError) {
      console.error(`更新数据库文档状态失败:`, dbError);
      // 数据库更新失败不影响Redis状态更新
    }

    console.log(`文档 ${docId} 状态已更新为: ${status}, 进度: ${progress}%`);
    return true;
  } catch (error) {
    console.error(`更新文档状态失败:`, error);
    return false;
  }
};

// 获取文档状态从Redis
const getDocumentStatus = async (docId) => {
  try {
    const statusData = await redisClient.get(`doc:status:${docId}`);
    return statusData; // redisClient.get 已处理JSON解析
  } catch (error) {
    console.error(`获取文档状态失败:`, error);
    return null;
  }
};

// 添加重试机制的函数
const retryOperation = async (operation, checkFn, maxRetries = 40, retryDelayMs = 3000) => {
  const startTime = Date.now();
  const timeoutMs = 600 * 1000; // 120秒超时

  let retries = 0;
  while (retries < maxRetries) {
    try {
      const result = await operation();

      // 检查结果是否有效
      if (checkFn(result)) {
        return { success: true, data: result };
      }

      // 检查是否超时
      if (Date.now() - startTime > timeoutMs) {
        return { success: false, error: '操作超时' };
      }

      console.log(`操作结果未满足条件，${retryDelayMs/1000}秒后重试，当前重试次数: ${retries + 1}`);
      await new Promise(resolve => setTimeout(resolve, retryDelayMs));
      retries++;
    } catch (error) {
      console.error(`操作失败:`, error);

      // 检查是否超时
      if (Date.now() - startTime > timeoutMs) {
        return { success: false, error: '操作超时: ' + error.message };
      }

      console.log(`操作出错，${retryDelayMs/1000}秒后重试，当前重试次数: ${retries + 1}`);
      await new Promise(resolve => setTimeout(resolve, retryDelayMs));
      retries++;
    }
  }

  return { success: false, error: `已达最大重试次数 ${maxRetries}` };
};

/**
 * 根据字典id查找字典名称
 * @param {number|string} id
 * @returns {Promise<string|null>} dictLabel
 */
async function getDictLabelById(id) {
  if (!id) return null;
  const dict = await DictionaryData.findOne({ where: { id } });
  return dict ? dict.dictLabel : null;
}

/**
 * 延迟为文档设置元数据
 * @param {string} documentId - Dify文档ID
 * @param {string} postValue - 岗位值
 * @param {string} typeValue - 类型值
 */
async function setDocumentMetadata(documentId, postValue, typeValue) {
  try {
    const DIFY_URL = process.env.DIFY_URL || 'https://cankao-sa-server.out.xmsxb.com';
    const DATASET_ID = process.env.DATASET_ID;
    if (!DIFY_URL || !DATASET_ID) {
      console.error('环境变量缺少DIFY_URL或DATASET_ID，无法设置元数据');
      return;
    }
    // 1. 查询元数据字段
    const metaUrl = `${DIFY_URL}/api/metadata/datasets/${DATASET_ID}`;
    const metaResp = await axios.get(metaUrl, { headers: { 'Accept': 'application/json' }, timeout: 10000 });
    if (!metaResp.data || !metaResp.data.success || !metaResp.data.data || !Array.isArray(metaResp.data.data.metadata)) {
      console.error('元数据查询失败', metaResp.data);
      return;
    }
    const metadataList = metaResp.data.data.metadata;

    console.log("metadataList",metadataList);
    // 2. 找到post和type的元数据id
    const postMeta = metadataList.find(m => m.name === 'post');
    const typeMeta = metadataList.find(m => m.name === 'type');
    console.log("typeMeta",typeMeta);
    console.log("typeValue",typeValue);
    if (!postMeta && !typeMeta) {
      console.error('未找到post或type元数据字段');
      return;
    }
    // 3. 组装metadata_list
    const metaArr = [];
    if (postMeta && postValue) metaArr.push({ id: postMeta.id, name: 'post', value: postValue });
    if (typeMeta && typeValue) metaArr.push({ id: typeMeta.id, name: 'type', value: typeValue });
    if (metaArr.length === 0) {
      console.error('没有可用的元数据配置');
      return;
    }
    console.log("postMeta",metaArr);
    // 4. 调用配置接口
    const configUrl = `${DIFY_URL}/api/metadata/datasets/${DATASET_ID}/documents/metadata`;
    const payload = {
      operationData: [
        {
          document_id: documentId,
          metadata_list: metaArr
        }
      ]
    };
    const configResp = await axios.post(configUrl, payload, { headers: { 'Accept': 'application/json', 'Content-Type': 'application/json' }, timeout: 10000 });
    if (!configResp.data || !configResp.data.success) {
      console.error('配置文档元数据失败', configResp.data);
      return;
    }
    console.log(`文档${documentId}元数据配置成功`);
  } catch (err) {
    console.error('设置文档元数据异常', err.message);
  }
}

// 获取知识库列表
const getKnowledgeBaseList = async (req, res) => {
  try {
    const { searchText, pageNum = 1, pageSize = 10, fileCategory, position, documentType } = req.query; // 从请求头获取企业ID，默认为1
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
    console.log("enterpriseId",enterpriseId);
    // 构建查询条件
    const where = {
      enterpriseId
    };

    // 添加文档类型过滤
    if (documentType) {
      where.documentType = documentType;
    }

    // 构建查询条件数组
    const andConditions = [];

    // 如果有搜索文本，添加搜索条件
    if (searchText) {
      andConditions.push({
        [Op.or]: [
          { name: { [Op.like]: `%${searchText}%` } },
          { fileName: { [Op.like]: `%${searchText}%` } }
        ]
      });
    }

    // 添加文件归属筛选条件
    if (fileCategory) {
      andConditions.push({ fileCategory: fileCategory });
    }

    // 添加所属岗位名称筛选条件
    if (position && fileCategory) {
      // 如果同时有岗位类型和岗位名称，查询该岗位或通用岗位
      andConditions.push({
        [Op.or]: [
          { position: position },    // 完全匹配指定岗位
          { position: 'COMMON' }      // 或者是通用岗位
        ]
      });
    } else if (position) {
      // 如果只有岗位名称，直接匹配
      andConditions.push({ position: position });
    }

    // 将所有条件添加到where中
    if (andConditions.length > 0) {
      where[Op.and] = andConditions;
    }

    // 查询总数
    const total = await KnowledgeBase.count({ where });

    // 分页查询
    const offset = (parseInt(pageNum) - 1) * parseInt(pageSize);
    const limit = parseInt(pageSize);

    const knowledgeList = await KnowledgeBase.findAll({
      where,
      order: [['createdTime', 'DESC']],
      offset,
      limit
    });

    // 格式化日期并转换为前端所需格式
    const formattedList = knowledgeList.map(item => {
      const data = item.toJSON();
      return {
        id: data.id,
        name: data.name,
        fileName: data.fileName,
        fileType: data.fileType,
        fileTypeChinese: getFileTypeChinese(data.fileType),
        size: data.fileSize,
        fileUrl: data.fileUrl,
        category: data.category,
        position: data.position,
        positionLevel: data.positionLevel,
        fileCategory: data.fileCategory,
        certificateType: data.certificateType,
        documentType: data.documentType || 'exam', // 添加文档类型，默认为exam
        status: data.status === null ? 'enable' : data.status,
        createdTime: formatDate(data.createdTime),
        updatedTime: formatDate(data.updatedTime)
      };
    });

    // 返回符合前端接口格式的数据
    res.json({
      code: 200,
      message: '获取成功',
      data: {
        list: formattedList,
        total,
        pageNum: parseInt(pageNum),
        pageSize: limit
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '获取知识库列表失败',
      error: error.message
    });
  }
};

// 上传文档
const uploadKnowledgeFile = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传的文件'
      });
    }

    const { originalname, filename, path: filePath, size } = req.file;

    // 确保文件名是正确解码的
    let decodedFileName = originalname;
    if (typeof decodedFileName === 'string') {
      try {
        // 尝试检测是否需要转换编码
        const testDecode = decodeURIComponent(escape(decodedFileName));
        // 如果解码成功但与原字符串不同，说明需要转换
        if (testDecode !== decodedFileName && /[\u4e00-\u9fa5]/.test(testDecode)) {
          decodedFileName = testDecode;
        }
      } catch (e) {
        // 如果解码失败，尝试直接从latin1转换
        decodedFileName = Buffer.from(decodedFileName, 'latin1').toString('utf8');
      }
    }

    // 获取文件类型
    const fileType = getFileType(decodedFileName);

    // 构建文件访问路径
    const fileUrl = `/uploads/knowledge-base/${filename}`;

    res.json({
      code: 200,
      message: '文件上传成功',
      data: {
        fileName: decodedFileName,
        fileType,
        fileTypeChinese: getFileTypeChinese(fileType),
        fileSize: size,
        fileUrl
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '文件上传失败',
      error: error.message
    });
  }
};

// 批量上传文档
const batchUploadFiles = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传的文件'
      });
    }

    const uploadResults = req.files.map(file => {
      const { originalname, filename, size } = file;

      // 确保文件名是正确解码的
      let decodedFileName = originalname;
      if (typeof decodedFileName === 'string') {
        try {
          // 尝试检测是否需要转换编码
          const testDecode = decodeURIComponent(escape(decodedFileName));
          // 如果解码成功但与原字符串不同，说明需要转换
          if (testDecode !== decodedFileName && /[\u4e00-\u9fa5]/.test(testDecode)) {
            decodedFileName = testDecode;
          }
        } catch (e) {
          // 如果解码失败，尝试直接从latin1转换
          decodedFileName = Buffer.from(decodedFileName, 'latin1').toString('utf8');
        }
      }

      // 获取文件类型
      const fileType = getFileType(decodedFileName);
      const fileUrl = `/uploads/knowledge-base/${filename}`;

      return {
        fileName: decodedFileName,
        fileType,
        fileTypeChinese: getFileTypeChinese(fileType),
        fileSize: size,
        fileUrl
      };
    });

    res.json({
      code: 200,
      message: '文件批量上传成功',
      data: uploadResults
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '文件批量上传失败',
      error: error.message
    });
  }
};

// 批量保存知识库文档
const batchSaveDocuments = async (req, res) => {
  try {
    const { documents, commonData } = req.body || {};
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从请求头获取企业ID
    const userId = req.headers['user-id'] || 'admin'; // 从请求头获取用户ID

    if (!documents || !Array.isArray(documents) || documents.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '缺少文档数据'
      });
    }

    const results = {
      success: [],
      failure: []
    };

    // 批量处理文档
    for (const doc of documents) {
      // 检查doc是否为有效对象
      if (!doc || typeof doc !== 'object') {
        results.failure.push({
          fileName: '未知文件',
          reason: '文档数据格式无效'
        });
        continue;
      }

      try {
        // 合并公共数据和文档特有数据
        // 先确保doc和commonData都是对象
        const safeDoc = typeof doc === 'object' && doc !== null ? doc : {};
        const safeCommonData = typeof commonData === 'object' && commonData !== null ? commonData : {};
        const dataIds = uuidv4();
        const documentData = {
          ...safeCommonData,
          ...safeDoc,
          id: dataIds,
          createdBy: userId,
          updatedBy: userId,
          enterpriseId,
          segments: [], // 初始化为空数组
          documentType: safeDoc.documentType || safeCommonData.documentType || 'exam' // 优先使用传入的documentType，默认为exam
        };

        // 初始化文档状态
        await setDocumentStatus(dataIds, DOC_STATUS.PREPARING, 0, '文档准备中');

        // 确保文件名存在
        if (!doc || !doc.fileName) {
          await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 0, '缺少文件名');
          results.failure.push({
            fileName: doc?.fileName || '未知文件',
            reason: '缺少文件名'
          });
          continue;
        }

        // 检查必填字段
        if (!documentData.name || !documentData.fileName || !documentData.fileType || !documentData.fileUrl) {
          await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 0, '缺少必要参数');
          results.failure.push({
            fileName: doc.fileName || '未知文件',
            reason: '缺少必要参数'
          });
          continue;
        }

        // 检查是否已存在相同的文件
        try {
          // 构建查询条件
          const whereCondition = {
            enterpriseId,
            deleted: false
          };

          // 安全地添加查询条件，只有当字段存在时才添加
          if (documentData.fileCategory) whereCondition.fileCategory = documentData.fileCategory;
          if (documentData.position) whereCondition.position = documentData.position;
          if (documentData.name) whereCondition.fileName = documentData.fileName;

          console.log("whereCondition",whereCondition);

          const existingDoc = await KnowledgeBase.findOne({
            where: whereCondition
          });

          if (existingDoc) {
            await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 0, '在相同岗位类型和岗位名称下已存在同名文档');
            results.failure.push({
              fileName: doc.fileName,
              reason: '在相同岗位类型和岗位名称下已存在同名文档'
            });
            continue;
          }
        } catch (findError) {
          await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 0, '查询数据库失败: ' + findError.message);
          results.failure.push({
            fileName: doc.fileName,
            reason: '查询数据库失败: ' + findError.message
          });
          continue;
        }

        // 更新状态为嵌入中
        await setDocumentStatus(dataIds, DOC_STATUS.EMBEDDING, 20, '文档嵌入中');

        // 调用DIFY API上传文件
        let document_id = null;
        try {
          // 获取文件本地路径
          const localFilePath = path.join(__dirname, '../../', documentData.fileUrl);

          // 检查文件是否存在
          if (!fs.existsSync(localFilePath)) {
            await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 20, '文件不存在或已被删除');
            results.failure.push({
              fileName: doc.fileName,
              reason: '文件不存在或已被删除'
            });
            continue;
          }

          // 检查文件是否可读
          try {
            fs.accessSync(localFilePath, fs.constants.R_OK);
          } catch (err) {
            await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 20, '文件存在但无法读取');
            results.failure.push({
              fileName: doc.fileName,
              reason: '文件存在但无法读取'
            });
            continue;
          }

          // 准备FormData
          const formData = new FormData();
          var fileses = fs.createReadStream(localFilePath);
          fileses.originalname = documentData.fileName;
          formData.append('file', fileses);
          formData.append("filename", documentData.fileName);

          // 添加datasetId
          let didAddDatasetId = false;

          if (doc.datasetId) {
            formData.append('datasetId', doc.datasetId);
            didAddDatasetId = true;
          }

          if (!didAddDatasetId && process.env.DATASET_ID) {
            formData.append('datasetId', process.env.DATASET_ID);
          }

          // 获取DIFY API URL
          const difyUrl = process.env.DIFY_URL;
          const apiUrl = `${difyUrl}/api/documents/upload`;

          // 准备请求头
          const headers = {
            'Accept': 'application/json'
          };

          // 如果formData有getHeaders方法，使用它
          if (typeof formData.getHeaders === 'function') {
            Object.assign(headers, formData.getHeaders());
          } else {
            headers['Content-Type'] = 'multipart/form-data';
          }

          // 调用API
          const response = await axios.post(apiUrl, formData, {
            headers,
            timeout: 30000 // 设置30秒超时
          });

          console.log("uploadses", response.data);

          // 检查响应
          if (response && response.data && response.data.success &&
              response.data.data && response.data.data.documentId) {
            // 保存DIFY文档ID
            document_id = response.data.data.documentId;
            await setDocumentStatus(dataIds, DOC_STATUS.INDEXING, 40, '文档索引中');
          } else {
            await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 30, '上传到DIFY失败: 响应无效');
            results.failure.push({
              fileName: doc.fileName,
              reason: '上传到DIFY失败: 响应无效'
            });
            continue;
          }
        } catch (apiError) {
          console.error("uploadserror", apiError);
          await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 30, '上传到DIFY失败: ' + apiError.message);
          results.failure.push({
            fileName: doc.fileName,
            reason: '上传到DIFY失败: ' + apiError.message
          });
          continue;
        }

        // 设置document_id（如果获取到的话）
        if (document_id) {
          documentData.document_id = document_id;
        } else {
          await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 30, '未获取到DIFY文档ID');
          results.failure.push({
            fileName: doc.fileName,
            reason: '未获取到DIFY文档ID'
          });
          continue;
        }

        // 检查name字段长度并截取
        if (documentData.name && documentData.name.length > 10) {
          documentData.name = documentData.name.substring(0, 10);
        }

        // 创建文档
        const newDocument = await KnowledgeBase.create(documentData);

        // 异步处理获取段落和生成问题
        (async () => {
          try {
            // 更新状态为等待文档索引
            await setDocumentStatus(dataIds, DOC_STATUS.INDEXING, 50, '等待文档段落生成');

            await new Promise(resolve => setTimeout(resolve, 5000));

            // 判断文档类型
            if (documentData.documentType === 'prompt') {
              // 提示类型文档：只进行嵌入，不生成题目
              console.log(`文档 ${documentData.fileName} 是提示类型，跳过题目生成`);

              // 等待嵌入完成（通过延时确保DIFY已完成索引）
              await new Promise(resolve => setTimeout(resolve, 5000));

              // 直接设置为完成状态
              await setDocumentStatus(dataIds, DOC_STATUS.COMPLETED, 100, '文档嵌入完成');
              console.log(`提示类型文档 ${documentData.fileName} 处理完成`);

            } else if (documentData.documentType === 'enterprise') {
              // 企业知识库类型文档：只进行嵌入和元数据操作，不生成题目
              console.log(`文档 ${documentData.fileName} 是企业知识库类型，跳过题目生成`);

              // 等待嵌入完成（通过延时确保DIFY已完成索引）
              await new Promise(resolve => setTimeout(resolve, 5000));

              // 设置元数据
              try {
                const { PositionName, PositionType } = require('../models');

                // 从PositionName模型获取岗位名称
                let postName = '';
                if (documentData.position) {
                  // 处理特殊值
                  if (documentData.position === 'COMMON') {
                    postName = '通用';
                  } else {
                    const positionRecord = await PositionName.findOne({
                      where: { id: documentData.position }
                    });
                    postName = positionRecord ? positionRecord.name : '';
                  }
                }

                // 从PositionType模型获取岗位类型名称
                let typeName = '';
                if (documentData.fileCategory) {
                  // 处理特殊值
                  if (documentData.fileCategory === 'COMMON_TYPE') {
                    typeName = '通用';
                  } else {
                    const typeRecord = await PositionType.findOne({
                      where: { id: documentData.fileCategory }
                    });
                    typeName = typeRecord ? typeRecord.name : '';
                  }
                }

                await setDocumentMetadata(document_id, postName, typeName);
                console.log('设置元数据成功，文档ID:', document_id);
              } catch (metaError) {
                console.error('设置元数据失败:', metaError);
                // 元数据设置失败不影响整体流程
              }

              // 直接设置为完成状态
              await setDocumentStatus(dataIds, DOC_STATUS.COMPLETED, 100, '文档嵌入完成');
              console.log(`企业知识库文档 ${documentData.fileName} 处理完成`);

            } else {
              // 练考类型文档：正常处理，包括获取段落、生成题目和设置元数据

              // 获取段落：带重试机制
              const segmentsResult = await retryOperation(
                // 操作函数
                async () => {
                  const difyUrl = process.env.DIFY_URL;
                  const datasetId = process.env.DATASET_ID;
                  const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${document_id}/segments`;

                  const response = await axios.get(apiUrl, {
                    headers: { 'Accept': 'application/json' },
                    timeout: 10000
                  });

                  let segments = [];
                  if (response.data && Array.isArray(response.data)) {
                    segments = response.data;
                  } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
                    segments = response.data.data;
                  } else if (response.data && response.data.segments && Array.isArray(response.data.segments)) {
                    segments = response.data.segments;
                  }

                  return segments;
                },
                // 检查函数：确保段落数组非空
                (segments) => Array.isArray(segments) && segments.length > 0
              );

              if (!segmentsResult.success) {
                await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 60, '获取文档段落失败: ' + segmentsResult.error);
                console.error(`获取文档段落失败: ${segmentsResult.error}`);
                return;
              }

              // 更新状态为生成问题中
              await setDocumentStatus(dataIds, DOC_STATUS.INDEXING, 70, '文档出题中');

              // 生成问题：带重试机制
              const questionsResult = await retryOperation(
                // 操作函数
                async () => {
                  return await generateQuestionsForDocument(document_id, dataIds,documentData.fileName);
                },
                // 检查函数
                (result) => result && result.success
              );

              if (!questionsResult.success) {
                await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 80, '生成问题失败: ' + questionsResult.error);
                console.error(`生成问题失败: ${questionsResult.error}`);
                return;
              }

              // 设置元数据
              try {
                const { PositionName, PositionType } = require('../models');

                // 从PositionName模型获取岗位名称
                let postName = '';
                if (documentData.position) {
                  // 处理特殊值
                  if (documentData.position === 'COMMON') {
                    postName = '通用';
                  } else {
                    const positionRecord = await PositionName.findOne({
                      where: { id: documentData.position }
                    });
                    postName = positionRecord ? positionRecord.name : '';
                  }
                }

                // 从PositionType模型获取岗位类型名称
                let typeName = '';
                if (documentData.fileCategory) {
                  // 处理特殊值
                  if (documentData.fileCategory === 'COMMON_TYPE') {
                    typeName = '通用';
                  } else {
                    const typeRecord = await PositionType.findOne({
                      where: { id: documentData.fileCategory }
                    });
                    typeName = typeRecord ? typeRecord.name : '';
                  }
                }

                await setDocumentMetadata(document_id, postName, typeName);
                console.log('设置元数据成功，文档ID:', document_id);
              } catch (metaError) {
                console.error('设置元数据失败:', metaError);
                // 元数据设置失败不影响整体流程
              }

              // 更新状态为完成
              await setDocumentStatus(dataIds, DOC_STATUS.COMPLETED, 100, '文档处理完成');
            }

          } catch (error) {
            console.error('异步处理文档失败:', error);
            await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 90, '文档处理失败: ' + error.message);
          }
        })();

        results.success.push({
          id: newDocument.id,
          fileName: doc.fileName
        });
      } catch (docError) {
        // 处理最外层的错误
        await setDocumentStatus(dataIds, DOC_STATUS.FAILED, 0, '处理文档失败: ' + docError.message);
        results.failure.push({
          fileName: doc.fileName || '未知文件',
          reason: docError.message
        });
      }
    }

    res.json({
      code: 200,
      message: `成功保存 ${results.success.length} 个文档，失败 ${results.failure.length} 个`,
      data: results
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '批量保存知识库文档失败',
      error: error.message
    });
  }
};

// 获取知识库文档详情
const getKnowledgeDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从请求头获取企业ID

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少文档ID'
      });
    }

    const document = await KnowledgeBase.findOne({
      where: {
        id,
        enterpriseId,
        deleted: false
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    const data = document.toJSON();

    res.json({
      code: 200,
      message: '获取成功',
      data: {
        id: data.id,
        name: data.name,
        fileName: data.fileName,
        fileType: data.fileType,
        fileTypeChinese: getFileTypeChinese(data.fileType),
        size: data.fileSize,
        fileUrl: data.fileUrl,
        category: data.category,
        position: data.position,
        positionLevel: data.positionLevel,
        fileCategory: data.fileCategory,
        certificateType: data.certificateType,
        status: data.status === null ? 'enable' : data.status,
        createdTime: formatDate(data.createdTime),
        updatedTime: formatDate(data.updatedTime)
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '获取知识库文档详情失败',
      error: error.message
    });
  }
};

// 更新知识库文档
const updateKnowledgeDocument = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, fileCategory, position, positionLevel, certificateType, documentType } = req.body;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从请求头获取企业ID
    const userId = req.headers['user-id'] || 'admin'; // 从请求头获取用户ID

    // 查找要更新的文档
    const document = await KnowledgeBase.findOne({
      where: {
        id,
        enterpriseId
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    // 更新文档信息
    await document.update({
      name,
      fileCategory,
      position,
      positionLevel,
      certificateType,
      documentType: documentType || document.documentType || 'exam', // 添加文档类型更新
      updatedBy: userId,
      updatedTime: new Date()
    });

    res.json({
      code: 200,
      message: '更新成功',
      data: document
    });
  } catch (error) {
    console.error('更新知识库文档失败', error);
    res.status(500).json({
      code: 500,
      message: '更新知识库文档失败',
      error: error.message
    });
  }
};

// 删除知识库文档
const deleteKnowledgeDocument = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从请求头获取企业ID

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少文档ID'
      });
    }

    const document = await KnowledgeBase.findOne({
      where: {
        id,
        enterpriseId
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    // 检查文档是否被练考配置引用
    const { ExamConfig, KBQuestions } = require('../models');
    const examConfigCount = await ExamConfig.count({
      where: {
        exam_subject: id,
        enterpriseId
      }
    });

    if (examConfigCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '该文档已被练考配置关联，不能删除'
      });
    }

    // 首先尝试删除 Dify 上的文档
    try {
      // 检查文档是否有 difyDocumentId
      if (document.document_id) {
        console.log(`尝试删除 Dify 文档：${document.document_id}`);

        // 调用 Dify 删除接口
        const difyUrl = process.env.DIFY_URL || 'https://cankao-sa-server.out.xmsxb.com';
    const datasetId = process.env.DATASET_ID;

    if (!datasetId) {
      return res.status(400).json({
        code: 400,
        message: '环境配置缺少数据集ID'
      });
    }

    // 调用Dify API删除文档
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${document.document_id}`;

        console.log(`成功删除 Dify 文档：${document.document_id}`);
        await axios.delete(apiUrl, {
          headers: {
            'Accept': 'application/json'
          }
        });
      } else {
        console.log(`文档 ${id} 没有关联的 Dify 文档 ID`);
      }
    } catch (difyError) {
      // 记录错误，但不影响主流程
      console.error(`删除 Dify 文档失败：${difyError.message}`);
      // 这里可以选择是否要通知客户端
    }

    // 删除知识库相关的题目数据
    try {
      const deletedQuestionsCount = await KBQuestions.destroy({
        where: {
          knowledge_base_id: id,
          enterprise_id: enterpriseId
        }
      });
      console.log(`删除了 ${deletedQuestionsCount} 条关联题目数据`);
    } catch (questionsError) {
      console.error(`删除关联题目失败：${questionsError.message}`);
      // 记录错误但继续删除知识库文档
    }

    // 物理删除数据库中的文档
    await document.destroy();

    res.json({
      code: 200,
      message: '删除成功'
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '删除知识库文档失败',
      error: error.message
    });
  }
};

// 下载知识库文档
const downloadKnowledgeFile = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseId = req.headers['enterprise-id'] || '1'; // 从请求头获取企业ID

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少文档ID'
      });
    }

    const document = await KnowledgeBase.findOne({
      where: {
        id,
        enterpriseId
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    // 文件路径
    const filePath = path.join(__dirname, '..', '..', document.fileUrl);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        code: 404,
        message: '文件不存在或已被删除'
      });
    }

    // 设置文件名的编码，处理中文文件名
    const fileName = encodeURIComponent(document.fileName);

    // 设置响应头，使用更安全的方式设置文件名
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${fileName}`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // 发送文件
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '下载知识库文档失败',
      error: error.message
    });
  }
};

// 获取文件类型中文名称
const getFileTypeChinese = (fileType) => {
  const typeMap = {
    'pdf': 'PDF文档',
    'word': 'Word文档',
    'excel': 'Excel表格',
    'ppt': 'PPT演示',
    'image': '图片',
    'text': '文本',
    'archive': '压缩包',
    'audio': '音频',
    'video': '视频',
    'unknown': '未知类型'
  };
  return typeMap[fileType] || '未知类型';
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 获取文档段落列表
const getDocumentSegments = async (req, res) => {
  try {
    const { id } = req.params; // 文档ID

    // 从数据库查询文档信息
    const document = await KnowledgeBase.findOne({
      where: {
        id,
        deleted: false
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    // 检查文档是否有Dify文档ID
    if (!document.document_id) {
      return res.json({
        code: 200,
        message: '获取成功',
        data: [] // 无段落数据
      });
    }

    // 获取Dify API URL和数据集ID
    const difyUrl = process.env.DIFY_URL || 'https://cankao-sa-server.out.xmsxb.com';
    const datasetId = process.env.DATASET_ID;

    if (!datasetId) {
      return res.status(400).json({
        code: 400,
        message: '环境配置缺少数据集ID'
      });
    }

    // 调用Dify API获取段落
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${document.document_id}/segments`;

    const response = await axios.get(apiUrl, {
      headers: {
        'Accept': 'application/json'
      }
    });

    // 输出完整响应结构以便调试
    console.log('API响应结构:', JSON.stringify(response.data).substring(0, 500) + '...');

    // 正确获取段落数组，考虑不同的响应结构可能性
    let segmentse = [];
    if (response.data && Array.isArray(response.data)) {
      segmentse = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      segmentse = response.data.data;
    } else if (response.data && response.data.segments && Array.isArray(response.data.segments)) {
      segmentse = response.data.segments;
    }

    console.log('段落数组长度:', segmentse.length);
    if (segmentse.length > 0) {
      console.log('第一个段落示例:', JSON.stringify(segmentse[0]));
    }

    // 返回段落数据
    return res.json({
      code: 200,
      message: '获取成功',
      data: segmentse
    });
  } catch (error) {
    console.error('获取文档段落列表失败', error);
    return res.status(500).json({
      code: 500,
      message: '获取文档段落列表失败',
      error: error.message
    });
  }
};

// 删除文档段落
const deleteDocumentSegment = async (req, res) => {
  try {
    const { id, segmentId } = req.params; // 文档ID和段落ID
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 从数据库查询文档信息
    const document = await KnowledgeBase.findOne({
      where: {
        id,
        deleted: false
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    // 检查文档是否有Dify文档ID
    if (!document.document_id) {
      return res.status(400).json({
        code: 400,
        message: '文档未关联Dify资源'
      });
    }

    // 获取Dify API URL和数据集ID
    const difyUrl = process.env.DIFY_URL || 'https://cankao-sa-server.out.xmsxb.com';
    const datasetId = process.env.DATASET_ID;

    if (!datasetId) {
      return res.status(400).json({
        code: 400,
        message: '环境配置缺少数据集ID'
      });
    }

    // 调用Dify API删除段落
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${document.document_id}/segments/${segmentId}`;

    await axios.delete(apiUrl, {
      headers: {
        'Accept': 'application/json'
      }
    });

    // 删除与该段落关联的所有题目
    try {
      const deletedQuestionsCount = await KBQuestions.destroy({
        where: {
          knowledge_base_id: id,
          segment_id: segmentId,
          enterprise_id: enterpriseId
        }
      });
      console.log(`已删除与段落${segmentId}关联的${deletedQuestionsCount}个题目`);
    } catch (error) {
      console.error('删除段落关联题目失败:', error);
      // 不影响主流程，记录错误但继续执行
    }

    // 返回成功信息
    return res.json({
      code: 200,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除文档段落失败', error);
    return res.status(500).json({
      code: 500,
      message: '删除文档段落失败',
      error: error.message
    });
  }
};

// 修改文档段落
const updateDocumentSegment = async (req, res) => {
  try {
    const { id, segmentId } = req.params; // 文档ID和段落ID
    const { content, answer, keywords, enabled } = req.body;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 从数据库查询文档信息
    const document = await KnowledgeBase.findOne({
      where: {
        id,
        deleted: false
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    // 检查文档是否有Dify文档ID
    if (!document.document_id) {
      return res.status(400).json({
        code: 400,
        message: '文档未关联Dify资源'
      });
    }

    // 获取Dify API URL和数据集ID
    const difyUrl = process.env.DIFY_URL || 'https://cankao-sa-server.out.xmsxb.com';
    const datasetId = process.env.DATASET_ID;

    if (!datasetId) {
      return res.status(400).json({
        code: 400,
        message: '环境配置缺少数据集ID'
      });
    }

    // 构建请求数据
    const requestData = {};
    if (content !== undefined) requestData.content = content;
    if (answer !== undefined) requestData.answer = answer;
    if (keywords !== undefined) requestData.keywords = keywords;
    if (enabled !== undefined) requestData.enabled = enabled;

    // 调用Dify API更新段落
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${document.document_id}/segments/${segmentId}`;

    await axios.put(apiUrl, requestData, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    // 删除与该段落关联的所有题目
    try {
      const deletedQuestionsCount = await KBQuestions.destroy({
        where: {
          knowledge_base_id: id,
          segment_id: segmentId,
          enterprise_id: enterpriseId
        }
      });
      console.log(`已删除与段落${segmentId}关联的${deletedQuestionsCount}个题目`);

      // 异步重新生成该段落的题目
      setTimeout(async () => {
        try {
          // 构造一个只包含当前段落的数组
          const segment = {
            id: segmentId,
            content: content || ''
          };

          // 获取文档名称和出题提示词
          const documentName = document.fileName || '';

          // 准备API输入参数
          const inputs = {
            "segment": JSON.stringify([segment]),
            "document_name": documentName
          };

          // 如果有出题提示词，则添加到输入参数中
          if (document.prompt) {
            inputs.prom = document.prompt;
          }

          // 调用生成题目API
          const apiUrls = `${difyUrl}/api/agent/workflow/run/${enterpriseId}/AGENT-EXAM`;
          const response = await axios.post(apiUrls, {
            "inputs": inputs
          }, {
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            },
            timeout: 300000
          });

          // 处理响应，提取题目并保存
          if (response.data && response.data.success && response.data.data && response.data.data.outputs) {
            // 获取输出的问题字符串
            const questionStr = response.data.data.outputs.question;

            // 移除Markdown代码块标记
            let jsonStr = questionStr;
            if (jsonStr.startsWith('```json\n')) {
              jsonStr = jsonStr.substring('```json\n'.length);
            }
            if (jsonStr.endsWith('\n```')) {
              jsonStr = jsonStr.substring(0, jsonStr.length - 4);
            }

            // 解析JSON字符串为数组
            const questionsArray = JSON.parse(jsonStr);

            // 保存生成的题目
            if (Array.isArray(questionsArray) && questionsArray.length > 0) {
              await saveGeneratedQuestions(id, questionsArray);
              console.log(`成功为段落 ${segmentId} 重新生成题目`);
            }
          }
        } catch (error) {
          console.error(`为段落 ${segmentId} 重新生成题目失败:`, error);
        }
      }, 2000); // 延迟2秒执行，确保段落更新已完成
    } catch (error) {
      console.error('删除段落关联题目失败:', error);
      // 不影响主流程，记录错误但继续执行
    }

    // 返回成功信息
    return res.json({
      code: 200,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新文档段落失败', error);
    return res.status(500).json({
      code: 500,
      message: '更新文档段落失败',
      error: error.message
    });
  }
};

// 创建文档段落
const createDocumentSegments = async (req, res) => {
  try {
    const { id } = req.params; // 文档ID
    const { segments } = req.body;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    if (!segments || !Array.isArray(segments) || segments.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请提供有效的段落数据'
      });
    }

    // 从数据库查询文档信息
    const document = await KnowledgeBase.findOne({
      where: {
        id,
        deleted: false
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    // 检查文档是否有Dify文档ID
    if (!document.document_id) {
      return res.status(400).json({
        code: 400,
        message: '文档未关联Dify资源'
      });
    }

    // 获取Dify API URL和数据集ID
    const difyUrl = process.env.DIFY_URL || 'https://cankao-sa-server.out.xmsxb.com';
    const datasetId = process.env.DATASET_ID;

    if (!datasetId) {
      return res.status(400).json({
        code: 400,
        message: '环境配置缺少数据集ID'
      });
    }

    // 调用Dify API创建段落
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${document.document_id}/segments`;

    const response = await axios.post(apiUrl, { segments }, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    // 异步为新增的段落生成题目
    setTimeout(async () => {
      try {
        console.log('开始为新增段落生成题目...');

        // 获取新添加的段落ID列表
        let newSegmentIds = [];
        let newSegments = [];

        console.log('新增段落响应数据:', response.data);

        // 从响应中提取段落ID信息
        if (response.data && response.data.data && Array.isArray(response.data.data)) {
          newSegmentIds = response.data.data.map(seg => seg.id);
          newSegments = response.data.data.map(seg => ({
            id: seg.id,
            content: seg.content || ''
          }));
        } else if (response.data && Array.isArray(response.data)) {
          newSegmentIds = response.data.map(seg => seg.id);
          newSegments = response.data.map(seg => ({
            id: seg.id,
            content: seg.content || ''
          }));
        }

        if (newSegments.length === 0) {
          console.log('没有找到有效的新增段落信息，无法生成题目');
          return;
        }

        console.log(`识别到${newSegments.length}个新段落，准备生成题目`);

        // 根据内容长度将段落分组，每组内容总长度不超过1000字
        const groupedSegments = [];
        let currentGroup = [];
        let currentGroupLength = 0;

        for (let i = 0; i < newSegments.length; i++) {
          const segment = newSegments[i];
          const contentLength = segment.content ? segment.content.length : 0;

          // 如果当前组为空或者添加后不超过1000字，则添加到当前组
          if (currentGroup.length === 0 || currentGroupLength + contentLength <= 1000) {
            currentGroup.push(segment);
            currentGroupLength += contentLength;
          } else {
            // 当前组已满，将其添加到分组结果中，并创建新组
            groupedSegments.push([...currentGroup]);
            currentGroup = [segment];
            currentGroupLength = contentLength;
          }
        }

        // 添加最后一组（如果有）
        if (currentGroup.length > 0) {
          groupedSegments.push(currentGroup);
        }

        console.log(`将新段落分为${groupedSegments.length}组进行处理`);

        // 处理每组段落，生成题目
        const allQuestions = [];

        // 获取文档名称和出题提示词
        const documentName = document.fileName || '';

        for (let groupIndex = 0; groupIndex < groupedSegments.length; groupIndex++) {
          const group = groupedSegments[groupIndex];
          console.log(`处理第${groupIndex + 1}组数据，包含${group.length}个段落`);

          // 准备API输入参数
          const inputs = {
            "segment": JSON.stringify(group),
            "document_name": documentName
          };

          // 如果有出题提示词，则添加到输入参数中
          if (document.prompt) {
            inputs.prom = document.prompt;
          }

          // 调用生成题目API
          const apiUrls = `${difyUrl}/api/agent/workflow/run/${enterpriseId}/AGENT-EXAM`;
          const response = await axios.post(apiUrls, {
            "inputs": inputs
          }, {
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            },
            timeout: 300000
          });

          // 处理响应，提取题目
          if (response.data && response.data.success && response.data.data && response.data.data.outputs) {
            try {
              // 获取输出的问题字符串
              const questionStr = response.data.data.outputs.question;

              // 移除Markdown代码块标记
              let jsonStr = questionStr;
              if (jsonStr.startsWith('```json\n')) {
                jsonStr = jsonStr.substring('```json\n'.length);
              }
              if (jsonStr.endsWith('\n```')) {
                jsonStr = jsonStr.substring(0, jsonStr.length - 4);
              }

              // 解析JSON字符串为数组
              const questionsArray = JSON.parse(jsonStr);

              console.log(`成功为第${groupIndex + 1}组生成${questionsArray.length}个题目`);

              // 将当前组的题目添加到总题目数组中
              if (Array.isArray(questionsArray)) {
                allQuestions.push(...questionsArray);
              }
            } catch (error) {
              console.error(`解析第${groupIndex + 1}组题目失败:`, error);
            }
          }
        }

        // 保存所有生成的题目
        if (allQuestions.length > 0) {
          const saveResult = await saveGeneratedQuestions(id, allQuestions);
          if (saveResult.success) {
            console.log(`成功为新段落保存${saveResult.count}个题目`);
          } else {
            console.error('保存题目失败:', saveResult.error);
          }
        } else {
          console.log('没有生成任何题目');
        }
      } catch (error) {
        console.error('为新增段落生成题目失败:', error);
      }
    }, 3000); // 延迟3秒执行，确保段落已成功创建

    // 返回创建成功的段落
    return res.json({
      code: 200,
      message: '创建成功',
      data: response.data
    });
  } catch (error) {
    console.error('创建文档段落失败', error);
    return res.status(500).json({
      code: 500,
      message: '创建文档段落失败',
      error: error.message
    });
  }
};

// 删除Dify文档
const deleteDifyDocument = async (req, res) => {
  try {
    const { id } = req.params; // 文档ID

    // 从数据库查询文档信息
    const document = await KnowledgeBase.findOne({
      where: {
        id,
        deleted: false
      }
    });

    if (!document) {
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    // 检查文档是否有Dify文档ID
    if (!document.document_id) {
      // 如果没有关联Dify文档，只需要标记本地文档为已删除
      await document.update({ deleted: true });
      return res.json({
        code: 200,
        message: '删除成功'
      });
    }

    // 获取DIFY API URL和数据集ID
    const difyUrl = process.env.DIFY_URL || 'https://cankao-sa-server.out.xmsxb.com';
    const datasetId = process.env.DATASET_ID;

    if (!datasetId) {
      return res.status(400).json({
        code: 400,
        message: '环境配置缺少数据集ID'
      });
    }

    // 调用DIFY API删除文档
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${document.document_id}`;

    await axios.delete(apiUrl, {
      headers: {
        'Accept': 'application/json'
      }
    });

    // 更新本地文档为已删除
    await document.update({ deleted: true });

    // 返回成功信息
    return res.json({
      code: 200,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除Dify文档失败', error);
    return res.status(500).json({
      code: 500,
      message: '删除Dify文档失败',
      error: error.message
    });
  }
};

// 获取知识库文档段落（DIFY接口调用）
exports.getDocumentSegments = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查知识库是否存在，添加企业ID过滤
    const knowledge = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    // 这里应该调用DIFY API获取文档段落
    // 暂时使用假数据替代
    const mockSegments = [
      { id: 'seg_001', content: '这是第一个段落内容', position: 1 },
      { id: 'seg_002', content: '这是第二个段落内容', position: 2 },
      { id: 'seg_003', content: '这是第三个段落内容', position: 3 },
      { id: 'seg_004', content: '这是第四个段落内容', position: 4 },
      { id: 'seg_005', content: '这是第五个段落内容', position: 5 }
    ];

    // 更新知识库中的段落数据 - 直接使用新数组
    await knowledge.update({
      segments: mockSegments
    });

    res.json({
      code: 200,
      message: '获取文档段落成功',
      data: mockSegments
    });
  } catch (error) {
    console.error('获取文档段落失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取文档段落失败',
      error: error.message
    });
  }
};

// 智能出题核心逻辑，参数为文档id
const generateQuestionsForDocument = async (documentId, dataIds,documentName) => {
  try {
    const difyUrl = process.env.DIFY_URL;
    const datasetId = process.env.DATASET_ID;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    if (!datasetId) {
      return { success: false, error: '环境配置缺少数据集ID' };
    }

    // 调用DIFY API获取段落
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${documentId}/segments`;

    const response = await axios.get(apiUrl, {
      headers: {
        'Accept': 'application/json'
      }
    });

    // 正确获取段落数组，考虑不同的响应结构可能性
    let segmentse = [];
    if (response.data && Array.isArray(response.data)) {
      segmentse = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      segmentse = response.data.data;
    } else if (response.data && response.data.segments && Array.isArray(response.data.segments)) {
      segmentse = response.data.segments;
    }

    if (!Array.isArray(segmentse) || segmentse.length === 0) {
      return { success: false, error: '未获取到有效段落数据' };
    }

    // 提取段落数据，只保留id和content属性
    const extractedSegments = segmentse.map(segment => ({
      id: segment.id,
      content: segment.content
    }));

    // 根据内容长度将段落分组，每组内容总长度不超过500字
    const groupedSegments = [];
    let currentGroup = [];
    let currentGroupLength = 0;

    for (let i = 0; i < extractedSegments.length; i++) {
      const segment = extractedSegments[i];
      const contentLength = segment.content ? segment.content.length : 0;

      if (currentGroup.length === 0 || currentGroupLength + contentLength <= 500) {
        currentGroup.push(segment);
        currentGroupLength += contentLength;
      } else {
        groupedSegments.push([...currentGroup]);
        currentGroup = [segment];
        currentGroupLength = contentLength;
      }
    }

    if (currentGroup.length > 0) {
      groupedSegments.push(currentGroup);
    }

    // 创建总题目数组
    const allQuestions = [];

    for (let groupIndex = 0; groupIndex < groupedSegments.length; groupIndex++) {
      const group = groupedSegments[groupIndex];

      try {
        // 提取outputs中的question字符串
        const apiUrls = `${difyUrl}/api/agent/workflow/run/${enterpriseId}/AGENT-EXAM`;
        const response = await axios.post(apiUrls, {
          "inputs": {
            "segment": JSON.stringify(group),
            "document_name": documentName
          },
          stream: true
        }, {
          headers: {
            'Accept': 'text/event-stream',
            'Content-Type': 'application/json'
          },
          responseType: 'stream',
        });

        // 处理响应，提取题目

        // 处理流式响应
        let questionsArray = [];

        let completeText = ''; // 用于拼接所有text_chunk的内容

        const questions = await new Promise((resolve, reject) => {
          response.data.on('data', (chunk) => {
            try {
              const chunkStr = chunk.toString();
              const lines = chunkStr.split('\n');
              
              for (const line of lines) {
                if (line.trim() && line.startsWith('data: ')) {
                  let jsonStr = line.slice(6);
                  let eventData;
                  
                  // 处理JSON断裂的情况
                  
                  eventData = safeJsonParse(jsonStr);
                  if (!eventData) {
                      console.error('JSON解析失败:', jsonStr);
                      continue; // 跳过这行，继续处理下一行
                  }
                  
                  try {
                    // 只处理 workflow_finished 事件

                    if (eventData.event === 'text_chunk' && eventData.data && eventData.data.text) {
                      completeText += eventData.data.text;
                    }
                    
                  } catch (jsonParseError) {
                    // 忽略JSON解析错误，继续处理下一行
                  }
                }
              }
            } catch (error) {
              console.error(`处理第${groupIndex + 1}组流数据失败:`, error);
            }
          });
          
          response.data.on('end', () => {
            if (completeText && questionsArray.length === 0) {
              try {
                let questionStr = completeText;
                
                // 清理文本内容
                if (questionStr.startsWith('```json\n')) {
                  questionStr = questionStr.substring('```json\n'.length);
                }
                if (questionStr.endsWith('\n```')) {
                  questionStr = questionStr.substring(0, questionStr.length - 4);
                }
                
                // 去除多余的空白字符
                questionStr = questionStr.trim();
                
                if (questionStr) {
                  try {
                    // 解析JSON内容
                    const parsedQuestions = JSON.parse(questionStr);
                    
                    if (Array.isArray(parsedQuestions)) {
                      questionsArray = parsedQuestions;
                      console.log(`成功为第${groupIndex + 1}组从拼接文本生成${parsedQuestions.length}个题目`);
                    }
                  } catch (parseError) {
                    console.error(`解析第${groupIndex + 1}组拼接文本JSON失败:`, parseError);
                    console.error('原始文本:', questionStr);
                  }
                }
              } catch (error) {
                console.error(`处理第${groupIndex + 1}组拼接文本失败:`, error);
              }
            }
            resolve(questionsArray);
          });
          
          response.data.on('error', (error) => {
            console.error(`第${groupIndex + 1}组流请求错误:`, error);
            reject(error);
          });
        });

        // 将当前组的题目添加到总题目数组中
        if (Array.isArray(questions) && questions.length > 0) {
          allQuestions.push(...questions);
        }

      } catch (groupError) {
        console.error(`处理第${groupIndex + 1}组段落出错:`, groupError);
        // 继续处理下一组段落
      }
    }

    // 保存题目
    if (allQuestions.length > 0) {
      const saveResult = await saveGeneratedQuestions(dataIds, allQuestions);
      if (saveResult.success) {
        return { success: true, count: saveResult.count };
      } else {
        return { success: false, error: '保存题目失败: ' + saveResult.error };
      }
    } else {
      return { success: false, error: '未能生成有效题目' };
    }

  } catch (error) {
    console.error('智能出题失败:', error);
    return { success: false, error: '智能出题失败: ' + error.message };
  }
};

// 根据文档段落智能出题 - API控制器方法
exports.generateQuestions = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await generateQuestionsForDocument(id);

    if (result.success) {
      res.json({
        code: result.code,
        message: result.message,
        data: result.data
      });
    } else {
      res.status(result.code).json({
        code: result.code,
        message: result.message,
        error: result.error
      });
    }
  } catch (error) {
    console.error('智能出题接口失败:', error);
    res.status(500).json({
      code: 500,
      message: '智能出题接口失败',
      error: error.message
    });
  }
};

// 获取知识库题目列表
const getQuestionList = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查知识库是否存在，添加企业ID过滤
    const knowledge = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    // 从题目表中获取题目列表
    const questions = await KBQuestions.findAll({
      where: {
        knowledge_base_id: id,
        enterprise_id: enterpriseId
      },
      order: [['created_at', 'DESC']]
    });

    res.json({
      code: 200,
      message: '获取题目列表成功',
      data: questions
    });
  } catch (error) {
    console.error('获取题目列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取题目列表失败',
      error: error.message
    });
  }
};

// 添加固定题目
const addQuestion = async (req, res) => {
  try {
    const { id } = req.params;
    const { question, answer } = req.body;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查知识库是否存在，添加企业ID过滤
    const knowledge = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    // 创建新题目记录
    const newQuestion = await KBQuestions.create({
      id: uuidv4(),
      knowledge_base_id: id,
      segment_id: null, // 固定题目没有分段ID
      question,
      answer,
      type: '人工出题',
      enterprise_id: enterpriseId
    });

    res.json({
      code: 200,
      message: '添加题目成功',
      data: newQuestion
    });
  } catch (error) {
    console.error('添加题目失败:', error);
    res.status(500).json({
      code: 500,
      message: '添加题目失败',
      error: error.message
    });
  }
};

// 更新题目
const updateQuestion = async (req, res) => {
  try {
    const { id } = req.params;
    const { questionId, question, answer } = req.body;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查知识库是否存在，添加企业ID过滤
    const knowledge = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    // 查找要更新的题目记录
    const questionRecord = await KBQuestions.findOne({
      where: {
        id: questionId,
        knowledge_base_id: id,
        enterprise_id: enterpriseId
      }
    });

    if (!questionRecord) {
      return res.status(404).json({
        code: 404,
        message: '题目不存在'
      });
    }

    // 更新题目记录
    await questionRecord.update({
      question,
      answer,
      updated_at: new Date()
    });

    res.json({
      code: 200,
      message: '更新题目成功',
      data: questionRecord
    });
  } catch (error) {
    console.error('更新题目失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新题目失败',
      error: error.message
    });
  }
};

// 删除题目
const deleteQuestion = async (req, res) => {
  try {
    const { id, questionId } = req.params;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查知识库是否存在，添加企业ID过滤
    const knowledge = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    // 查找并删除题目记录
    const deletedCount = await KBQuestions.destroy({
      where: {
        id: questionId,
        knowledge_base_id: id,
        enterprise_id: enterpriseId
      }
    });

    if (deletedCount === 0) {
      return res.status(404).json({
        code: 404,
        message: '题目不存在'
      });
    }

    res.json({
      code: 200,
      message: '删除题目成功'
    });
  } catch (error) {
    console.error('删除题目失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除题目失败',
      error: error.message
    });
  }
};

// 批量保存题目 - 新方法，用于保存智能出题生成的题目
const batchSaveQuestions = async (req, res) => {
  try {
    const { id } = req.params;
    const { questions } = req.body;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查知识库是否存在
    const knowledge = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    if (!Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '题目数据不能为空'
      });
    }

    // 创建批量插入的数据
    const questionsToInsert = questions.map(q => ({
      id: q.id || uuidv4(),
      knowledge_base_id: id,
      segment_id: q.segmentId || null,
      question: q.quest || q.question,
      answer: q.answer,
      type: '智能出题',
      enterprise_id: enterpriseId,
      created_at: new Date(),
      updated_at: new Date()
    }));

    // 批量创建题目记录
    const createdQuestions = await KBQuestions.bulkCreate(questionsToInsert);

    res.json({
      code: 200,
      message: `成功保存${createdQuestions.length}个题目`,
      data: createdQuestions
    });
  } catch (error) {
    console.error('批量保存题目失败:', error);
    res.status(500).json({
      code: 500,
      message: '批量保存题目失败',
      error: error.message
    });
  }
};

// 处理allQuestions数组，将所有生成的题目保存到数据库
const saveGeneratedQuestions = async (knowledgeBaseId, questionsArray) => {
  try {
    if (!Array.isArray(questionsArray) || questionsArray.length === 0) {
      console.log('没有题目需要保存');
      return { success: false };
    }

    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 准备批量插入数据
    const questionsToInsert = questionsArray.map(q => ({
      id: uuidv4(),
      knowledge_base_id: knowledgeBaseId,
      segment_id: q.id,
      question: q.quest || q.question,
      answer: q.answer,
      type: '智能出题',
      enterprise_id: enterpriseId,
      created_at: new Date(),
      updated_at: new Date()
    }));

    // 批量创建题目记录
    const savedQuestions = await KBQuestions.bulkCreate(questionsToInsert);
    console.log(`成功保存${savedQuestions.length}个题目到数据库`);

    return {
      success: true,
      count: savedQuestions.length,
      data: savedQuestions
    };
  } catch (error) {
    console.error('保存生成题目失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 切换文档状态（启用/禁用）
const toggleDocumentStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body; // status: 'enable' 或 'disable'
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    console.log(`收到文档状态切换请求:`, {
      documentId: id,
      status,
      enterpriseId
    });

    // 验证status值
    if (status !== 'enable' && status !== 'disable') {
      return res.status(400).json({
        code: 400,
        message: '状态值无效，必须为enable或disable'
      });
    }

    // 查找文档
    const document = await KnowledgeBase.findOne({
      where: {
        id,
        enterpriseId,
        deleted: false
      }
    });

    if (!document) {
      console.log(`文档不存在:`, { id, enterpriseId });
      return res.status(404).json({
        code: 404,
        message: '文档不存在'
      });
    }

    console.log(`找到文档:`, {
      id: document.id,
      name: document.name,
      document_id: document.document_id,
      currentStatus: document.status
    });

    // 检查是否有DIFY文档ID
    if (!document.document_id) {
      console.log(`文档未关联DIFY资源:`, { id: document.id });
      return res.status(400).json({
        code: 400,
        message: '文档未关联DIFY资源，无法切换状态'
      });
    }

    // 获取当前状态，如果为null则视为'enable'
    const currentStatus = document.status === null ? 'enable' : document.status;

    console.log(`当前状态处理:`, {
      rawStatus: document.status,
      processedStatus: currentStatus,
      newStatus: status
    });

    // 如果新状态与当前状态相同，直接返回成功
    if (status === currentStatus) {
      console.log(`状态未发生变化，跳过API调用:`, {
        currentStatus,
        newStatus: status
      });

      return res.json({
        code: 200,
        message: status === 'enable' ? '文档已启用' : '文档已禁用',
        data: { status }
      });
    }

    // 获取DIFY API URL和数据集ID
    const difyUrl = process.env.DIFY_URL || 'https://cankao-sa-server.out.xmsxb.com';
    const datasetId = process.env.DATASET_ID;

    if (!datasetId) {
      return res.status(400).json({
        code: 400,
        message: '环境配置缺少数据集ID'
      });
    }

    // 构建API URL - 直接使用status值，无需转换
    const action = status;
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${document.document_id}/status/${action}`;

    console.log(`正在调用文档状态切换API: ${apiUrl}`);

    // 调用DIFY API切换状态
    const response = await axios.patch(apiUrl, {}, {
      headers: {
        'Accept': 'application/json'
      }
    });

    // 打印API响应内容
    console.log('文档状态切换API响应:', {
      status: response.status,
      statusText: response.statusText,
      data: JSON.stringify(response.data),
      headers: response.headers ? 'Headers存在' : 'Headers不存在'
    });

    // 更新本地文档状态
    await document.update({ status });

    console.log(`文档状态已更新:`, {
      documentId: document.id,
      oldStatus: currentStatus,
      newStatus: status
    });

    res.json({
      code: 200,
      message: status === 'enable' ? '文档已启用' : '文档已禁用',
      data: { status }
    });
  } catch (error) {
    console.error('切换文档状态失败', error);
    res.status(500).json({
      code: 500,
      message: '切换文档状态失败',
      error: error.message
    });
  }
};

// 获取文档上传状态接口
const getDocumentProcessStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 首先从数据库获取文档状态
    const document = await KnowledgeBase.findOne({
      where: {
        id,
        enterpriseId,
        deleted: false
      },
      attributes: ['id', 'processStatus', 'processProgress', 'processMessage', 'updatedTime']
    });

    // 如果文档不存在
    if (!document) {
      return res.json({
        code: 200,
        message: '未找到文档',
        data: null
      });
    }

    // 如果文档状态已完成或失败，直接返回数据库中的状态
    if (document.processStatus === 'completed' || document.processStatus === 'failed') {
      return res.json({
        code: 200,
        message: '获取文档处理状态成功',
        data: {
          id: document.id,
          status: document.processStatus,
          progress: document.processProgress,
          message: document.processMessage,
          updatedAt: document.updatedTime,
          fromDatabase: true
        }
      });
    }

    // 对于未完成的文档，尝试从Redis获取更详细的状态
    const redisStatus = await getDocumentStatus(id);

    if (redisStatus) {
      // 如果Redis有状态数据，并且是完成或失败状态，同步更新到数据库
      if (redisStatus.status === 'completed' || redisStatus.status === 'failed') {
        try {
          await KnowledgeBase.update({
            processStatus: redisStatus.status,
            processProgress: redisStatus.progress,
            processMessage: redisStatus.message,
            updatedTime: new Date()
          }, {
            where: { id }
          });
        } catch (dbError) {
          console.error(`同步Redis状态到数据库失败:`, dbError);
        }
      }

      // 返回Redis中的状态
      return res.json({
        code: 200,
        message: '获取文档处理状态成功',
        data: redisStatus
      });
    }

    // 如果Redis中没有状态，返回数据库状态
    return res.json({
      code: 200,
      message: '获取文档处理状态成功',
      data: {
        id: document.id,
        status: document.processStatus,
        progress: document.processProgress,
        message: document.processMessage,
        updatedAt: document.updatedTime,
        fromDatabase: true
      }
    });
  } catch (error) {
    console.error('获取文档处理状态失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取文档处理状态失败',
      error: error.message
    });
  }
};

// 获取文档的出题策略提示词
const getPromptStrategy = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查知识库是否存在
    const knowledge = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    // 返回提示词，如果不存在则返回null
    res.json({
      code: 200,
      data: {prompt: knowledge.prompt}
    });
  } catch (error) {
    console.error('获取出题策略失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取出题策略失败',
      error: error.message
    });
  }
};

// 保存文档的出题策略提示词并生成新题目
const savePromptStrategy = async (req, res) => {
  try {
    const { id } = req.params;
    const { prompt } = req.body;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    if (!prompt || typeof prompt !== 'string') {
      return res.status(400).json({
        code: 400,
        message: '提示词不能为空且必须是字符串'
      });
    }

    // 检查知识库是否存在
    const knowledge = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    if (knowledge.processStatus === DOC_STATUS.INDEXING) {
      // 检查处理消息是否包含出题相关的字符
      const isGeneratingQuestions = knowledge.processMessage && 
        (knowledge.processMessage.includes('出题中') || 
         knowledge.processMessage.includes('生成题目') || 
         knowledge.processMessage.includes('策略生成'));
      
      if (isGeneratingQuestions) {
        return res.status(409).json({
          code: 409,
          message: '文档正在出题中，请稍后再试',
          data: {
            id: knowledge.id,
            status: knowledge.processStatus,
            progress: knowledge.processProgress,
            message: knowledge.processMessage
          }
        });
      }
    }

    // 保存提示词到知识库
    knowledge.prompt = prompt;
    await knowledge.save();
    console.log(`成功保存提示词到知识库(ID: ${id})`);

    // 删除旧的题目
    await KBQuestions.destroy({
      where: {
        knowledge_base_id: id,
        enterprise_id: enterpriseId
      }
    });

    // 更新处理状态
    await setDocumentStatus(id, DOC_STATUS.INDEXING, 70, '文档出题中');

    // 获取文档段落
    const segmentsResult = await retryOperation(
      // 操作函数
      async () => {
        const difyUrl = process.env.DIFY_URL;
        const datasetId = process.env.DATASET_ID;
        const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${knowledge.document_id}/segments`;


        const response = await axios.get(apiUrl, {
          headers: { 'Accept': 'application/json' },
          timeout: 10000
        });

        console.log('获取文档段落成功:', response.data);

        let segments = [];
        if (response.data && Array.isArray(response.data)) {
          segments = response.data;
        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
          segments = response.data.data;
        } else if (response.data && response.data.segments && Array.isArray(response.data.segments)) {
          segments = response.data.segments;
        }

        return segments;
      },
      // 检查函数：确保段落数组非空
      (segments) => Array.isArray(segments) && segments.length > 0
    );

    if (!segmentsResult.success) {
      await setDocumentStatus(id, DOC_STATUS.FAILED, 60, '获取文档段落失败: ' + segmentsResult.error);
      console.error(`获取文档段落失败: ${segmentsResult.error}`);
      return res.status(500).json({
        code: 500,
        message: '获取文档段落失败',
        error: segmentsResult.error
      });
    }

    // 使用提示词生成问题
    // 将文档处理放入后台，不阻塞API响应
    generateQuestionsWithPrompt(id, knowledge.document_id, prompt, knowledge.fileName);

    // 返回成功消息，告知前端题目正在生成中
    res.json({
      code: 200,
      message: '出题策略已保存，题目正在生成中',
      data: {
        id: knowledge.id,
        prompt: knowledge.prompt
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '保存出题策略失败',
      error: error.message
    });
  }
};

// 后台生成题目的函数
const generateQuestionsWithPrompt = async (knowledgeId, documentId, prompt, documentName) => {
  try {
    // 设置文档状态为生成题目中
    await setDocumentStatus(knowledgeId, DOC_STATUS.INDEXING, 70, '使用新策略生成题目中');

    // 从DIFY获取文档段落
    const difyUrl = process.env.DIFY_URL;
    const datasetId = process.env.DATASET_ID;
    const apiUrl = `${difyUrl}/api/documents/datasets/${datasetId}/documents/${documentId}/segments`;

    const response = await axios.get(apiUrl, {
      headers: { 'Accept': 'application/json' },
      timeout: 10000
    });

    // 提取段落数据
    let segments = [];
    if (response.data && Array.isArray(response.data)) {
      segments = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      segments = response.data.data;
    } else if (response.data && response.data.segments && Array.isArray(response.data.segments)) {
      segments = response.data.segments;
    }

    if (!segments || segments.length === 0) {
      await setDocumentStatus(knowledgeId, DOC_STATUS.FAILED, 75, '文档没有段落内容');
      return;
    }

    // 提取段落数据，只保留id和content属性
    const extractedSegments = segments.map(segment => ({
      id: segment.id,
      content: segment.content
    }));

    const allQuestions = [];
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

    // 根据内容长度将段落分组，每组内容总长度不超过500字
    const groupedSegments = [];
    let currentGroup = [];
    let currentGroupLength = 0;

    for (let i = 0; i < extractedSegments.length; i++) {
      const segment = extractedSegments[i];
      const contentLength = segment.content ? segment.content.length : 0;

      if (currentGroup.length === 0 || currentGroupLength + contentLength <= 500) {
        currentGroup.push(segment);
        currentGroupLength += contentLength;
      } else {
        groupedSegments.push([...currentGroup]);
        currentGroup = [segment];
        currentGroupLength = contentLength;
      }
    }

    if (currentGroup.length > 0) {
      groupedSegments.push(currentGroup);
    }

    await setDocumentStatus(knowledgeId, DOC_STATUS.INDEXING, 80, `开始生成题目，共${groupedSegments.length}组段落`);

    // 处理每组段落，生成题目
    for (let groupIndex = 0; groupIndex < groupedSegments.length; groupIndex++) {
      const group = groupedSegments[groupIndex];
      console.log(`处理第${groupIndex + 1}组数据，包含${group.length}个段落`);

      // 更新状态
      const progress = 80 + Math.floor((groupIndex / groupedSegments.length) * 20);
      await setDocumentStatus(
        knowledgeId,
        DOC_STATUS.INDEXING,
        progress,
        `正在为第${groupIndex + 1}/${groupedSegments.length}组段落生成题目`
      );

      try {
        // 调用生成题目API，使用自定义提示词
        const apiUrls = `${difyUrl}/api/agent/workflow/run/${enterpriseId}/AGENT-EXAM`;
        const response = await axios.post(apiUrls, {
          "inputs": {
            "segment": JSON.stringify(group),
            "prom": prompt,
            "document_name": documentName || "文档"
          },
          stream: true
        }, {
          headers: {
            'Accept': 'text/event-stream',
            'Content-Type': 'application/json'
          },
          responseType: 'stream',
        });

        // 处理响应，提取题目

        // 处理流式响应
        let questionsArray = [];

        let completeText = ''; // 用于拼接所有text_chunk的内容

        const questions = await new Promise((resolve, reject) => {
          response.data.on('data', (chunk) => {
            try {
              const chunkStr = chunk.toString();
              const lines = chunkStr.split('\n');
              
              for (const line of lines) {
                if (line.trim() && line.startsWith('data: ')) {
                  let jsonStr = line.slice(6);
                  let eventData;
                  
                  eventData = safeJsonParse(jsonStr);
                  if (!eventData) {
                      console.error('JSON解析失败:', jsonStr);
                      continue; // 跳过这行，继续处理下一行
                  }
                  
                  try {
                    
                    // 只处理 workflow_finished 事件

                    if (eventData.event === 'text_chunk' && eventData.data && eventData.data.text) {
                      completeText += eventData.data.text;
                    }
                    
                  } catch (jsonParseError) {
                    // 忽略JSON解析错误，继续处理下一行
                  }
                }
              }
            } catch (error) {
              console.error(`处理第${groupIndex + 1}组流数据失败:`, error);
            }
          });
          
          response.data.on('end', () => {
            if (completeText && questionsArray.length === 0) {
              try {
                let questionStr = completeText;
                
                // 清理文本内容
                if (questionStr.startsWith('```json\n')) {
                  questionStr = questionStr.substring('```json\n'.length);
                }
                if (questionStr.endsWith('\n```')) {
                  questionStr = questionStr.substring(0, questionStr.length - 4);
                }
                
                // 去除多余的空白字符
                questionStr = questionStr.trim();
                
                if (questionStr) {
                  try {
                    // 解析JSON内容
                    const parsedQuestions = JSON.parse(questionStr);
                    
                    if (Array.isArray(parsedQuestions)) {
                      questionsArray = parsedQuestions;
                      console.log(`成功为第${groupIndex + 1}组从拼接文本生成${parsedQuestions.length}个题目`);
                    }
                  } catch (parseError) {
                    console.error(`解析第${groupIndex + 1}组拼接文本JSON失败:`, parseError);
                    console.error('原始文本:', questionStr);
                  }
                }
              } catch (error) {
                console.error(`处理第${groupIndex + 1}组拼接文本失败:`, error);
              }
            }
            resolve(questionsArray);
          });
          
          response.data.on('error', (error) => {
            console.error(`第${groupIndex + 1}组流请求错误:`, error);
            reject(error);
          });
        });

        // 将当前组的题目添加到总题目数组中
        if (Array.isArray(questions) && questions.length > 0) {
          allQuestions.push(...questions);
        }
      } catch (error) {
        console.error(`为第${groupIndex + 1}组生成题目失败:`, error);
        // 继续处理下一组，不中断整个过程
      }
    }

    // 保存所有生成的题目
    if (allQuestions.length > 0) {
      const saveResult = await saveGeneratedQuestions(knowledgeId, allQuestions);
      if (saveResult.success) {
        await setDocumentStatus(
          knowledgeId,
          DOC_STATUS.COMPLETED,
          100,
          `完成题目生成，共生成${saveResult.count}个题目`
        );
      } else {
        await setDocumentStatus(
          knowledgeId,
          DOC_STATUS.FAILED,
          90,
          '保存题目失败: ' + saveResult.error
        );
      }
    } else {
      await setDocumentStatus(knowledgeId, DOC_STATUS.FAILED, 90, '未能生成有效题目');
    }
  } catch (error) {
    console.error('使用自定义策略生成题目失败:', error);
    await setDocumentStatus(knowledgeId, DOC_STATUS.FAILED, 90, '生成题目失败: ' + error.message);
  }
};

module.exports = {
  getKnowledgeBaseList,
  uploadKnowledgeFile,
  batchUploadFiles,
  batchSaveDocuments,
  getKnowledgeDetail,
  updateKnowledgeDocument,
  deleteKnowledgeDocument,
  downloadKnowledgeFile,
  getDocumentSegments,
  createDocumentSegments,
  updateDocumentSegment,
  deleteDocumentSegment,
  deleteDifyDocument,
  getQuestionList,
  addQuestion,
  updateQuestion,
  deleteQuestion,
  batchSaveQuestions,
  generateQuestionsWithPrompt,
  generateQuestionsForDocument,
  toggleDocumentStatus,
  getDocumentProcessStatus,
  getPromptStrategy,
  savePromptStrategy
};
