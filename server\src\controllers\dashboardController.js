const {KnowledgeBase, Employee, ExamReviewApplication} = require('../models');
const {Op} = require('sequelize');
const {DEFAULT_ENTERPRISE_ID, addEnterpriseFilter} = require('../utils/enterpriseFilter');
const PracticeRecord = require('../models/practice-record');
const sequelize = require('../config/database');
const CertificateRecord = require('../models/CertificateRecord');
const ExamConfig = require('../models/ExamConfigModel');
const EmployeePosition = require('../models/EmployeePosition');
const PositionName = require('../models/PositionName');
const Level = require('../models/Level');
const ExamRecord = require('../models/ExamRecord');
const moment = require('moment');

// 获取仪表盘概览数据
exports.getDashboardOverview = async (req, res) => {
    try {
        // 从环境变量获取企业ID
        const enterpriseId = DEFAULT_ENTERPRISE_ID;

        // 查询知识库总数
        const knowledgeBaseCount = await KnowledgeBase.count({
            where: {
                enterpriseId,
                deleted: false
            }
        });

        // 查询员工总数
        const employeeCount = await Employee.count({
            where: {
                enterpriseId,
                status: '1'
            }
        });

        // 查询待审核考试申请总数
        const pendingExamCount = await ExamReviewApplication.count({
            where: {
                enterpriseId,
                status: '1',
                delFlag: 0
            }
        });

        // 查询待确认成绩申请总数
        const pendingCertificateCount = await ExamReviewApplication.count({
            where: {
                enterpriseId,
                scoreConfirmStatus: '1',
                delFlag: 0
            }
        });

        // 整合数据返回
        res.json({
            code: 200,
            message: '获取仪表盘概览数据成功',
            data: {
                employeeCount,
                knowledgeBaseCount,
                pendingExamCount,
                pendingCertificateCount
            }
        });
    } catch (error) {
        console.error('获取仪表盘概览数据失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取仪表盘概览数据失败',
            error: error.message
        });
    }
};

// 获取练习排行数据
exports.getPracticeRanking = async (req, res) => {
    try {
        // 从环境变量获取企业ID
        const enterpriseId = DEFAULT_ENTERPRISE_ID;

        // 获取时间周期参数
        const {periodType = 'monthly'} = req.query;

        // 根据时间周期确定查询的时间范围
        let startDate = new Date();
        const endDate = new Date();

        switch (periodType) {
            case 'daily':
                startDate.setHours(0, 0, 0, 0);
                break;
            case 'weekly':
                const dayOfWeek = startDate.getDay();
                const diff = startDate.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
                startDate = new Date(startDate.setDate(diff));
                startDate.setHours(0, 0, 0, 0);
                break;
            case 'monthly':
                startDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                break;
            case 'quarterly':
                const quarter = Math.floor(startDate.getMonth() / 3);
                startDate = new Date(startDate.getFullYear(), quarter * 3, 1);
                break;
            case 'yearly':
                startDate = new Date(startDate.getFullYear(), 0, 1);
                break;
            default:
                startDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
        }

        console.log("查询时间范围:", startDate, "至", endDate);

        // 查询练习记录，按openId分组，计算总时长和题目总数
        const rankingData = await PracticeRecord.findAll({
            attributes: [
                ['open_id', 'openId'],
                [sequelize.fn('SUM', sequelize.cast(sequelize.col('total_duration'), 'INTEGER')), 'totalPracticeTime'],
                [sequelize.fn('SUM', sequelize.col('question_num')), 'totalQuestions'],
                [sequelize.fn('COUNT', sequelize.col('practice_record.id')), 'practiceCount']
            ],
            include: [
                {
                    model: Employee,
                    as: 'employee',
                    attributes: ['name', 'gender', 'departmentId', 'positionId', 'levelId', 'openId'],
                    required: true,
                    include: [
                        {
                            model: sequelize.models.Department,
                            as: 'department',
                            attributes: ['name'],
                            required: false
                        },
                        {
                            model: sequelize.models.PositionName,
                            as: 'positionName',
                            attributes: ['name'],
                            required: false
                        },
                        {
                            model: sequelize.models.Level,
                            as: 'level',
                            attributes: ['name'],
                            required: false
                        }
                    ]
                }
            ],
            where: {
                enterpriseId,
                createTime: {
                    [Op.between]: [startDate, endDate]
                }
            },
            group: [
                'practice_record.open_id',
                'employee.id',
                'employee.department.id',
                'employee.positionName.id',
                'employee.level.id'
            ],
            order: [[sequelize.literal('totalPracticeTime'), 'DESC']],
            limit: 10
        });

        // 格式化返回数据
        const formattedRanking = rankingData.map((item, index) => {
            const employee = item.employee || {};
            const department = employee.department || {};
            const position = employee.positionName || {};
            const level = employee.level || {};

            return {
                rank: index + 1,
                name: employee.name || '',
                openId: item.openId,
                department: department.name || '',
                position: position.name || '',
                level: level.name || '',
                practiceCount: parseInt(item.dataValues.practiceCount) || 0,
                practiceTime: parseInt(item.dataValues.totalPracticeTime) || 0,
                totalQuestions: parseInt(item.dataValues.totalQuestions) || 0,
                avatar: '' // 暂不处理头像，如果需要可以从微信API获取或者员工表中获取
            };
        });

        // 返回数据
        res.json({
            code: 200,
            message: '获取练习排行数据成功',
            data: formattedRanking
        });
    } catch (error) {
        console.error('获取练习排行数据失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取练习排行数据失败',
            error: error.message
        });
    }
};

// 获取证书排行榜数据
exports.getCertificateRanking = async (req, res) => {
    try {
        // 获取请求参数
        const {limit = 10, enterpriseId: reqEnterpriseId} = req.query;
        const enterpriseId = reqEnterpriseId || DEFAULT_ENTERPRISE_ID;

        // 根据openId分组查询证书数量
        const certificateRankingData = await CertificateRecord.findAll(addEnterpriseFilter({
            attributes: [
                'openId',
                [sequelize.fn('COUNT', sequelize.col('certificate_records.id')), 'count']
            ],
            where: {
                openId: {
                    [Op.ne]: null // 确保openId不为空
                },
                delFlag: 0 // 未删除的记录
            },
            group: ['openId'],
            order: [[sequelize.literal('count'), 'DESC']],
            limit: parseInt(limit)
        }, enterpriseId));

        // 处理结果并获取员工信息
        const result = await Promise.all(certificateRankingData.map(async (item) => {
            // 根据openId查询员工信息
            const employee = await Employee.findOne(addEnterpriseFilter({
                where: {
                    openId: item.openId
                },
                include: [
                    {
                        model: PositionName,
                        as: 'positionName',
                        attributes: ['name'],
                        required: false
                    },
                    {
                        model: Level,
                        as: 'level',
                        attributes: ['name'],
                        required: false
                    }
                ]
            }, enterpriseId));

            if (!employee) {
                return null;
            }

            const position = employee.positionName || {};
            const level = employee.level || {};

            // 获取员工当前岗位的所有必考科目数量
            const requiredExamCount = await ExamConfig.count(addEnterpriseFilter({
                where: {
                    positionName: String(employee.positionId),
                    status: '必考'
                }
            }, enterpriseId));

            // 计算进度
            const certificateCount = parseInt(item.dataValues.count) || 0;
            const progress = requiredExamCount > 0
                ? Math.min(100, Math.round((certificateCount / requiredExamCount) * 100))
                : 0;

            return {
                name: employee.name || '',
                position: position.name || '',
                level: level.name || '',
                count: certificateCount,
                progress: progress
            };
        }));

        // 过滤掉null值并确保结果有序
        const filteredResult = result.filter(item => item !== null);

        res.json({
            code: 200,
            msg: 'success',
            data: filteredResult
        });
    } catch (error) {
        console.error('获取证书排行榜数据失败:', error);
        res.status(500).json({
            code: 500,
            msg: '获取证书排行榜数据失败',
            error: error.message
        });
    }
};

// 获取科目练习和考试统计数据
exports.getSubjectExamStats = async (req, res) => {
    try {
        const {enterpriseId: reqEnterpriseId} = req.query;
        const enterpriseId = reqEnterpriseId || DEFAULT_ENTERPRISE_ID;

        // 使用Promise.all并行查询多个数据
        const [examConfigsData, practiceRecordsData, examRecordsData] = await Promise.all([
            // 1. 查询ExamConfig获取所有必练数据（优化字段筛选）
            fetchExamConfigs(enterpriseId),
            
            // 2. 查询练习记录（优化字段筛选）
            fetchPracticeRecords(enterpriseId),
            
            // 3. 查询考试记录，用于计算通过率（使用优化的数据库查询）
            fetchExamRecords(enterpriseId)
        ]);
        
        // 创建科目基础映射
        const subjectMap = createSubjectMap(examConfigsData);
        
        // 处理练习记录和参与人数
        const { subjectParticipantsMap } = processPracticeRecords(practiceRecordsData, subjectMap);
        
        // 处理考试记录
        processExamRecords(examRecordsData, subjectMap);
        
        // 处理学生排名
        processTopStudentRankings(practiceRecordsData, subjectMap);
        
        // 计算平均值
        calculateAverages(subjectMap, subjectParticipantsMap);
        
        // 转换为结果数组并排序
        const resultData = formatResults(subjectMap);

        return res.json({
            code: 200,
            msg: 'success',
            data: resultData
        });
    } catch (error) {
        const errorMsg = '获取科目练习和考试统计数据失败';
        
        // 增强错误处理
        if (error.name === 'SequelizeConnectionError') {
            console.error(`${errorMsg} - 数据库连接错误:`, error.message);
            return res.status(503).json({
                code: 503,
                msg: '数据库服务暂时不可用',
                error: '请稍后再试'
            });
        } else if (error.name === 'SequelizeValidationError') {
            console.error(`${errorMsg} - 数据验证错误:`, error.message);
            return res.status(400).json({
                code: 400,
                msg: '请求数据验证失败',
                error: error.message
            });
        }
        
        console.error(`${errorMsg}:`, error);
        return res.status(500).json({
            code: 500,
            msg: errorMsg,
            error: error.message
        });
    }
};

// 查询ExamConfig数据的辅助函数
async function fetchExamConfigs(enterpriseId) {
    return await ExamConfig.findAll(addEnterpriseFilter({
        attributes: ['examSubject', 'positionName', 'positionLevel'],
        where: {
            status: '必考'
        },
        include: [
            {
                model: KnowledgeBase,
                as: 'knowledgeBase',
                attributes: ['name'],
                required: false
            },
            {
                model: Level,
                as: 'positionLevelData',
                attributes: ['name'],
                required: false
            }
        ],
    }, enterpriseId));
}

// 查询练习记录的辅助函数
async function fetchPracticeRecords(enterpriseId) {
    return await PracticeRecord.findAll(addEnterpriseFilter({
        attributes: [
            'openId', 'examSubject', 'positionName', 'positionLevel',
            'totalDuration', 'questionNum'
        ],
        where: {
            status: '必考'
        },
        include: [
            {
                model: Employee,
                as: 'employee',
                attributes: ['name'],
                required: false
            }
        ]
    }, enterpriseId));
}

// 查询考试记录的辅助函数
async function fetchExamRecords(enterpriseId) {
    // 使用优化的SQL查询来获取考试统计数据
    const examStatsQuery = `
        SELECT 
            kb_id as examSubject,
            position_id as positionId,
            level_id as levelId,
            COUNT(*) as examTotal,
            SUM(CASE WHEN score >= pass_score AND confirm_status = '2' THEN 1 ELSE 0 END) as examPassed
        FROM exam_records
        WHERE enterprise_id = :enterpriseId AND del_flag = 0
        GROUP BY kb_id, position_id, level_id
    `;

    try {
        return await sequelize.query(examStatsQuery, {
            replacements: { enterpriseId },
            type: sequelize.QueryTypes.SELECT
        });
    } catch (error) {
        // 如果聚合查询失败，回退到常规查询方式
        return await ExamRecord.findAll(addEnterpriseFilter({
            attributes: [
                'examSubject', 'positionId', 'levelId',
                'score', 'passScore', 'confirmStatus', 'kbId'
            ]
        }, enterpriseId));
    }
}

// 创建科目基础映射的辅助函数
function createSubjectMap(examConfigs) {
    const subjectMap = new Map();
    
    examConfigs.forEach(config => {
        const examSubject = config.examSubject;
        const positionName = config.positionName;
        const positionLevel = config.positionLevel;
        const knowledgeBaseName = config.knowledgeBase?.name || '未知科目';
        const levelName = config.positionLevelData?.name || '未知等级';

        // 创建唯一键
        const uniqueKey = `${examSubject}_${positionName}_${positionLevel}`;

        if (!subjectMap.has(uniqueKey)) {
            subjectMap.set(uniqueKey, {
                examSubject,
                positionName,
                positionLevel,
                name: knowledgeBaseName,
                level: levelName,
                avgDuration: 0,
                avgCount: 0,
                topStudents: [],
                examTotal: 0,
                examPassed: 0,
                passRate: 0
            });
        }
    });
    
    return subjectMap;
}

// 定义转换时间格式为分钟的函数
function convertToMinutes(timeString) {
    if (!timeString) return 0;
    const [minutes, seconds] = timeString.split(':').map(Number);
    // 将秒转换为分钟的小数部分，然后四舍五入到整数分钟
    return Math.round(minutes + seconds / 60);
}

// 处理练习记录数据的辅助函数
function processPracticeRecords(practiceRecords, subjectMap) {
    // 记录每个科目的唯一参与人数
    const subjectParticipantsMap = new Map();

    practiceRecords.forEach(record => {
        const examSubject = record.examSubject;
        const positionName = record.positionName;
        const positionLevel = record.positionLevel;
        const openId = record.openId;
        const timeString = record.totalDuration || '0:0';
        const duration = convertToMinutes(timeString);
        const questionCount = record.questionNum || 0;

        // 创建与ExamConfig相同的唯一键
        const uniqueKey = `${examSubject}_${positionName}_${positionLevel}`;

        // 如果科目不存在于subjectMap中，跳过这条数据
        if (!subjectMap.has(uniqueKey)) return;

        // 累加科目的总时长和总题数
        const subjectData = subjectMap.get(uniqueKey);
        subjectData.avgDuration += duration;
        subjectData.avgCount += questionCount;

        // 记录每个科目的唯一参与人数
        if (!subjectParticipantsMap.has(uniqueKey)) {
            subjectParticipantsMap.set(uniqueKey, new Set());
        }
        subjectParticipantsMap.get(uniqueKey).add(openId);
    });

    return { subjectParticipantsMap };
}

// 处理考试记录数据的辅助函数
function processExamRecords(examRecords, subjectMap) {
    // 检查是否为优化的聚合查询结果
    const isAggregatedResult = examRecords.length > 0 && 'examTotal' in examRecords[0];
    
    if (isAggregatedResult) {
        // 处理优化的聚合查询结果
        examRecords.forEach(record => {
            const uniqueKey = `${record.examSubject}_${record.positionId}_${record.levelId}`;
            
            if (subjectMap.has(uniqueKey)) {
                const subjectData = subjectMap.get(uniqueKey);
                subjectData.examTotal = parseInt(record.examTotal) || 0;
                subjectData.examPassed = parseInt(record.examPassed) || 0;
                
                // 计算通过率
                if (subjectData.examTotal > 0) {
                    subjectData.passRate = parseFloat(((subjectData.examPassed / subjectData.examTotal) * 100).toFixed(2));
                }
            }
        });
    } else {
        // 回退处理：处理未聚合的原始考试记录
        const examStatsMap = new Map();
        
        // 处理考试记录数据，计算每个科目的通过率
        examRecords.forEach(record => {
            const examSubject = record.kbId;
            const positionId = record.positionId;
            const levelId = record.levelId;
            const uniqueKey = `${examSubject}_${positionId}_${levelId}`;

            // 如果这个科目还没有统计数据，初始化它
            if (!examStatsMap.has(uniqueKey)) {
                examStatsMap.set(uniqueKey, {
                    examTotal: 0,
                    examPassed: 0
                });
            }

            // 获取当前科目的统计数据
            const statsData = examStatsMap.get(uniqueKey);
            statsData.examTotal += 1;

            // 判断是否通过：score >= passScore 且 confirmStatus = '2'
            const score = parseFloat(record.score) || 0;
            const passScore = parseFloat(record.passScore) || 0;
            const confirmStatus = record.confirmStatus;

            if (score >= passScore && confirmStatus === '2') {
                statsData.examPassed += 1;
            }
        });

        // 将计算好的考试统计数据合并到subjectMap中
        examStatsMap.forEach((statsData, uniqueKey) => {
            if (subjectMap.has(uniqueKey)) {
                const subjectData = subjectMap.get(uniqueKey);
                subjectData.examTotal = statsData.examTotal;
                subjectData.examPassed = statsData.examPassed;
                
                // 计算通过率
                if (statsData.examTotal > 0) {
                    subjectData.passRate = parseFloat(((statsData.examPassed / statsData.examTotal) * 100).toFixed(2));
                }
            }
        });
    }
    
    // 确保所有科目都有通过率
    subjectMap.forEach((subjectData) => {
        if (subjectData.examTotal === undefined) {
            subjectData.examTotal = 0;
            subjectData.examPassed = 0;
            subjectData.passRate = 0;
        }
    });
}

// 处理学生排名的辅助函数
function processTopStudentRankings(practiceRecords, subjectMap) {
    // 优化：直接按科目分组处理学生数据
    const studentsBySubject = new Map();
    
    practiceRecords.forEach(record => {
        const examSubject = record.examSubject;
        const positionName = record.positionName;
        const positionLevel = record.positionLevel;
        const uniqueKey = `${examSubject}_${positionName}_${positionLevel}`;
        const employeeName = record.employee?.name || '未知用户';
        const duration = convertToMinutes(record.totalDuration || '0:0');
        
        if (!subjectMap.has(uniqueKey)) return;
        
        if (!studentsBySubject.has(uniqueKey)) {
            studentsBySubject.set(uniqueKey, []);
        }
        
        // 查找是否已存在该学生
        const existingStudentIndex = studentsBySubject.get(uniqueKey)
            .findIndex(student => student.name === employeeName);
            
        if (existingStudentIndex === -1) {
            // 添加新学生
            studentsBySubject.get(uniqueKey).push({ name: employeeName, duration });
        } else {
            // 累加已存在学生的时长
            studentsBySubject.get(uniqueKey)[existingStudentIndex].duration += duration;
        }
    });
    
    // 计算每个科目的前三名
    studentsBySubject.forEach((students, uniqueKey) => {
        if (!subjectMap.has(uniqueKey)) return;
        
        // 过滤、排序并取前三名
        const validStudents = students.filter(student => student.duration > 0);
        validStudents.sort((a, b) => b.duration - a.duration);
        subjectMap.get(uniqueKey).topStudents = validStudents.slice(0, 3);
    });
}

// 计算平均值的辅助函数
function calculateAverages(subjectMap, subjectParticipantsMap) {
    subjectMap.forEach((subjectData, uniqueKey) => {
        const participants = subjectParticipantsMap.get(uniqueKey);
        const participantCount = participants ? participants.size : 0;

        // 存储原始总数和计算平均值
        if (participantCount > 0) {
            subjectData.totalDuration = subjectData.avgDuration;
            subjectData.totalCount = subjectData.avgCount;
            
            subjectData.avgDuration = Math.round(subjectData.avgDuration / participantCount);
            subjectData.avgCount = Math.round(subjectData.avgCount / participantCount);
            
            subjectData.participantCount = participantCount;
        } else {
            subjectData.totalDuration = 0;
            subjectData.totalCount = 0;
            subjectData.avgDuration = 0;
            subjectData.avgCount = 0;
            subjectData.participantCount = 0;
        }
    });
}

// 格式化结果的辅助函数
function formatResults(subjectMap) {
    // 转换为结果数组
    const resultData = Array.from(subjectMap.values()).map(item => {
        return {
            name: item.name,
            level: item.level,
            totalDuration: item.totalDuration,
            totalCount: item.totalCount,
            avgDuration: item.avgDuration,
            avgCount: item.avgCount,
            participantCount: item.participantCount,
            topStudents: item.topStudents,
            examTotal: item.examTotal,
            examPassed: item.examPassed,
            passRate: item.passRate
        };
    });

    // 按照每个科目前三名学生的数量从大到小排序
    return resultData.sort((a, b) => b.topStudents.length - a.topStudents.length);
}

// 获取考试统计数据
exports.getExamStatistics = async (req, res) => {
    try {
        // 从环境变量获取企业ID
        const enterpriseId = DEFAULT_ENTERPRISE_ID;

        // 获取时间范围参数
        const {startDate, endDate, periodType = 'monthly'} = req.query;

        // 构建基础查询条件
        let where = {
            enterpriseId,
            delFlag: 0
        };

        // 确定查询的时间范围
        let startMoment, endMoment;

        // 如果提供了时间范围，直接使用
        if (startDate && endDate) {
            startMoment = moment(startDate);
            endMoment = moment(endDate);
        } else {
            // 根据不同的时间周期设置查询范围
            const now = moment();
            switch (periodType) {
                case 'daily':
                    // 查询最近30天的每日数据
                    startMoment = now.clone().subtract(30, 'days');
                    endMoment = now.clone();
                    break;
                case 'weekly':
                    // 查询最近12周的每周数据
                    startMoment = now.clone().subtract(12, 'weeks');
                    endMoment = now.clone();
                    break;
                case 'monthly':
                    // 返回从本月1号到今天的每日数据
                    startMoment = now.clone().startOf('month');
                    endMoment = now.clone();
                    break;
                case 'quarterly':
                    // 返回从第一季度到当前季度的每个季度数据
                    startMoment = now.clone().startOf('year');
                    endMoment = now.clone();
                    break;
                case 'yearly':
                    // 返回从1月到本月的每月数据
                    startMoment = now.clone().startOf('year');
                    endMoment = now.clone();
                    break;
                default:
                    // 默认查询最近12个月的每月数据
                    startMoment = now.clone().subtract(12, 'months');
                    endMoment = now.clone();
            }
        }

        // 添加时间过滤条件
        where.examTime = {
            [Op.between]: [startMoment.toDate(), endMoment.toDate()]
        };

        console.log("查询时间范围:", startMoment.format('YYYY-MM-DD'), "至", endMoment.format('YYYY-MM-DD'));

        // 1. 查询考试记录总数
        const totalExamCount = await ExamRecord.count({where});

        // 2. 查询考试通过数量（confirmStatus为'2'且score >= passScore）
        const passedExamCount = await ExamRecord.count({
            where: {
                ...where,
                confirmStatus: '2',
                [Op.and]: [
                    sequelize.where(
                        sequelize.col('score'),
                        Op.gte,
                        sequelize.col('pass_score')
                    )
                ]
            }
        });

        // 3. 计算考试通过率
        const passRate = totalExamCount > 0 ? ((passedExamCount / totalExamCount) * 100).toFixed(2) : 0;

        // 4. 获取考试通过率走势数据
        let trendData = [];

        // 根据时间周期确定分组方式
        let groupBy = '';

        switch (periodType) {
            case 'daily':
                groupBy = 'DATE(exam_time)';
                break;
            case 'weekly':
                groupBy = 'YEARWEEK(exam_time)';
                break;
            case 'monthly':
                groupBy = 'DATE(exam_time)';
                break;
            case 'quarterly':
                groupBy = 'CONCAT(YEAR(exam_time), "-Q", QUARTER(exam_time))';
                break;
            case 'yearly':
                groupBy = 'DATE_FORMAT(exam_time, "%Y-%m")';
                break;
            default:
                groupBy = 'DATE_FORMAT(exam_time, "%Y-%m")';
        }

        // 查询走势数据
        const trendQuery = `
            SELECT ${groupBy}                                                                    as period,
                   COUNT(*)                                                                      as total_count,
                   SUM(CASE WHEN confirm_status = '2' AND score >= pass_score THEN 1 ELSE 0 END) as passed_count,
                   ROUND(
                           (SUM(CASE WHEN confirm_status = '2' AND score >= pass_score THEN 1 ELSE 0 END) / COUNT(*)) *
                           100,
                           2
                   )                                                                             as pass_rate
            FROM exam_records
            WHERE enterprise_id = :enterpriseId
              AND del_flag = 0
              AND exam_time BETWEEN :startDate AND :endDate
            GROUP BY ${groupBy}
            ORDER BY period ASC
        `;

        const trendResults = await sequelize.query(trendQuery, {
            replacements: {
                enterpriseId,
                startDate: startMoment.format('YYYY-MM-DD'),
                endDate: endMoment.format('YYYY-MM-DD')
            },
            type: sequelize.QueryTypes.SELECT
        });

        // 生成完整的时间段列表
        const generatePeriodList = (start, end, type) => {
            const periods = [];
            const current = start.clone();

            while (current.isSameOrBefore(end)) {
                let periodKey;
                switch (type) {
                    case 'daily':
                    case 'monthly': // monthly 也是按日返回
                        periodKey = current.format('YYYY-MM-DD');
                        current.add(1, 'day');
                        break;
                    case 'weekly':
                        // 使用与 YEARWEEK() 函数相同的格式
                        periodKey = current.format('YYYYWW');
                        current.add(1, 'week');
                        break;
                    case 'quarterly':
                        const quarter = Math.ceil((current.month() + 1) / 3);
                        periodKey = `${current.year()}-Q${quarter}`;
                        current.add(1, 'quarter');
                        break;
                    case 'yearly':
                        periodKey = current.format('YYYY-MM');
                        current.add(1, 'month');
                        break;
                    default:
                        periodKey = current.format('YYYY-MM');
                        current.add(1, 'month');
                }
                periods.push(periodKey);
            }
            return periods;
        };

        // 生成完整的时间段列表
        const allPeriods = generatePeriodList(startMoment, endMoment, periodType);

        // 将查询结果转换为Map，便于查找
        const resultMap = new Map();
        trendResults.forEach(item => {
            resultMap.set(item.period, {
                totalCount: parseInt(item.total_count) || 0,
                passedCount: parseInt(item.passed_count) || 0,
                passRate: parseFloat(item.pass_rate) || 0
            });
        });

        // 格式化走势数据，确保所有时间段都有数据
        trendData = allPeriods.map(period => ({
            period: period,
            totalCount: resultMap.has(period) ? resultMap.get(period).totalCount : 0,
            passedCount: resultMap.has(period) ? resultMap.get(period).passedCount : 0,
            passRate: resultMap.has(period) ? resultMap.get(period).passRate : 0
        }));

        // 返回结果
        res.json({
            code: 200,
            message: '获取考试统计数据成功',
            data: {
                summary: {
                    totalExamCount,
                    passedExamCount,
                    passRate: parseFloat(passRate)
                },
                trend: trendData
            }
        });

    } catch (error) {
        console.error('获取考试统计数据失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取考试统计数据失败',
            error: error.message
        });
    }
};

// 获取岗位通过率统计数据
exports.getPositionPassRates = async (req, res) => {
    try {
        // 从环境变量获取企业ID
        const enterpriseId = DEFAULT_ENTERPRISE_ID;

        // 获取时间范围参数
        const {periodType = 'monthly'} = req.query;

        // 构建基础查询条件
        let where = {
            enterpriseId,
            delFlag: 0
        };

        // 根据时间周期设置查询范围
        const now = moment();
        let startMoment, endMoment;

        switch (periodType) {
            case 'monthly':
                // 返回从本月1号到今天的数据
                startMoment = now.clone().startOf('month');
                endMoment = now.clone();
                break;
            case 'quarterly':
                // 返回从第一季度到当前季度的数据
                startMoment = now.clone().startOf('year');
                endMoment = now.clone();
                break;
            case 'yearly':
                // 返回从1月到本月的数据
                startMoment = now.clone().startOf('year');
                endMoment = now.clone();
                break;
            default:
                // 默认查询最近12个月的数据
                startMoment = now.clone().subtract(12, 'months');
                endMoment = now.clone();
        }

        // 添加时间过滤条件
        where.examTime = {
            [Op.between]: [startMoment.toDate(), endMoment.toDate()]
        };

        // 1. 查询所有岗位
        const positionNames = await PositionName.findAll(addEnterpriseFilter({
            attributes: ['id', 'name'],
            where: {
                status: true // 只查询状态正常的岗位
            }
        }, enterpriseId));

        // 2. 根据岗位ID查询考试记录并统计通过率
        const examRecords = await ExamRecord.findAll(addEnterpriseFilter({
            attributes: [
                'positionId',
                'score',
                'passScore',
                'confirmStatus'
            ],
            where
        }, enterpriseId));

        // 3. 创建岗位通过率的Map
        const positionPassRateMap = new Map();

        // 初始化每个岗位的统计数据
        positionNames.forEach(position => {
            positionPassRateMap.set(position.id, {
                position: position.name,
                totalCount: 0,
                passedCount: 0,
                passRate: 0
            });
        });

        // 4. 统计各岗位的考试通过情况
        examRecords.forEach(record => {
            const positionId = record.positionId;
            if (!positionId || !positionPassRateMap.has(positionId)) {
                return; // 跳过没有岗位ID或岗位不存在的记录
            }

            const positionData = positionPassRateMap.get(positionId);
            positionData.totalCount += 1;

            // 判断是否通过：score >= passScore 且 confirmStatus = '2'
            const score = parseFloat(record.score) || 0;
            const passScore = parseFloat(record.passScore) || 0;
            const confirmStatus = record.confirmStatus;

            if (score >= passScore && confirmStatus === '2') {
                positionData.passedCount += 1;
            }
        });

        // 5. 计算每个岗位的通过率
        const positionRates = [];
        positionPassRateMap.forEach(data => {
            // 计算通过率，如果总考试数为0，则设为0
            if (data.totalCount > 0) {
                data.passRate = parseFloat(((data.passedCount / data.totalCount) * 100).toFixed(2));
            } else {
                data.passRate = 0;
            }

            // 添加到结果数组中，只保留需要的字段
            positionRates.push({
                position: data.position,
                passRate: data.passRate
            });
        });

        // 按通过率降序排序
        positionRates.sort((a, b) => b.passRate - a.passRate);

        res.json({
            code: 200,
            msg: 'success',
            data: {
                positionRates
            }
        });

    } catch (error) {
        console.error('获取岗位通过率统计失败:', error);
        res.status(500).json({
            code: 500,
            msg: '获取岗位通过率统计失败',
            error: error.message
        });
    }
};
