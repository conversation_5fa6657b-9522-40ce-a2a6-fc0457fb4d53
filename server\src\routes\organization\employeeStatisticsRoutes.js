const express = require('express');
const router = express.Router();
const { 
  getEmployeeStatistics, 
  getEmployeePositionStatistics, 
  exportEmployeeStatistics,
  getEmployeeOtherPositionStatistics
} = require('../../controllers/organization/employeeStatisticsController');

// 获取员工统计数据
router.get('/', getEmployeeStatistics);

// 导出员工统计数据 (需要在参数路由之前)
router.get('/export', exportEmployeeStatistics);

// 获取员工职位统计数据
router.get('/:employeeId/positions', getEmployeePositionStatistics);

// 获取员工其他岗位统计数据
router.get('/:employeeId/other-positions', getEmployeeOtherPositionStatistics);

module.exports = router; 