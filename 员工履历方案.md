-- 创建员工履历表
CREATE TABLE `employee_career_record` (
`id` int NOT NULL AUTO_INCREMENT COMMENT '履历ID',
`employee_id` int NOT NULL COMMENT '员工ID',
`open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'open_id',
`enterprise_id` varchar(255) NOT NULL COMMENT '企业ID',
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态（1在职 0离职）',
`entry_time` date DEFAULT NULL COMMENT '入职时间',
`departure_time` datetime DEFAULT NULL COMMENT '离职时间',
`departure_reason` varchar(500) DEFAULT NULL COMMENT '离职原因',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
`update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
PRIMARY KEY (`id`),
KEY `idx_employee_id` (`employee_id`),
KEY `idx_enterprise_id` (`enterprise_id`),
KEY `idx_status` (`status`),
CONSTRAINT `fk_career_employee` FOREIGN KEY (`employee_id`) REFERENCES `org_employee` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='员工履历记录表';


-- 为所有相关表添加履历ID和活跃状态字段

-- 1. 练习记录详情表
ALTER TABLE `practice_record_detail`
ADD COLUMN `career_record_id` int DEFAULT NULL COMMENT '履历ID' AFTER `practice_record_id`,
ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）',
ADD INDEX `idx_prd_career_record` (`career_record_id`),
ADD INDEX `idx_prd_is_active` (`is_active`),
ADD CONSTRAINT `fk_prd_career` FOREIGN KEY (`career_record_id`) REFERENCES `employee_career_record` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 2. 练习记录表
ALTER TABLE `practice_record`
ADD COLUMN `career_record_id` int DEFAULT NULL COMMENT '履历ID' AFTER `enterprise_id`,
ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）',
ADD INDEX `idx_pr_career_record` (`career_record_id`),
ADD INDEX `idx_pr_is_active` (`is_active`),
ADD CONSTRAINT `fk_pr_career` FOREIGN KEY (`career_record_id`) REFERENCES `employee_career_record` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `practice_record`
ADD COLUMN `position_belong_name` varchar(100) DEFAULT NULL COMMENT '岗位归属名称' AFTER `position_belong`,
ADD COLUMN `position_name_cn` varchar(100) DEFAULT NULL COMMENT '岗位名称' AFTER `position_name`,
ADD COLUMN `position_level_name` varchar(100) DEFAULT NULL COMMENT '岗位等级名称' AFTER `position_level`,
ADD COLUMN `exam_subject_name` varchar(100) DEFAULT NULL COMMENT '练习科目名称' AFTER `exam_subject`;

-- 3. 考试记录表
ALTER TABLE `exam_records`
ADD COLUMN `career_record_id` int DEFAULT NULL COMMENT '履历ID' AFTER `enterprise_id`,
ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）',
ADD INDEX `idx_er_career_record` (`career_record_id`),
ADD INDEX `idx_er_is_active` (`is_active`),
ADD CONSTRAINT `fk_er_career` FOREIGN KEY (`career_record_id`) REFERENCES `employee_career_record` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `exam_records`
ADD COLUMN `position_belong_name` varchar(100) DEFAULT NULL COMMENT '岗位归属名称' AFTER `position_belong_id`,
ADD COLUMN `position_name_cn` varchar(100) DEFAULT NULL COMMENT '岗位名称' AFTER `position_id`,
ADD COLUMN `position_level_name` varchar(100) DEFAULT NULL COMMENT '岗位等级名称' AFTER `level_id`;

-- 4. 考试审核申请表
ALTER TABLE `exam_review_applications`
ADD COLUMN `career_record_id` int DEFAULT NULL COMMENT '履历ID' AFTER `enterprise_id`,
ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）',
ADD INDEX `idx_era_career_record` (`career_record_id`),
ADD INDEX `idx_era_is_active` (`is_active`),
ADD CONSTRAINT `fk_era_career` FOREIGN KEY (`career_record_id`) REFERENCES `employee_career_record` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 5. 证书记录表
ALTER TABLE `certificate_records`
ADD COLUMN `career_record_id` int DEFAULT NULL COMMENT '履历ID' AFTER `enterprise_id`,
ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）',
ADD INDEX `idx_cr_career_record` (`career_record_id`),
ADD INDEX `idx_cr_is_active` (`is_active`),
ADD CONSTRAINT `fk_cr_career` FOREIGN KEY (`career_record_id`) REFERENCES `employee_career_record` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `certificate_records`
ADD COLUMN `position_belong_name` varchar(100) DEFAULT NULL COMMENT '岗位归属名称' AFTER `position_belong`,
ADD COLUMN `position_name_cn` varchar(100) DEFAULT NULL COMMENT '岗位名称' AFTER `position_name`,
ADD COLUMN `position_level_name` varchar(100) DEFAULT NULL COMMENT '岗位等级名称' AFTER `position_level`,
ADD COLUMN `exam_subject_name` varchar(100) DEFAULT NULL COMMENT '练习科目名称' AFTER `kb_id`;

-- 6. 用户成就表
ALTER TABLE `user_achievements`
ADD COLUMN `career_record_id` int DEFAULT NULL COMMENT '履历ID' AFTER `enterprise_id`,
ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）',
ADD INDEX `idx_ua_career_record` (`career_record_id`),
ADD INDEX `idx_ua_is_active` (`is_active`),
ADD CONSTRAINT `fk_ua_career` FOREIGN KEY (`career_record_id`) REFERENCES `employee_career_record` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 7. 员工岗位关联表
ALTER TABLE `org_employee_position`
ADD COLUMN `career_record_id` int DEFAULT NULL COMMENT '履历ID' AFTER `enterprise_id`,
ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）',
ADD INDEX `idx_oep_career_record` (`career_record_id`),
ADD INDEX `idx_oep_is_active` (`is_active`),
ADD CONSTRAINT `fk_oep_career` FOREIGN KEY (`career_record_id`) REFERENCES `employee_career_record` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `org_employee_position`
ADD COLUMN `position_belong_name` varchar(100) DEFAULT NULL COMMENT '岗位归属名称' AFTER `position_type_id`,
ADD COLUMN `position_name_cn` varchar(100) DEFAULT NULL COMMENT '岗位名称' AFTER `position_id`,
ADD COLUMN `position_level_name` varchar(100) DEFAULT NULL COMMENT '岗位等级名称' AFTER `level_id`;

-- 8. 员工晋升表
ALTER TABLE `org_employee_promotion`
ADD COLUMN `career_record_id` int DEFAULT NULL COMMENT '履历ID' AFTER `enterprise_id`,
ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）',
ADD INDEX `idx_oepr_career_record` (`career_record_id`),
ADD INDEX `idx_oepr_is_active` (`is_active`),
ADD CONSTRAINT `fk_oepr_career` FOREIGN KEY (`career_record_id`) REFERENCES `employee_career_record` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `org_employee_promotion`
ADD COLUMN `position_belong_name` varchar(100) DEFAULT NULL COMMENT '岗位归属名称' AFTER `position_type_id`,
ADD COLUMN `position_name_cn` varchar(100) DEFAULT NULL COMMENT '岗位名称' AFTER `position_id`,
ADD COLUMN `position_level_name` varchar(100) DEFAULT NULL COMMENT '岗位等级名称' AFTER `level_id`;

### 任务1
输入：岗位归属从org_position_type获取，岗位名称从org_position_name获取，等级名称从org_level获取，科目名称从kb_knowledge_base获取。
practice_record 增加position_belong_name（岗位归属名称）、position_name_cn（岗位名称）、position_level_name(等级名称)、exam_subject_name(练习科目名称)
exam_records 增加position_belong_name(岗位归属名称，通过category_id获取)、position_name_cn(岗位名称)、position_level_name(等级名称)
certificate_records 增加position_belong_name(岗位归属名称)、position_name_cn(岗位名称)、position_level_name(等级名称)、exam_subject_name(练习科目名称)
org_employee_position 增加position_belong_name(岗位归属名称)、position_name_cn(岗位名称)、position_level_name(等级名称)
org_employee_promotion 增加position_belong_name(岗位归属名称)、position_name_cn(岗位名称)、position_level_name(等级名称)


### 任务2
输入：离职接口：/api/organization/employee，传参：{"id":69,"status":"0"}，id为员工id。status 0 代表离职,
输出：1、需要把employee_career_record、practice_record_detail、practice_record、exam_records、exam_review_applications、certificate_records、user_achievements、org_employee_position、org_employee_promotion这8个表有关该员工的记录is_active都变成0，且employee_career_record  也要变成离职,employee_career_record 的entry_time 填写org_employee表的时间。
2、任务1中的所有名称全部从相关表中获取并填写上相关名称。
3、以上所有的表该员工的员工履历id为空的，加上employee_career_record的当前的id
4、如果离职时，该员工一条都没有employee_career_record记录，则新建一条
### 任务3
输入：在职接口：/api/organization/employee，传参：{"id":69,"status":"1"}，id为员工id。status 1 代表在职，
输出：employee_career_record 新增一条记录。
### 任务4
输入：员工申请通过接口：/api/organization/employee/application/{员工id}/audit，参数为：{"id":18,"auditStatus":"通过","auditRemark":"","currentUser":{"id":1,"username":"admin"}}。
输出：当auditStatus为通过时，则employee_career_record 新增一条记录。





