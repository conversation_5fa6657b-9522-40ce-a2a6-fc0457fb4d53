const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// 确保上传目录存在
const createUploadDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  return dir;
};

// 配置上传存储
const createStorage = (customPath) => {
  return multer.diskStorage({
    destination: function (req, file, cb) {
      // 如果customPath不为空，则使用customPath替代knowledge-base
      const basePath = customPath ? customPath : 'knowledge-base';
      const uploadDir = createUploadDir(`./uploads/${basePath}`);
      cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
      // 处理中文文件名问题
      // 确保文件名是UTF-8编码
      let originalname = file.originalname;
      
      // 如果是Buffer对象，转为字符串
      if (Buffer.isBuffer(originalname)) {
        originalname = originalname.toString('utf8');
      } 
      // 如果是字符串但可能是latin1编码，转换为utf8
      else if (typeof originalname === 'string') {
        try {
          // 尝试检测是否需要转换编码
          // 如果中文被编码为latin1，转换回utf8后再解码会导致错误
          const testDecode = decodeURIComponent(escape(originalname));
          // 如果解码成功但与原字符串不同，说明需要转换
          if (testDecode !== originalname && /[\u4e00-\u9fa5]/.test(testDecode)) {
            originalname = testDecode;
          }
        } catch (e) {
          // 如果解码失败，尝试直接从latin1转换
          originalname = Buffer.from(originalname, 'latin1').toString('utf8');
        }
      }
      
      // 从原始文件名获取扩展名
      const ext = path.extname(originalname);
      // 获取无扩展名的原始文件名
      const originalNameNoExt = path.basename(originalname, ext);
      
      // 获取正确的上传路径
      const basePath = customPath ? customPath : 'knowledge-base';
      const filePath = path.join(`./uploads/${basePath}`, originalname);
      
      if (fs.existsSync(filePath)) {
        // 文件已存在，添加唯一ID
        const uniqueId = uuidv4().substring(0, 8);
        const newFilename = `${originalNameNoExt}_${uniqueId}${ext}`;
        cb(null, newFilename);
      } else {
        // 文件不存在，直接使用原始文件名
        cb(null, originalname);
      }
    }
  });
};

// 默认存储配置
const storage = createStorage(null);

// 文件过滤器
const fileFilter = (req, file, cb) => {
  console.log('文件上传类型检查:', file.mimetype, file.originalname); // 添加日志
  
  // 允许的文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'image/jpeg',
    'image/jpg', // 添加image/jpg类型
    'image/png',
    'image/gif',
    'image/bmp',
    'text/plain',
    'text/markdown',
    'application/zip',
    'application/x-rar-compressed',
    'audio/mpeg',
    'video/mp4'
  ];
  
  // 检查文件扩展名，确保图片类型也能通过
  const ext = path.extname(file.originalname).toLowerCase().substring(1);
  const imgExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
  
  if (allowedTypes.includes(file.mimetype) || imgExts.includes(ext)) {
    cb(null, true);
  } else {
    console.log('文件类型被拒绝:', file.mimetype, ext);
    cb(new Error('不支持的文件类型'), false);
  }
};

// 配置上传限制
const limits = {
  fileSize: 50 * 1024 * 1024, // 最大50MB
};

// 创建multer实例
const createUpload = (customPath) => {
  return multer({ 
    storage: createStorage(customPath),
    fileFilter: fileFilter,
    limits: limits,
    preservePath: true // 添加此选项以保留文件名的路径信息
  });
};

// 默认上传实例
const upload = createUpload(null);

// 获取文件类型
const getFileType = (fileName) => {
  const ext = path.extname(fileName).toLowerCase().substring(1);
  
  // 文档类型
  if (ext === 'pdf') {
    return 'pdf';
  } else if (['doc', 'docx'].includes(ext)) {
    return 'word';
  } else if (['xls', 'xlsx'].includes(ext)) {
    return 'excel';
  } else if (['ppt', 'pptx'].includes(ext)) {
    return 'ppt';
  } 
  // 图片类型
  else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
    return 'image';
  } 
  // 文本类型
  else if (['txt', 'log', 'md', 'json', 'xml', 'html', 'css', 'js'].includes(ext)) {
    return 'text';
  }
  // 压缩文件
  else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return 'archive';
  }
  // 音视频文件
  else if (['mp3', 'wav', 'ogg', 'flac', 'aac'].includes(ext)) {
    return 'audio';
  }
  else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) {
    return 'video';
  }
  
  return 'unknown';
};

module.exports = {
  upload,
  createUpload,
  getFileType
}; 