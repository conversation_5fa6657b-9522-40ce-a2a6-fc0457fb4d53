const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 考试通用规则配置模型
 */
const ExamGlobalConfig = sequelize.define('ExamGlobalConfig', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  questionMode: {
    type: DataTypes.ENUM('题目', '题数'),
    allowNull: true,
    defaultValue: '题目',
    field: 'question_mode',
    comment: '只有必考才有，考试资格：时长/题数'
  },
  practiceDuration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 60,
    field: 'practice_duration',
    comment: '练习时长(分钟)'
  },
  practiceQuestionCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 20,
    field: 'practice_question_count',
    comment: '练习题目数量'
  },
  questionCount: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'question_count',
    comment: '考试题目数量'
  },
  examCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 3,
    field: 'exam_count',
    comment: '考试次数'
  },
  examDuration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 90,
    field: 'exam_duration',
    comment: '考试时长(分钟)'
  },

  needConfirmScore: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    field: 'need_confirm_score',
    comment: '是否需要确认成绩'
  },
  passScore: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 80,
    field: 'pass_score',
    comment: '通过标准(分)'
  },
  scoreReleaseRule: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: '自动发布',
    field: 'score_release_rule',
    comment: '成绩发布规则：自动发布/手动发布'
  },
  questionDistribution: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: '选择题70%，问答题30%',
    field: 'question_distribution',
    comment: '题目类型分布'
  },
  questionType: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: '单选,多选',
    field: 'question_type',
    comment: '试题类型，多个用逗号分隔'
  },
  difficultyLevel: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: '中级',
    field: 'difficulty_level',
    comment: '难度等级：初级/中级/高级'
  },
  certificateValidDays: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 90,
    field: 'certificate_valid_days',
    comment: '证书有效时间(天)'
  },
  manualQuestionRatio: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 50,
    field: 'manual_question_ratio',
    comment: '人工出题占比(总占比为100)'
  },
  intelligentQuestionRatio: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 50,
    field: 'intelligent_question_ratio',
    comment: '智能出题占比(总占比为100)'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 1,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by',
    comment: '创建者ID'
  },
  updatedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'updated_by',
    comment: '更新者ID'
  }
}, {
  tableName: 'exam_global_config',
  timestamps: true,
  createdAt: 'created_time',
  updatedAt: 'updated_time'
});

module.exports = ExamGlobalConfig;
