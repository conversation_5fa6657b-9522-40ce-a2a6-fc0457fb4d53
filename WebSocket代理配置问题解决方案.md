# WebSocket代理配置问题解决方案

## 问题现状确认
✅ nginx服务器可以访问后端：`curl -I http://***********:31490/` 返回200  
✅ 后端Express服务正常运行  
❌ WebSocket代理不工作

**结论：nginx的WebSocket代理配置有问题**

## 主要问题分析

### 1. 路径匹配问题
你的nginx配置：
```nginx
location /ws/ {
    proxy_pass http://***********:31490/;
    ...
}
```

但从你的后端代码来看，WebSocket可能监听在根路径 `/`，而不是 `/ws/`。

### 2. 后端WebSocket路径确认
从你的后端代码分析：
```javascript
// server/src/app.js
const wss = new WebSocket.Server({ server });
```

后端WebSocket服务监听在根路径，所以nginx应该这样配置。

## 修复方案

### 方案1：支持根路径WebSocket（推荐）

```nginx
server {
    listen 80;
    server_name cankao-admin-api.dev.lingmiaoai.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name cankao-admin-api.dev.lingmiaoai.com;

    ssl_certificate /etc/nginx/ssl/dev.lingmiaoai.crt;
    ssl_certificate_key /etc/nginx/ssl/dev.lingmiaoai.key;

    # 处理所有请求，自动识别WebSocket
    location / {
        proxy_pass http://***********:31490;
        proxy_http_version 1.1;
        
        # WebSocket升级处理
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $http_connection;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 60;
        
        # 重要：禁用缓冲
        proxy_buffering off;
    }
}
```

### 方案2：双路径支持

```nginx
server {
    listen 443 ssl;
    server_name cankao-admin-api.dev.lingmiaoai.com;

    ssl_certificate /etc/nginx/ssl/dev.lingmiaoai.crt;
    ssl_certificate_key /etc/nginx/ssl/dev.lingmiaoai.key;

    # WebSocket专用路径
    location /ws {
        proxy_pass http://***********:31490;  # 注意：去掉尾随斜杠
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_buffering off;
    }

    # 默认路径处理
    location / {
        proxy_pass http://***********:31490;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $http_connection;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_buffering off;
    }
}
```

## 关键修复点

### 1. Connection头处理
```nginx
# 原配置（可能有问题）
proxy_set_header Connection "upgrade";

# 改为（推荐）
proxy_set_header Connection $http_connection;
```

### 2. proxy_pass路径问题
```nginx
# 如果location是 /ws/，proxy_pass应该是：
proxy_pass http://***********:31490/;

# 如果location是 /ws，proxy_pass应该是：
proxy_pass http://***********:31490;
```

### 3. 必须添加的配置
```nginx
proxy_buffering off;  # 这个很重要！
proxy_connect_timeout 60;
```

## 立即测试步骤

### 1. 应用新配置
```bash
# 备份现有配置
cp /etc/nginx/sites-available/your-config /etc/nginx/sites-available/your-config.backup

# 应用方案1的配置
nginx -t  # 检查语法
nginx -s reload  # 重新加载
```

### 2. 测试不同路径
```javascript
// 测试1：根路径（最可能成功）
const ws1 = new WebSocket('wss://cankao-admin-api.dev.lingmiaoai.com/');

// 测试2：/ws路径
const ws2 = new WebSocket('wss://cankao-admin-api.dev.lingmiaoai.com/ws');

// 监听结果
[ws1, ws2].forEach((ws, index) => {
  ws.onopen = () => console.log(`连接${index + 1}成功！`);
  ws.onerror = (error) => console.log(`连接${index + 1}失败：`, error);
  ws.onclose = (event) => console.log(`连接${index + 1}关闭：`, event.code, event.reason);
});
```

### 3. 查看nginx日志验证
```bash
# 实时查看访问日志
tail -f /var/log/nginx/access.log

# 查看错误日志
tail -f /var/log/nginx/error.log
```

成功的WebSocket连接应该在access.log中显示：
```
101状态码 - 表示协议升级成功
```

## 预期结果

使用方案1配置后，这个应该成功：
```javascript
const ws = new WebSocket('wss://cankao-admin-api.dev.lingmiaoai.com/');
```

**我强烈建议先试方案1，这是最简单也最可能成功的配置！** 