# WebSocket连接深度调试指南

## 当前状况
已确认正确的连接地址：`wss://cankao-admin-api.dev.lingmiaoai.com/ws/`
但连接仍然失败，需要深入调试。

## 逐步调试方案

### 第1步：验证HTTPS基础连接
```bash
# 测试域名解析
nslookup cankao-admin-api.dev.lingmiaoai.com

# 测试HTTPS连接
curl -I https://cankao-admin-api.dev.lingmiaoai.com

# 测试SSL证书
openssl s_client -connect cankao-admin-api.dev.lingmiaoai.com:443 -servername cankao-admin-api.dev.lingmiaoai.com
```

### 第2步：测试nginx配置生效性
```bash
# 测试WebSocket路径是否能被nginx识别
curl -I https://cankao-admin-api.dev.lingmiaoai.com/ws/

# 测试根路径
curl -I https://cankao-admin-api.dev.lingmiaoai.com/
```

### 第3步：检查后端WebSocket服务
从你的nginx配置看，后端服务在 `***********:31490`

```bash
# 在服务器上测试后端服务是否可达
curl -I http://***********:31490/
telnet *********** 31490

# 直接测试后端WebSocket服务
wscat -c ws://***********:31490/
```

### 第4步：检查nginx错误日志
```bash
# 实时查看nginx错误日志
tail -f /var/log/nginx/error.log

# 查看nginx访问日志
tail -f /var/log/nginx/access.log

# 然后再次尝试WebSocket连接，观察日志输出
```

### 第5步：检查防火墙和网络策略
```bash
# 检查防火墙状态
iptables -L
ufw status

# 检查端口监听情况
netstat -tlnp | grep :443
netstat -tlnp | grep :31490
```

## 常见问题诊断

### 问题1：SSL证书问题
如果SSL握手失败，可能是：
- 证书过期
- 证书域名不匹配
- 证书路径错误

**解决方案**：
```nginx
# 确保证书文件存在且可读
ls -la /etc/nginx/ssl/dev.lingmiaoai.crt
ls -la /etc/nginx/ssl/dev.lingmiaoai.key

# 检查证书有效期
openssl x509 -in /etc/nginx/ssl/dev.lingmiaoai.crt -text -noout | grep -A2 Validity
```

### 问题2：后端服务未运行
**检查方法**：
```bash
# 检查容器或进程状态
docker ps | grep cankao
ps aux | grep node

# 检查端口监听
lsof -i :31490
```

### 问题3：nginx配置语法错误
```bash
# 检查nginx配置语法
nginx -t

# 重新加载nginx配置
nginx -s reload
```

### 问题4：路径匹配问题
你的nginx配置中 `/ws/` 路径可能有问题。尝试以下优化：

```nginx
server {
    listen 443 ssl;
    server_name cankao-admin-api.dev.lingmiaoai.com;

    ssl_certificate /etc/nginx/ssl/dev.lingmiaoai.crt;
    ssl_certificate_key /etc/nginx/ssl/dev.lingmiaoai.key;

    # 添加SSL优化配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # WebSocket路径 - 去掉尾随斜杠试试
    location /ws {
        proxy_pass http://***********:31490;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_buffering off;
    }

    # 备用：支持根路径的WebSocket
    location / {
        # 检查是否为WebSocket请求
        if ($http_upgrade ~* websocket) {
            proxy_pass http://***********:31490;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
            proxy_buffering off;
        }
        
        # 普通HTTP请求
        proxy_pass http://***********:31490/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 多路径测试方案

尝试这些不同的连接方式：

```javascript
// 测试1：带尾随斜杠
const ws1 = new WebSocket('wss://cankao-admin-api.dev.lingmiaoai.com/ws/');

// 测试2：不带尾随斜杠
const ws2 = new WebSocket('wss://cankao-admin-api.dev.lingmiaoai.com/ws');

// 测试3：根路径
const ws3 = new WebSocket('wss://cankao-admin-api.dev.lingmiaoai.com/');

// 测试4：检查是否有其他路径
const ws4 = new WebSocket('wss://cankao-admin-api.dev.lingmiaoai.com');

// 监听所有错误信息
[ws1, ws2, ws3, ws4].forEach((ws, index) => {
  ws.onopen = () => console.log(`连接${index + 1}成功！`);
  ws.onerror = (error) => console.log(`连接${index + 1}失败：`, error);
  ws.onclose = (event) => console.log(`连接${index + 1}关闭：`, event.code, event.reason);
});
```

## 临时绕过方案

如果以上都无效，可以尝试：

### 方案1：直接连接后端（测试用）
```javascript
// 如果后端服务可以直接访问，先测试直连
const ws = new WebSocket('ws://***********:31490/');
```

### 方案2：使用HTTP轮询替代
暂时使用HTTP API替代WebSocket：
```javascript
// 轮询方式获取数据
setInterval(async () => {
  try {
    const response = await fetch('https://cankao-admin-api.dev.lingmiaoai.com/api/poll');
    const data = await response.json();
    // 处理数据
  } catch (error) {
    console.error('轮询失败:', error);
  }
}, 1000);
```

## 关键调试信息收集

请帮我收集以下信息：

1. **nginx错误日志**：`tail -n 50 /var/log/nginx/error.log`
2. **nginx访问日志**：`tail -n 20 /var/log/nginx/access.log`
3. **后端服务状态**：`docker ps` 或 `ps aux | grep node`
4. **端口监听状态**：`netstat -tlnp | grep -E "(443|31490)"`
5. **SSL证书状态**：`openssl x509 -in /etc/nginx/ssl/dev.lingmiaoai.crt -text -noout | grep -A2 Validity`

有了这些信息，我们就能精确定位问题所在！ 