const axios = require('axios');

// 测试证书生成接口
async function testCertificateGeneration() {
  try {
    const testData = {
      name: '姜子介',
      courseName: '预定流程标准',
      position: '服务员（p4级）',
      certificateNo: 'C2505493172',
      issueDate: '2025-05-28',
      validDate: '2025-08-26'
    };

    console.log('测试数据:', testData);
    console.log('正在测试证书生成接口...');

    // 假设服务器运行在 localhost:3000
    const response = await axios.post('http://localhost:3000/api/wechat/certificate/generate', testData);
    
    console.log('接口响应:', response.data);
    console.log('证书生成成功！');
    
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testCertificateGeneration(); 