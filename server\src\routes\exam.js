const express = require('express');
const router = express.Router();
const examConfigController = require('../controllers/exam/examConfigController');
const { requireLogin } = require('../middleware/auth');

// // 练考配置相关路由
// router.get('/config/list', requireLogin, examConfigController.getExamConfigList);
// router.get('/config/:id', requireLogin, examConfigController.getExamConfigDetail);
// router.post('/config', requireLogin, examConfigController.createExamConfig);
// router.put('/config/:id', requireLogin, examConfigController.updateExamConfig);
// router.delete('/config/:id', requireLogin, examConfigController.deleteExamConfig);
// router.post('/config/batch-delete', requireLogin, examConfigController.batchDeleteExamConfig);
//
// // 考试通用规则配置相关路由
// router.get('/global-config', requireLogin, examConfigController.getExamGlobalConfig);
// router.put('/global-config', requireLogin, examConfigController.updateExamGlobalConfig);
//
// // 新增路由 - 匹配前端接口路径
// 练考配置相关路由
router.get('/configs', requireLogin, examConfigController.getExamConfigList);
router.get('/configs/:id', requireLogin, examConfigController.getExamConfigDetail);
router.post('/configs', requireLogin, examConfigController.createExamConfig);
router.put('/configs/:id', requireLogin, examConfigController.updateExamConfig);
router.delete('/configs/:id', requireLogin, examConfigController.deleteExamConfig);

// 单个配置的规则更新路由
router.put('/configs/:id/practice-rules', requireLogin, examConfigController.updateConfigPracticeRules);
router.put('/configs/:id/exam-rules', requireLogin, examConfigController.updateConfigExamRules);

// 考试规则相关路由 - 注意路由顺序，具体路径应该放在参数路径之前
router.get('/rules/global', requireLogin, examConfigController.getExamGlobalConfig);
router.put('/rules/practice', requireLogin, examConfigController.updatePracticeGlobalConfig);
router.put('/rules/exam', requireLogin, examConfigController.updateExamGlobalConfig);
router.post('/rules/sync', requireLogin, examConfigController.syncExamConfigs);

module.exports = router;
