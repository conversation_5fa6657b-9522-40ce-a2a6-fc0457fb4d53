const express = require('express');
const router = express.Router();
const { createUpload } = require('../utils/fileUpload');
const {
  getTemplateList,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  getTemplateDetail,
  initDefaultTemplates,
  uploadIcon
} = require('../controllers/achievementController');

// 创建成就图标上传中间件
const achievementUpload = createUpload('achievement');

// 成就模板相关路由
router.get('/templates', getTemplateList);           // 获取成就模板列表
router.post('/templates', createTemplate);          // 创建成就模板
router.get('/templates/:id', getTemplateDetail);    // 获取成就模板详情
router.put('/templates/:id', updateTemplate);       // 更新成就模板
router.delete('/templates/:id', deleteTemplate);    // 删除成就模板

// 图标上传路由
router.post('/upload-icon', achievementUpload.single('file'), uploadIcon);

// 初始化默认模板
router.post('/init-default', initDefaultTemplates); // 初始化默认成就模板

module.exports = router; 