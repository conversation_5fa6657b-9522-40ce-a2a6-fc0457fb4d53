/**
 * 测试 Transaction 导入修复
 */

const { Op, Transaction } = require('sequelize');

console.log('🧪 测试 Sequelize Transaction 导入');
console.log('=' .repeat(40));

try {
  console.log('✅ Op 导入成功:', typeof Op);
  console.log('✅ Transaction 导入成功:', typeof Transaction);
  
  if (Transaction && Transaction.ISOLATION_LEVELS) {
    console.log('✅ ISOLATION_LEVELS 可用:', Object.keys(Transaction.ISOLATION_LEVELS));
    console.log('✅ READ_COMMITTED:', Transaction.ISOLATION_LEVELS.READ_COMMITTED);
  } else {
    console.log('❌ ISOLATION_LEVELS 不可用');
  }
  
  console.log('\n🎯 修复验证:');
  console.log('- Transaction.ISOLATION_LEVELS.READ_COMMITTED 应该可用');
  console.log('- 不再出现 "Cannot read properties of undefined" 错误');
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
}
