const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 考试记录数据模型
 */
const ExamRecord = sequelize.define('ExamRecord', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },

  enterpriseId: {
    type: DataTypes.BIGINT,
    field: 'enterprise_id',
    allowNull: false,
    comment: '企业ID'
  },

  examSubject: {
    type: DataTypes.STRING(100),
    field: 'exam_subject',
    allowNull: true,
    comment: '考试科目名称'
  },

  examTime: {
    type: DataTypes.DATE,
    field: 'exam_time',
    allowNull: true,
    comment: '考试时间'
  },

  positionBelongId: {
    type: DataTypes.BIGINT,
    field: 'position_belong_id',
    allowNull: true,
    comment: '岗位归属ID'
  },

  positionId: {
    type: DataTypes.BIGINT,
    field: 'position_id',
    allowNull: true,
    comment: '岗位ID'
  },

  levelId: {
    type: DataTypes.BIGINT,
    field: 'level_id',
    allowNull: true,
    comment: '岗位等级ID'
  },

  categoryId: {
    type: DataTypes.BIGINT,
    field: 'category_id',
    allowNull: true,
    comment: '分类ID'
  },

  examineeId: {
    type: DataTypes.BIGINT,
    field: 'examinee_id',
    allowNull: true,
    comment: '参考人ID'
  },

  examinee: {
    type: DataTypes.STRING(50),
    field: 'examinee',
    allowNull: true,
    comment: '参考人姓名'
  },

  openId: {
    type: DataTypes.STRING(255),
    field: 'open_id',
    allowNull: true,
    comment: '微信openId'
  },

  score: {
    type: DataTypes.DECIMAL(5, 2),
    field: 'score',
    allowNull: true,
    comment: '考试得分'
  },

  usedDuration: {
    type: DataTypes.INTEGER,
    field: 'used_duration',
    allowNull: true,
    comment: '考试用时(分钟)'
  },

  confirmStatus: {
    type: DataTypes.STRING(20),
    field: 'confirm_status',
    allowNull: true,
    defaultValue: '1',
    comment: '确认状态：1待审核，2已通过、3未通过'
  },

  confirmPersonId: {
    type: DataTypes.BIGINT,
    field: 'confirm_person_id',
    allowNull: true,
    comment: '确认人ID'
  },

  confirmPerson: {
    type: DataTypes.STRING(50),
    field: 'confirm_person',
    allowNull: true,
    comment: '确认人姓名'
  },

  kbId: {
    type: DataTypes.STRING(50),
    field: 'kb_id',
    allowNull: true,
    comment: '知识库id'
  },

  confirmTime: {
    type: DataTypes.DATE,
    field: 'confirm_time',
    allowNull: true,
    comment: '确认时间'
  },

  reviewApplicationId: {
    type: DataTypes.BIGINT,
    field: 'review_application_id',
    allowNull: true,
    comment: '考试审核申请ID'
  },

  examContent: {
    type: DataTypes.JSON,
    field: 'exam_content',
    allowNull: true,
    comment: '考试内容，包含题目和回答等JSON格式'
  },

  delFlag: {
    type: DataTypes.BOOLEAN,
    field: 'del_flag',
    allowNull: true,
    defaultValue: 0,
    comment: '逻辑删除标志：0-未删除，1-已删除'
  },

  questionNumber: {
    type: DataTypes.STRING(50),
    field: 'question_number',
    allowNull: true,
    comment: '题目数量'
  },
  createdBy: {
    type: DataTypes.BIGINT,
    field: 'created_by',
    allowNull: true,
    comment: '创建人ID'
  },

  updatedBy: {
    type: DataTypes.BIGINT,
    field: 'updated_by',
    allowNull: true,
    comment: '更新人ID'
  },

  passScore: {
    type: DataTypes.INTEGER,
    field: 'pass_score',
    allowNull: true,
    defaultValue: 80,
    comment: '合格分'
  },

  examDuration: {
    type: DataTypes.INTEGER,
    field: 'exam_duration',
    allowNull: true,
    comment: '考试分钟数'
  },

  endTime: {
    type: DataTypes.STRING(255),
    field: 'end_time',
    allowNull: true,
    comment: '截止时间'
  },

  advantage: {
    type: DataTypes.STRING,
    field: 'advantage',
    allowNull: true,
    comment: '优点'
  },

  improve: {
    type: DataTypes.STRING,
    field: 'improve',
    allowNull: true,
    comment: '改进点'
  },

  examStatus: {
    type: DataTypes.STRING(20),
    field: 'exam_status',
    allowNull: true,
    defaultValue: 'pending',
    comment: '考试状态：ongoing-考试中，completed-已完成，timeout-已超时'
  },

  isActive: {
    type: DataTypes.BOOLEAN,
    field: 'is_active',
    allowNull: true,
    defaultValue: true,
    comment: '是否有效(1有效 0无效)'
  },
  careerRecordId: {
    type: DataTypes.INTEGER,
    field: 'career_record_id',
    allowNull: true,
    comment: '员工履历记录ID'
  },
  positionBelongName: {
    type: DataTypes.STRING(100),
    field: 'position_belong_name',
    allowNull: true,
    comment: '岗位归属名称'
  },
  positionNameCn: {
    type: DataTypes.STRING(100),
    field: 'position_name_cn',
    allowNull: true,
    comment: '岗位名称'
  },
  positionLevelName: {
    type: DataTypes.STRING(100),
    field: 'position_level_name',
    allowNull: true,
    comment: '岗位等级名称'
  },
  reportData: {
    type: DataTypes.TEXT,
    field: 'report_data',
    allowNull: true,
    comment: '完整的考试报告数据(JSON字符串)'
  },
}, {
  tableName: 'exam_records',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_enterprise_exam_subject',
      fields: ['enterprise_id', 'exam_subject']
    },
    {
      name: 'idx_enterprise_examinee',
      fields: ['enterprise_id', 'examinee']
    },
    {
      name: 'idx_enterprise_confirm_status',
      fields: ['enterprise_id', 'confirm_status']
    },
    {
      name: 'idx_enterprise_exam_time',
      fields: ['enterprise_id', 'exam_time']
    },
    {
      name: 'idx_review_application',
      fields: ['review_application_id']
    },
    {
      name: 'idx_del_flag',
      fields: ['del_flag']
    },
    {
      name: 'idx_open_id',
      fields: ['open_id']
    }
  ]
});

module.exports = ExamRecord;

// 定义模型关联关系
ExamRecord.associate = (models) => {
  // 与ExamReviewApplication的关联
  ExamRecord.belongsTo(models.ExamReviewApplication, {
    foreignKey: 'review_application_id',
    as: 'reviewApplication'
  });

  // 与PositionName的关联
  ExamRecord.belongsTo(models.PositionName, {
    foreignKey: 'position_id',
    as: 'positionName'
  });

  // 与Level的关联
  ExamRecord.belongsTo(models.Level, {
    foreignKey: 'level_id',
    as: 'level'
  });

  // 与User的关联
  ExamRecord.belongsTo(models.User, {
    foreignKey: 'examinee_id',
    as: 'examineeUser'
  });

  // 与DictionaryData的关联 - 岗位归属
  ExamRecord.belongsTo(models.DictionaryData, {
    foreignKey: 'position_belong_id',
    as: 'positionBelongDict'
  });

  // 与DictionaryData的关联 - 分类
  ExamRecord.belongsTo(models.DictionaryData, {
    foreignKey: 'category_id',
    as: 'categoryDict'
  });
};
