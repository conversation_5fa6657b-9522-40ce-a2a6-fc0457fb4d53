const { v4: uuidv4 } = require('uuid');
const KBQuestions = require('../models/kb-questions');
const KnowledgeBase = require('../models/knowledge-base');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 新增：引入DEFAULT_ENTERPRISE_ID常量
const { DEFAULT_ENTERPRISE_ID } = require('../utils/enterpriseFilter');

/**
 * 获取所有题目列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getAllQuestions = async (req, res) => {
  try {
    const { id } = req.params;
    const { pageNum = 1, pageSize = 10, question, type, knowledgeBaseId, fileCategory, position } = req.query;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    console.log('请求参数:', {
      paramsId: id,
      url: req.originalUrl,
      path: req.path,
      query: req.query,
      enterpriseId
    });

    // 构建查询条件
    const where = { enterprise_id: enterpriseId };

    // 特殊处理：当URL包含'/all/questions'时，不检查知识库
    const isAllQuestions = req.originalUrl.includes('/all/questions');
    console.log('是否查询所有知识库题目:', isAllQuestions);

    // 如果指定了具体的知识库ID且不查询所有知识库
    if (id && !isAllQuestions) {
      console.log('正在检查知识库ID:', id);
      // 检查知识库是否存在
      const knowledge = await KnowledgeBase.findOne({
        where: {
          id,
          enterprise_id: enterpriseId
        }
      });

      if (!knowledge) {
        console.log('知识库不存在:', id);
        return res.status(404).json({
          code: 404,
          message: '知识库不存在'
        });
      }

      console.log('知识库存在,ID:', id);
      where.knowledge_base_id = id;
    } else if (knowledgeBaseId) {
      // 处理通过筛选参数传入的知识库ID
      where.knowledge_base_id = knowledgeBaseId;
    }

    // 添加题目内容筛选
    if (question) {
      where.question = {
        [Op.like]: `%${question}%`
      };
    }

    // 添加题目类型筛选
    if (type) {
      where.type = type;
    }

    // 构建知识库关联查询条件
    const kbWhere = { enterprise_id: enterpriseId };

    // 添加文件归属筛选
    if (fileCategory) {
      kbWhere.file_category = fileCategory;
    }

    // 添加所属岗位筛选
    if (position && fileCategory) {
      // 如果同时有岗位类型和岗位名称，查询该岗位或通用岗位
      kbWhere[Op.or] = [
        { position: position },    // 完全匹配指定岗位
        { position: 'COMMON' }      // 或者是通用岗位
      ];
    } else if (position) {
      // 如果只有岗位名称，直接匹配
      kbWhere.position = position;
    }

    console.log('题目查询条件:', where);
    console.log('知识库关联查询条件:', kbWhere);

    // 分页
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询题目列表，关联知识库表进行过滤
    const result = await KBQuestions.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      offset,
      limit,
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['name', 'file_name', 'file_category', 'position'],
          where: Object.keys(kbWhere).length > 0 ? kbWhere : undefined,
          required: Object.keys(kbWhere).length > 0
        }
      ],
      attributes: {
        include: [
          [
            sequelize.literal(`(
              SELECT COALESCE(name, file_name) 
              FROM kb_knowledge_base 
              WHERE kb_knowledge_base.id = kb_questions.knowledge_base_id
              COLLATE utf8mb4_0900_ai_ci
            )`),
            'knowledgeBaseName'
          ]
        ]
      },
      raw: true
    });
    
    // 查询所有涉及到的岗位ID
    const positionIds = [...new Set(result.rows
      .map(item => item['knowledgeBase.position'])
      .filter(id => id && id !== 'COMMON'))];
      
    let positionMap = new Map();
    
    // 如果有岗位ID，查询对应的岗位名称
    if (positionIds.length > 0) {
      try {
        const { PositionName } = require('../models');
        const positions = await PositionName.findAll({
          where: {
            id: {
              [Op.in]: positionIds
            }
          },
          attributes: ['id', 'name']
        });
        
        // 构建岗位ID到岗位名称的映射
        positions.forEach(pos => {
          positionMap.set(pos.id.toString(), pos.name);
        });
      } catch (error) {
        console.error('获取岗位名称失败:', error);
      }
    }

    console.log('查询结果数量:', result.count);

    // 确保每条记录都有knowledgeBaseName字段和所属岗位字段
    const processedData = result.rows.map(item => {
      // 如果knowledgeBaseName为null，设置为空字符串
      if (item.knowledgeBaseName === null || item.knowledgeBaseName === undefined) {
        item.knowledgeBaseName = '';
      }

      // 处理查询结果中的嵌套属性
      const cleanedItem = { ...item };

      // 提取所属岗位信息
      let positionId = item['knowledgeBase.position'];
      let positionName = '';
      
      // 检查岗位ID
      if (positionId) {
        if (positionId === 'COMMON') {
          positionName = '通用岗位';
        } else {
          // 记录原始岗位ID，前端可能会需要
          cleanedItem.positionId = positionId;
          // 从映射表中获取岗位名称
          positionName = positionMap.get(positionId.toString()) || `岗位ID: ${positionId}`;
        }
      }
      
      // 添加所属岗位字段
      cleanedItem.positionName = positionName;

      // 删除include产生的嵌套字段
      Object.keys(cleanedItem).forEach(key => {
        if (key.startsWith('knowledgeBase.')) {
          delete cleanedItem[key];
        }
      });

      return cleanedItem;
    });

    res.json({
      code: 200,
      message: '获取题目列表成功',
      data: {
        list: processedData,
        total: result.count,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      }
    });

  } catch (error) {
    console.error('获取题目列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取题目列表失败',
      error: error.message
    });
  }
};

/**
 * 添加题目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const addQuestion = async (req, res) => {
  try {
    const { id } = req.params;
    const { question, answer } = req.body;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查知识库是否存在
    const knowledge = await KnowledgeBase.findOne({
      where: {
        id,
        enterprise_id: enterpriseId
      }
    });

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    // 创建新题目记录
    const newQuestion = await KBQuestions.create({
      id: uuidv4(),
      knowledge_base_id: id,
      segment_id: null, // 固定题目没有分段ID
      question,
      answer,
      type: '人工出题',
      enterprise_id: enterpriseId
    });

    res.json({
      code: 200,
      message: '添加题目成功',
      data: newQuestion
    });
  } catch (error) {
    console.error('添加题目失败:', error);
    res.status(500).json({
      code: 500,
      message: '添加题目失败',
      error: error.message
    });
  }
};

/**
 * 更新题目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const updateQuestion = async (req, res) => {
  try {
    const { id } = req.params;
    const { questionId, question, answer } = req.body;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查知识库是否存在
    const knowledge = await KnowledgeBase.findOne({
      where: {
        id,
        enterprise_id: enterpriseId
      }
    });

    if (!knowledge) {
      return res.status(404).json({
        code: 404,
        message: '知识库不存在'
      });
    }

    // 查找要更新的题目记录
    const questionRecord = await KBQuestions.findOne({
      where: {
        id: questionId,
        knowledge_base_id: id,
        enterprise_id: enterpriseId
      },
      attributes: {
        include: [
          [
            sequelize.literal(`(
              SELECT COALESCE(name, file_name) 
              FROM kb_knowledge_base 
              WHERE kb_knowledge_base.id = kb_questions.knowledge_base_id
              COLLATE utf8mb4_0900_ai_ci
            )`),
            'knowledgeBaseName'
          ]
        ]
      }
    });

    if (!questionRecord) {
      return res.status(404).json({
        code: 404,
        message: '题目不存在'
      });
    }

    // 更新题目记录
    await questionRecord.update({
      question,
      answer,
      updated_at: new Date()
    });

    res.json({
      code: 200,
      message: '更新题目成功',
      data: questionRecord
    });
  } catch (error) {
    console.error('更新题目失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新题目失败',
      error: error.message
    });
  }
};

/**
 * 删除题目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const deleteQuestion = async (req, res) => {
  try {
    const { id, questionId } = req.params;
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 查找并删除题目记录
    const deletedCount = await KBQuestions.destroy({
      where: {
        id: questionId,
        knowledge_base_id: id,
        enterprise_id: enterpriseId
      }
    });

    if (deletedCount === 0) {
      return res.status(404).json({
        code: 404,
        message: '题目不存在或已被删除'
      });
    }

    res.json({
      code: 200,
      message: '删除题目成功',
      data: { success: true }
    });
  } catch (error) {
    console.error('删除题目失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除题目失败',
      error: error.message
    });
  }
};

/**
 * 获取知识库列表（用于筛选）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getKnowledgeBaseOptions = async (req, res) => {
  try {
    console.log('获取知识库选项列表 - 请求参数:', req.query);
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;
    console.log('当前企业ID:', enterpriseId);

    // 查询知识库列表
    const knowledgeList = await KnowledgeBase.findAll({
      where: {
        enterprise_id: enterpriseId,
        deleted: false
      },
      attributes: ['id', 'name', 'fileName'],
      order: [['created_time', 'DESC']]
    });

    console.log('查询到的知识库数量:', knowledgeList.length);

    // 转换为前端选项格式
    const options = knowledgeList.map(kb => ({
      value: kb.id,
      label: kb.name || kb.fileName
    }));

    console.log('生成的选项列表:', options);

    res.json({
      code: 200,
      message: '获取知识库列表成功',
      data: options
    });
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取知识库列表失败',
      error: error.message
    });
  }
};

/**
 * 下载题目导入模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const downloadTemplate = async (req, res) => {
  try {
    const path = require('path');
    const fs = require('fs');

    // 模板文件路径
    const templatePath = path.join(__dirname, '../../uploads/题目导入.xlsx');

    // 检查文件是否存在
    if (!fs.existsSync(templatePath)) {
      return res.status(404).json({
        code: 404,
        message: '模板文件不存在'
      });
    }

    // 设置响应头，对中文文件名进行正确编码
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

    // 使用encodeURIComponent编码中文文件名
    const filename = encodeURIComponent('题目导入模板.xlsx');
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${filename}`);

    // 发送文件
    res.sendFile(templatePath);

  } catch (error) {
    console.error('下载模板失败:', error);
    res.status(500).json({
      code: 500,
      message: '下载模板失败',
      error: error.message
    });
  }
};

/**
 * 导入题目Excel文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const importQuestions = async (req, res) => {
  try {
    const XLSX = require('xlsx');
    const enterpriseId = req.headers['enterprise-id'] || process.env.DEFAULT_ENTERPRISE_ID;

    // 检查是否有上传文件
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请选择要导入的Excel文件'
      });
    }

    // 读取Excel文件
    const workbook = XLSX.readFile(req.file.path);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // 将工作表转换为JSON
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    if (data.length < 2) {
      return res.status(400).json({
        code: 400,
        message: 'Excel文件内容为空或格式不正确'
      });
    }

    // 获取表头
    const headers = data[0];
    const knowledgeBaseNameIndex = headers.indexOf('知识库名');
    const questionIndex = headers.indexOf('题目');
    const answerIndex = headers.indexOf('答案');

    if (knowledgeBaseNameIndex === -1 || questionIndex === -1 || answerIndex === -1) {
      return res.status(400).json({
        code: 400,
        message: 'Excel文件格式不正确，必须包含"知识库名"、"题目"、"答案"列'
      });
    }

    // 验证数据并收集错误
    const errors = [];
    const validRows = [];

    // 获取所有知识库列表，用于名称到ID的映射
    const knowledgeBases = await KnowledgeBase.findAll({
      where: { enterprise_id: enterpriseId, deleted: false },
      attributes: ['id', 'name', 'fileName']
    });

    const knowledgeBaseMap = new Map();
    knowledgeBases.forEach(kb => {
      const displayName = kb.name || kb.fileName;
      knowledgeBaseMap.set(displayName, kb.id);
    });

    // 从第二行开始验证数据（跳过表头）
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const rowNum = i + 1; // Excel行号（从1开始）

      // 跳过空行
      if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
        continue;
      }

      const knowledgeBaseName = row[knowledgeBaseNameIndex]?.toString().trim();
      const question = row[questionIndex]?.toString().trim();
      const answer = row[answerIndex]?.toString().trim();

      // 验证知识库名
      if (!knowledgeBaseName) {
        errors.push(`第${rowNum}行：知识库名不能为空`);
        continue;
      }

      const knowledgeBaseId = knowledgeBaseMap.get(knowledgeBaseName);
      if (!knowledgeBaseId) {
        errors.push(`第${rowNum}行：知识库"${knowledgeBaseName}"不存在`);
        continue;
      }

      // 验证题目
      if (!question) {
        errors.push(`第${rowNum}行：题目不能为空`);
        continue;
      }

      // 验证答案
      if (!answer) {
        errors.push(`第${rowNum}行：答案不能为空`);
        continue;
      }

      // 如果验证通过，添加到有效行列表
      validRows.push({
        knowledgeBaseId,
        knowledgeBaseName,
        question,
        answer,
        rowNum
      });
    }

    // 如果有验证错误，返回错误信息
    if (errors.length > 0) {
      // 删除临时上传的文件
      const fs = require('fs');
      if (fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      return res.status(400).json({
        code: 400,
        message: '数据验证失败',
        errors: errors
      });
    }

    // 如果没有有效数据
    if (validRows.length === 0) {
      // 删除临时上传的文件
      const fs = require('fs');
      if (fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      return res.status(400).json({
        code: 400,
        message: '没有找到有效的数据行'
      });
    }

    // 开始批量导入
    const transaction = await sequelize.transaction();

    try {
      const createdQuestions = [];

      for (const row of validRows) {
        const newQuestion = await KBQuestions.create({
          id: uuidv4(),
          knowledge_base_id: row.knowledgeBaseId,
          segment_id: null, // 导入的题目都是固定题目
          question: row.question,
          answer: row.answer,
          type: '人工出题',
          enterprise_id: enterpriseId
        }, { transaction });

        createdQuestions.push(newQuestion);
      }

      await transaction.commit();

      // 删除临时上传的文件
      const fs = require('fs');
      if (fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.json({
        code: 200,
        message: `成功导入${createdQuestions.length}条题目`,
        data: {
          total: createdQuestions.length,
          imported: createdQuestions.length
        }
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('导入题目失败:', error);

    // 删除临时上传的文件
    const fs = require('fs');
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      code: 500,
      message: '导入题目失败',
      error: error.message
    });
  }
};

/**
 * 导出题目列表为Excel
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const exportQuestions = async (req, res) => {
  try {
    const { id } = req.params;
    const { pageNum, pageSize, question, type, knowledgeBaseId, fileCategory, position } = req.query;
    // 从环境变量获取企业ID
    const enterpriseId = DEFAULT_ENTERPRISE_ID;

    console.log('导出请求参数:', {
      paramsId: id,
      url: req.originalUrl,
      path: req.path,
      query: req.query,
      enterpriseId
    });

    // 构建查询条件
    const where = { enterprise_id: enterpriseId };

    // 特殊处理：当URL包含'/all/export'时，不检查知识库
    const isAllQuestions = req.originalUrl.includes('/all/export');
    console.log('是否导出所有知识库题目:', isAllQuestions);

    // 如果指定了具体的知识库ID且不查询所有知识库
    if (id && !isAllQuestions) {
      console.log('正在检查知识库ID:', id);
      // 检查知识库是否存在
      const knowledge = await KnowledgeBase.findOne({
        where: {
          id,
          enterprise_id: enterpriseId
        }
      });

      if (!knowledge) {
        console.log('知识库不存在:', id);
        return res.status(404).json({
          code: 404,
          message: '知识库不存在'
        });
      }

      console.log('知识库存在,ID:', id);
      where.knowledge_base_id = id;
    } else if (knowledgeBaseId) {
      // 处理通过筛选参数传入的知识库ID
      where.knowledge_base_id = knowledgeBaseId;
    }

    // 添加题目内容筛选
    if (question) {
      where.question = {
        [Op.like]: `%${question}%`
      };
    }

    // 添加题目类型筛选
    if (type) {
      where.type = type;
    }

    // 构建知识库关联查询条件
    const kbWhere = { enterprise_id: enterpriseId };

    // 添加文件归属筛选
    if (fileCategory) {
      kbWhere.file_category = fileCategory;
    }

    // 添加所属岗位筛选
    if (position && fileCategory) {
      // 如果同时有岗位类型和岗位名称，查询该岗位或通用岗位
      kbWhere[Op.or] = [
        { position: position },    // 完全匹配指定岗位
        { position: 'COMMON' }      // 或者是通用岗位
      ];
    } else if (position) {
      // 如果只有岗位名称，直接匹配
      kbWhere.position = position;
    }

    console.log('导出题目查询条件:', where);
    console.log('知识库关联查询条件:', kbWhere);

    // 查询题目列表，关联知识库表进行过滤，不做分页限制
    const result = await KBQuestions.findAll({
      where,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: KnowledgeBase,
          as: 'knowledgeBase',
          attributes: ['name', 'file_name', 'file_category', 'position'],
          where: Object.keys(kbWhere).length > 0 ? kbWhere : undefined,
          required: Object.keys(kbWhere).length > 0
        }
      ],
      attributes: {
        include: [
          [
            sequelize.literal(`(
              SELECT COALESCE(name, file_name) 
              FROM kb_knowledge_base 
              WHERE kb_knowledge_base.id = kb_questions.knowledge_base_id
              COLLATE utf8mb4_0900_ai_ci
            )`),
            'knowledgeBaseName'
          ]
        ]
      },
      raw: true
    });
    
    console.log('导出查询结果数量:', result.length);

    // 查询所有涉及到的岗位ID
    const positionIds = [...new Set(result
      .map(item => item['knowledgeBase.position'])
      .filter(id => id && id !== 'COMMON'))];
      
    let positionMap = new Map();
    
    // 如果有岗位ID，查询对应的岗位名称
    if (positionIds.length > 0) {
      try {
        const { PositionName } = require('../models');
        const positions = await PositionName.findAll({
          where: {
            id: {
              [Op.in]: positionIds
            }
          },
          attributes: ['id', 'name']
        });
        
        // 构建岗位ID到岗位名称的映射
        positions.forEach(pos => {
          positionMap.set(pos.id.toString(), pos.name);
        });
      } catch (error) {
        console.error('获取岗位名称失败:', error);
      }
    }

    console.log('岗位映射表:', [...positionMap.entries()]);

    // 确保每条记录都有knowledgeBaseName字段和所属岗位字段
    const processedData = result.map(item => {
      // 如果knowledgeBaseName为null，设置为空字符串
      if (item.knowledgeBaseName === null || item.knowledgeBaseName === undefined) {
        item.knowledgeBaseName = '';
      }

      // 处理查询结果中的嵌套属性
      const cleanedItem = { ...item };

      // 提取所属岗位信息
      let positionId = item['knowledgeBase.position'];
      let positionName = '';
      
      // 检查岗位ID
      if (positionId) {
        if (positionId === 'COMMON') {
          positionName = '通用岗位';
        } else {
          // 记录原始岗位ID，前端可能会需要
          cleanedItem.positionId = positionId;
          // 从映射表中获取岗位名称
          positionName = positionMap.get(positionId.toString()) || `岗位ID: ${positionId}`;
        }
      }
      
      // 添加所属岗位字段
      cleanedItem.positionName = positionName;

      // 删除include产生的嵌套字段
      Object.keys(cleanedItem).forEach(key => {
        if (key.startsWith('knowledgeBase.')) {
          delete cleanedItem[key];
        }
      });

      return cleanedItem;
    });

    console.log('处理后的数据条数:', processedData.length);
    
    // 格式化日期函数
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    };

    // 处理查询结果，准备导出数据
    const exportData = processedData.map(item => {
      // 构建导出数据行
      return {
        '题目内容': item.question || '',
        '答案内容': item.answer || '',
        '所属知识库': item.knowledgeBaseName || '',
        '所属岗位': item.positionName || '-',
        '题目类型': item.type || '',
        '创建时间': formatDate(item.created_at),
        '更新时间': formatDate(item.updated_at)
      };
    });
    
    // 引入Excel导出工具
    const ExcelExport = require('../utils/excelExport');
    
    // 设置表头和列宽
    const header = ['题目内容', '答案内容', '所属知识库', '所属岗位', '题目类型', '创建时间', '更新时间'];
    const colWidths = [
      { wpx: 250 },  // 题目内容
      { wpx: 300 },  // 答案内容
      { wpx: 150 },  // 所属知识库
      { wpx: 100 },  // 所属岗位
      { wpx: 100 },  // 题目类型
      { wpx: 150 },  // 创建时间
      { wpx: 150 }   // 更新时间
    ];
    
    // 获取知识库名称（用于文件名）
    let fileName = '知识库题目';
    if (id && !isAllQuestions) {
      const knowledge = await KnowledgeBase.findOne({
        where: { id, enterprise_id: enterpriseId },
        attributes: ['name', 'file_name']
      });
      
      if (knowledge) {
        fileName = (knowledge.name || knowledge.file_name) + '题目';
      }
    }
    
    // 导出Excel
    ExcelExport.exportToResponse(res, exportData, fileName, header, colWidths);
    
  } catch (error) {
    console.error('导出题目列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '导出题目列表失败',
      error: error.message
    });
  }
};

module.exports = {
  getAllQuestions,
  addQuestion,
  updateQuestion,
  deleteQuestion,
  getKnowledgeBaseOptions,
  downloadTemplate,
  importQuestions,
  exportQuestions
};
