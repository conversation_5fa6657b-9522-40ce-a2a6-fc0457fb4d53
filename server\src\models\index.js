const Role = require('./role');
const Menu = require('./menu');
const Agent = require('./agent');
const Enterprise = require('./enterprise');
const Knowledge = require('./knowledge');
const User = require('./user');
const UserRole = require('./userRole');
const SystemSetting = require('./systemSetting');
const { DictionaryType, DictionaryData } = require('./dictionary');
const sequelize = require('../config/database');
const Department = require('./Department');
const Employee = require('./Employee');
const Position = require('./Position');
const Level = require('./Level');
const ExamConfig = require('./ExamConfigModel');
const ExamGlobalConfig = require('./ExamGlobalConfigModel');
const KnowledgeBase = require('./knowledge-base');
const ExamReviewApplication = require('./ExamReviewApplication');
const PracticeRecord = require('./practice-record');
const PracticeRecordDetail = require('./practice-record-detail');
const ExamRecord = require('./ExamRecord');
const PositionType = require('./PositionType');
const PositionName = require('./PositionName');
const CertificateRecord = require('./CertificateRecord');
const EmployeePromotion = require('./EmployeePromotion');
const EmployeePosition = require('./EmployeePosition');
const EmployeeCareerRecord = require('./EmployeeCareerRecord');
const RestaurantConfig = require('./RestaurantConfig');
const InfoConfig = require('./InfoConfig');
const RestaurantRecord = require('./RestaurantRecord');
const RestaurantRecordDetail = require('./RestaurantRecordDetail');
const AchievementTemplate = require('./AchievementTemplate');
const AchievementProgress = require('./AchievementProgress');
const UserAchievement = require('./UserAchievement');
/**
 * 重要说明：
 * 所有模型都必须包含enterpriseId字段，用于企业数据隔离。
 * 企业ID字段定义示例：
 * enterpriseId: {
 *   type: DataTypes.INTEGER,
 *   field: 'enterprise_id',
 *   allowNull: false,
 *   defaultValue: 1,
 *   comment: '企业ID'
 * }
 *
 * 所有底层查询、修改、删除操作都需要使用utils/enterpriseFilter.js中的工具函数
 * 添加企业ID过滤条件。
 */

// 角色-菜单多对多关系
const RoleMenu = sequelize.define('role_menus', {
  id: {
    type: sequelize.Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  enterpriseId: {
    type: sequelize.Sequelize.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  roleId: {
    type: sequelize.Sequelize.INTEGER,
    field: 'role_id',
    references: {
      model: Role,
      key: 'id'
    }
  },
  menuId: {
    type: sequelize.Sequelize.INTEGER,
    field: 'menu_id',
    references: {
      model: Menu,
      key: 'id'
    }
  },
  createTime: {
    type: sequelize.Sequelize.DATE,
    field: 'create_time',
    defaultValue: sequelize.Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'role_menus'
});

// 设置多对多关系
Role.belongsToMany(Menu, { through: RoleMenu, foreignKey: 'roleId', otherKey: 'menuId' });
Menu.belongsToMany(Role, { through: RoleMenu, foreignKey: 'menuId', otherKey: 'roleId' });

// 设置企业和智能体的一对多关系
Enterprise.hasMany(Agent, { foreignKey: 'enterpriseId' });
Agent.belongsTo(Enterprise, { foreignKey: 'enterpriseId' });

// 设置企业和知识库的一对多关系
Enterprise.hasMany(Knowledge, { foreignKey: 'enterpriseId' });
Knowledge.belongsTo(Enterprise, { foreignKey: 'enterpriseId' });

// 设置用户和角色的多对多关系
User.belongsToMany(Role, { through: UserRole, foreignKey: 'userId', otherKey: 'roleId' });
Role.belongsToMany(User, { through: UserRole, foreignKey: 'roleId', otherKey: 'userId' });

// 设置岗位与岗位等级的关联关系
Position.belongsTo(Level, { foreignKey: 'levelId', as: 'level' });
Level.hasMany(Position, { foreignKey: 'levelId' });

// 添加 Level 与 User 的关联关系
Level.belongsTo(User, { foreignKey: 'operatorId', as: 'operator' });
User.hasMany(Level, { foreignKey: 'operatorId' });

// 设置岗位类型和岗位名称的关联关系
PositionType.hasMany(PositionName, { foreignKey: 'typeId', as: 'positionNames' });
PositionName.belongsTo(PositionType, { foreignKey: 'typeId', as: 'type' });

// 设置岗位与岗位类型和岗位名称的关联关系
Position.belongsTo(PositionType, { foreignKey: 'typeId', as: 'positionType' });
Position.belongsTo(PositionName, { foreignKey: 'nameId', as: 'positionName' });

// 注释掉不再使用的字典关联
// Position.belongsTo(DictionaryData, { foreignKey: 'typeId', as: 'positionTypeDict' });
// DictionaryData.hasMany(Position, { foreignKey: 'typeId' });

// 注释掉不再使用的字典关联
// Position.belongsTo(DictionaryData, { foreignKey: 'name', as: 'positionNameDict', constraints: false });

// 设置企业和练考配置的一对多关系
Enterprise.hasMany(ExamConfig, { foreignKey: 'enterpriseId' });
ExamConfig.belongsTo(Enterprise, { foreignKey: 'enterpriseId' });

// 设置ExamConfig与KnowledgeBase的关联关系
// 注意: examSubject(STRING)与id(INTEGER)类型不匹配，需要特殊处理
ExamConfig.belongsTo(KnowledgeBase, {
  as: 'subjectKnowledge',
  foreignKey: 'examSubject',  // ExamConfig表中的字段
  targetKey: 'id',            // 关联Knowledge表的id字段
  constraints: false,         // 不强制外键约束
  scope: {
    // 使用sequelize.where和sequelize.cast处理类型转换
    id: sequelize.where(
      sequelize.cast(sequelize.col('ExamConfig.exam_subject'), 'INTEGER'),
      sequelize.col('subjectKnowledge.id')
    )
  }
});

// 添加ExamConfig与PositionType和PositionName的关联关系
// 岗位归属(positionBelong)关联到岗位类型表(PositionType)
ExamConfig.belongsTo(PositionType, {
  as: 'positionBelongDict',
  foreignKey: 'positionBelong', 
  targetKey: 'id',  // 修改为使用id字段
  constraints: false
});

// 岗位名称(positionName)关联到岗位名称表(PositionName)
ExamConfig.belongsTo(PositionName, {
  as: 'positionDict',
  foreignKey: 'positionName',
  targetKey: 'id',  // 修改为使用id字段
  constraints: false
});

// 岗位等级(positionLevel)关联到岗位等级表(Level)
ExamConfig.belongsTo(Level, {
  as: 'level',
  foreignKey: 'positionLevel', // 字符串字段
  targetKey: 'id', // 关联模型的字段
  constraints: false // 不强制外键约束
});

// 设置企业和考试通用规则配置的一对多关系
Enterprise.hasMany(ExamGlobalConfig, { foreignKey: 'enterpriseId' });
ExamGlobalConfig.belongsTo(Enterprise, { foreignKey: 'enterpriseId' });

// 添加 PracticeRecord 与字典表和其他表的关联关系
// 练习科目(examSubject)关联到知识库表(KnowledgeBase)
PracticeRecord.belongsTo(KnowledgeBase, {
  as: 'subjectKnowledge',
  foreignKey: 'examSubject',
  targetKey: 'id',
  constraints: false
});

// 练习人(userId)关联到用户表(User)
// 练习记录与用户的关联关系（通过openId关联）
PracticeRecord.belongsTo(User, {
  as: 'user',
  foreignKey: 'openId',
  targetKey: 'openId',  // 使用openId作为关联字段
  constraints: false
});

// Employee 与其他模型的关联关系已在各自的模型文件中定义

// 添加ExamRecord与其他表的关联关系
// 考试记录关联到考试审核申请
ExamRecord.belongsTo(ExamReviewApplication, {
  as: 'reviewApplication',
  foreignKey: 'reviewApplicationId',
  targetKey: 'id',
  constraints: false
});

// 岗位归属(positionBelongId)关联到字典数据表(DictionaryData)
ExamRecord.belongsTo(DictionaryData, {
  as: 'positionBelongDict',
  foreignKey: 'positionBelongId',
  targetKey: 'id',
  constraints: false
});

// 岗位名称(positionId)关联到PositionName表
ExamRecord.belongsTo(PositionName, {
  as: 'positionName',
  foreignKey: 'positionId',
  targetKey: 'id',
  constraints: false
});

// 岗位等级(levelId)关联到岗位等级表(Level)
ExamRecord.belongsTo(Level, {
  as: 'level',
  foreignKey: 'levelId',
  targetKey: 'id',
  constraints: false
});

// 分类(categoryId)关联到字典数据表(DictionaryData)
ExamRecord.belongsTo(DictionaryData, {
  as: 'categoryDict',
  foreignKey: 'categoryId',
  targetKey: 'id',
  constraints: false
});

// 参考人(examineeId)关联到用户表(User)
ExamRecord.belongsTo(User, {
  as: 'examineeUser',
  foreignKey: 'examineeId',
  targetKey: 'id',
  constraints: false
});

// 考试审核申请关联到考试记录(一对多)
ExamReviewApplication.hasMany(ExamRecord, {
  as: 'examRecords',
  foreignKey: 'reviewApplicationId',
  sourceKey: 'id',
  constraints: false
});

// 证书记录与员工的关联关系
CertificateRecord.belongsTo(Employee, {
  as: 'employee',
  foreignKey: 'employeeId',
  targetKey: 'id',
  constraints: false
});

// 证书记录与考试记录的关联关系
CertificateRecord.belongsTo(ExamRecord, {
  as: 'examRecord',
  foreignKey: 'examRecordId',
  targetKey: 'id',
  constraints: false
});

// 证书记录与企业的关联关系
CertificateRecord.belongsTo(Enterprise, {
  as: 'enterprise',
  foreignKey: 'enterpriseId',
  targetKey: 'id',
  constraints: false
});

// 建立关联关系
RestaurantConfig.belongsTo(PositionType, {
  foreignKey: 'positionBelongId',
  as: 'positionType'
});

// 添加成就模板与用户成就的关联关系
AchievementTemplate.hasMany(UserAchievement, { 
  foreignKey: 'templateId', 
  as: 'userAchievements' 
});
UserAchievement.belongsTo(AchievementTemplate, { 
  foreignKey: 'templateId', 
  as: 'template' 
});

// 添加成就模板与成就进度的关联关系
AchievementTemplate.hasMany(AchievementProgress, { 
  foreignKey: 'templateId', 
  as: 'progressRecords' 
});
AchievementProgress.belongsTo(AchievementTemplate, { 
  foreignKey: 'templateId', 
  as: 'template' 
});

// 添加用户与成就进度的关联关系
User.hasMany(AchievementProgress, { 
  foreignKey: 'userId', 
  as: 'achievementProgress' 
});
AchievementProgress.belongsTo(User, { 
  foreignKey: 'userId', 
  as: 'user' 
});

module.exports = {
  Role,
  Menu,
  RoleMenu,
  Agent,
  Enterprise,
  Knowledge,
  User,
  UserRole,
  SystemSetting,
  DictionaryType,
  DictionaryData,
  sequelize,
  Department,
  Employee,
  Position,
  Level,
  ExamConfig,
  ExamGlobalConfig,
  KnowledgeBase,
  ExamReviewApplication,
  PracticeRecord,
  PracticeRecordDetail,
  ExamRecord,
  CertificateRecord,
  PositionType,
  PositionName,
  EmployeePromotion,
  EmployeePosition,
  EmployeeCareerRecord,
  RestaurantConfig,
  InfoConfig,
  RestaurantRecord,
  RestaurantRecordDetail,
  AchievementTemplate,
  UserAchievement,
  AchievementProgress
};
