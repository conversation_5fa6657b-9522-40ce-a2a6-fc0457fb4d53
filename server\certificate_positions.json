{"certificatePositions": {"name": {"x": "centerX", "y": 580, "fontSize": 60, "fontWeight": "bold", "color": "#000000", "textAlign": "center", "description": "姓名位置 - 黑色粗体大字"}, "congratsText": {"x": "centerX", "y": 680, "fontSize": 26, "fontWeight": "bold", "color": "#000000", "textAlign": "center", "maxWidth": 650, "lineHeight": 40, "description": "恭喜词 - 黑色粗体，自动换行"}, "position": {"x": "centerX", "y": 900, "fontSize": 22, "fontWeight": "bold", "color": "#FFFFFF", "textAlign": "center", "description": "职位信息 - 白色粗体"}, "certificateNo": {"x": "centerX", "y": 1000, "fontSize": 28, "fontWeight": "bold", "color": "#000000", "textAlign": "center", "description": "证书编号 - 黑色粗体"}, "issueDate": {"x": 580, "y": 1150, "fontSize": 18, "fontWeight": "bold", "color": "#988A79", "textAlign": "left", "description": "颁发日期 - 棕色"}, "validDate": {"x": 580, "y": 1180, "fontSize": 18, "fontWeight": "bold", "color": "#988A79", "textAlign": "left", "description": "有效期至 - 棕色"}}, "instructions": {"howToUse": "修改此文件中的坐标值，然后重新生成证书", "coordinateSystem": "左上角为(0,0)，X轴向右，Y轴向下", "centerX": "canvas.width / 2，会自动计算画布中心点", "tips": ["使用调试接口查看当前坐标", "根据网格线调整位置", "修改后需要重启服务或重新加载配置"]}}