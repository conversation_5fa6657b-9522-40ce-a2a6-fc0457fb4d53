// 角色数据模型
let roleData = [
  {
    id: 1,
    roleName: '超级管理员',
    roleCode: 'ROLE_ADMIN',
    sort: 1,
    status: true,
    remark: '系统超级管理员',
    createTime: '2024-03-20 10:00:00',
    permissions: ['system:user:query', 'system:user:add', 'system:user:edit', 'system:user:delete',
                 'system:role:query', 'system:role:add', 'system:role:edit', 'system:role:delete',
                 'system:menu:query', 'system:menu:add', 'system:menu:edit', 'system:menu:delete']
  },
  {
    id: 2,
    roleName: '普通用户',
    roleCode: 'ROLE_USER',
    sort: 2,
    status: true,
    remark: '普通用户角色',
    createTime: '2024-03-20 10:00:00',
    permissions: ['system:user:query', 'system:role:query', 'system:menu:query']
  },
  {
    id: 3,
    roleName: '访客',
    roleCode: 'ROLE_GUEST',
    sort: 3,
    status: false,
    remark: '访客角色',
    createTime: '2024-03-20 10:00:00',
    permissions: []
  }
];

// 获取角色列表
exports.getRoleList = (query = {}) => {
  let result = [...roleData];
  
  // 支持按角色名称过滤
  if (query.roleName) {
    result = result.filter(role => 
      role.roleName.toLowerCase().includes(query.roleName.toLowerCase())
    );
  }
  
  // 支持按状态过滤
  if (query.status !== undefined) {
    const status = query.status === 'true' || query.status === true;
    result = result.filter(role => role.status === status);
  }
  
  // 计算总数
  const total = result.length;
  
  // 分页处理
  const pageNum = parseInt(query.pageNum) || 1;
  const pageSize = parseInt(query.pageSize) || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = pageNum * pageSize;
  const list = result.slice(startIndex, endIndex);
  
  return {
    list,
    total
  };
};

// 获取角色权限
exports.getRolePermissions = (roleId) => {
  const role = roleData.find(role => role.id === roleId);
  if (!role) {
    return null;
  }
  
  return {
    roleId: role.id,
    permissions: role.permissions
  };
};

// 创建角色
exports.createRole = (role) => {
  const newRole = {
    ...role,
    id: Date.now(),
    createTime: new Date().toLocaleString(),
    permissions: []
  };
  
  roleData.push(newRole);
  return newRole;
};

// 更新角色
exports.updateRole = (role) => {
  const index = roleData.findIndex(item => item.id === role.id);
  if (index > -1) {
    roleData[index] = { 
      ...roleData[index],
      ...role,
      permissions: roleData[index].permissions
    };
    return roleData[index];
  }
  return null;
};

// 删除角色
exports.deleteRole = (id) => {
  const index = roleData.findIndex(role => role.id === id);
  if (index > -1) {
    const deleted = roleData.splice(index, 1)[0];
    return deleted;
  }
  return null;
};

// 更新角色权限
exports.updateRolePermissions = ({ roleId, permissions }) => {
  const role = roleData.find(role => role.id === roleId);
  if (role) {
    role.permissions = permissions;
    return {
      roleId,
      permissions
    };
  }
  return null;
}; 