const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const PositionName = require('./PositionName');
const Level = require('./Level');
const PositionType = require('./PositionType');

/**
 * 证书记录模型
 */
const CertificateRecord = sequelize.define('certificate_records', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  certificateNo: {
    type: DataTypes.STRING(50),
    field: 'certificate_no',
    allowNull: false,
    comment: '证书编号'
  },
  certificateName: {
    type: DataTypes.STRING(100),
    field: 'certificate_name',
    allowNull: false,
    comment: '证书名称,知识体系id的证书名称'
  },
  kbId: {
    type: DataTypes.STRING(50),
    field: 'kb_id',
    allowNull: false,
    comment: '知识体系ID'
  },
  obtainTime: {
    type: DataTypes.DATE,
    field: 'obtain_time',
    allowNull: false,
    comment: '获取证书时间'
  },
  positionName: {
    type: DataTypes.STRING(50),
    field: 'position_name',
    allowNull: false,
    comment: '岗位名称'
  },
  positionBelong: {
    type: DataTypes.STRING(50),
    field: 'position_belong',
    allowNull: true,
    comment: '岗位归属'
  },
  positionLevel: {
    type: DataTypes.STRING(50),
    field: 'position_level',
    allowNull: false,
    comment: '岗位等级'
  },
  employeeName: {
    type: DataTypes.STRING(50),
    field: 'employee_name',
    allowNull: false,
    comment: '员工名称'
  },
  employeeId: {
    type: DataTypes.INTEGER,
    field: 'employee_id',
    allowNull: false,
    comment: '员工ID'
  },
  certificateFileUrl: {
    type: DataTypes.STRING(255),
    field: 'certificate_file_url',
    allowNull: true,
    comment: '证书文件URL'
  },
  examRecordId: {
    type: DataTypes.INTEGER,
    field: 'exam_record_id',
    allowNull: true,
    comment: '关联的考试记录ID'
  },
  validUntil: {
    type: DataTypes.DATE,
    field: 'valid_until',
    allowNull: true,
    comment: '证书有效期'
  },
  enterpriseId: {
    type: DataTypes.STRING(50),
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  applicationId: {
    type: DataTypes.INTEGER,
    field: 'application_id',
    allowNull: true,
    comment: '关联的申请ID'
  },
  openId: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'open_id',
    comment: '微信openId'
  },
  delFlag: {
    type: DataTypes.INTEGER,
    field: 'del_flag',
    allowNull: false,
    defaultValue: 0,
    comment: '删除标志（0代表存在 1代表删除）'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    field: 'is_active',
    allowNull: true,
    defaultValue: true,
    comment: '是否有效(1有效 0无效)'
  },
  careerRecordId: {
    type: DataTypes.INTEGER,
    field: 'career_record_id',
    allowNull: true,
    comment: '员工履历记录ID'
  },
  positionBelongName: {
    type: DataTypes.STRING(100),
    field: 'position_belong_name',
    allowNull: true,
    comment: '岗位归属名称'
  },
  positionNameCn: {
    type: DataTypes.STRING(100),
    field: 'position_name_cn',
    allowNull: true,
    comment: '岗位名称'
  },
  positionLevelName: {
    type: DataTypes.STRING(100),
    field: 'position_level_name',
    allowNull: true,
    comment: '岗位等级名称'
  },
  examSubjectName: {
    type: DataTypes.STRING(100),
    field: 'exam_subject_name',
    allowNull: true,
    comment: '考试科目名称'
  }
}, {
  timestamps: false,
  tableName: 'certificate_records',
  indexes: [
    {
      name: 'idx_employee_id',
      fields: ['employee_id']
    },
    {
      name: 'idx_obtain_time',
      fields: ['obtain_time']
    },
    {
      name: 'idx_enterprise_id',
      fields: ['enterprise_id']
    }
  ]
});


CertificateRecord.belongsTo(PositionName, {
  foreignKey: 'position_name',
  targetKey: 'id', // 关联 PositionName 表的 name 字段
    as: 'positionNameInfo'
});


CertificateRecord.belongsTo(Level, {
  foreignKey: 'position_level',
  targetKey: 'id', // 关联 Level 表的 name 字段
  as: 'positionLevelInfo'
});


CertificateRecord.belongsTo(PositionType, {
  foreignKey: 'position_belong',
  targetKey: 'id', // 关联 PositionType 表的 name 字段
  as: 'positionBelongInfo'
});

module.exports = CertificateRecord;