/**
 * 测试成就弹出功能
 * 验证获取未弹出成就并标记为已弹出的功能
 */
require('dotenv').config();

console.log('成就弹出功能实现说明');
console.log('='.repeat(60));

console.log('\n功能描述：');
console.log('1. 获取用户 is_pop 为 0 的成就');
console.log('2. 返回这些成就给前端');
console.log('3. 将这些成就的 is_pop 标记为 1（已弹出）');

console.log('\n数据库修改：');
console.log('✅ 在 UserAchievement 模型中添加了 is_pop 字段');
console.log('   - 类型：tinyint(1)');
console.log('   - 默认值：0（未弹出）');
console.log('   - 注释：是否弹出：0-未弹出，1-已弹出');

console.log('\n新增的路由：');
console.log('GET /api/wechat/achievements/unpopped');
console.log('参数：openId (query parameter)');

console.log('\n新增的控制器方法：');
console.log('wechatController.getUnpoppedAchievements');

console.log('\n方法实现逻辑：');
console.log('1. 验证 openId 参数');
console.log('2. 查找用户信息');
console.log('3. 查询用户未弹出的成就（is_pop = 0, status = "achieved"）');
console.log('4. 包含成就模板信息（关联查询）');
console.log('5. 如果有未弹出成就，将其标记为已弹出（is_pop = 1）');
console.log('6. 返回格式化的成就数据');

console.log('\n查询条件：');
console.log('- openId: 用户的微信openId');
console.log('- isPop: 0 (未弹出)');
console.log('- status: "achieved" (已获得)');
console.log('- 按获得时间倒序排列');

console.log('\n返回数据格式：');
console.log(`{
  "code": 200,
  "data": {
    "achievements": [
      {
        "id": 1,
        "templateId": 1,
        "achievementName": "初入宝殿",
        "achievementIcon": "/uploads/achievements/1.png",
        "category": "learning",
        "status": "achieved",
        "achievedAt": "2024-01-01T10:00:00.000Z",
        "rewardPoints": 10,
        "achievementData": {...},
        "template": {
          "id": 1,
          "name": "初入宝殿",
          "description": "完成第一次练习",
          "icon": "/uploads/achievements/1.png",
          "category": "learning",
          "rewardPoints": 10
        }
      }
    ],
    "total": 1
  },
  "message": "获取未弹出成就成功"
}`);

console.log('\n使用场景：');
console.log('1. 用户登录小程序时调用此接口');
console.log('2. 检查是否有新获得的成就需要弹出显示');
console.log('3. 前端收到成就数据后显示弹窗');
console.log('4. 成就被标记为已弹出，下次不会重复显示');

console.log('\n数据库迁移：');
console.log('执行 server/src/sql/add_is_pop_to_user_achievements.sql');
console.log('该脚本会安全地添加 is_pop 字段（如果不存在）');

console.log('\n测试步骤：');
console.log('1. 执行数据库迁移脚本');
console.log('2. 确保用户有一些成就记录');
console.log('3. 将某些成就的 is_pop 设置为 0');
console.log('4. 调用 GET /api/wechat/achievements/unpopped?openId=xxx');
console.log('5. 验证返回的成就数据');
console.log('6. 检查数据库中这些成就的 is_pop 是否变为 1');
console.log('7. 再次调用接口，验证不会返回相同的成就');

console.log('\n注意事项：');
console.log('- 只返回 status = "achieved" 的成就');
console.log('- 使用企业过滤确保数据隔离');
console.log('- 包含详细的错误处理和日志记录');
console.log('- 支持批量更新多个成就的弹出状态');
