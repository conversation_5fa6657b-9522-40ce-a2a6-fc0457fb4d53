.org-structure-container {
    width: 100%;
  }
  
  .org-structure-layout {
    display: flex;
    justify-content: flex-start;
    /* min-height: calc(100vh - 200px); */
    height: 100%;
    overflow: hidden;
  }
  
  .department-tree-container {
    width: 280px;
    margin-right: 24px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .tree-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .tree-header h3 {
    margin: 0;
    font-size: 16px;
    line-height: 24px;
  }
  
  .tree-content {
    padding: 16px;
    flex: 1;
    overflow: auto;
  }
  .tipText{
    line-height: 25px;
    color:#999;
    font-size:12px;
    text-align: center;
  }
  
  .employee-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .table-header h3 {
    margin: 0;
    font-size: 16px;
    line-height: 24px;
  }
  
  .operation-area {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }
  
  .search-area {
    margin-bottom: 16px;
  }
  
  .search-form-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
  }
  
  .add-btn-wrapper {
    margin-left: auto;
  }
  
  .primary-button {
    background-color: #a18cd1;
    border-color: #a18cd1;
    min-width: 88px;
    color: #fff;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 32px;
    line-height: 32px;
  }
  
  .primary-button:hover {
    background-color: #8a65c9;
    border-color: #8a65c9;
    color: #fff;
  }
  
  
  
  
  .delete-icon,.edit-icon {
    display: none;
    color: #a18cd1;
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    margin-right: -4px;
  }
  
  .delete-icon:hover {
    color: #ff4d4f;
  }
  
  
  .tree-node-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    /* padding-right: 8px; */
    /* gap: 24px; */
  }
  .tree-node-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .tree-node-title:hover span {
    flex-shrink: 0;
  }
  .tree-node-title:hover .delete-icon,.tree-node-title:hover .edit-icon {
    display: inline-block;
  }
  /* :deep(.ant-tree-node-content-wrapper) {
    display: flex !important;
    flex: 1;
    padding-right: 0 !important;
  }
  
  :deep(.ant-tree-node-content-wrapper .ant-tree-title) {
    flex: 1;
    display: flex !important;
    align-items: center;
  } */