# 成就系统openId请求头修改说明

## 修改概述

按照您的要求，已将成就系统中所有的 `openId` 获取方式从查询参数(`req.query`)或请求体(`req.body`)改为从请求头(`req.headers.openid`)中获取。

## 修改文件列表

### 1. 用户成就控制器 - `server/src/controllers/userAchievementController.js`

#### 修改的函数：

**1.1 getUserAchievements() - 获取用户成就列表**
```javascript
// 修改前
const { page = 1, pageSize = 10, category, userId, openId } = req.query;

// 修改后
const { page = 1, pageSize = 10, category, userId } = req.query;
const openId = req.headers.openid; // 从请求头获取openId
```

**1.2 getUserAchievementProgress() - 获取用户成就进度列表**
```javascript
// 修改前
const { page = 1, pageSize = 10, status, userId, openId } = req.query;

// 修改后
const { page = 1, pageSize = 10, status, userId } = req.query;
const openId = req.headers.openid; // 从请求头获取openId
```

**1.3 getUserAchievementStats() - 获取用户成就统计**
```javascript
// 修改前
const { userId, openId } = req.query;

// 修改后
const { userId } = req.query;
const openId = req.headers.openid; // 从请求头获取openId
```

**1.4 getAchievementDetail() - 获取成就详情**
```javascript
// 修改前
const { userId, openId } = req.query;

// 修改后
const { userId } = req.query;
const openId = req.headers.openid; // 从请求头获取openId
```

**1.5 getAvailableAchievements() - 获取可用成就列表**
```javascript
// 修改前
const { userId, openId, category } = req.query;

// 修改后
const { userId, category } = req.query;
const openId = req.headers.openid; // 从请求头获取openId
```

**1.6 triggerAchievementCheck() - 手动触发成就检测**
```javascript
// 修改前
const { userId, openId, eventType, eventData } = req.body;

// 修改后
const { userId, eventType, eventData } = req.body;
const openId = req.headers.openid; // 从请求头获取openId
```

### 2. 成就中间件 - `server/src/middleware/achievementMiddleware.js`

#### 修改的函数：

**2.1 handlePracticeAchievementTrigger() - 处理练习成就触发**
```javascript
// 修改前
const { openId, time, positionName, positionLevel } = req.body;

// 修改后
const { time, positionName, positionLevel } = req.body;
const openId = req.headers.openid; // 从请求头获取openId
```

**2.2 handleExamAchievementTrigger() - 处理考试成就触发**
```javascript
// 修改前
const { openId } = req.body;

// 修改后
const openId = req.headers.openid; // 从请求头获取openId
```

**2.3 handleStudyTimeAchievementTrigger() - 处理学习时间成就触发**
```javascript
// 修改前
const { openId } = req.body || req.query;

// 修改后
const openId = req.headers.openid; // 从请求头获取openId
```

## API接口变化

### 变化后的API调用方式

所有用户成就相关的API调用现在需要将 `openId` 放在请求头中：

```javascript
// 示例：获取用户成就列表
const response = await fetch('/api/user-achievement/achievements', {
  method: 'GET',
  headers: {
    'openid': 'user_openid_value',
    'Content-Type': 'application/json'
  }
});
```

### 受影响的API端点：

1. `GET /api/user-achievement/achievements` - 获取用户成就列表
2. `GET /api/user-achievement/progress` - 获取用户成就进度
3. `GET /api/user-achievement/stats` - 获取用户成就统计
4. `GET /api/user-achievement/detail/:templateId` - 获取成就详情
5. `GET /api/user-achievement/available` - 获取可用成就列表
6. `POST /api/user-achievement/trigger` - 手动触发成就检测

### 中间件自动处理的接口：

这些接口的openId会被成就中间件自动从请求头中获取：

1. `POST /api/wechat/practice/*` - 练习相关接口
2. `POST /api/wechat/exam/*` - 考试相关接口
3. `POST /api/wechat/studyTime/*` - 学习时间相关接口

## 兼容性说明

### 向后兼容
- 修改后的代码仍然支持通过 `userId` 参数来查询成就
- 如果同时提供了 `userId` 和 `openId`，系统会优先使用找到的有效值

### 错误处理
- 如果请求头中没有 `openid` 且没有提供 `userId`，系统会返回相应的错误提示
- 所有相关的日志都已更新，便于调试和问题排查

## 测试建议

### 1. 前端测试
确保前端请求拦截器正确添加 `openid` 请求头：

```javascript
// 在 axios 拦截器中
request.interceptors.request.use(config => {
  const openId = getOpenIdFromStorage(); // 从存储中获取openId
  if (openId) {
    config.headers.openid = openId;
  }
  return config;
});
```

### 2. 后端测试
可以使用以下curl命令测试API：

```bash
# 测试获取用户成就列表
curl -X GET "http://localhost:3000/api/user-achievement/achievements" \
  -H "openid: test_openid_123" \
  -H "Content-Type: application/json"

# 测试手动触发成就检测
curl -X POST "http://localhost:3000/api/user-achievement/trigger" \
  -H "openid: test_openid_123" \
  -H "Content-Type: application/json" \
  -d '{"eventType": "practice_completed", "eventData": {"score": 85}}'
```

## 验证方法

1. **检查日志输出**：所有函数都会输出包含openId的日志，确认从请求头正确获取
2. **功能测试**：通过微信端或前端界面进行成就相关操作，验证功能正常
3. **API测试**：使用上述curl命令或Postman测试API接口

## 注意事项

1. **请求头格式**：确保前端传递的请求头名称是 `openid`（小写）
2. **中间件执行顺序**：成就中间件会在响应阶段自动处理，无需额外配置
3. **错误处理**：如果openId获取失败，相关功能会优雅降级，不会影响主要业务流程

---

**✅ 所有修改已完成，成就系统现在统一从请求头中获取openId！** 