const { Enterprise, Agent } = require('../models');
const { Op } = require('sequelize');
const { addEnterpriseId } = require('../utils/enterpriseFilter');

// 获取企业列表
exports.getEnterpriseList = async (req, res) => {
  try {
    const { name, code, status, pageNum = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }
    if (code) {
      where.code = { [Op.like]: `%${code}%` };
    }
    if (status !== undefined) {
      where.status = status === 'true' || status === '1' || status === 1;
    }
    
    // 分页查询 - 注意：企业不受企业ID过滤
    const { count, rows } = await Enterprise.findAndCountAll({
      where,
      order: [['id', 'ASC']],
      limit: parseInt(pageSize),
      offset: (parseInt(pageNum) - 1) * parseInt(pageSize)
    });
    
    res.json({
      code: 200,
      message: '获取企业列表成功',
      data: {
        list: rows,
        total: count
      }
    });
  } catch (error) {
    console.error('获取企业列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取企业列表失败',
      error: error.message
    });
  }
};

// 获取企业详情
exports.getEnterpriseDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 注意：企业不受企业ID过滤
    const enterprise = await Enterprise.findByPk(id);
    
    if (!enterprise) {
      return res.status(404).json({
        code: 404,
        message: '企业不存在'
      });
    }
    
    res.json({
      code: 200,
      message: '获取企业详情成功',
      data: enterprise
    });
  } catch (error) {
    console.error('获取企业详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取企业详情失败',
      error: error.message
    });
  }
};

// 创建企业
exports.createEnterprise = async (req, res) => {
  try {
    const { name, code, type, address, contact, phone, email, status } = req.body;
    
    // 检查编码是否已存在
    const existEnterprise = await Enterprise.findOne({ where: { code } });
    if (existEnterprise) {
      return res.status(400).json({
        code: 400,
        message: '企业编码已存在'
      });
    }
    
    // 创建企业 - 企业也应添加企业ID
    const enterprise = await Enterprise.create(
      addEnterpriseId({
        name,
        code,
        type,
        address,
        contact,
        phone,
        email,
        status
      })
    );
    
    res.json({
      code: 200,
      message: '创建企业成功',
      data: enterprise
    });
  } catch (error) {
    console.error('创建企业失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建企业失败',
      error: error.message
    });
  }
};

// 更新企业
exports.updateEnterprise = async (req, res) => {
  try {
    const { id, name, code, type, address, contact, phone, email, status } = req.body;
    
    // 检查企业是否存在
    const enterprise = await Enterprise.findByPk(id);
    if (!enterprise) {
      return res.status(404).json({
        code: 404,
        message: '企业不存在'
      });
    }
    
    // 检查编码是否已被其他企业使用
    if (code !== enterprise.code) {
      const existEnterprise = await Enterprise.findOne({ where: { code } });
      if (existEnterprise) {
        return res.status(400).json({
          code: 400,
          message: '企业编码已存在'
        });
      }
    }
    
    // 更新企业
    await enterprise.update({
      name,
      code,
      type,
      address,
      contact,
      phone,
      email,
      status
    });
    
    res.json({
      code: 200,
      message: '更新企业成功',
      data: enterprise
    });
  } catch (error) {
    console.error('更新企业失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新企业失败',
      error: error.message
    });
  }
};

// 删除企业
exports.deleteEnterprise = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查企业是否存在
    const enterprise = await Enterprise.findByPk(id);
    if (!enterprise) {
      return res.status(404).json({
        code: 404,
        message: '企业不存在'
      });
    }
    
    // 检查是否有关联的智能体
    const agentCount = await Agent.count({ where: { enterpriseId: id } });
    if (agentCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '该企业下存在智能体，无法删除'
      });
    }
    
    // 删除企业
    await enterprise.destroy();
    
    res.json({
      code: 200,
      message: '删除企业成功'
    });
  } catch (error) {
    console.error('删除企业失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除企业失败',
      error: error.message
    });
  }
}; 