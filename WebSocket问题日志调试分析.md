# WebSocket问题日志调试分析

## 当前状况
✅ nginx配置已更新为方案1  
❌ WebSocket连接仍然失败：`wss://cankao-admin-api.dev.lingmiaoai.com/`

**下一步：通过日志精确定位问题**

## 立即调试步骤

### 1. 查看nginx日志（关键）

在服务器上运行这些命令，然后再次尝试WebSocket连接：

```bash
# 清空日志，方便观察
> /var/log/nginx/access.log
> /var/log/nginx/error.log

# 实时监控日志
tail -f /var/log/nginx/access.log &
tail -f /var/log/nginx/error.log &
```

然后在浏览器中再次运行：
```javascript
const ws = new WebSocket('wss://cankao-admin-api.dev.lingmiaoai.com/');
```

**请将日志输出结果发给我！**

### 2. 验证nginx配置生效

```bash
# 检查nginx配置语法
nginx -t

# 确认配置已重新加载
nginx -s reload

# 查看nginx进程
ps aux | grep nginx
```

### 3. 测试HTTPS基础连接

```bash
# 测试HTTPS是否正常
curl -I https://cankao-admin-api.dev.lingmiaoai.com/

# 测试SSL握手
openssl s_client -connect cankao-admin-api.dev.lingmiaoai.com:443 -servername cankao-admin-api.dev.lingmiaoai.com < /dev/null
```

## 常见问题及对应日志

### 问题1：SSL证书问题
**日志特征**：
```
error.log: SSL_do_handshake() failed
access.log: 400 Bad Request
```

**解决**：检查证书文件
```bash
ls -la /etc/nginx/ssl/dev.lingmiaoai.crt
ls -la /etc/nginx/ssl/dev.lingmiaoai.key
openssl x509 -in /etc/nginx/ssl/dev.lingmiaoai.crt -text -noout | grep -A2 Validity
```

### 问题2：后端连接失败
**日志特征**：
```
error.log: connect() failed (111: Connection refused)
access.log: 502 Bad Gateway
```

**解决**：检查后端服务
```bash
netstat -tlnp | grep 31490
curl -I http://***********:31490/
```

### 问题3：WebSocket升级失败
**日志特征**：
```
access.log: 426 Upgrade Required
或者 400 Bad Request
```

**解决**：检查nginx配置中的WebSocket头设置

### 问题4：防火墙阻止
**日志特征**：
```
没有任何日志记录（请求根本没到达nginx）
```

**解决**：检查防火墙
```bash
iptables -L | grep 443
ufw status
```

## 如果日志没有记录

如果nginx日志完全没有记录，说明请求根本没有到达nginx服务器，可能的原因：

### 1. DNS解析问题
```bash
# 检查域名解析
nslookup cankao-admin-api.dev.lingmiaoai.com
dig cankao-admin-api.dev.lingmiaoai.com
```

### 2. 防火墙阻止
```bash
# 检查443端口是否开放
telnet cankao-admin-api.dev.lingmiaoai.com 443
```

### 3. nginx未监听443端口
```bash
# 检查端口监听
netstat -tlnp | grep :443
ss -tlnp | grep :443
```

## 备用测试方案

### 方案A：测试HTTP版本（临时）
在nginx中临时添加：
```nginx
server {
    listen 80;
    server_name cankao-admin-api.dev.lingmiaoai.com;
    
    location / {
        proxy_pass http://***********:31490;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $http_connection;
        proxy_set_header Host $host;
        proxy_buffering off;
    }
}
```

然后测试：
```javascript
const ws = new WebSocket('ws://cankao-admin-api.dev.lingmiaoai.com/');
```

### 方案B：直接测试后端
如果nginx有问题，先直接测试后端：
```javascript
// 如果能访问到后端IP
const ws = new WebSocket('ws://***********:31490/');
ws.onopen = () => console.log('后端直连成功！');
```

## 调试检查清单

请按顺序检查并报告结果：

1. **nginx错误日志输出**：`tail -f /var/log/nginx/error.log`
2. **nginx访问日志输出**：`tail -f /var/log/nginx/access.log`
3. **nginx配置语法检查**：`nginx -t`
4. **HTTPS基础连接测试**：`curl -I https://cankao-admin-api.dev.lingmiaoai.com/`
5. **443端口监听状态**：`netstat -tlnp | grep :443`
6. **域名解析检查**：`nslookup cankao-admin-api.dev.lingmiaoai.com`

**最重要的是第1和第2项的日志输出，这能直接告诉我们问题所在！** 