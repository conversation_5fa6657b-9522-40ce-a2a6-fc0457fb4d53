<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          v-model="searchForm"
          :items="searchFormItems"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <template #actions>
        <a-space>
          <a-button type="primary" @click="handleAddQuestion">
            <plus-outlined /> 新增题目
          </a-button>
          <a-button @click="handleShowImport">
            <upload-outlined /> 导入题目
          </a-button>
          <a-button type="primary" @click="handleExport">
            <download-outlined /> 导出Excel
          </a-button>
          <a-button danger :disabled="!selectedRowKeys.length" @click="handleBatchDelete">
            <delete-outlined /> 批量删除
          </a-button>
          <a-button v-if="knowledgeId" @click="backToList">
            <arrow-left-outlined /> 返回知识库
          </a-button>
        </a-space>
      </template>
    </page-header>

    <!-- 知识库信息卡片 -->
    <a-card v-if="knowledgeDetail.id" class="mb-4">
      <template #title>知识库信息</template>
      <a-descriptions :column="3">
        <a-descriptions-item label="知识库名称">{{ knowledgeDetail.fileName }}</a-descriptions-item>
        <a-descriptions-item label="文件类型">{{ knowledgeDetail.fileType }}</a-descriptions-item>
        <a-descriptions-item label="文件大小">{{ formatFileSize(knowledgeDetail.fileSize) }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDate(knowledgeDetail.createdTime) }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ formatDate(knowledgeDetail.updatedTime) }}</a-descriptions-item>
      </a-descriptions>
    </a-card>

    <!-- 表格区域 -->
    <base-table
      :columns="columns"
      :data-source="questionList"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      :action-config="{edit: true, delete: true}"
      :delete-title="'确定要删除该题目吗？'"
      @edit="handleEditQuestion"
      @delete="handleDeleteQuestion"
      :row-selection="rowSelection"
    >
      <template #bodyCell="{ column, record }">
        <!-- 题目类型列 -->
        <template v-if="column.key === 'type'">
          <a-tag :color="record.type === '智能出题' ? 'blue' : 'green'">{{ record.type }}</a-tag>
        </template>

        <!-- 题目内容列 -->
        <template v-if="column.key === 'question'">
          <div class="question-content-cell">
            <a-tooltip
              placement="topLeft"
              :get-popup-container="(triggerNode) => triggerNode.parentElement"
              :overlay-style="{ maxWidth: '400px', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }"
            >
              <template #title>{{ record.question }}</template>
              {{ record.question }}
            </a-tooltip>
          </div>
        </template>

        <!-- 答案内容列 -->
        <template v-if="column.key === 'answer'">
          <div class="answer-content-cell">
            <a-tooltip
              placement="topLeft"
              :get-popup-container="(triggerNode) => triggerNode.parentElement"
              :overlay-style="{ maxWidth: '500px', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }"
            >
              <template #title>{{ record.answer }}</template>
              {{ record.answer }}
            </a-tooltip>
          </div>
        </template>

        <!-- 创建时间列 -->
        <template v-if="column.key === 'createdAt'">
          <span>{{ formatDate(record.created_at) }}</span>
        </template>

        <!-- 更新时间列 -->
        <template v-if="column.key === 'updatedAt'">
          <span>{{ formatDate(record.updated_at) }}</span>
        </template>

        <!-- 知识库名称列 -->
        <template v-if="column.key === 'knowledgeBase'">
          <span>{{ record.knowledgeBase ? (record.knowledgeBase.name || record.knowledgeBase.fileName) : (record.knowledgeBaseName || '-') }}</span>
        </template>

        <!-- 所属岗位列 -->
        <template v-if="column.key === 'positionName'">
          <a-tag color="green">{{ record.positionName || '-' }}</a-tag>
        </template>
      </template>
    </base-table>

    <!-- 新增/编辑题目弹窗 -->
    <a-modal
      :visible="modalVisible"
      :title="modalType === 'add' ? '新增题目' : '编辑题目'"
      @ok="handleModalSubmit"
      @cancel="modalVisible = false"
      :confirm-loading="submitLoading"
      width="700px"
    >
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="题目" name="question">
          <a-textarea
            v-model:value="formState.question"
            placeholder="请输入题目内容"
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
        <a-form-item label="答案" name="answer">
          <a-textarea
            v-model:value="formState.answer"
            placeholder="请输入答案内容"
            :auto-size="{ minRows: 4, maxRows: 8 }"
          />
        </a-form-item>
        <a-form-item v-if="knowledgeOptions.length > 0" label="知识库" name="knowledgeBaseId">
          <a-select
            v-model:value="formState.knowledgeBaseId"
            placeholder="请选择知识库"
            :options="knowledgeOptions"
            show-search
            option-filter-prop="label"
            :filter-option="(input, option) => {
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入题目弹窗 -->
    <a-modal
      :visible="importModalVisible"
      title="导入题目"
      @ok="handleImportSubmit"
      @cancel="importModalVisible = false"
      :confirm-loading="importLoading"
      width="600px"
    >
      <div class="import-content">
        <div class="import-tips">
          <h4>导入说明：</h4>
          <ul>
            <li>1、红色列为必填项</li>
            <li>2、知识库名对应系统中知识库名称，需完全匹配</li>
            <li>3、题目和答案不能为空</li>
            <li>4、请先下载模板文件，按照模板格式填写数据</li>
            <li>5、支持格式：.xlsx, .xls，文件大小限制：10MB</li>
          </ul>
        </div>

        <div class="import-actions">
          <a-space direction="vertical" style="width: 100%;">
            <div>
              <a-button type="link" @click="handleDownloadTemplate" :loading="downloadLoading">
                下载导入模板
              </a-button>
            </div>

            <div>
              <a-upload
                :file-list="fileList"
                :before-upload="beforeUpload"
                @remove="handleRemoveFile"
                accept=".xlsx,.xls"
              >
                <a-button>
                  <upload-outlined />
                  选择文件
                </a-button>
              </a-upload>
            </div>

            <div v-if="importErrors.length > 0" class="import-result">
              <a-alert
                message="数据验证失败"
                type="error"
                show-icon
                closable
                @close="importErrors = []"
              />
              <div class="error-details">
                <div v-for="(error, index) in importErrors" :key="index" class="error-item">
                  {{ error }}
                </div>
              </div>
            </div>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import {
  PlusOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  UploadOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue';
import { useTablePagination, getToolTip } from '@/utils/common';
import { SearchFormCard } from '@/components/SearchForm';
import {
  getQuestionList,
  addQuestion,
  updateQuestion,
  deleteQuestion,
  getKnowledgeBaseDetail,
  getKnowledgeOptions,
  downloadTemplate,
  importQuestions,
  exportQuestions
} from '@/api/knowledge-base/questions';
import { getPositionNameOptions } from '@/api/organization/position';

// 路由参数
const route = useRoute();
const router = useRouter();
const knowledgeId = computed(() => route.params.id);

// 知识库详情
const knowledgeDetail = ref({});

// 获取知识库详情
const fetchKnowledgeDetail = async () => {
  if (!knowledgeId.value) return;

  try {
    const res = await getKnowledgeBaseDetail(knowledgeId.value);
    if (res?.code === 200 && res?.data) {
      knowledgeDetail.value = res.data;
    }
  } catch (error) {
    console.error('获取知识库详情失败:', error);
  }
};

// 题目列表数据
const questionList = ref([]);
const loading = ref(false);

// 初始化分页对象
const paginationRef = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

// 搜索表单配置
const searchForm = reactive({
  question: '',
  type: undefined,
  knowledgeBaseId: undefined,
  position: undefined
});

// 搜索表单配置项
const searchFormItems = [
  {
    label: '题目内容',
    field: 'question',
    type: 'input',
    placeholder: '请输入题目内容',
    width: '200px'
  },
  {
    label: '题目类型',
    field: 'type',
    type: 'select',
    placeholder: '请选择题目类型',
    options: [
      { label: '人工出题', value: '人工出题' },
      { label: '智能出题', value: '智能出题' }
    ],
    width: '180px'
  },
  {
    label: '所属知识库',
    field: 'knowledgeBaseId',
    type: 'select',
    placeholder: '请选择知识库',
    options: ref([]),
    showSearch: true,
    optionFilterProp: 'children',
    props: {
      style: 'width: 400px'
    },
    width: '180px'
  },
  {
    label: '所属岗位',
    field: 'position',
    type: 'select',
    placeholder: '请选择所属岗位',
    width: '180px',
    options: ref([]),
    selectLabel: 'label',
    selectValue: 'value'
  }
];

// 初始化加载数据
const fetchQuestions = async (params = {}) => {
  loading.value = true;

  try {
    // 构建查询参数，如果没有传递参数则使用当前的searchForm
    const queryParams = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm,  // 先添加searchForm的值
      ...params       // 再用传入的params覆盖（如果有的话）
    };

    console.log('发送请求参数:', queryParams);

    // 如果有知识库ID，则按知识库ID筛选
    const apiId = knowledgeId.value || 'all';
    const res = await getQuestionList(apiId, queryParams);
    console.log('查询结果:', res);

    // 将查询结果赋值给questionList
    questionList.value = res.list || [];

    // 更新分页信息，但保留当前页码
    if (res.total !== undefined) {
      updatePagination({
        total: res.total,
        // 不更新current，保持用户选择的页码
      });
      console.log('更新后的分页信息:', pagination);
    }

  } catch (error) {
    console.error('获取题目列表失败:', error);
    message.error('获取题目列表失败');
    questionList.value = [];
    updatePagination({ total: 0 });
  } finally {
    loading.value = false;
  }
};

// 使用表格分页
const { pagination, handleTableChange, updatePagination, resetPagination } = useTablePagination({
  fetchData: fetchQuestions,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
});

// 选择行配置
const selectedRowKeys = ref([]);
const rowSelection = {
  onChange: (keys) => {
    selectedRowKeys.value = keys;
  }
};

// 表格列定义
const columns = [
  {
    title: '题目内容',
    dataIndex: 'question',
    key: 'question',
    width: '25%'
  },
  {
    title: '答案内容',
    dataIndex: 'answer',
    key: 'answer',
    width: '25%'
  },
  {
    title: '所属知识库',
    dataIndex: 'knowledgeBase',
    key: 'knowledgeBase',
    width: '15%'
  },
  {
    title: '所属岗位',
    dataIndex: 'positionName',
    key: 'positionName',
    width: '120px'
  },
  {
    title: '题目类型',
    dataIndex: 'type',
    key: 'type',
    width: '100px'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'createdAt',
    width: '160px',
    sorter: true
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    key: 'updatedAt',
    width: '160px',
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: '150px',
    fixed: 'right'
  }
];

// 知识库选项
const knowledgeOptions = ref([]);
const positionOptions = ref([]);

// 模态框相关
const modalVisible = ref(false);
const modalType = ref('add'); // 'add' or 'edit'
const formRef = ref(null);
const submitLoading = ref(false);
const formState = ref({
  id: '',
  question: '',
  answer: '',
  knowledgeBaseId: ''
});

// 导入相关数据
const importModalVisible = ref(false);
const importLoading = ref(false);
const fileList = ref([]);
const importErrors = ref([]);
const selectedFile = ref(null);
const downloadLoading = ref(false);

// 表单验证规则
const rules = {
  question: [
    { required: true, message: '请输入题目内容', trigger: 'blur' },
    { max: 500, message: '题目内容不能超过500个字符', trigger: 'blur' }
  ],
  answer: [
    { required: true, message: '请输入答案内容', trigger: 'blur' },
    { max: 1000, message: '答案内容不能超过1000个字符', trigger: 'blur' }
  ],
  knowledgeBaseId: [
    { required: true, message: '请选择知识库', trigger: 'change' }
  ]
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0 || !bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 处理搜索
const handleSearch = (values) => {
  resetPagination(); // 使用resetPagination替代手动设置pagination.current = 1
  Object.assign(searchForm, values);
  fetchQuestions(searchForm);
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = undefined;
  });
  resetPagination(); // 使用resetPagination替代手动设置pagination.current = 1
  fetchQuestions();
};

// 新增题目
const handleAddQuestion = () => {
  modalType.value = 'add';
  formState.value = {
    id: '',
    question: '',
    answer: '',
    knowledgeBaseId: knowledgeId.value || ''
  };
  modalVisible.value = true;
};

// 编辑题目
const handleEditQuestion = (record) => {
  modalType.value = 'edit';
  formState.value = {
    id: record.id,
    question: record.question,
    answer: record.answer,
    knowledgeBaseId: record.knowledge_base_id
  };
  modalVisible.value = true;
};

// 提交表单
const handleModalSubmit = () => {
  formRef.value.validate().then(async () => {
    submitLoading.value = true;

    try {
      const apiId = formState.value.knowledgeBaseId;

      if (modalType.value === 'add') {
        // 新增题目
        const response = await addQuestion(apiId, {
          question: formState.value.question,
          answer: formState.value.answer
        });


          message.success('添加题目成功');
          modalVisible.value = false;
          await fetchQuestions(searchForm);

      } else {
        // 编辑题目
        const response = await updateQuestion(apiId, {
          questionId: formState.value.id,
          question: formState.value.question,
          answer: formState.value.answer
        });


          message.success('更新题目成功');
          modalVisible.value = false;
          await fetchQuestions(searchForm);

      }
    } catch (error) {
      console.error(modalType.value === 'add' ? '添加题目失败:' : '更新题目失败:', error);
      message.error(modalType.value === 'add' ? '添加题目失败' : '更新题目失败');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 删除题目
const handleDeleteQuestion = async (record) => {
  try {
    const response = await deleteQuestion(record.knowledge_base_id, record.id);


      message.success('删除题目成功');
      await fetchQuestions(searchForm);

  } catch (error) {
    console.error('删除题目失败:', error);
    message.error('删除题目失败');
  }
};

// 批量删除
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请至少选择一条记录');
    return;
  }

  Modal.confirm({
    title: '确定要删除选中的题目吗？',
    content: '删除后将无法恢复',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      const deletePromises = selectedRowKeys.value.map(id => {
        const record = questionList.value.find(item => item.id === id);
        return deleteQuestion(record.knowledge_base_id, id);
      });

      try {
        await Promise.all(deletePromises);
        message.success('批量删除成功');
        selectedRowKeys.value = [];
        await fetchQuestions(searchForm);
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    }
  });
};

// 返回知识库列表
const backToList = () => {
  router.push('/knowledge-base');
};

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchKnowledgeDetail();
  }
  fetchQuestions();
}, { immediate: true });

// 组件挂载时执行
onMounted(async () => {
  await loadKnowledgeOptions(); // 先加载知识库选项
  await fetchPositionOptions(); // 加载岗位选项
  await fetchQuestions();       // 再加载题目列表
  if (knowledgeId.value) {
    fetchKnowledgeDetail();
  }
});

// 加载知识库选项
const loadKnowledgeOptions = async () => {
  try {
    console.log('开始加载知识库选项...');
    const res = await getKnowledgeOptions();
    console.log('获取知识库选项结果:', res);

    // 更新搜索表单中的知识库选项（响应式）
    const knowledgeBaseItem = searchFormItems.find(item => item.field === 'knowledgeBaseId');
    if (knowledgeBaseItem) {
      knowledgeBaseItem.options.value.splice(0, knowledgeBaseItem.options.value.length, ...res);
      console.log('已更新搜索表单知识库选项:', knowledgeBaseItem.options.value);
    }

    // 更新模态框中的知识库选项
    knowledgeOptions.value = res;
    console.log('已更新模态框知识库选项:', res);

  } catch (error) {
    console.error('获取知识库选项失败:', error);
  }
};

// 下载模板
const handleDownloadTemplate = async () => {
  downloadLoading.value = true;

  try {
    const response = await downloadTemplate();

    // 创建blob链接进行下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '题目导入模板.xlsx';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    message.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  } finally {
    downloadLoading.value = false;
  }
};

// 显示导入弹窗
const handleShowImport = () => {
  importModalVisible.value = true;
  importErrors.value = [];
  fileList.value = [];
  selectedFile.value = null;
};

// 文件上传前处理
const beforeUpload = (file) => {
  // 检查文件类型
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传Excel文件');
    return false;
  }

  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB');
    return false;
  }

  // 更新文件列表和选中文件
  fileList.value = [file];
  selectedFile.value = file;
  importErrors.value = [];

  // 返回false阻止自动上传
  return false;
};

// 移除文件
const handleRemoveFile = () => {
  fileList.value = [];
  selectedFile.value = null;
  importErrors.value = [];
};

// 执行导入
const handleImportSubmit = async () => {
  if (!selectedFile.value) {
    message.error('请选择要导入的Excel文件');
    return;
  }

  importLoading.value = true;
  importErrors.value = [];

  try {
    const response = await importQuestions(selectedFile.value);

    message.success(response.message || '导入成功');
    importModalVisible.value = false;

    // 刷新题目列表
    await fetchQuestions(searchForm);

  } catch (error) {
    console.error('导入失败:', error);

    if (error.response && error.response.data) {
      const { message: errorMessage, errors } = error.response.data;

      if (errors && errors.length > 0) {
        // 显示详细的验证错误
        importErrors.value = errors;
        message.error(errorMessage || '导入失败，请检查数据格式');
      } else {
        message.error(errorMessage || '导入失败');
      }
    } else {
      message.error('导入失败');
    }
  } finally {
    importLoading.value = false;
  }
};

// 加载岗位选项
const fetchPositionOptions = async () => {
  try {
    const res = await getPositionNameOptions();
    // 处理岗位名称数据为选项格式 - 确保是数组
    const positions = Array.isArray(res) ? res :
                      (res && res.rows ? res.rows : []);

    // 将岗位数据转换为标准格式
    positionOptions.value = [
      // 添加通用选项
      { label: '通用', value: 'COMMON' },
      // 原有岗位数据
      ...positions.map(item => ({
        label: item.dictLabel || item.name || item.label,
        value: item.id || item.value || item.dictValue
      }))
    ];

    // 更新搜索表单中的岗位选项
    const positionItem = searchFormItems.find(item => item.field === 'position');
    if (positionItem) {
      positionItem.options.value = positionOptions.value;
    }

    console.log('已加载岗位选项:', positionOptions.value);
  } catch (error) {
    console.error('获取岗位选项失败:', error);
  }
};

// 导出Excel
const handleExport = async () => {
  try {
    // 显示加载提示
    message.loading({ content: '正在导出Excel，请稍候...', key: 'exportLoading', duration: 0 });

    // 构建导出参数，使用当前的搜索条件
    const exportParams = { ...searchForm };

    // 调用导出API
    const apiId = knowledgeId.value || 'all';
    const response = await exportQuestions(apiId, exportParams);

    // 获取文件名
    let fileName = '知识库题目.xlsx';
    if (knowledgeDetail.value.id) {
      fileName = `${knowledgeDetail.value.fileName || '知识库'}题目.xlsx`;
    }

    // 创建blob链接进行下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    // 关闭加载提示并显示成功提示
    message.success({ content: 'Excel导出成功', key: 'exportLoading' });

  } catch (error) {
    console.error('导出Excel失败:', error);
    message.error({ content: '导出Excel失败', key: 'exportLoading' });
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.question-content-cell,
.answer-content-cell {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.import-content {
  padding: 16px 0;
}


.import-actions {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 24px;
  text-align: center;

  .import-result {
    margin-top: 16px;
    text-align: left;

    .error-details {
      margin-top: 8px;
      max-height: 200px;
      overflow-y: auto;
      padding: 8px 12px;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;

      .error-item {
        color: #cf1322;
        font-size: 13px;
        line-height: 1.6;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
