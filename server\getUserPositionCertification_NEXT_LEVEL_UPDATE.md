# getUserPositionCertification 方法 - 下一等级查询修改

## 修改目标
修改 `getUserPositionCertification` 方法，使其基于当前晋升岗位的**下一个等级**进行查询，而不是当前等级。

## 修改位置
- **文件**: `server/src/controllers/weichat/wechatExamListController.js`
- **方法**: `getUserPositionCertification`
- **行数**: 约 616-700 行

## 核心修改逻辑

### 1. 查找下一个等级
```javascript
// 查找下一级（orderNum更大的值）
const nextLevel = await Level.findOne({
    where: {
        enterpriseId,
        orderNum: { [Op.gt]: currentLevel.orderNum }, // 查找orderNum大于当前级别的
        status: '1' // 确保是有效状态
    },
    order: [['orderNum', 'ASC']], // 按orderNum升序排列，获取最接近的下一级
});

// 如果没有下一级，则使用当前级
const targetLevelId = nextLevel ? nextLevel.id : currentLevel.id;
const targetLevelName = nextLevel ? nextLevel.name : currentLevel.name;
```

### 2. 等级查找逻辑说明
- **orderNum 规则**: orderNum 越大表示等级越高
- **查找条件**: `orderNum > currentLevel.orderNum`
- **排序方式**: 按 orderNum 升序，获取最接近的下一级
- **兜底处理**: 如果没有下一级，使用当前级别

### 3. 修改前后对比

#### 修改前
```javascript
// 直接使用当前岗位的等级
const targetLevelId = employeePosition.levelId;
const targetLevelName = currentLevel?.name || '';
```

#### 修改后
```javascript
// 查找下一个等级
const nextLevel = await Level.findOne({
    where: {
        enterpriseId,
        orderNum: { [Op.gt]: currentLevel.orderNum },
        status: '1'
    },
    order: [['orderNum', 'ASC']],
});

// 使用下一个等级或当前等级（如果没有下一级）
const targetLevelId = nextLevel ? nextLevel.id : currentLevel.id;
const targetLevelName = nextLevel ? nextLevel.name : currentLevel.name;
```

## 影响的查询

### 1. 考试配置查询
```javascript
// 基于下一个等级查询考试配置
const examConfigs = await ExamConfig.findAll({
    where: {
        enterpriseId,
        positionName: positionId,
        positionLevel: targetLevelId.toString(), // 使用下一个等级
        status: '必考'
    }
});
```

### 2. 晋升时间记录查询
```javascript
// 基于下一个等级查询晋升记录
const promotionRecord = await EmployeePromotion.findOne({
    where: {
        employeeId: employee.id,
        enterpriseId,
        positionId: positionId,
        levelId: targetLevelId, // 使用下一个等级
        positionTypeId: employeePosition.positionTypeId
    }
});
```

### 3. 证书数量查询
```javascript
// 基于下一个等级查询证书数量
const certificateCount = await CertificateRecord.count({
    where: {
        enterpriseId,
        openId,
        positionName: positionId,
        positionLevel: targetLevelId, // 使用下一个等级
        delFlag: 0
    }
});
```

### 4. 考试科目状态查询
```javascript
// 基于下一个等级获取考试科目信息和状态
const { examResults } = await getExamSubjectsAndStatus(
    finalExamConfigs, 
    openId, 
    enterpriseId, 
    positionId, 
    targetLevelId // 使用下一个等级
);
```

## 业务逻辑说明

### 1. 为什么要查找下一个等级？
- **晋升考试**: 员工考试是为了晋升到下一个等级
- **考试配置**: 考试配置通常针对目标等级（下一级）设置
- **证书认证**: 证书是为了证明达到下一个等级的能力

### 2. 等级层次结构
```
初级 (orderNum: 1) → 中级 (orderNum: 2) → 高级 (orderNum: 3) → 专家 (orderNum: 4)
```

### 3. 查询示例
- 员工当前等级：中级 (orderNum: 2)
- 查找下一级：高级 (orderNum: 3)
- 查询考试配置：基于高级等级的考试要求

## 调试信息
添加了控制台日志输出：
```javascript
console.log('当前等级:', currentLevel.name, 'ID:', currentLevel.id, 'orderNum:', currentLevel.orderNum);
console.log('找到下一级:', nextLevel ? `${nextLevel.name} (ID: ${nextLevel.id}, orderNum: ${nextLevel.orderNum})` : '未找到，使用当前级');
```

## 兼容性处理
- 如果没有找到下一级等级，则使用当前等级
- 保持原有的错误处理逻辑
- 确保所有查询都基于统一的目标等级

## 测试建议
1. 测试有下一级等级的情况
2. 测试没有下一级等级的情况（最高级员工）
3. 验证考试配置、证书数量等查询结果的正确性
4. 检查控制台日志输出是否符合预期 