const express = require('express');
const router = express.Router();
const infoConfigController = require('../controllers/infoConfigController');
const { requireLogin } = require('../middleware/auth');
const { createUpload } = require('../utils/fileUpload');

// 创建专门的info-config上传实例
const infoConfigUpload = createUpload('info-config');

// 获取当前企业的信息配置
router.get('/', requireLogin, infoConfigController.getInfoConfig);

// 更新当前企业的信息配置
router.put('/', requireLogin, infoConfigController.updateInfoConfig);

// 上传信息配置图片
router.post('/upload', requireLogin, infoConfigUpload.single('file'), infoConfigController.uploadConfigImage);

module.exports = router; 