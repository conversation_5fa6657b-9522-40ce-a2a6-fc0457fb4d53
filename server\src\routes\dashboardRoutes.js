const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');

// 获取仪表盘概览数据
router.get('/overview', dashboardController.getDashboardOverview);

// 获取练习排行数据
router.get('/practice-ranking', dashboardController.getPracticeRanking);

// 获取证书排行榜数据
router.get('/certificate-ranking', dashboardController.getCertificateRanking);

// 获取科目练习和考试统计数据
router.get('/subject-exam-stats', dashboardController.getSubjectExamStats);

// 获取考试统计数据
router.get('/exam-statistics', dashboardController.getExamStatistics);

// 获取岗位通过率统计数据
router.get('/position-pass-rates', dashboardController.getPositionPassRates);

module.exports = router;
