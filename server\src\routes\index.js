const express = require('express');
const router = express.Router();
const userRoutes = require('./userRoutes');
const roleRoutes = require('./roleRoutes');
const menuRoutes = require('./menuRoutes');
const enterpriseRoutes = require('./enterpriseRoutes');
const agentRoutes = require('./agentRoutes');
const dashboardRoutes = require('./dashboardRoutes');
const organizationRoutes = require('./organization');

const systemSettingRoutes = require('./systemSettingRoutes');
const dictionaryRoutes = require('./dictionaryRoutes');
const wechatRoutes = require('./wechatRoutes');
const examReviewRoutes = require('./exam-manage/review');
const examRecordRoutes = require('./exam-manage/exam-record');
const testRoutes = require('./exam-manage/test');
const announcementRoutes = require('./announcement');
const feedbackRouter = require('./feedback');
const certificateRouter = require('./certificate');
const infoConfigRoutes = require('./infoConfig');
const dimensionRoutes = require('./dimensionRoutes');
const achievementRoutes = require('./achievementRoutes');
const userAchievementRoutes = require('./userAchievementRoutes');

// 用户相关路由
router.use('/system/user', userRoutes);

// 角色相关路由
router.use('/system/role', roleRoutes);

// 菜单相关路由
router.use('/system/menu', menuRoutes);

// 企业相关路由
router.use('/system/enterprise', enterpriseRoutes);

// 智能体相关路由
router.use('/system/agent', agentRoutes);

// 仪表盘相关路由
router.use('/dashboard', dashboardRoutes);

// 组织相关路由
router.use('/organization', organizationRoutes);

// 知识库相关路由


// 系统设置路由
router.use('/system/setting', systemSettingRoutes);

// 字典管理路由
router.use('/system/dict', dictionaryRoutes);

// 微信小程序相关路由
router.use('/wechat', wechatRoutes);

// 考试审核管理路由
router.use('/exam/review', examReviewRoutes);

// 考试记录管理路由
router.use('/exam/record', examRecordRoutes);

// 测试功能路由 (仅用于开发和测试环境)
router.use('/test', testRoutes);

// 公告相关路由
router.use('/announcements', announcementRoutes);

// 注册反馈路由
router.use('/feedback', feedbackRouter);

// 证书相关路由
router.use('/certificate', certificateRouter);

// 信息配置路由
router.use('/info-config', infoConfigRoutes);

// 维度数据路由
router.use('/dimension', dimensionRoutes);

// 成就管理路由
router.use('/achievement', achievementRoutes);

// 用户成就路由
router.use('/user-achievement', userAchievementRoutes);

module.exports = router;
