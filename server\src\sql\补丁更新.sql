-- ========================================
-- 数据库补丁更新SQL
-- 用于将cankao-admin-dev-可删.sql同步到init.sql标准
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 1. 修复org_level表缺失的字段和外键约束
-- ========================================

-- 添加缺失的operator_id字段
ALTER TABLE `org_level` 
ADD COLUMN `operator_id` int DEFAULT NULL COMMENT '操作人ID' AFTER `order_num`;

-- 添加外键约束
ALTER TABLE `org_level` 
ADD KEY `fk_level_operator` (`operator_id`);

ALTER TABLE `org_level` 
ADD CONSTRAINT `fk_level_operator` FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- ========================================
-- 2. 修复exam_records表confirm_status字段默认值
-- ========================================

-- 修改confirm_status字段默认值从'1'改为'3'
ALTER TABLE `exam_records` 
MODIFY COLUMN `confirm_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '3' COMMENT '确认状态：2已通过、3未通过';

-- ========================================
-- 3. 检查并确保所有表都有企业字段enterprise_id
-- ========================================

-- 验证所有表的enterprise_id字段是否存在（这些表在两个文件中都已存在）
-- 如果后续发现其他表缺失enterprise_id，可以在这里添加

-- ========================================
-- 4. 检查索引完整性
-- ========================================

-- 确保所有重要的索引都存在
-- 这些在两个文件中基本一致，不需要额外修改

-- ========================================
-- 5. 数据类型一致性检查
-- ========================================

-- 确保bigint类型字段一致性
-- exam_records表的id字段
ALTER TABLE `exam_records` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID';

-- certificate_records表的id字段  
ALTER TABLE `certificate_records` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID';

-- announcement表的id字段
ALTER TABLE `announcement` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID';

-- ========================================
-- 6. 字符集和排序规则统一
-- ========================================

-- 确保所有varchar字段使用utf8mb4字符集
-- 这在两个文件中基本一致

-- ========================================
-- 7. 默认值修正
-- ========================================

-- 确保enterprise_id字段的默认值一致
-- dictionary_data表
ALTER TABLE `dictionary_data` 
MODIFY COLUMN `enterprise_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业ID';

-- dictionary_type表
ALTER TABLE `dictionary_type` 
MODIFY COLUMN `enterprise_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32' COMMENT '企业ID';

-- ========================================
-- 8. 外键约束完整性检查
-- ========================================

-- 检查并修复可能缺失的外键约束
-- dictionary_data表的外键已存在

-- ========================================
-- 执行完成提示
-- ========================================

SELECT '数据库补丁更新完成！' AS message;

SET FOREIGN_KEY_CHECKS = 1; 