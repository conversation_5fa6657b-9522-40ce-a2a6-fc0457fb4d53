﻿USE ayl_admin;

-- 首次学习进度成就模板插入脚本

-- 首次学习达到30%进度成就
INSERT INTO achievement_template (
    name, 
    description, 
    icon, 
    category, 
    rule_type, 
    trigger_condition, 
    reward_points, 
    is_active, 
    enterprise_id, 
    create_time, 
    update_time
) VALUES (
    '初学者', 
    '首次学习一门科目达到30%进度', 
    '🌱', 
    '学习进度', 
    'first_progress', 
    '{"type": "first_progress", "progress": 30}', 
    50, 
    1, 
    1, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);

-- 首次学习达到50%进度成就
INSERT INTO achievement_template (
    name, 
    description, 
    icon, 
    category, 
    rule_type, 
    trigger_condition, 
    reward_points, 
    is_active, 
    enterprise_id, 
    create_time, 
    update_time
) VALUES (
    '勤奋学习者', 
    '首次学习一门科目达到50%进度', 
    '📚', 
    '学习进度', 
    'first_progress', 
    '{"type": "first_progress", "progress": 50}', 
    100, 
    1, 
    1, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);

-- 首次学习达到80%进度成就
INSERT INTO achievement_template (
    name, 
    description, 
    icon, 
    category, 
    rule_type, 
    trigger_condition, 
    reward_points, 
    is_active, 
    enterprise_id, 
    create_time, 
    update_time
) VALUES (
    '坚持不懈', 
    '首次学习一门科目达到80%进度', 
    '💪', 
    '学习进度', 
    'first_progress', 
    '{"type": "first_progress", "progress": 80}', 
    150, 
    1, 
    1, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);

-- 首次学习完成（100%）成就
INSERT INTO achievement_template (
    name, 
    description, 
    icon, 
    category, 
    rule_type, 
    trigger_condition, 
    reward_points, 
    is_active, 
    enterprise_id, 
    create_time, 
    update_time
) VALUES (
    '首次完成', 
    '首次完成一门科目的学习', 
    '🎉', 
    '学习进度', 
    'first_progress', 
    '{"type": "first_progress", "progress": 100}', 
    200, 
    1, 
    1, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);
