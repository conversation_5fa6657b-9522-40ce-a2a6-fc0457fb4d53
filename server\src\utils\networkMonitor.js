const axios = require('axios');
const { Agent: HttpAgent } = require('http');
const { Agent: HttpsAgent } = require('https');

// 网络监控配置
const MONITOR_CONFIG = {
  interval: 30000, // 30秒检查一次
  timeout: 10000,  // 10秒超时
  retryAttempts: 3,
  services: []
};

// 创建优化的HTTP Agent
const monitorHttpAgent = new HttpAgent({
  keepAlive: true,
  keepAliveMsecs: 15000,
  maxSockets: 10,
  maxFreeSockets: 5,
  timeout: 10000,
});

const monitorHttpsAgent = new HttpsAgent({
  keepAlive: true,
  keepAliveMsecs: 15000,
  maxSockets: 10,
  maxFreeSockets: 5,
  timeout: 10000,
  rejectUnauthorized: false
});

class NetworkMonitor {
  constructor() {
    this.serviceStatus = new Map();
    this.isMonitoring = false;
    this.monitorInterval = null;
  }

  // 添加要监控的服务
  addService(name, url, healthCheckPath = '/health') {
    const service = {
      name,
      url,
      healthCheckPath,
      status: 'unknown',
      lastCheck: null,
      consecutiveFailures: 0,
      totalRequests: 0,
      successRequests: 0,
      averageResponseTime: 0
    };
    
    MONITOR_CONFIG.services.push(service);
    this.serviceStatus.set(name, service);
    console.log(`已添加监控服务: ${name} -> ${url}`);
  }

  // 检查单个服务的健康状态
  async checkServiceHealth(service) {
    const startTime = Date.now();
    const checkUrl = service.url + service.healthCheckPath;
    
    try {
      const response = await axios.get(checkUrl, {
        timeout: MONITOR_CONFIG.timeout,
        httpAgent: monitorHttpAgent,
        httpsAgent: monitorHttpsAgent,
        validateStatus: (status) => status >= 200 && status < 500 // 接受2xx-4xx状态码
      });

      const responseTime = Date.now() - startTime;
      
      // 更新服务状态
      service.status = response.status < 400 ? 'healthy' : 'degraded';
      service.lastCheck = new Date();
      service.consecutiveFailures = 0;
      service.totalRequests++;
      service.successRequests++;
      
      // 计算平均响应时间
      service.averageResponseTime = (service.averageResponseTime + responseTime) / 2;

      console.log(`✅ 服务健康检查通过: ${service.name} - ${responseTime}ms - ${response.status}`);
      return { success: true, responseTime, status: response.status };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // 更新服务状态
      service.status = 'unhealthy';
      service.lastCheck = new Date();
      service.consecutiveFailures++;
      service.totalRequests++;

      console.error(`❌ 服务健康检查失败: ${service.name} - ${error.message} - ${responseTime}ms`);
      console.error(`连续失败次数: ${service.consecutiveFailures}`);
      
      return { success: false, error: error.message, responseTime };
    }
  }

  // 检查所有服务
  async checkAllServices() {
    console.log('🔍 开始执行服务健康检查...');
    
    const promises = MONITOR_CONFIG.services.map(service => 
      this.checkServiceHealth(service)
    );

    try {
      await Promise.all(promises);
      this.logHealthSummary();
    } catch (error) {
      console.error('批量健康检查出错:', error);
    }
  }

  // 记录健康状态摘要
  logHealthSummary() {
    const summary = {
      healthy: 0,
      degraded: 0,
      unhealthy: 0,
      unknown: 0
    };

    MONITOR_CONFIG.services.forEach(service => {
      summary[service.status]++;
    });

    console.log('📊 服务健康状态摘要:', summary);
    
    // 如果有不健康的服务，记录详细信息
    const unhealthyServices = MONITOR_CONFIG.services.filter(s => s.status === 'unhealthy');
    if (unhealthyServices.length > 0) {
      console.warn('⚠️  不健康的服务:', unhealthyServices.map(s => ({
        name: s.name,
        consecutiveFailures: s.consecutiveFailures,
        lastCheck: s.lastCheck
      })));
    }
  }

  // 开始监控
  startMonitoring() {
    if (this.isMonitoring) {
      console.log('网络监控已在运行中');
      return;
    }

    console.log(`🚀 启动网络监控，检查间隔: ${MONITOR_CONFIG.interval / 1000}秒`);
    this.isMonitoring = true;

    // 立即执行一次检查
    this.checkAllServices();

    // 设置定时检查
    this.monitorInterval = setInterval(() => {
      this.checkAllServices();
    }, MONITOR_CONFIG.interval);
  }

  // 停止监控
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    console.log('🛑 停止网络监控');
    this.isMonitoring = false;
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
  }

  // 获取服务状态
  getServiceStatus(name) {
    return this.serviceStatus.get(name);
  }

  // 获取所有服务状态
  getAllServiceStatus() {
    const status = {};
    this.serviceStatus.forEach((service, name) => {
      status[name] = {
        status: service.status,
        lastCheck: service.lastCheck,
        consecutiveFailures: service.consecutiveFailures,
        successRate: service.totalRequests > 0 ? 
          (service.successRequests / service.totalRequests * 100).toFixed(2) + '%' : '0%',
        averageResponseTime: Math.round(service.averageResponseTime) + 'ms'
      };
    });
    return status;
  }

  // 检查特定服务是否健康
  isServiceHealthy(name) {
    const service = this.serviceStatus.get(name);
    return service && service.status === 'healthy';
  }
}

// 创建全局监控实例
const networkMonitor = new NetworkMonitor();

module.exports = {
  NetworkMonitor,
  networkMonitor,
  MONITOR_CONFIG
}; 