const { Role, <PERSON>u, RoleMenu, sequelize, User } = require('../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

// 获取角色列表
exports.getRoleList = async (req, res) => {
  try {
    const { roleName, pageNum = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (roleName) {
      where.roleName = { [Op.like]: `%${roleName}%` };
    }
    
    // 查询数据，添加企业ID过滤
    const { count, rows } = await Role.findAndCountAll(
      addEnterpriseFilter({
        where,
        order: [['sort', 'ASC']],
        limit: parseInt(pageSize),
        offset: (parseInt(pageNum) - 1) * parseInt(pageSize)
      })
    );
    
    res.json({
      code: 200,
      message: '获取角色列表成功',
      data: {
        list: rows,
        total: count
      }
    });
  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取角色列表失败',
      error: error.message
    });
  }
};

// 获取角色权限
exports.getRolePermissions = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log('获取角色权限请求，角色ID:', id);
    
    // 查询角色是否存在，添加企业ID过滤
    const role = await Role.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!role) {
      console.log('角色不存在，ID:', id);
      return res.status(404).json({
        code: 404,
        message: '角色不存在'
      });
    }
    
    console.log('找到角色:', { id: role.id, name: role.roleName });
    
    // 获取角色的菜单权限，添加企业ID过滤
    const roleMenus = await RoleMenu.findAll(
      addEnterpriseFilter({
        where: { roleId: id }
      })
    );
    
    console.log(`查询到角色的菜单关联记录: ${roleMenus ? roleMenus.length : 0}条`);
    
    const menuIds = roleMenus.map(item => item.menuId);
    console.log('提取的菜单ID列表:', menuIds);
    
    // 获取菜单权限标识，添加企业ID过滤
    const menus = await Menu.findAll(
      addEnterpriseFilter({
        where: { id: { [Op.in]: menuIds } },
        attributes: ['id', 'perms']
      })
    );
    
    console.log(`查询到的菜单记录: ${menus ? menus.length : 0}条`);
    
    const permissions = menus.map(menu => menu.perms).filter(perm => perm);
    console.log('提取的权限标识:', permissions);
    
    const response = {
      code: 200,
      message: '获取角色权限成功',
      data: {
        roleId: parseInt(id),
        permissions,
        menuIds
      }
    };
    
    console.log('返回的角色权限数据:', JSON.stringify(response));
    res.json(response);
  } catch (error) {
    console.error('获取角色权限失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取角色权限失败',
      error: error.message
    });
  }
};

// 创建角色
exports.createRole = async (req, res) => {
  try {
    const { roleName, sort, status, remark } = req.body;
    
    // 生成随机roleCode (8位随机字母数字组合)
    const generateRoleCode = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let code = '';
      for (let i = 0; i < 8; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return code;
    };

    let roleCode;
    let existRole;
    
    // 确保生成的roleCode是唯一的
    do {
      roleCode = generateRoleCode();
      existRole = await Role.findOne(
        addEnterpriseFilter({
          where: { roleCode }
        })
      );
    } while (existRole);
    
    // 创建角色，添加企业ID
    const role = await Role.create(
      addEnterpriseId({
        roleName,
        roleCode,
        sort,
        status,
        remark
      })
    );
    
    res.json({
      code: 200,
      message: '创建角色成功',
      data: role
    });
  } catch (error) {
    console.error('创建角色失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建角色失败',
      error: error.message
    });
  }
};

// 更新角色
exports.updateRole = async (req, res) => {
  try {
    const { id, roleName, sort, status, remark } = req.body;
    
    // 查询角色是否存在，添加企业ID过滤
    const role = await Role.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!role) {
      return res.status(404).json({
        code: 404,
        message: '角色不存在'
      });
    }
    
    // 更新角色(移除roleCode的更新)
    await role.update({
      roleName,
      sort,
      status,
      remark
    });
    
    res.json({
      code: 200,
      message: '更新角色成功',
      data: role
    });
  } catch (error) {
    console.error('更新角色失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新角色失败',
      error: error.message
    });
  }
};

// 删除角色
exports.deleteRole = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询角色是否存在，添加企业ID过滤
    const role = await Role.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!role) {
      return res.status(404).json({
        code: 404,
        message: '角色不存在'
      });
    }
    
    // 删除角色与菜单的关联关系
    await RoleMenu.destroy({ where: { roleId: id } });
    
    // 删除角色
    await role.destroy();
    
    res.json({
      code: 200,
      message: '删除角色成功'
    });
  } catch (error) {
    console.error('删除角色失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除角色失败',
      error: error.message
    });
  }
};

// 更新角色权限
exports.updateRolePermissions = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { roleId, permissions } = req.body;
    
    console.log('更新角色权限请求:', { roleId, permissions });
    
    // 查询角色是否存在，添加企业ID过滤
    const role = await Role.findOne(
      addEnterpriseFilter({
        where: { id: roleId }
      })
    );
    if (!role) {
      return res.status(404).json({
        code: 404,
        message: '角色不存在'
      });
    }
    
    // 解析菜单ID，确保为数字
    let menuIds = [];
    if (Array.isArray(permissions)) {
      // 直接使用传入的ID，假设这些就是菜单ID
      menuIds = permissions.map(id => Number(id)).filter(id => !isNaN(id));
      console.log('解析后的菜单ID:', menuIds);
    }
    
    // 删除原有关联关系
    await RoleMenu.destroy({ 
      where: { roleId },
      transaction
    });
    
    // 创建新的关联关系
    if (menuIds.length > 0) {
      const roleMenus = menuIds.map(menuId => addEnterpriseId({
        roleId,
        menuId
      }));
      
      await RoleMenu.bulkCreate(roleMenus, { transaction });
      console.log(`已为角色 ${roleId} 创建 ${menuIds.length} 条菜单关联`);
    }
    
    await transaction.commit();
    
    // 查询角色菜单关联表记录数进行验证
    const roleMenuCount = await RoleMenu.count({ where: { roleId } });
    console.log(`角色 ${roleId} 现有菜单关联数: ${roleMenuCount}`);
    
    res.json({
      code: 200,
      message: '更新角色权限成功'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新角色权限失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新角色权限失败',
      error: error.message
    });
  }
};

// 获取角色的用户列表
exports.getRoleUsers = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询角色是否存在，添加企业ID过滤
    const role = await Role.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    if (!role) {
      return res.status(404).json({
        code: 404,
        message: '角色不存在'
      });
    }
    
    // 获取具有该角色的用户，添加企业ID过滤
    const users = await User.findAll(
      addEnterpriseFilter({
        include: [
          {
            model: Role,
            where: { id },
            through: { attributes: [] }
          }
        ],
        attributes: { exclude: ['password'] }
      })
    );
    
    res.json({
      code: 200,
      message: '获取角色用户列表成功',
      data: {
        list: users,
        total: users.length
      }
    });
  } catch (error) {
    console.error('获取角色用户列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取角色用户列表失败',
      error: error.message
    });
  }
};