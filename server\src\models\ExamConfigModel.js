const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 练考配置模型
 */
const ExamConfig = sequelize.define('ExamConfig', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  positionBelong: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'position_belong',
    comment: '岗位归属'
  },
  positionName: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'position_name',
    comment: '岗位名称'
  },
  positionLevel: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'position_level',
    comment: '岗位等级'
  },
  examSubject: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'exam_subject',
    comment: '练考科目'
  },
  status: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '状态：必练/必考'
  },
  questionMode: {
    type: DataTypes.ENUM('题目', '题数'),
    allowNull: true,
    defaultValue: '题目',
    field: 'question_mode',
    comment: '只有必考才有，考试资格：时长/题数'
  },
  practiceDuration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 60,
    field: 'practice_duration',
    comment: '练习时长(分钟)'
  },
  practiceQuestionCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 20,
    field: 'practice_question_count',
    comment: '练习题目数量'
  },
  questionCount: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'question_count',
    comment: '考试题目数量'
  },
  examCount: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'exam_count',
    comment: '考试次数'
  },
  examDuration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'exam_duration',
    comment: '考试时长(分钟)'
  },
  needConfirmScore: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    defaultValue: false,
    field: 'need_confirm_score',
    comment: '是否需要确认成绩'
  },
  passScore: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'pass_score',
    comment: '通过标准(分)'
  },
  scoreReleaseRule: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'score_release_rule',
    comment: '成绩发布规则：自动发布/手动发布'
  },
  certificateValidDays: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'certificate_valid_days',
    comment: '证书有效时间(天)'
  },
  questionDistribution: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'question_distribution',
    comment: '题目类型分布'
  },
  questionType: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'question_type',
    comment: '试题类型，多个用逗号分隔'
  },
  difficultyLevel: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'difficulty_level',
    comment: '难度等级：初级/中级/高级'
  },

  manualQuestionRatio: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 50,
    field: 'manual_question_ratio',
    comment: '人工出题占比(总占比为100)'
  },
  intelligentQuestionRatio: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 50,
    field: 'intelligent_question_ratio',
    comment: '智能出题占比(总占比为100)'
  },

  enterpriseId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 1,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by',
    comment: '创建者ID'
  },
  updatedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'updated_by',
    comment: '更新者ID'
  }
}, {
  tableName: 'exam_config',
  timestamps: true,
  createdAt: 'created_time',
  updatedAt: 'updated_time',
  indexes: [
    {
      name: 'uk_exam_config_position_subject',
      unique: true,
      fields: ['position_belong', 'position_name', 'position_level', 'exam_subject', 'enterprise_id']
    }
  ]
});

// 导入关联模型（在表定义之后）
const KnowledgeBase = require('./knowledge-base');
const Level = require('./Level');

// 添加关联关系
// 与知识库的关联
ExamConfig.belongsTo(KnowledgeBase, {
  foreignKey: 'examSubject',
  targetKey: 'id',
  as: 'knowledgeBase'
});

// 与岗位等级的关联
ExamConfig.belongsTo(Level, {
  foreignKey: 'positionLevel',
  targetKey: 'id',
  as: 'positionLevelData'
});

module.exports = ExamConfig; 