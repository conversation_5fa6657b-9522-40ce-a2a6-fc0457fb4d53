-- 为 org_employee_position 表添加 position_type_id 字段
ALTER TABLE org_employee_position 
ADD COLUMN position_type_id INT COMMENT '岗位类型ID';

-- 添加外键约束
ALTER TABLE org_employee_position 
ADD CONSTRAINT fk_employee_position_type 
FOREIGN KEY (position_type_id) REFERENCES org_position_type(id);

-- 更新现有数据，将 position_type_id 设置为对应员工的 position_type_id
UPDATE org_employee_position ep
INNER JOIN org_employee e ON ep.employee_id = e.id
SET ep.position_type_id = e.position_type_id
WHERE e.position_type_id IS NOT NULL;

-- 添加索引
CREATE INDEX idx_employee_position_type_id ON org_employee_position(position_type_id); 