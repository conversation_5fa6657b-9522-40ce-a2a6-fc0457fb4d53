const Level = require('../../models/Level');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../../utils/enterpriseFilter');
const User = require('../../models/user');

/**
 * 获取岗位等级列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getLevelList = async (req, res) => {
  try {
    const { name, pageNum = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) {
      where.name = {
        [Op.like]: `%${name}%`
      };
    }
    
    // 分页查询
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);
    
    // 查询岗位等级数据，添加企业ID过滤，并关联操作人信息
    const { count, rows } = await Level.findAndCountAll(
      addEnterpriseFilter({
        where,
        offset,
        limit,
        order: [
          ['orderNum', 'DESC'],
          ['id', 'ASC']
        ],
        include: [
          {
            model: User,
            as: 'operator',
            attributes: ['id', 'username', 'nickname', 'realName'],
            required: false
          }
        ]
      })
    );
    
    res.json({
      code: 200,
      data: {
        total: count,
        rows,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      },
      message: '获取岗位等级列表成功'
    });
  } catch (error) {
    console.error('获取岗位等级列表失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位等级列表失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位等级选项（下拉列表用）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getLevelOptions = async (req, res) => {
  try {
    // 查询岗位等级，添加企业ID过滤
    const levels = await Level.findAll(
      addEnterpriseFilter({
        attributes: ['id', 'name'],
        order: [
          ['orderNum', 'DESC'],
          ['id', 'ASC']
        ]
      })
    );
    
    console.log('获取到的岗位等级选项:', JSON.stringify(levels));
    
    res.json({
      code: 200,
      data: levels,
      message: '获取岗位等级选项成功'
    });
  } catch (error) {
    console.error('获取岗位等级选项失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位等级选项失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位等级详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getLevelDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 添加企业ID过滤，并关联操作人信息
    const level = await Level.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [
          {
            model: User,
            as: 'operator',
            attributes: ['id', 'username', 'nickname', 'realName'],
            required: false
          }
        ]
      })
    );
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '岗位等级不存在'
      });
    }
    
    res.json({
      code: 200,
      data: level,
      message: '获取岗位等级详情成功'
    });
  } catch (error) {
    console.error('获取岗位等级详情失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位等级详情失败',
      error: error.message
    });
  }
};

/**
 * 新增岗位等级
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.addLevel = async (req, res) => {
  try {
    const {
      name,
      code,
      orderNum,
      remark
    } = req.body;
    
    // 校验必填字段
    if (!name) {
      return res.status(400).json({
        code: 400,
        message: '岗位等级名称不能为空'
      });
    }
    
    // 校验岗位等级名称是否已存在，添加企业ID过滤
    const existLevel = await Level.findOne(
      addEnterpriseFilter({
        where: {
          name
        }
      })
    );
    
    if (existLevel) {
      return res.status(400).json({
        code: 400,
        message: '岗位等级名称已存在'
      });
    }
    
    // 如果有编码，校验编码是否已存在，添加企业ID过滤
    if (code) {
      const existCode = await Level.findOne(
        addEnterpriseFilter({
          where: {
            code
          }
        })
      );
      
      if (existCode) {
        return res.status(400).json({
          code: 400,
          message: '岗位等级编码已存在'
        });
      }
    }
    

    
    // 获取当前登录用户ID作为操作人
    const operatorId = req.user ? req.user.id : null;
    
    // 创建岗位等级，添加企业ID和操作人ID
    const level = await Level.create(
      addEnterpriseId({
        name,
        code,
        orderNum: orderNum || 0,
        remark,
        operatorId,
        createTime: new Date()
      })
    );
    
    res.json({
      code: 200,
      data: level,
      message: '新增岗位等级成功'
    });
  } catch (error) {
    console.error('新增岗位等级失败：', error);
    res.status(500).json({
      code: 500,
      message: '新增岗位等级失败',
      error: error.message
    });
  }
};

/**
 * 更新岗位等级
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateLevel = async (req, res) => {
  try {
    const {
      id,
      name,
      code,
      orderNum,
      remark
    } = req.body;
    
    // 校验必填字段
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '岗位等级ID不能为空'
      });
    }
    
    // 查询岗位等级是否存在，添加企业ID过滤
    const level = await Level.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '岗位等级不存在'
      });
    }
    
    // 如果修改了名称，校验名称是否已存在（排除自身），添加企业ID过滤
    if (name && name !== level.name) {
      const existLevel = await Level.findOne(
        addEnterpriseFilter({
          where: {
            name,
            id: { [Op.ne]: id }
          }
        })
      );
      
      if (existLevel) {
        return res.status(400).json({
          code: 400,
          message: '岗位等级名称已存在'
        });
      }
    }
    
    // 如果修改了编码，校验编码是否已存在（排除自身），添加企业ID过滤
    if (code !== undefined && code !== level.code) {
      if (code) {
        const existCode = await Level.findOne(
          addEnterpriseFilter({
            where: {
              code,
              id: { [Op.ne]: id }
            }
          })
        );
        
        if (existCode) {
          return res.status(400).json({
            code: 400,
            message: '岗位等级编码已存在'
          });
        }
      }
    }
    

    
    // 获取当前登录用户ID作为操作人
    const operatorId = req.user ? req.user.id : null;
    
    // 更新岗位等级
    await level.update({
      name: name || level.name,
      code: code === undefined ? level.code : code,
      orderNum: orderNum === undefined ? level.orderNum : orderNum,
      remark: remark === undefined ? level.remark : remark,
      operatorId,
      updateTime: new Date()
    });
    
    res.json({
      code: 200,
      data: level,
      message: '更新岗位等级成功'
    });
  } catch (error) {
    console.error('更新岗位等级失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新岗位等级失败',
      error: error.message
    });
  }
};

/**
 * 删除岗位等级
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deleteLevel = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询岗位等级是否存在，添加企业ID过滤
    const level = await Level.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '岗位等级不存在'
      });
    }
    
    // 查询岗位等级下是否有员工，添加企业ID过滤
    const Employee = require('../../models/Employee');
    const employeeCount = await Employee.count(
      addEnterpriseFilter({
        where: {
          levelId: id
        }
      })
    );
    
    if (employeeCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '岗位等级下存在员工，无法删除。请先将关联的员工更换到其他岗位等级后再删除。'
      });
    }
    
    // 删除岗位等级
    await level.destroy();
    
    res.json({
      code: 200,
      message: '删除岗位等级成功'
    });
  } catch (error) {
    console.error('删除岗位等级失败：', error);
    res.status(500).json({
      code: 500,
      message: '删除岗位等级失败',
      error: error.message
    });
  }
}; 