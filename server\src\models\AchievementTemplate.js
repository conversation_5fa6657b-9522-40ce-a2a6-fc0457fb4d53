const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const AchievementTemplate = sequelize.define('AchievementTemplate', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '成就模板ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '成就名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '成就条件描述'
  },
  icon: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '成就图标路径'
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'learning',
    comment: '成就分类：learning-学习类, time-时间类, exam-考试类, practice-练习类'
  },
  ruleType: {
    type: DataTypes.STRING(50),
    field: 'rule_type',
    allowNull: false,
    comment: '规则类型：progress-进度类, time-时间类, count-计数类, streak-连续类'
  },
  triggerCondition: {
    type: DataTypes.TEXT,
    field: 'trigger_condition',
    allowNull: false,
    comment: '触发条件JSON字符串'
  },
  // 以下字段保留在数据库中但前端不使用，用于向后兼容
  rewardPoints: {
    type: DataTypes.INTEGER,
    field: 'reward_points',
    allowNull: false,
    defaultValue: 0,
    comment: '奖励积分'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    field: 'is_active',
    allowNull: false,
    defaultValue: true,
    comment: '是否启用'
  },
  sort: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排序序号'
  },
  createBy: {
    type: DataTypes.STRING(50),
    field: 'create_by',
    allowNull: true,
    comment: '创建人'
  },
  updateBy: {
    type: DataTypes.STRING(50),
    field: 'update_by',
    allowNull: true,
    comment: '更新人'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  tableName: 'achievement_templates',
  timestamps: false,
  freezeTableName: true
});

module.exports = AchievementTemplate; 