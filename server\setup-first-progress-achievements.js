const { sequelize } = require('./src/utils/responseHelper');
const fs = require('fs');
const path = require('path');

async function setupFirstProgressAchievements() {
  try {
    console.log('正在设置首次学习进度成就...');
    
    // 读取SQL文件
    const sqlFile = path.join(__dirname, 'first_progress_achievement_fixed.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // 分割SQL语句（去掉USE语句）
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('USE'))
      .filter(stmt => stmt.startsWith('INSERT'));
    
    console.log(`找到 ${sqlStatements.length} 条INSERT语句`);
    
    // 执行每条SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const sql = sqlStatements[i];
      try {
        console.log(`执行第 ${i + 1} 条语句...`);
        await sequelize.query(sql);
        console.log(`✅ 第 ${i + 1} 条语句执行成功`);
      } catch (error) {
        if (error.message.includes('Duplicate entry')) {
          console.log(`⚠️ 第 ${i + 1} 条语句跳过（记录已存在）`);
        } else {
          console.error(`❌ 第 ${i + 1} 条语句执行失败:`, error.message);
        }
      }
    }
    
    // 检查插入结果
    console.log('\n检查插入结果...');
    const templates = await sequelize.query(
      `SELECT * FROM achievement_template WHERE rule_type = 'first_progress'`,
      { type: sequelize.QueryTypes.SELECT }
    );
    
    console.log(`\n✅ 成功创建 ${templates.length} 个首次学习进度成就模板:`);
    templates.forEach(template => {
      console.log(`- ID: ${template.id}, 名称: ${template.name}, 描述: ${template.description}`);
      console.log(`  触发条件: ${template.trigger_condition}`);
      console.log(`  奖励积分: ${template.reward_points}, 是否激活: ${template.is_active}`);
      console.log('---');
    });
    
    // 关闭数据库连接
    await sequelize.close();
    console.log('\n🎉 首次学习进度成就设置完成！');
    
  } catch (error) {
    console.error('设置首次学习进度成就失败:', error);
    process.exit(1);
  }
}

setupFirstProgressAchievements(); 