const { Menu } = require('../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

// 递归构建菜单树
const buildMenuTree = (menus, parentId = 0) => {
  const result = [];
  
  for (const menu of menus) {
    if (menu.parentId === parentId) {
      const children = buildMenuTree(menus, menu.id);
      if (children.length > 0) {
        menu.children = children;
      }
      result.push(menu);
    }
  }
  
  return result;
};

// 获取菜单列表
exports.getMenuList = async (req, res) => {
  try {
    console.log('开始获取菜单列表...');
    
    // 查询所有菜单，添加企业ID过滤
    const menus = await Menu.findAll(
      addEnterpriseFilter({
        order: [
          ['parentId', 'ASC'],
          ['sort', 'ASC']
        ]
      })
    );
    
    console.log(`查询到 ${menus ? menus.length : 0} 条菜单记录`);
    
    // 转换为普通对象
    const plainMenus = menus.map(menu => menu.get({ plain: true }));
    
    // 构建菜单树
    const menuTree = buildMenuTree(plainMenus);
    
    console.log(`构建了 ${menuTree ? menuTree.length : 0} 个顶级菜单项`);
    
    res.json({
      code: 200,
      message: '获取菜单列表成功',
      data: menuTree
    });
  } catch (error) {
    console.error('获取菜单列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取菜单列表失败',
      error: error.message
    });
  }
};

// 创建菜单
exports.createMenu = async (req, res) => {
  try {
    let { parentId, name, path, component, redirect, icon, sort, hidden, type, perms, status } = req.body;
    
    console.log('创建菜单请求数据:', JSON.stringify(req.body, null, 2));
    
    // 确保菜单类型和组件路径正确匹配
    if (type === 0 || type === 2) { // 目录或按钮
      component = null;
    }
    
    // 创建菜单，添加企业ID
    const menu = await Menu.create(
      addEnterpriseId({
        parentId,
        name,
        path,
        component,
        redirect,
        icon,
        sort,
        hidden,
        type,
        perms,
        status
      })
    );
    
    console.log('创建菜单成功:', JSON.stringify(menu, null, 2));
    console.log('组件路径:', component);
    
    res.json({
      code: 200,
      message: '创建菜单成功',
      data: menu
    });
  } catch (error) {
    console.error('创建菜单失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建菜单失败',
      error: error.message
    });
  }
};

// 更新菜单
exports.updateMenu = async (req, res) => {
  try {
    let { id, parentId, name, path, component, redirect, icon, sort, hidden, type, perms, status } = req.body;
    
    console.log('更新菜单请求数据:', JSON.stringify(req.body, null, 2));
    
    // 检查菜单是否存在，添加企业ID过滤
    const menu = await Menu.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!menu) {
      return res.status(404).json({
        code: 404,
        message: '菜单不存在'
      });
    }
    
    // 父节点不能是自己
    if (parentId === id) {
      return res.status(400).json({
        code: 400,
        message: '父节点不能是自己'
      });
    }
    
    // 确保菜单类型和组件路径正确匹配
    if (type === 0 || type === 2) { // 目录或按钮
      component = null;
    }
    
    // 更新菜单
    await menu.update({
      parentId,
      name,
      path,
      component,
      redirect,
      icon,
      sort,
      hidden,
      type,
      perms,
      status
    });
    
    console.log('更新菜单成功:', JSON.stringify(menu, null, 2));
    console.log('组件路径:', component);
    
    res.json({
      code: 200,
      message: '更新菜单成功',
      data: menu
    });
  } catch (error) {
    console.error('更新菜单失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新菜单失败',
      error: error.message
    });
  }
};

// 删除菜单
exports.deleteMenu = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查菜单是否存在，添加企业ID过滤
    const menu = await Menu.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!menu) {
      return res.status(404).json({
        code: 404,
        message: '菜单不存在'
      });
    }
    
    // 检查是否有子菜单，添加企业ID过滤
    const childCount = await Menu.count(
      addEnterpriseFilter({
        where: { parentId: id }
      })
    );
    
    if (childCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '该菜单下有子菜单，无法删除'
      });
    }
    
    // 删除菜单
    await menu.destroy();
    
    res.json({
      code: 200,
      message: '删除菜单成功'
    });
  } catch (error) {
    console.error('删除菜单失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除菜单失败',
      error: error.message
    });
  }
}; 