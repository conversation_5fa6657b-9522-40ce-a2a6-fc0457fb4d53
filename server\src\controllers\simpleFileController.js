const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const XLSX = require('xlsx');

/**
 * 简单文件上传接口 - 接收文件和detail参数
 * 公共接口，不需要认证
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const uploadSimpleFile = (req, res) => {
  // 创建专用上传目录
  const uploadDir = './uploads/simple-files';
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  
  // 创建独立的存储配置
  const storage = multer.diskStorage({
    destination: function(req, file, cb) {
      cb(null, uploadDir);
    },
    filename: function(req, file, cb) {
      // 使用时间戳+随机数作为文件名，完全避开原文件名的编码问题
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 10);
      
      // 获取原始文件扩展名
      let ext = '';
      try {
        if (file.originalname) {
          ext = path.extname(file.originalname);
        }
      } catch (e) {
        console.error('获取扩展名失败，使用空扩展名');
      }
      
      // 生成新文件名 - 时间戳_随机ID.扩展名
      const newFilename = `${timestamp}_${randomId}${ext}`;
      cb(null, newFilename);
    }
  });
  
  // 创建简单上传处理器，不使用全局过滤器
  const simpleUpload = multer({ 
    storage: storage,
    limits: { fileSize: 50 * 1024 * 1024 } // 最大50MB
  });
  
  // 处理单文件上传
  const uploadHandler = simpleUpload.single('file');
  
  uploadHandler(req, res, (err) => {
    if (err) {
      return res.status(400).json({
        code: 400,
        message: '文件上传失败',
        error: err.message
      });
    }
    
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传的文件'
      });
    }
    
    // 获取detail参数
    const { detail } = req.body;
    
    // 处理detail参数
    let jsonContent = null;
    
    if (detail) {
      console.log('原始detail字符串:', detail);
      
      // 提取JSON内容
      let jsonStr = detail;
      
      // 检查是否是Markdown格式代码块
      if (detail.startsWith('```json')) {
        // 移除 ```json 前缀和 ``` 后缀
        const startIndex = detail.indexOf('```json') + 7;
        const endIndex = detail.lastIndexOf('```');
        
        if (endIndex > startIndex) {
          jsonStr = detail.substring(startIndex, endIndex).trim();
          console.log('从Markdown代码块提取JSON:', jsonStr);
        }
      } 
      // 检查是否以"json\n"开头
      else if (detail.startsWith('json\n')) {
        jsonStr = detail.substring(5); // 移除"json\n"前缀
        console.log('从json\\n前缀提取JSON:', jsonStr);
      }
      
      // 尝试解析JSON字符串
      try {
        jsonContent = JSON.parse(jsonStr);
        console.log('成功解析JSON内容:', jsonContent);
      } catch (e) {
        console.error('解析JSON内容失败:', e.message);
      }
    }
    
    // 检查上传的文件是否为Excel文件
    const isExcel = req.file.originalname.endsWith('.xlsx') || 
                   req.file.originalname.endsWith('.xls') || 
                   req.file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                   req.file.mimetype === 'application/vnd.ms-excel';
    
    // 存储Excel表数据
    const sheetData = {};
    
    // 存储处理后的数据
    const processedData = {};
    
    // 如果是Excel文件并且有tableList，则读取指定表的数据
    if (isExcel && jsonContent && jsonContent.tableList && Array.isArray(jsonContent.tableList)) {
      try {
        // 读取Excel文件
        const workbook = XLSX.readFile(req.file.path);
        
        // 获取所有表名
        const sheetNames = workbook.SheetNames;
        console.log('Excel文件中的所有表:', sheetNames);
        
        // 读取tableList中指定的表
        jsonContent.tableList.forEach(sheetName => {
          if (sheetNames.includes(sheetName)) {
            // 获取工作表
            const worksheet = workbook.Sheets[sheetName];
            
            // 常规处理：将工作表转换为JSON对象数组，使用第一行作为键名
            const data = XLSX.utils.sheet_to_json(worksheet, {
              defval: '', // 空单元格默认值
              raw: false  // 返回格式化的字符串
            });
            
            // 存储表数据
            sheetData[sheetName] = data;
            console.log(`成功读取表 ${sheetName}，行数: ${data.length}`);
            
            // ----------- 处理销售预测表 -----------
            if (sheetName === '销售预测') {
              // 将工作表转换为二维数组，包括所有行
              const rawData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1, // 使用数字索引的数组，不将第一行作为表头
                defval: '', 
                raw: false
              });
              
              console.log(`"${sheetName}"表原始数据行数: ${rawData.length}`);
              
              if (rawData.length > 0) {
                // 查找表头行和规格列、总合计数量列的索引
                let headerRowIndex = -1;
                let specColumnIndex = -1;
                let totalColumnIndex = -1;
                
                // 查找包含"规格"的行，这通常是表头行
                for (let i = 0; i < rawData.length; i++) {
                  const row = rawData[i];
                  for (let j = 0; j < row.length; j++) {
                    const cell = String(row[j]);
                    if (cell === '规格') {
                      headerRowIndex = i;
                      specColumnIndex = j;
                    }
                    if (cell === '总合计数量' || cell.includes('总合计')) {
                      if (headerRowIndex === -1) headerRowIndex = i;
                      totalColumnIndex = j;
                    }
                  }
                  
                  // 如果找到了两个列，就退出循环
                  if (specColumnIndex !== -1 && totalColumnIndex !== -1) {
                    break;
                  }
                }
                
                console.log(`找到表头行索引: ${headerRowIndex}, 规格列索引: ${specColumnIndex}, 总合计数量列索引: ${totalColumnIndex}`);
                
                // 如果找到了表头行和两个目标列
                if (headerRowIndex !== -1 && specColumnIndex !== -1 && totalColumnIndex !== -1) {
                  // 提取规格和总合计数量数据，从表头行的下一行开始
                  const filteredData = [];
                  
                  for (let i = headerRowIndex + 1; i < rawData.length; i++) {
                    const row = rawData[i];
                    // 确保行长度足够
                    if (row.length > Math.max(specColumnIndex, totalColumnIndex)) {
                      const spec = row[specColumnIndex];
                      const total = row[totalColumnIndex];
                      
                      // 如果productDetail存在，只提取匹配的规格行
                      const shouldInclude = jsonContent.productDetail 
                        ? spec === jsonContent.productDetail // 精确匹配
                        : true; // 没有productDetail时包含所有行
                        
                      // 只加入有效数据行，并且如果有productDetail则必须匹配
                      if (spec && total && spec !== '规格' && total !== '总合计数量' && shouldInclude) {
                        filteredData.push({
                          '规格': spec,
                          '总合计数量': total
                        });
                      }
                    }
                  }
                  
                  // 如果有productDetail但没有找到匹配项，进行模糊匹配
                  if (jsonContent.productDetail && filteredData.length === 0) {
                    console.log(`未找到完全匹配的规格 ${jsonContent.productDetail}，尝试模糊匹配`);
                    
                    for (let i = headerRowIndex + 1; i < rawData.length; i++) {
                      const row = rawData[i];
                      if (row.length > Math.max(specColumnIndex, totalColumnIndex)) {
                        const spec = String(row[specColumnIndex]);
                        const total = row[totalColumnIndex];
                        
                        // 进行模糊匹配，检查规格字符串是否包含productDetail
                        if (spec && total && 
                            spec !== '规格' && 
                            total !== '总合计数量' && 
                            spec.includes(jsonContent.productDetail)) {
                          filteredData.push({
                            '规格': spec,
                            '总合计数量': total
                          });
                        }
                      }
                    }
                  }
                  
                  // 记录筛选信息
                  if (jsonContent.productDetail) {
                    console.log(`根据productDetail:'${jsonContent.productDetail}'筛选，结果行数: ${filteredData.length}`);
                  }
                  
                  processedData[sheetName] = filteredData;
                  console.log(`成功处理${sheetName}表，提取规格和总合计数量字段，处理后行数: ${filteredData.length}`);
                } else {
                  console.warn(`在${sheetName}表中无法找到规格或总合计数量列`);
                  processedData[sheetName] = [];
                }
              }
            }
            // ----------- 处理产品库存表 -----------
            else if (sheetName === '产品库存') {
              // 将工作表转换为二维数组，包括所有行
              const rawData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1, // 使用数字索引的数组，不将第一行作为表头
                defval: '', 
                raw: false
              });
              
              console.log(`"${sheetName}"表原始数据行数: ${rawData.length}`);
              
              if (rawData.length > 0) {
                // 查找表头行和各个关键列的索引
                let headerRowIndex = -1;
                let specColumnIndex = -1;
                let serialColumnIndex = -1; // 序号列
                let remarkColumnIndex = -1; // 备注列
                
                // 查找表头行和关键列索引
                for (let i = 0; i < rawData.length; i++) {
                  const row = rawData[i];
                  for (let j = 0; j < row.length; j++) {
                    const cell = String(row[j]);
                    if (cell === '规格') {
                      headerRowIndex = i;
                      specColumnIndex = j;
                    }
                    if (cell === '序号') {
                      if (headerRowIndex === -1) headerRowIndex = i;
                      serialColumnIndex = j;
                    }
                    if (cell === '备注') {
                      if (headerRowIndex === -1) headerRowIndex = i;
                      remarkColumnIndex = j;
                    }
                  }
                  
                  // 如果找到了所有需要的列，就退出循环
                  if (headerRowIndex !== -1 && specColumnIndex !== -1 && 
                      serialColumnIndex !== -1 && remarkColumnIndex !== -1) {
                    break;
                  }
                }
                
                console.log(`找到表头行索引: ${headerRowIndex}, 规格列索引: ${specColumnIndex}, ` +
                           `序号列索引: ${serialColumnIndex}, 备注列索引: ${remarkColumnIndex}`);
                
                // 如果找到了表头行和必要的列
                if (headerRowIndex !== -1 && specColumnIndex !== -1) {
                  // 提取规格和库存余额数据，从表头行的下一行开始
                  const filteredData = [];
                  const headers = rawData[headerRowIndex]; // 表头行
                  
                  for (let i = headerRowIndex + 1; i < rawData.length; i++) {
                    const row = rawData[i];
                    if (row.length <= specColumnIndex) continue; // 跳过数据不完整的行
                    
                    const spec = row[specColumnIndex];
                    if (!spec || spec === '规格') continue; // 跳过空规格或表头重复行
                    
                    // 如果productDetail存在，只提取匹配的规格行
                    const shouldInclude = jsonContent.productDetail 
                      ? spec === jsonContent.productDetail || spec.includes(jsonContent.productDetail) // 精确或模糊匹配
                      : true; // 没有productDetail时包含所有行
                    
                    if (!shouldInclude) continue; // 跳过不匹配的行
                    
                    // 计算库存余额：求和所有数字列（备注列之前且非序号列）
                    let inventoryBalance = 0;
                    const processedRow = {
                      '规格': spec
                    };
                    
                    // 确定数字列的范围：在序号列之后，备注列之前
                    const startCol = serialColumnIndex !== -1 ? serialColumnIndex + 1 : 0;
                    const endCol = remarkColumnIndex !== -1 ? remarkColumnIndex : row.length;
                    
                    // 收集所有数字列
                    for (let j = startCol; j < endCol; j++) {
                      // 跳过规格列
                      if (j === specColumnIndex) continue;
                      
                      const value = row[j];
                      // 尝试将值转换为数字，如果是数字则累加
                      const numValue = parseFloat(value);
                      if (!isNaN(numValue)) {
                        inventoryBalance += numValue;
                        // 同时保留原始列数据 - 不再需要
                        // if (j < headers.length && headers[j]) {
                        //   processedRow[headers[j]] = value;
                        // }
                      }
                    }
                    
                    // 添加库存余额字段
                    processedRow['库存余额'] = inventoryBalance.toString();
                    
                    filteredData.push(processedRow);
                  }
                  
                  // 记录处理信息
                  processedData[sheetName] = filteredData;
                  console.log(`成功处理${sheetName}表，提取了规格和库存余额字段，处理后行数: ${filteredData.length}`);
                  
                  if (jsonContent.productDetail) {
                    console.log(`根据productDetail:'${jsonContent.productDetail}'筛选，结果行数: ${filteredData.length}`);
                  }
                } else {
                  console.warn(`在${sheetName}表中无法找到必要的列`);
                  processedData[sheetName] = [];
                }
              }
            }
            // ----------- 处理销售订单表 -----------
            else if (sheetName === '销售订单') {
              // 将工作表转换为二维数组，包括所有行
              const rawData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1, // 使用数字索引的数组，不将第一行作为表头
                defval: '', 
                raw: false
              });
              
              console.log(`"${sheetName}"表原始数据行数: ${rawData.length}`);
              
              if (rawData.length > 0) {
                // 查找表头行和各个关键列的索引
                let headerRowIndex = -1;
                let productNameColumnIndex = -1; // 产品名称列
                let regionColumnIndex = -1;      // 大区列
                
                // 查找表头行和关键列索引
                for (let i = 0; i < rawData.length; i++) {
                  const row = rawData[i];
                  for (let j = 0; j < row.length; j++) {
                    const cell = String(row[j]);
                    if (cell === '产品名称' || cell === '产品' || cell === '商品名称') {
                      headerRowIndex = i;
                      productNameColumnIndex = j;
                    }
                    if (cell === '大区' || cell === '区域' || cell === '销售区域') {
                      if (headerRowIndex === -1) headerRowIndex = i;
                      regionColumnIndex = j;
                    }
                  }
                  
                  // 如果找到了需要的列，就退出循环
                  if (headerRowIndex !== -1 && productNameColumnIndex !== -1 && regionColumnIndex !== -1) {
                    break;
                  }
                }
                
                console.log(`找到表头行索引: ${headerRowIndex}, 产品名称列索引: ${productNameColumnIndex}, 大区列索引: ${regionColumnIndex}`);
                
                // 如果找到了表头行和必要的列
                if (headerRowIndex !== -1 && productNameColumnIndex !== -1 && regionColumnIndex !== -1) {
                  // 提取符合条件的数据，从表头行的下一行开始
                  const filteredData = [];
                  const headers = rawData[headerRowIndex]; // 表头行
                  
                  for (let i = headerRowIndex + 1; i < rawData.length; i++) {
                    const row = rawData[i];
                    if (row.length <= Math.max(productNameColumnIndex, regionColumnIndex)) continue; // 跳过数据不完整的行
                    
                    const productName = row[productNameColumnIndex];
                    const region = row[regionColumnIndex];
                    
                    if (!productName || productName === '产品名称') continue; // 跳过空产品名或表头重复行
                    
                    // 根据筛选条件判断是否包含此行
                    let includeByProduct = true;
                    let includeByRegion = true;
                    
                    // 如果productDetail存在，只保留匹配的产品名称行
                    if (jsonContent.productDetail) {
                      includeByProduct = productName === jsonContent.productDetail || 
                                       productName.includes(jsonContent.productDetail);
                    }
                    
                    // 如果address存在，只保留匹配的大区行
                    if (jsonContent.address) {
                      includeByRegion = region === jsonContent.address || 
                                       region.includes(jsonContent.address) ||
                                       jsonContent.address.includes(region);
                    }
                    
                    // 两个条件都满足才加入结果集
                    if (includeByProduct && includeByRegion) {
                      // 创建结果行对象，包含所有列数据
                      const resultRow = {};
                      
                      // 添加所有表头对应的数据
                      for (let j = 0; j < row.length && j < headers.length; j++) {
                        if (headers[j]) {
                          resultRow[headers[j]] = row[j];
                        }
                      }
                      
                      filteredData.push(resultRow);
                    }
                  }
                  
                  // 记录处理信息
                  processedData[sheetName] = filteredData;
                  console.log(`成功处理${sheetName}表，根据筛选条件处理后行数: ${filteredData.length}`);
                  
                  if (jsonContent.productDetail) {
                    console.log(`根据productDetail:'${jsonContent.productDetail}'筛选`);
                  }
                  
                  if (jsonContent.address) {
                    console.log(`根据address:'${jsonContent.address}'筛选`);
                  }
                } else {
                  console.warn(`在${sheetName}表中无法找到必要的列`);
                  processedData[sheetName] = [];
                }
              }
            }
            // ----------- 处理年度指标表 -----------
            else if (sheetName === '区域总年度指标' || sheetName === '区域总年度' || sheetName === '总年度') {
              console.log(`找到年度指标表: ${sheetName}`);
              
              // 将工作表转换为二维数组，包括所有行
              const rawData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1, // 使用数字索引的数组，不将第一行作为表头
                defval: '', 
                raw: false
              });
              
              console.log(`"${sheetName}"表原始数据行数: ${rawData.length}`);
              
              if (rawData.length > 0) {
                // 默认第一行为表头
                let headerRowIndex = 0;
                let regionColumnIndex = -1; // 区域列索引
                
                // 查找表头行 - 通常包含"指标名称"或"指标"的行可能是表头
                for (let i = 0; i < Math.min(10, rawData.length); i++) { // 只检查前10行
                  const row = rawData[i];
                  for (let j = 0; j < row.length; j++) {
                    const cell = String(row[j]);
                    if (cell === '指标名称' || cell === '指标' || cell === '年度指标') {
                      headerRowIndex = i;
                    }
                    if (cell === '区域' || cell === '大区' || cell === '销售区域') {
                      regionColumnIndex = j;
                    }
                  }
                  
                  // 如果找到了表头行和区域列，就退出循环
                  if (headerRowIndex !== -1 && regionColumnIndex !== -1) {
                    break;
                  }
                }
                
                // 使用找到的表头行
                const headers = rawData[headerRowIndex];
                console.log(`年度指标表头: ${headers.join(', ')}`);
                
                // 从表头行的下一行开始提取数据
                const filteredData = [];
                
                for (let i = headerRowIndex + 1; i < rawData.length; i++) {
                  const row = rawData[i];
                  if (row.length === 0 || !row.some(cell => cell)) continue; // 跳过空行
                  
                  // 创建结果行对象
                  const resultRow = {};
                  
                  // 添加所有表头对应的数据
                  for (let j = 0; j < row.length && j < headers.length; j++) {
                    if (headers[j]) {
                      resultRow[headers[j]] = row[j];
                    }
                  }
                  
                  // 如果存在address且区域列存在，则进行区域筛选
                  if (jsonContent.address && regionColumnIndex !== -1 && row.length > regionColumnIndex) {
                    const region = String(row[regionColumnIndex]);
                    // 只有当区域包含address或address包含区域时才添加此行
                    if (region && (region.includes(jsonContent.address) || jsonContent.address.includes(region))) {
                      filteredData.push(resultRow);
                    }
                  } else {
                    // 如果没有筛选条件，添加全部数据行
                    filteredData.push(resultRow);
                  }
                }
                
                // 记录处理信息
                processedData[sheetName] = filteredData;
                console.log(`成功处理${sheetName}表，处理后行数: ${filteredData.length}`);
                
                if (jsonContent.address) {
                  console.log(`根据address:'${jsonContent.address}'筛选区域`);
                }
              } else {
                console.warn(`${sheetName}表为空`);
                processedData[sheetName] = [];
              }
            }
            // ----------- 处理各区年度指标表 -----------
            else if (sheetName.includes('各区年度指标')) {
              console.log(`找到各区年度指标表: ${sheetName}`);
              
              // 将工作表转换为二维数组，包括所有行
              const rawData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1, // 使用数字索引的数组，不将第一行作为表头
                defval: '', 
                raw: false
              });
              
              console.log(`"${sheetName}"表原始数据行数: ${rawData.length}`);
              
              if (rawData.length > 0) {
                // 默认第一行为表头
                let headerRowIndex = 0;
                let productColumnIndex = -1; // 产品列索引
                
                // 查找表头行和产品列
                for (let i = 0; i < Math.min(10, rawData.length); i++) { // 只检查前10行
                  const row = rawData[i];
                  for (let j = 0; j < row.length; j++) {
                    const cell = String(row[j]);
                    if (cell === '产品' || cell === '产品名称' || cell === '商品名称') {
                      headerRowIndex = i;
                      productColumnIndex = j;
                    }
                  }
                  
                  // 如果找到了产品列，就退出循环
                  if (productColumnIndex !== -1) {
                    break;
                  }
                }
                
                // 使用找到的表头行
                const headers = rawData[headerRowIndex];
                console.log(`各区年度指标表头: ${headers.join(', ')}`);
                
                // 从表头行的下一行开始提取数据
                const filteredData = [];
                
                for (let i = headerRowIndex + 1; i < rawData.length; i++) {
                  const row = rawData[i];
                  if (row.length === 0 || !row.some(cell => cell)) continue; // 跳过空行
                  
                  // 创建结果行对象
                  const resultRow = {};
                  
                  // 添加所有表头对应的数据
                  for (let j = 0; j < row.length && j < headers.length; j++) {
                    if (headers[j]) {
                      resultRow[headers[j]] = row[j];
                    }
                  }
                  
                  // 如果存在productDetail且找到了产品列，则进行产品筛选
                  if (jsonContent.productDetail && productColumnIndex !== -1 && row.length > productColumnIndex) {
                    const product = String(row[productColumnIndex]);
                    // 只有当产品名称包含productDetail或完全匹配时才添加此行
                    if (product && (product.includes(jsonContent.productDetail) || product === jsonContent.productDetail)) {
                      filteredData.push(resultRow);
                    }
                  } else {
                    // 如果没有productDetail参数或找不到产品列，添加全部数据行
                    filteredData.push(resultRow);
                  }
                }
                
                // 记录处理信息
                processedData[sheetName] = filteredData;
                console.log(`成功处理${sheetName}表，处理后行数: ${filteredData.length}`);
                
                if (jsonContent.productDetail) {
                  console.log(`根据productDetail:'${jsonContent.productDetail}'筛选产品`);
                }
              } else {
                console.warn(`${sheetName}表为空`);
                processedData[sheetName] = [];
              }
            }
            // ----------- 处理OA植入表 -----------
            else if (sheetName === 'OA植入' || sheetName.includes('OA') || sheetName.includes('植入')) {
              // 将工作表转换为二维数组，包括所有行
              const rawData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1, // 使用数字索引的数组，不将第一行作为表头
                defval: '', 
                raw: false
              });
              
              console.log(`"${sheetName}"表原始数据行数: ${rawData.length}`);
              
              if (rawData.length > 0) {
                // 默认第一行为表头
                let headerRowIndex = 0;
                
                // 查找可能的表头行 - 查找前10行中最有可能是表头的行
                let maxHeaderCells = 0;
                for (let i = 0; i < Math.min(10, rawData.length); i++) {
                  const row = rawData[i];
                  // 计算非空单元格数量
                  const nonEmptyCells = row.filter(cell => cell && String(cell).trim() !== '').length;
                  
                  // 如果这一行有更多的非空单元格，可能是表头
                  if (nonEmptyCells > maxHeaderCells) {
                    maxHeaderCells = nonEmptyCells;
                    headerRowIndex = i;
                  }
                }
                
                // 使用找到的表头行
                const headers = rawData[headerRowIndex];
                console.log(`OA植入表头: ${headers.join(', ')}`);
                
                // 从表头行的下一行开始提取数据
                const filteredData = [];
                
                for (let i = headerRowIndex + 1; i < rawData.length; i++) {
                  const row = rawData[i];
                  if (row.length === 0 || !row.some(cell => cell)) continue; // 跳过空行
                  
                  // 创建结果行对象
                  const resultRow = {};
                  
                  // 添加所有表头对应的数据
                  for (let j = 0; j < row.length && j < headers.length; j++) {
                    const header = headers[j];
                    if (header && String(header).trim() !== '') {
                      resultRow[header] = row[j];
                    }
                  }
                  
                  // 只有当行对象有至少一个属性时才添加
                  if (Object.keys(resultRow).length > 0) {
                    filteredData.push(resultRow);
                  }
                }
                
                // 记录处理信息
                processedData[sheetName] = filteredData;
                console.log(`成功处理${sheetName}表，处理后行数: ${filteredData.length}`);
              } else {
                console.warn(`${sheetName}表为空`);
                processedData[sheetName] = [];
              }
            }
          } else {
            console.warn(`找不到工作表: ${sheetName}`);
            sheetData[sheetName] = null;
          }
        });
      } catch (error) {
        console.error('读取Excel文件数据失败:', error);
      }
    }
    
    // 返回成功响应
    return res.status(200).json({
      code: 200,
      data: {
        processedData: processedData
      }
    });
  });
};

module.exports = {
  uploadSimpleFile
}; 