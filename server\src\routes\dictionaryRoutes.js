const express = require('express');
const router = express.Router();
const dictionaryController = require('../controllers/dictionaryController');
const { requireLogin } = require('../middleware/auth');

// 字典类型相关路由
// 获取字典类型列表
router.get('/type/list', requireLogin, dictionaryController.getDictionaryTypeList);

// 获取字典类型详情
router.get('/type/:id', requireLogin, dictionaryController.getDictionaryTypeDetail);

// 创建字典类型
router.post('/type', requireLogin, dictionaryController.createDictionaryType);

// 更新字典类型
router.put('/type', requireLogin, dictionaryController.updateDictionaryType);

// 删除字典类型
router.delete('/type/:id', requireLogin, dictionaryController.deleteDictionaryType);

// 批量更新字典类型状态
router.put('/type/batch-status', requireLogin, dictionaryController.batchUpdateDictionaryStatus);

// 字典数据相关路由
// 获取字典数据列表
router.get('/data/list', requireLogin, dictionaryController.getDictionaryDataList);

// 通过字典类型编码获取字典数据
router.get('/data/type/:typeCode', dictionaryController.getDictionaryDataByTypeCode);

// 获取字典数据详情
router.get('/data/:id', requireLogin, dictionaryController.getDictionaryDataDetail);

// 创建字典数据
router.post('/data', requireLogin, dictionaryController.createDictionaryData);

// 更新字典数据
router.put('/data', requireLogin, dictionaryController.updateDictionaryData);

// 删除字典数据
router.delete('/data/:id', requireLogin, dictionaryController.deleteDictionaryData);

module.exports = router; 