const {
    axios,
    bcrypt,
    generateToken,
    crypto,
    addEnterpriseId,
    addEnterpriseFilter,
    User,
    Employee,
    ExamConfig,
    PracticeRecord,
    PracticeRecordDetail,
    sequelize,
    KnowledgeBase,
    Op,
    createResponse
} = require('../../utils/responseHelper');
const { SystemSetting } = require('../../models');

const CertificateRecord = require('../../models/CertificateRecord');
const Level = require('../../models/Level');
const PositionName = require('../../models/PositionName');
const EmployeePosition = require('../../models/EmployeePosition');
// 引入岗位控制器
const positionController = require('../organization/positionController');
const redis = require('../../utils/redisClient');
const {promisify} = require('util');
const { safeJsonParse } = require('../../utils/jsonHelper');


// 设置模型关联
Employee.setupEmployeePositionAssociations();

// Redis expire方法现在可以直接使用
// const expireAsync = promisify(redis.client.expire).bind(redis.client);

/**
 * 更新用户岗位
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updatePosition = async (req, res) => {
    try {
        const {positionId, openId} = req.body;

        // 验证必填参数
        if (!positionId) {
            return res.status(400).json(createResponse(400, '岗位ID不能为空'));
        }

        if (!openId) {
            return res.status(400).json(createResponse(400, 'openId不能为空'));
        }

        // 获取当前用户
        const user = await User.findOne(
            addEnterpriseFilter({
                where: {openId: openId}
            })
        );

        if (!user) {
            return res.status(404).json(createResponse(404, '用户不存在'));
        }

        // 更新用户岗位
        await user.update(
            addEnterpriseId({
                currentPosition: positionId
            })
        );

        return res.json(createResponse(200, '岗位更新成功', {
            success: true,
            message: '岗位切换成功'
        }));

    } catch (error) {
        console.error('更新岗位失败:', error);
        return res.status(500).json(createResponse(500, '更新岗位失败', {
            success: false,
            error: error.message
        }));
    }
};

/**
 * 更新用户级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateLevel = async (req, res) => {
    try {
        const {levelId, openId} = req.body;

        // 验证必填参数
        if (!levelId) {
            return res.status(400).json(createResponse(400, '级别ID不能为空'));
        }

        if (!openId) {
            return res.status(400).json(createResponse(400, 'openId不能为空'));
        }

        // 获取当前用户
        const user = await User.findOne(
            addEnterpriseFilter({
                where: {openId: openId}
            })
        );

        if (!user) {
            return res.status(404).json(createResponse(404, '用户不存在'));
        }

        // 更新用户级别
        await user.update(
            addEnterpriseId({
                currentLevel: levelId
            })
        );

        return res.json(createResponse(200, '级别更新成功', {
            success: true,
            message: '级别切换成功'
        }));

    } catch (error) {
        console.error('更新级别失败:', error);
        return res.status(500).json(createResponse(500, '更新级别失败', {
            success: false,
            error: error.message
        }));
    }
};

/**
 * 获取用户练习记录
 * @param {String} openId - 用户openId
 * @param {String} positionName - 岗位名称
 * @param {String} positionLevel - 岗位级别
 * @returns {Promise<Array>} - 练习记录列表
 */
const getPracticeRecords = async (openId, positionName, positionLevel) => {
    try {
        const practiceRecords = await PracticeRecord.findAll({
            where: {
                openId: openId,
                positionName: positionName,
                positionLevel: positionLevel,
            },
            attributes: [
                'examSubject',
                'status',
                [
                    sequelize.literal(`
            SUM(
              CASE 
                WHEN total_duration REGEXP '^[0-9]+:[0-9]+$' 
                THEN 
                  CAST(SUBSTRING_INDEX(total_duration, ':', 1) AS UNSIGNED) + 
                  CAST(SUBSTRING_INDEX(total_duration, ':', -1) AS UNSIGNED) / 60.0
                ELSE 0 
              END
            )
          `),
                    'totalStudyDuration'
                ],
                [
                    sequelize.literal(`
            SUM(
              CASE 
                WHEN total_duration REGEXP '^[0-9]+:[0-9]+$' 
                THEN 
                  (CAST(SUBSTRING_INDEX(total_duration, ':', 1) AS UNSIGNED) * 60 + 
                  CAST(SUBSTRING_INDEX(total_duration, ':', -1) AS UNSIGNED)) / 3600.0
                ELSE 0 
              END
            )
          `),
                    'totalStudyDurationHours'
                ],
                [
                    sequelize.literal(`
            SUM(
              CASE 
                WHEN question_num IS NOT NULL 
                THEN CAST(question_num AS UNSIGNED)
                ELSE 0 
              END
            )
          `),
                    'totalQuestionNum'
                ],
                [sequelize.fn('max', sequelize.col('update_time')), 'lastUpdateTime']
            ],
            group: ['exam_subject', 'status'],
            raw: true  // 添加这个选项以获取原始数据
        });

        return practiceRecords;
    } catch (error) {
        console.error('获取练习记录失败:', error);
        throw error;
    }
};

/**
 * 导出获取用户练习记录方法供其他模块使用
 */
exports.getPracticeRecords = getPracticeRecords;


/**
 * 计算考试资格百分比
 * @param {Object} config - 考试配置信息
 * @param {Object} record - 练习记录信息
 * @returns {Number} - 计算后的考试资格百分比（0-100）
 */
const calculateExamQualification = (config, record) => {
    // 从record中提取数据
    const totalQuestionNum = parseInt(record.totalQuestionNum) || 0;
    const totalStudyDuration = parseFloat(record.totalStudyDuration) || 0;

    // 从config中提取数据
    const questionMode = config.questionMode;
    const practiceQuestionCount = config.practiceQuestionCount || 1; // 避免除以0
    const totalRequireDuration = config.practiceDuration;

    let examQualification = 0;

    // 根据questionMode计算examQualification
    if (questionMode === '题数') {
        // 当为数量时，根据totalQuestionNum/practiceQuestionCount计算
        examQualification = practiceQuestionCount > 0
            ? Math.min((totalQuestionNum / practiceQuestionCount * 100), 100).toFixed(2)
            : 0;
    } else {
        // 当为时长(默认)时，使用原来的计算规则
        examQualification = totalRequireDuration > 0
            ? Math.min((totalStudyDuration / totalRequireDuration * 100), 100).toFixed(2)
            : 0;
    }

    return parseFloat(examQualification);
};


/**
 * 获取练习列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPracticeList = async (req, res) => {
    try {
        const {positionName, positionLevel, openId} = req.query;

        // 参数验证
        if (!positionName || !positionLevel || !openId) {
            return res.json(createResponse(200, '获取练习列表成功，但缺少必要参数', {}));
            // return res.status(400).json(createResponse(400, '缺少必要参数'));
        }

        // 获取练习配置信息 - 移除有问题的关联查询
        const examConfigs = await ExamConfig.findAll(
            addEnterpriseFilter({
                where: {
                    positionName: positionName,
                    positionLevel: positionLevel
                },
                include: [
                    {
                        model: PositionName,
                        as: 'positionDict',
                        attributes: ['id', 'name'],
                        required: false
                    },
                    {
                        model: Level,
                        as: 'level',
                        attributes: ['id', 'name'],
                        required: false
                    }
                ],
                attributes: ['examSubject', 'status', 'practiceDuration', 'questionMode', 'practiceQuestionCount']
            })
        );

        // 单独获取所有知识库数据
        const examSubjectIds = examConfigs.map(config => config.examSubject);
        const knowledgeBases = await KnowledgeBase.findAll(
            addEnterpriseFilter({
                where: {
                    id: {
                        [Op.in]: examSubjectIds
                    }
                },
                attributes: ['id', 'name'],
                raw: true
            })
        );

        // 创建知识库ID到name的映射
        const knowledgeBaseMap = knowledgeBases.reduce((map, kb) => {
            map[kb.id] = kb.name;
            return map;
        }, {});

        // 获取练习记录
        const practiceRecords = await getPracticeRecords(openId, positionName, positionLevel);

        console.log('练习记录:', practiceRecords);
        // 整合数据
        const result = examConfigs.map(config => {
            const practiceRecord = practiceRecords.find(record =>
                record.examSubject === config.examSubject &&
                record.status === config.status
            ) || {
                status: config.status,
                totalStudyDuration: '0',
                totalStudyDurationHours: '0',
                totalQuestionNum: '0',
                lastUpdateTime: null
            };

            const totalStudyDuration = parseFloat(practiceRecord.totalStudyDuration) || 0;
            const totalStudyDurationHours = parseFloat(practiceRecord.totalStudyDurationHours) || 0;
            const totalRequireDuration = config.practiceDuration;
            const totalQuestionNum = parseInt(practiceRecord.totalQuestionNum) || 0;

            // 计算考试资格百分比
            const examQualification = calculateExamQualification(
                config,
                practiceRecord
            );

            return {
                examSubject: config.examSubject,//考试科目
                examSubjectName: knowledgeBaseMap[config.examSubject] || config.examSubject,//考试科目名称
                status: config.status,//考试状态
                totalRequireDuration: totalRequireDuration,//需练习时长
                totalStudyDuration: totalStudyDuration.toFixed(0),//总学习时长
                totalStudyDurationHours: parseFloat(totalStudyDurationHours.toFixed(2)),//总学习时长小时
                examQualification: examQualification,//考试资格百分比
                totalQuestionNum: totalQuestionNum,//已学习题数
                practiceQuestionCount: config.practiceQuestionCount,//需练习题数
                questionMode: config.questionMode,//考试模式
                lastUpdateTime: practiceRecord.lastUpdateTime,//最后更新时间
                positionNameZh: config.positionDict?.name || '',//岗位中文名称
                positionLevelZh: config.level?.name || ''//岗位等级中文名称
            };
        });

        // 按照修改时间倒序排序
        result.sort((a, b) => {
            // 如果lastUpdateTime为null，将其放在末尾
            if (!a.lastUpdateTime) return 1;
            if (!b.lastUpdateTime) return -1;
            // 否则按时间倒序排序
            return new Date(b.lastUpdateTime) - new Date(a.lastUpdateTime);
        });

        return res.json(createResponse(200, '获取练习列表成功', result));
    } catch (error) {
        console.error('获取练习列表失败:', error);
        return res.status(500).json(createResponse(500, '获取练习列表失败', {
            error: error.message
        }));
    }
};

/**
 * 获取练习题目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getQuestion = async (req, res) => {
    try {
        const {file_id, practice_id, time, positionId, leverId, status} = req.body;
        const openId = req.headers['openid'];

        // 验证必填参数
        if (!file_id) {
            return res.status(400).json(createResponse(400, '文件ID不能为空'));
        }

        if (!openId) {
            return res.status(400).json(createResponse(400, 'openId不能为空'));
        }

        // 使用Redis进行题目随机抽取
        // 定义Redis key
        const allQuestionsKey = `kb_all_questions:${file_id}`;
        const usedQuestionsKey = `kb_used_questions:${file_id}:${openId}`;

        let question = null;

        // 检查Redis中是否已缓存所有题目
        const isAllQuestionsCached = await redis.exists(allQuestionsKey);

        // 如果未缓存，则从数据库中查询所有题目并缓存到Redis
        if (isAllQuestionsCached === 0) {
            const allQuestions = await sequelize.query(
                `SELECT id, question, segment_id, answer, type
                 FROM kb_questions
                 WHERE knowledge_base_id = :file_id`,
                {
                    replacements: {file_id},
                    type: sequelize.QueryTypes.SELECT
                }
            );

            if (!allQuestions || allQuestions.length === 0) {
                return res.status(200).json(createResponse(200, '未找到相关题目'));
            }

            // 将所有题目ID缓存到Redis，过期时间1分钟
            await redis.set(allQuestionsKey, allQuestions, 60);
        }

        // 从Redis获取所有题目
        const allQuestions = await redis.get(allQuestionsKey);

        if (!allQuestions || allQuestions.length === 0) {
            return res.status(200).json(createResponse(200, '未找到相关题目'));
        }

        // 从Redis获取已使用过的题目ID
        let usedQuestionIds = await redis.smembers(usedQuestionsKey);

        // 如果所有题目都已使用过，则清空已使用记录，重新开始
        if (usedQuestionIds.length >= allQuestions.length) {
            await redis.del(usedQuestionsKey);
            usedQuestionIds = [];
        }

        // 过滤出未使用的题目
        const unusedQuestions = allQuestions.filter(q => !usedQuestionIds.includes(q.id.toString()));

        // 如果有未使用的题目，随机选择一个
        if (unusedQuestions.length > 0) {
            const randomIndex = Math.floor(Math.random() * unusedQuestions.length);
            question = unusedQuestions[randomIndex];

            // 将已选择的题目ID添加到已使用列表
            await redis.sadd(usedQuestionsKey, question.id);

            // 设置Redis key的过期时间（24小时）
            await redis.expire(usedQuestionsKey, 24 * 60 * 60);
        } else {
            // 异常情况处理，应该不会发生
            return res.status(500).json(createResponse(500, '题目选择出错'));
        }

        let practiceRecordId = practice_id;

        // 如果没有练习ID，创建新的练习记录
        if (!practiceRecordId) {
            // 获取用户信息
            const user = await User.findOne({
                where: {openId}
            });

            if (!user) {
                return res.status(404).json(createResponse(404, '用户不存在'));
            }

            // 获取员工信息
            const employee = await Employee.findOne(
                addEnterpriseFilter({
                    where: {openId},
                    attributes: ['id', 'positionId', 'levelId', 'positionTypeId']
                })
            );

            // 按照优先级确定positionName
            let finalPositionName = null;
            if (positionId) {
                finalPositionName = positionId;
            } else if (user.currentPosition) {
                finalPositionName = user.currentPosition;
            } else if (employee && employee.positionId) {
                finalPositionName = employee.positionId;
            }

            // 按照优先级确定positionLevel
            let finalPositionLevel = null;
            if (leverId) {
                finalPositionLevel = leverId;
            } else if (user.currentLevel) {
                finalPositionLevel = user.currentLevel;
            } else if (employee && employee.levelId) {
                finalPositionLevel = employee.levelId;
            }

            // 获取岗位归属
            let positionBelong = null;
            if (employee) {
                positionBelong = employee.positionTypeId;
            }

            // 如果没有找到examConfig，尝试使用最终确定的岗位信息查询
            let examConfig = null;
            if (finalPositionName && finalPositionLevel) {
                examConfig = await ExamConfig.findOne({
                    where: {
                        positionName: finalPositionName,
                        positionLevel: finalPositionLevel,
                        examSubject: file_id,
                        status: status
                    }
                });
            }

            // 创建练习记录
            const practiceRecord = await PracticeRecord.create(
                addEnterpriseId({
                    openId,
                    positionName: finalPositionName,
                    positionLevel: finalPositionLevel,
                    positionBelong: examConfig ? examConfig.positionBelong : positionBelong,
                    examSubject: file_id,
                    status: status,
                    questionNum: 0,
                    totalDuration: time || '00:00',
                    createTime: new Date(),
                    updateTime: new Date()
                })
            );

            practiceRecordId = practiceRecord.id;
        } else {
            // 更新练习记录的时间
            await PracticeRecord.update(
                addEnterpriseId({
                    totalDuration: time || '00:00',
                    updateTime: new Date()
                }),
                {
                    where: {id: practiceRecordId}
                }
            );
        }

        // 创建练习记录详情
        await PracticeRecordDetail.create(
            addEnterpriseId({
                practiceRecordId,
                role: '系统',
                type: 'question',
                num: 1,
                content: question.question,
                createTime: new Date(),
                updateTime: new Date()
            })
        );

        // 返回结果
        return res.json({
            success: true,
            code: 200,
            message: '获取题目成功',
            data: {
                ...question,
                practice_id: practiceRecordId
            }
        });

    } catch (error) {
        console.error('获取题目失败:', error.response?.data || error.message);
        return res.status(500).json(createResponse(500, '获取题目失败', {
            success: false,
            error: error.message
        }));
    }
};

/**
 * 获取用户练习记录统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPracticeStatistics = async (req, res) => {
    try {
        // 从请求头获取openid
        const openId = req.headers['openid'];

        // 验证必填参数
        if (!openId) {
            return res.status(400).json(createResponse(400, 'openId不能为空'));
        }

        // 计算时间范围
        const now = new Date();

        // 本周开始时间（周一）
        const thisWeekStart = new Date(now);
        thisWeekStart.setDate(now.getDate() - now.getDay() + (now.getDay() === 0 ? -6 : 1));
        thisWeekStart.setHours(0, 0, 0, 0);

        // 本月开始时间
        const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

        // 近三个月开始时间
        const threeMonthsAgo = new Date(now);
        threeMonthsAgo.setMonth(now.getMonth() - 3);
        threeMonthsAgo.setHours(0, 0, 0, 0);

        // 查询所有练习记录
        const allRecords = await PracticeRecord.findAll(
            addEnterpriseFilter({
                where: {
                    openId: openId
                },
                attributes: [
                    'id',
                    'totalDuration',
                    'createTime'
                ],
                order: [['createTime', 'DESC']],
                raw: true
            })
        );

        // 按时间段分类记录
        const weekRecords = allRecords.filter(record => new Date(record.createTime) >= thisWeekStart);
        const monthRecords = allRecords.filter(record => new Date(record.createTime) >= thisMonthStart);
        const threeMonthRecords = allRecords.filter(record => new Date(record.createTime) >= threeMonthsAgo);

        // 处理每个时间段的记录，计算总时长
        const calculateTotalMinutes = (records) => {
            let totalMinutes = 0;

            records.forEach(record => {
                if (record.totalDuration && /^\d+:\d+$/.test(record.totalDuration)) {
                    const [minutes, seconds] = record.totalDuration.split(':').map(Number);
                    totalMinutes += minutes + (seconds / 60);
                }
            });

            // 返回整数分钟数
            return Math.round(totalMinutes);
        };

        // 计算各时间段的总时长
        const weeklyMinutes = calculateTotalMinutes(weekRecords);
        const monthlyMinutes = calculateTotalMinutes(monthRecords);
        const threeMonthMinutes = calculateTotalMinutes(threeMonthRecords);
        const totalMinutes = calculateTotalMinutes(allRecords);

        // 查询已获取证书数量
        const obtainedCertificates = await CertificateRecord.count(
            addEnterpriseFilter({
                where: {
                    openId: openId,
                    delFlag: 0
                }
            })
        );

        // 查询待获取证书数量
        // 1. 获取员工信息
        const employee = await Employee.findOne(
            addEnterpriseFilter({
                where: {openId: openId},
                attributes: ['id']
            })
        );

        let pendingCertificates = 0;
        let totalCertificates = 0;

        if (employee) {
            // 2. 获取该员工的所有岗位关联
            const employeePositions = await EmployeePosition.findAll(
                addEnterpriseFilter({
                    where: {
                        employeeId: employee.id
                    },
                    attributes: ['positionTypeId', 'positionId', 'levelId']
                })
            );
            console.log("employeePositions", employeePositions);

            // 用于收集所有科目的数组（不去重）
            const allRequiredSubjects = [];

            // 3. 遍历每个岗位，查询该岗位的所有必考配置（限制等级范围）
            for (const empPosition of employeePositions) {
                // 获取员工当前岗位的等级信息
                const currentLevel = await Level.findOne(
                    addEnterpriseFilter({
                        where: { id: empPosition.levelId },
                        attributes: ['id', 'name', 'orderNum']
                    })
                );

                if (!currentLevel) {
                    console.log(`未找到等级ID ${empPosition.levelId} 对应的等级信息`);
                    continue;
                }

                // 计算最大允许等级：当前等级orderNum + 1
                const maxAllowedOrderNum = currentLevel.orderNum + 1;
                console.log(`员工岗位 ${empPosition.positionId} 当前等级: ${currentLevel.name}(orderNum: ${currentLevel.orderNum}), 最大允许等级orderNum: ${maxAllowedOrderNum}`);

                // 查询符合等级范围的所有等级ID
                const allowedLevels = await Level.findAll(
                    addEnterpriseFilter({
                        where: {
                            orderNum: { [Op.lte]: maxAllowedOrderNum }
                        },
                        attributes: ['id']
                    })
                );
                const allowedLevelIds = allowedLevels.map(level => level.id);
                console.log(`允许的等级ID列表:`, allowedLevelIds);

                // 查询该岗位在允许等级范围内的所有必考配置
                const examConfigs = await ExamConfig.findAll(
                    addEnterpriseFilter({
                        where: {
                            positionBelong: empPosition.positionTypeId,
                            positionName: empPosition.positionId,
                            status: '必考',
                            positionLevel: { [Op.in]: allowedLevelIds }
                        },
                        attributes: ['examSubject', 'positionLevel']
                    })
                );
                console.log(`岗位 ${empPosition.positionId} 在等级范围内的必考配置:`, examConfigs);

                // 将所有科目添加到数组中
                examConfigs.forEach(exam => {
                    allRequiredSubjects.push(exam.examSubject);
                });
            }

            console.log("所有岗位的必考配置科目:", allRequiredSubjects);

            // 总证书数（不去重，因为不同岗位可能有相同科目的不同配置）
            totalCertificates = allRequiredSubjects.length;

            if (allRequiredSubjects.length > 0) {
                // 查询已获取的证书中的kbId
                const obtainedCertificateKbIds = await CertificateRecord.findAll(
                    addEnterpriseFilter({
                        where: {
                            openId: openId,
                            delFlag: 0
                        },
                        attributes: ['kbId'],
                        raw: true
                    })
                );

                const obtainedKbIds = obtainedCertificateKbIds.map(cert => cert.kbId);

                // 计算待获取的证书数量（排除已获取的）
                const pendingSubjects = allRequiredSubjects.filter(subject => !obtainedKbIds.includes(subject));
                pendingCertificates = pendingSubjects.length;
            }
        }

        // 构建响应数据
        const result = {
            thisWeek: {
                minutes: weeklyMinutes,
                formattedMinutes: `${weeklyMinutes}m`,
                count: weekRecords.length
            },
            thisMonth: {
                minutes: monthlyMinutes,
                formattedMinutes: `${monthlyMinutes}m`,
                count: monthRecords.length
            },
            threeMonths: {
                minutes: threeMonthMinutes,
                formattedMinutes: `${threeMonthMinutes}m`,
                count: threeMonthRecords.length
            },
            total: {
                minutes: totalMinutes,
                formattedMinutes: `${totalMinutes}m`,
                count: allRecords.length
            },
            certificates: {
                obtained: obtainedCertificates,
                pending: pendingCertificates,
                total: totalCertificates
            },
            personalStats: {
                totalMinutes: totalMinutes,
                obtainedCertificates: obtainedCertificates
            }
        };

        return res.json(createResponse(200, '获取练习统计成功', result));
    } catch (error) {
        console.error('获取练习统计失败:', error);
        return res.status(500).json(createResponse(500, '获取练习统计失败', {
            error: error.message
        }));
    }
};

/**
 * 调用Dify API进行答案解析
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.parseAnswer = async (req, res) => {
    try {
        const {question, answer_yh, tip, practice_id, time} = req.body;

        // 验证必填参数
        if (!question || !answer_yh || !tip || !practice_id) {
            return res.status(400).json(createResponse(400, '缺少必要参数'));
        }

        // 从环境变量获取配置
        const difyUrl = process.env.DIFY_URL;
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
        const agentCode = 'AGENT-PARSE';

        if (!difyUrl || !enterpriseId) {
            return res.status(500).json(createResponse(500, '系统配置错误：缺少必要的环境变量'));
        }

        // 构建请求URL
        const apiUrl = `${difyUrl}/api/agent/workflow/run/${enterpriseId}/${agentCode}`;
        console.log('请求URL:', apiUrl);

        // 构建请求体
        const requestBody = {
            inputs: {
                question,
                answer_yh,
                tip
            }
        };

        console.log('请求体:', requestBody);

        // 调用Dify API
        const response = await axios.post(apiUrl, requestBody, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('Dify API响应:', response.data);

        // 更新练习记录的时间和题目数量
        await PracticeRecord.update(
            addEnterpriseId({
                questionNum: sequelize.literal('question_num + 1'),
                totalDuration: time || '00:00',
                updateTime: new Date()
            }),
            {
                where: {id: practice_id}
            }
        );

        // 获取当前练习记录的最大序号
        const maxNum = await PracticeRecordDetail.max('num', {
            where: {practiceRecordId: practice_id}
        });

        // 创建用户答案记录
        await PracticeRecordDetail.create(
            addEnterpriseId({
                practiceRecordId: practice_id,
                role: '用户',
                type: 'answer',
                num: (maxNum || 0) + 1,
                content: answer_yh,
                createTime: new Date(),
                updateTime: new Date()
            })
        );

        // 检查API响应
        let analysisContent = '暂无解析';
        let resultValue = false;
        let correctAnswer = '';

        if (response.data && response.data.success && response.data.data && response.data.data.outputs) {
            let content = response.data.data.outputs.text;
            console.log("content", content);

            // 直接使用content作为分析内容
            analysisContent = content || '暂无解析';

            // 根据文本内容判断是否回答正确
            resultValue = content.includes('回答正确');

            // 清空correctAnswer，因为不再需要
            correctAnswer = '';

            console.log('解析结果:', {
                content: analysisContent,
                answer: correctAnswer,
                result: resultValue
            });
        } else {
            console.error('解析API返回格式不符合预期:', response.data);
            analysisContent = '解析服务异常，请稍后再试';
            resultValue = false;
            correctAnswer = '';
        }

        await PracticeRecordDetail.create(
            addEnterpriseId({
                practiceRecordId: practice_id,
                role: '系统',
                type: 'analysis',
                num: (maxNum || 0) + 2,
                content: analysisContent,
                result: resultValue,
                createTime: new Date(),
                updateTime: new Date()
            })
        );

        // 返回结果
        return res.json({
            success: true,
            code: 200,
            message: '操作成功',
            data: {
                analysisContent,
                resultValue,
                correctAnswer
            }
        });

    } catch (error) {
        console.error('答案解析失败:', error.response?.data || error.message);
        return res.status(500).json(createResponse(500, '答案解析失败', {
            success: false,
            error: error.message
        }));
    }
};

/**
 * 调用Dify API进行答案解析 - WebSocket流式版本
 * @param {Object} ws - WebSocket连接对象
 * @param {Object} data - 请求数据
 */
exports.parseAnswerWebSocket = async (ws, data) => {
    try {
        const {question, answer_yh, tip, practice_id, time} = data;

        // 验证必填参数
        if (!question || !answer_yh || !tip || !practice_id) {
            ws.send(JSON.stringify({
                event: 'error',
                data: {
                    success: false,
                    error: '缺少必要参数'
                }
            }));
            return;
        }

        // 从环境变量获取配置
        const difyUrl = process.env.DIFY_URL;
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
        const agentCode = 'AGENT-PARSE';

        if (!difyUrl || !enterpriseId) {
            ws.send(JSON.stringify({
                event: 'error',
                data: {
                    success: false,
                    error: '系统配置错误：缺少必要的环境变量'
                }
            }));
            return;
        }

        // 构建请求URL
        const apiUrl = `${difyUrl}/api/agent/workflow/run/${enterpriseId}/${agentCode}`;

        // 构建请求体
        const requestBody = {
            inputs: {
                question,
                answer_yh,
                tip
            },
            stream: true
        };

        console.log('请求体:', requestBody);

        // 更新练习记录的时间和题目数量
        await PracticeRecord.update(
            addEnterpriseId({
                questionNum: sequelize.literal('question_num + 1'),
                totalDuration: time || '00:00',
                updateTime: new Date()
            }),
            {
                where: {id: practice_id}
            }
        );

        // 获取当前练习记录的最大序号
        const maxNum = await PracticeRecordDetail.max('num', {
            where: {practiceRecordId: practice_id}
        });

        // 创建用户答案记录
        await PracticeRecordDetail.create(
            addEnterpriseId({
                practiceRecordId: practice_id,
                role: '用户',
                type: 'answer',
                num: (maxNum || 0) + 1,
                content: answer_yh,
                createTime: new Date(),
                updateTime: new Date()
            })
        );

        let completeContent = '';
        let analysisContent = '暂无解析';
        let resultValue = false;
        let correctAnswer = '';

        try {
            // 调用Dify API - 流式请求
            const response = await axios.post(apiUrl, requestBody, {
                headers: {
                    'Accept': 'text/event-stream',
                    'Content-Type': 'application/json'
                },
                responseType: 'stream'
            });

            // 处理流式响应
            response.data.on('data', (chunk) => {
                const lines = chunk.toString().split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            let jsonStr = line.slice(6);
                            let streamData;

                            // 使用安全的JSON解析（支持断裂修复）
                            streamData = safeJsonParse(jsonStr);
                            if (!streamData) {
                                throw new Error('JSON解析失败');
                            }

                            // 过滤掉不需要发送的事件
                            if (streamData.event === 'node_finished' ||
                                streamData.event === 'node_started') {
                                continue; // 跳过这些事件，不发送给前端
                            }

                            // 检查是否为workflow_finished事件
                            if (streamData.event === 'workflow_finished') {
                                // 处理最终结果，但不发送流式内容
                                if (streamData.data && streamData.data.outputs && streamData.data.outputs.text) {
                                    completeContent = streamData.data.outputs.text;

                                    // 直接使用completeContent作为分析内容
                                    analysisContent = completeContent || '暂无解析';

                                    // 根据文本内容判断是否回答正确
                                    resultValue = completeContent.includes('回答正确');

                                    // 清空correctAnswer，因为不再需要
                                    correctAnswer = '';
                                }
                                continue; // 跳过workflow_finished事件的流式内容发送
                            }

                            // 只提取并发送纯文本内容
                            let contentToSend = '';

                            // 根据不同的事件类型提取content
                            if (streamData.event === 'text_chunk' && streamData.data && streamData.data.text) {
                                contentToSend = streamData.data.text;
                            } else if (streamData.event === 'agent_message' && streamData.data && streamData.data.answer) {
                                contentToSend = streamData.data.answer;
                            } else if (streamData.data && streamData.data.outputs && streamData.data.outputs.text) {
                                let rawText = streamData.data.outputs.text;

                                // 清理文本内容
                                contentToSend = rawText;
                            }

                            // 如果有content内容且不为空，发送纯文本给前端
                            if (contentToSend && contentToSend.trim() && ws.readyState === ws.OPEN) {
                                // 额外检查：只过滤纯字段名和布尔值，保留正常内容
                                const finalContent = contentToSend.trim();

                                // 过滤纯字段名和布尔值，其他内容都发送


                                    ws.send(JSON.stringify({
                                        event: 'stream_content',
                                        data: finalContent
                                    }));

                            }

                        } catch (error) {
                            console.error('解析流式数据失败:', error);
                        }
                    }
                }
            });

            response.data.on('end', async () => {
                try {
                    // 保存系统分析记录
                    await PracticeRecordDetail.create(
                        addEnterpriseId({
                            practiceRecordId: practice_id,
                            role: '系统',
                            type: 'analysis',
                            num: (maxNum || 0) + 2,
                            content: analysisContent,
                            result: resultValue,
                            createTime: new Date(),
                            updateTime: new Date()
                        })
                    );

                    // 异步触发成就检测，不影响WebSocket响应
                    setImmediate(async () => {
                        try {
                            // 检查成就系统是否启用
                            const achieveSetting = await SystemSetting.findOne(
                                addEnterpriseFilter({
                                    where: { code: 'achieve' },
                                    attributes: ['value']
                                })
                            );

                            const isAchieveEnabled = achieveSetting && achieveSetting.value === 'true';

                            if (!isAchieveEnabled) {
                                console.log('[parseAnswerWebSocket] 成就系统已禁用，跳过成就检测');
                            }else{
                                console.log('[parseAnswerWebSocket] 成就系统已启用，异步触发完整的成就检测系统');

                                const { handlePracticeAchievementTrigger } = require('../../utils/achievementUtils');

                                // 构建模拟的请求对象，包含WebSocket数据
                                console.log('[parseAnswerWebSocket] 原始 data:', JSON.stringify(data, null, 2));

                                // 从 WebSocket 连接上下文中获取 openid（从请求头传递）
                                let openid = ws.openid || data.openId || data.openid || data.headers?.openid;

                                console.log('[parseAnswerWebSocket] 从WebSocket连接获取的 openid:', ws.openid);
                                console.log('[parseAnswerWebSocket] 从消息数据获取的 openid:', data.openid || data.openId);
                                console.log('[parseAnswerWebSocket] 最终使用的 openid:', openid);

                                const mockReq = {
                                    headers: {
                                        openid: openid,
                                        ...(data.headers || {})
                                    },
                                    body: {
                                        ...data,
                                        time: data.time,
                                        positionName: data.positionName || data.positionId,
                                        positionLevel: data.positionLevel || data.leverId,
                                        file_id: data.file_id
                                    }
                                };

                                // 构建响应体对象
                                const responseData = {
                                    success: true,
                                    code: 200,
                                    message: '操作成功',
                                    data: {
                                        analysisContent,
                                        resultValue,
                                        correctAnswer
                                    }
                                };

                                console.log('[parseAnswerWebSocket] 调用完整的成就触发器');
                                await handlePracticeAchievementTrigger(mockReq, responseData);
                            }



                        } catch (achievementError) {
                            // 成就检测失败不影响主要功能
                            console.error('[parseAnswerWebSocket] 异步成就检测失败:', achievementError);
                        }
                    });

                    // 发送最终完成事件
                    if (ws.readyState === ws.OPEN) {
                        ws.send(JSON.stringify({
                            event: 'analysis_complete',
                            data: {
                                analysisContent,
                                resultValue,
                                correctAnswer,
                                success: true
                            }
                        }));
                    }
                } catch (error) {
                    console.error('保存分析记录失败:', error);
                    if (ws.readyState === ws.OPEN) {
                        ws.send(JSON.stringify({
                            event: 'error',
                            data: {
                                success: false,
                                error: '保存分析记录失败'
                            }
                        }));
                    }
                }
            });

            response.data.on('error', (error) => {
                console.error('流式响应错误:', error);
                if (ws.readyState === ws.OPEN) {
                    ws.send(JSON.stringify({
                        event: 'error',
                        data: {
                            success: false,
                            error: '流式响应错误'
                        }
                    }));
                }
            });

        } catch (error) {
            console.error('调用Dify API失败:', error.response?.data || error.message);
            if (ws.readyState === ws.OPEN) {
                ws.send(JSON.stringify({
                    event: 'error',
                    data: {
                        success: false,
                        error: '调用Dify API失败'
                    }
                }));
            }
        }

    } catch (error) {
        console.error('答案解析失败:', error.message);
        if (ws.readyState === ws.OPEN) {
            ws.send(JSON.stringify({
                event: 'error',
                data: {
                    success: false,
                    error: '答案解析失败'
                }
            }));
        }
    }
};

/**
 * 根据用户类型获取岗位名称选项
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPositionNamesByUserType = async (req, res) => {
    try {
        // 从请求头获取openid
        const openId = req.headers['openid'];

        // 验证必填参数
        if (!openId) {
            return res.status(400).json(createResponse(400, 'openId不能为空'));
        }

        // 查询员工信息，获取position_type_id和levelId
        const employee = await Employee.findOne(
            addEnterpriseFilter({
                where: {openId: openId},
                attributes: ['id', 'positionTypeId', 'levelId'],
            })
        );

        if (!employee) {
            return res.status(404).json(createResponse(404, '员工信息不存在'));
        }

        // 创建mock请求和响应对象以获取positionController.getPositionNameOptions的返回值
        const mockReq = {
            query: {
                typeId: employee.positionTypeId
            }
        };

        let responseData = null;
        const mockRes = {
            json: (data) => {
                responseData = data;
            },
            status: (code) => {
                return {
                    json: (data) => {
                        responseData = data;
                    }
                };
            }
        };

        // 调用positionController的getPositionNameOptions方法，获取返回值
        await positionController.getPositionNameOptions(mockReq, mockRes);

        // 获取当前等级信息
        let higherLevels = [];
        if (employee.levelId) {
            // 先获取当前等级信息，以便获取orderNum
            const currentLevel = await Level.findOne(
                addEnterpriseFilter({
                    where: {
                        id: employee.levelId
                    }
                })
            );

            if (currentLevel) {
                // 查询当前等级及之前的所有等级（orderNum <= 当前等级的orderNum）
                const currentAndPreviousLevels = await Level.findAll(
                    addEnterpriseFilter({
                        where: {
                            orderNum: {[Op.lte]: currentLevel.orderNum}
                        },
                        order: [['orderNum', 'ASC']],
                        attributes: ['id', 'name', 'code', 'orderNum']
                    })
                );

                // 查询当前等级之后的两个等级（orderNum > 当前等级的orderNum）
                const nextTwoLevels = await Level.findAll(
                    addEnterpriseFilter({
                        where: {
                            orderNum: {[Op.gt]: currentLevel.orderNum}
                        },
                        order: [['orderNum', 'ASC']],
                        limit: 2,
                        attributes: ['id', 'name', 'code', 'orderNum']
                    })
                );

                // 合并当前及之前的等级和后面两个等级
                higherLevels = [...currentAndPreviousLevels, ...nextTwoLevels];
            }
        }

        // 合并岗位选项和更高等级数据
        const finalResponse = {
            ...responseData,
            data: {
                ...responseData.data,
                higherLevels
            }
        };

        // 将获取到的结果返回给客户端
        return res.json(finalResponse);

    } catch (error) {
        console.error('获取岗位名称选项失败:', error);
        return res.status(500).json(createResponse(500, '获取岗位名称选项失败', {
            error: error.message
        }));
    }
};

/**
 * 获取最近练习的三个科目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRecentPracticeSubjects = async (req, res) => {
    try {
        const openId = req.headers['openid'];

        // 验证必填参数
        if (!openId) {
            return res.status(400).json(createResponse(400, 'openId不能为空'));
        }

        // 获取所有不同科目的最近练习记录
        const allRecentPractices = await sequelize.query(`
            SELECT
                positionName,
                positionLevel,
                examSubject,
                status,
                create_time
            FROM (
                SELECT
                    position_name as positionName,
                    position_level as positionLevel,
                    exam_subject as examSubject,
                    status,
                    create_time,
                    ROW_NUMBER() OVER (
                        PARTITION BY exam_subject 
                        ORDER BY create_time DESC
                    ) as rn
                FROM practice_record 
                WHERE open_id = :openId AND enterprise_id = :enterpriseId
                    AND status = '必考'
            ) ranked_records
            WHERE rn = 1
            ORDER BY create_time DESC
        `, {
            replacements: {
                openId: openId,
                enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
            },
            type: sequelize.QueryTypes.SELECT
        });

        // 过滤出有效的练习记录（有对应的考试配置），并取前3条
        const validPractices = [];
        for (const practice of allRecentPractices) {
            // 检查是否存在对应的考试配置
            const hasExamConfig = await ExamConfig.findOne(
                addEnterpriseFilter({
                    where: {
                        positionName: practice.positionName,
                        positionLevel: practice.positionLevel,
                        examSubject: practice.examSubject,
                        status: practice.status
                    },
                    attributes: ['id']
                })
            );

            if (hasExamConfig) {
                validPractices.push(practice);
                // 只保留前3条有效记录
                if (validPractices.length >= 3) {
                    break;
                }
            } else {
                console.log(`跳过练习记录：未找到练考配置 - positionName: ${practice.positionName}, positionLevel: ${practice.positionLevel}, examSubject: ${practice.examSubject}, status: ${practice.status}`);
            }
        }

        const recentPractices = validPractices;

        // 获取所有examSubject对应的知识库信息
        const examSubjects = recentPractices.map(practice => practice.examSubject);
        let knowledgeBaseMap = {};

        if (examSubjects.length > 0) {
            const knowledgeBases = await KnowledgeBase.findAll(
                addEnterpriseFilter({
                    where: {
                        id: {
                            [Op.in]: examSubjects
                        }
                    },
                    attributes: ['id', 'name'],
                    raw: true
                })
            );

            // 创建ID到name的映射
            knowledgeBaseMap = knowledgeBases.reduce((map, kb) => {
                map[kb.id] = kb.name;
                return map;
            }, {});
        }

        // 整合数据（此时所有记录都已确认有对应的考试配置）
        const result = [];

        for (const practice of recentPractices) {
            // 获取考试配置详细信息
            const examConfig = await ExamConfig.findOne(
                addEnterpriseFilter({
                    where: {
                        positionName: practice.positionName,
                        positionLevel: practice.positionLevel,
                        examSubject: practice.examSubject,
                        status: practice.status
                    },
                    attributes: ['examSubject', 'status', 'practiceDuration', 'questionMode', 'practiceQuestionCount'],
                    include: [
                        {
                            model: PositionName,
                            as: 'positionDict',
                            attributes: ['id', 'name'],
                            required: false
                        },
                        {
                            model: Level,
                            as: 'level',
                            attributes: ['id', 'name'],
                            required: false
                        }
                    ]
                })
            );

            // 获取该科目的练习记录
            const practiceRecords = await getPracticeRecords(
                openId,
                practice.positionName,
                practice.positionLevel
            );

            // 找到对应科目的记录
            const practiceRecord = practiceRecords.find(record =>
                record.examSubject === practice.examSubject
            ) || {
                totalStudyDuration: '0',
                totalStudyDurationHours: '0',
                totalQuestionNum: '0'
            };

            const totalStudyDuration = parseFloat(practiceRecord.totalStudyDuration) || 0;
            const totalStudyDurationHours = parseFloat(practiceRecord.totalStudyDurationHours) || 0;
            const totalQuestionNum = parseInt(practiceRecord.totalQuestionNum) || 0;

            // 计算考试资格百分比
            const examQualification = calculateExamQualification(
                examConfig,
                practiceRecord
            );

            // 获取岗位中文名称
            let positionNameZh = examConfig?.positionDict?.name;
            if (!positionNameZh && practice.positionName) {
                const positionNameRecord = await PositionName.findOne(
                    addEnterpriseFilter({
                        where: { id: practice.positionName },
                        attributes: ['name']
                    })
                );
                positionNameZh = positionNameRecord?.name || practice.positionName;
            }

            // 获取岗位等级中文名称
            let positionLevelZh = examConfig?.level?.name;
            if (!positionLevelZh && practice.positionLevel) {
                const levelRecord = await Level.findOne(
                    addEnterpriseFilter({
                        where: { id: practice.positionLevel },
                        attributes: ['name']
                    })
                );
                positionLevelZh = levelRecord?.name || practice.positionLevel;
            }

            result.push({
                examSubject: practice.examSubject, // 考试科目
                examSubjectName: knowledgeBaseMap[practice.examSubject] || practice.examSubject, // 考试科目名称
                status: practice.status, // 考试状态
                totalRequireDuration: examConfig?.practiceDuration || 0, // 需练习时长
                totalStudyDuration: totalStudyDuration.toFixed(0), // 总学习时长
                totalStudyDurationHours: parseFloat(totalStudyDurationHours.toFixed(2)), // 总学习时长小时
                examQualification: examQualification, // 考试资格百分比
                totalQuestionNum: totalQuestionNum, // 已学习题数
                practiceQuestionCount: examConfig?.practiceQuestionCount || 0, // 需练习题数
                questionMode: examConfig?.questionMode || '题目', // 考试模式
                lastUpdateTime: practice.create_time, // 最后更新时间
                positionName: practice.positionName, // 岗位名称
                positionLevel: practice.positionLevel, // 岗位等级
                positionNameZh: positionNameZh, // 岗位中文名称
                positionLevelZh: positionLevelZh // 岗位等级中文名称
            });
        }

        return res.json(createResponse(200, '获取最近练习科目成功', result));
    } catch (error) {
        console.error('获取最近练习科目失败:', error);
        return res.status(500).json(createResponse(500, '获取最近练习科目失败', {
            error: error.message
        }));
    }
};

/**
 * 导出获取练习记录供其他模块使用
 */
exports.getPracticeRecords = getPracticeRecords;
/**
 * 导出计算考试资格方法供其他模块使用
 */
exports.calculatePracticeQualification = calculateExamQualification;
