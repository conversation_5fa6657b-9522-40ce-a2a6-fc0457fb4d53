# 成就徽章系统功能总结

## 系统概述

成就徽章系统是一个完整的学习激励系统，通过监听用户的学习行为，自动触发相应的成就奖励，提升用户学习积极性。系统采用事件驱动架构，支持多种成就类型的配置和管理。

## 表数据
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (33, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '初入宝殿', '首次学习一门科目的进度达到2%', '/uploads/achievements/1.png', 'learning', 'progress', '{\"type\":\"first_complete\",\"rule\":\"初入宝殿\",\"progress\":2}', 10, 1, 1, NULL, 'system', '2025-06-18 10:33:48', '2025-06-18 15:20:37');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (34, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '知识探索', '完成2门科目的进度达到50%', '/uploads/achievements/2.png', 'learning', 'progress', '{\"type\":\"multiple_complete\",\"rule\":\"知识探索\",\"progress\":50,\"subjectCount\":2}', 20, 1, 2, NULL, 'system', '2025-06-18 10:33:48', '2025-06-19 15:57:53');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (35, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '学霸模式', '连续学习超过3天', '/uploads/achievements/3.png', 'learning', 'consecutive_days', '{\"type\":\"study_streak\",\"rule\":\"学霸模式\",\"days\":3}', 30, 1, 3, NULL, 'system', '2025-06-18 10:33:48', '2025-06-20 11:01:12');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (36, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '学无止境', '累计学习时长超过1小时', '/uploads/achievements/4.png', 'learning', 'study_time', '{\"type\":\"study_time\",\"rule\":\"学无止境\",\"hours\":1}', 40, 1, 4, NULL, 'system', '2025-06-18 10:33:48', '2025-06-20 15:35:11');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (37, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '碎片时间大师', '一天内三个时间范围都有练习记录', '/uploads/achievement/13.png', 'learning', 'time_based', '{\"type\":\"time_master\",\"rule\":\"跨片时间大师\",\"start1\":1,\"end1\":12,\"start2\":12,\"end2\":18,\"start3\":18,\"end3\":24,\"startTime1\":0,\"endTime1\":12,\"startTime2\":12,\"endTime2\":18,\"startTime3\":18,\"endTime3\":23}', 50, 1, 5, NULL, 'system', '2025-06-18 10:33:48', '2025-06-20 10:22:12');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (38, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '全能力者', '所有岗位都有练习记录', '/uploads/achievement/12.png', 'learning', 'progress', '{\"type\":\"time_master\",\"rule\":\"跨片时间大师\",\"start1\":1,\"end1\":12,\"start2\":12,\"end2\":18,\"start3\":18,\"end3\":24}', 60, 1, 6, NULL, 'system', '2025-06-18 10:33:48', '2025-06-18 10:33:48');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (39, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '金牌毕业生', '所有考试都是通过率100%', '/uploads/achievement/11.png', 'exam', 'progress', '{\"type\":\"all_pass\",\"rule\":\"全能毕业生\",\"passRate\":100}', 70, 1, 7, NULL, 'system', '2025-06-18 10:33:48', '2025-06-18 10:33:48');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (40, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '早起鸟', '4点～7点之间有练习记录，连续5天', '/uploads/achievements/8.png', 'learning', 'time_based', '{\"type\":\"early_bird\",\"rule\":\"早起鸟\",\"startHour\":4,\"endHour\":7,\"days\":5,\"startTime\":4,\"endTime\":7}', 25, 1, 8, NULL, 'system', '2025-06-18 10:33:48', '2025-06-18 10:33:48');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (41, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '夜猫子', '22点～2点之间有练习记录，连续7天', '/uploads/achievements/9.png', 'learning', 'time_based', '{\"type\":\"夜猫子\",\"rule\":\"夜猫子\",\"timeRange\":null,\"days\":7,\"startTime\":22,\"endTime\":2}', 25, 1, 9, NULL, 'system', '2025-06-18 10:33:48', '2025-06-18 10:33:48');
INSERT INTO `ayilai`.`achievement_templates`(`id`, `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (42, '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32', '旗开得胜', '获得第一个考试满分', '/uploads/achievements/10.png', 'exam', 'count', '{\"type\":\"first_perfect_score\",\"rule\":\"旗开得胜\"}', 80, 1, 10, NULL, NULL, '2025-06-18 10:33:48', '2025-06-18 10:33:48');


## 功能特性

### 1. 成就类型支持（共10种）

#### 学习类 (category: 'learning')
- **初入宝殿**: 首次学习一门科目的进度达到xx%（可配置进度百分比），触发位置为server\src\controllers\weichat\wechatPracticeController.js中的calculatePracticeQualification方法 (rule_type: 'progress')，触发位置：server\src\controllers\weichat\wechatPracticeController.js的parseAnswerWebSocket方法 → server\src\utils\achievementUtils.js的handlePracticeAchievementTrigger → processPracticeProgressAchievements → processFirstEntryAchievement（直接检测first_complete类型成就，去掉必考条件）
- **知识探索**: 完成xx门科目的进度达到xx%（可配置科目数量和进度百分比）(rule_type: 'progress')，触发位置同初入宝殿，但检测所有科目而非单个科目，触发位置：server\src\controllers\weichat\wechatPracticeController.js的parseAnswerWebSocket方法 → server\src\utils\achievementUtils.js的handlePracticeAchievementTrigger → processPracticeProgressAchievements → processKnowledgeExplorationAchievement（直接检测multiple_complete类型成就，通过checkAllSubjectsProgressWithoutRequired方法统计所有科目进度，去掉必考条件）
- **碎片时间大师**: 一天内xx点～xx点、xx点～xx点、xx点～xx点三个时间范围都有练习记录（可配置6个时间点）(rule_type: 'time_based')，触发位置：server\src\controllers\weichat\wechatPracticeController.js的parseAnswerWebSocket方法 → server\src\utils\achievementUtils.js的handlePracticeAchievementTrigger → processPracticeProgressAchievements → processTimeMasterAchievement（直接检测time_master类型成就）
- **全能力者**: 所有岗位都有练习记录（无需配置）(rule_type: 'progress')
- **早起鸟**: xx点～xx点之间有练习记录，连续xx天（可配置时间范围和连续天数）(rule_type: 'time_based')
- **夜猫子**: xx点～xx点之间有练习记录，连续xx天（可配置时间范围和连续天数）(rule_type: 'time_based')

#### 时间类 (category: 'time')
- **学霸模式**: 连续学习超过xx天（可配置连续天数）(rule_type: 'streak')
- **学无止境**: 累计学习时长超过xx小时（可配置学习时长）(rule_type: 'time') 

#### 考试类 (category: 'exam')
- **金牌毕业生**: 所有考试都是通过率xx%（可配置通过率百分比，默认100%）(rule_type: 'progress')
- **旗开得胜**: 获得第一个考试满分（无需配置）(rule_type: 'count')，触发位置：server\src\controllers\weichat\wechatExamStartController.js的getExamReport方法 → server\src\utils\achievementUtils.js的processFirstPerfectScoreAchievement（直接检测first_perfect_score类型成就），满分判断条件：考试得分 = 题目总数

### 2. 核心功能

#### 成就模板管理
- 支持10种成就类型的创建和编辑
- 可视化配置界面，参数嵌入描述文本中
- 支持成就图标上传和管理
- 自动生成成就描述

#### 事件监听系统
- 实时监听用户学习行为
- 支持多种触发条件检测
- 防重复触发机制
- 异步事件处理

#### 进度计算
- 集成考试资格计算方法
- 支持首次学习检测
- 连续学习天数统计
- 时间范围练习记录检测

## 技术架构

### 后端文件结构

```
server/src/
├── controllers/
│   └── achievementController.js          # 成就控制器
├── utils/
│   ├── achievementEventListener.js      # 成就事件监听器（通用成就处理逻辑）
│   └── achievementUtils.js              # 成就工具方法（主要成就检测逻辑）
│       ├── processFirstEntryAchievement         # 初入宝殿成就检测
│       ├── processKnowledgeExplorationAchievement # 知识探索成就检测
│       ├── processTimeMasterAchievement         # 碎片时间大师成就检测
│       ├── processFirstPerfectScoreAchievement  # 旗开得胜成就检测
│       ├── processPracticeProgressAchievements  # 练习进度成就检测主方法
│       ├── checkFirstEntryAchievement           # 初入宝殿成就检测（旧方法）
│       ├── checkKnowledgeExplorationAchievement # 知识探索成就检测（旧方法）
│       └── checkAllSubjectsProgressWithoutRequired # 所有科目进度统计（无必考限制）
├── models/
│   ├── AchievementTemplate.js           # 成就模板模型
│   └── UserAchievement.js               # 用户成就模型
└── routes/
    └── achievementRoutes.js             # 成就路由
```

### 前端文件结构

```
web/src/views/achievement/
├── template/
│   └── index.vue                        # 成就模板管理界面
└── user/
    └── index.vue                        # 用户成就查看界面
```

### 数据库表结构

```sql
-- 成就模板表
achievement_templates (
    id,
    enterprise_id,
    name,                    -- 成就名称
    description,             -- 成就描述（自动生成）
    icon,                    -- 成就图标
    category,                -- 成就分类
    rule_type,               -- 规则类型
    trigger_condition,       -- 触发条件（JSON格式）
    reward_points,           -- 奖励积分
    is_active,              -- 是否启用
    sort,                   -- 排序
    create_time,
    update_time
)

-- 用户成就表
user_achievements (
    id,
    enterprise_id,
    user_id,                -- 用户ID
    template_id,            -- 成就模板ID
    achieved_at,            -- 获得时间
    progress,               -- 进度
    is_completed,           -- 是否完成
    create_time,
    update_time
)
```

## 核心实现逻辑

### 1. 事件触发流程

```javascript
// 1. 用户答题行为（WebSocket）
// 2. parseAnswerWebSocket 调用 handlePracticeAchievementTrigger
// 3. checkFirstSubjectProgressAchievement 计算当前科目进度
// 4. 直接调用两个独立的成就检测方法：
//    - checkFirstEntryAchievement：检测初入宝殿成就（first_complete类型）
//    - checkKnowledgeExplorationAchievement：检测知识探索成就（multiple_complete类型）
// 5. 每个方法内部直接调用 awardAchievement 颁发相应成就
// 6. 所有检测都去掉了必考条件，扩大成就触发范围
```

### 2. 首次学习检测

```javascript
// 通过查询练习记录数量判断是否为首次学习
const practiceCount = await WechatPracticeRecord.count({
    where: { userId, subjectId }
});
const isFirstTime = practiceCount <= 1;
```

### 3. 多科目完成检测

```javascript
// 检查所有科目的进度，统计达到要求的科目数量
const allSubjectsProgress = await checkAllSubjectsProgress(openId, positionName, positionLevel, enterpriseId);
const completedSubjectsCount = allSubjectsProgress.filter(subject => subject.progress >= requiredProgress).length;

// 如果达到要求的科目数量，触发知识探索成就
if (completedSubjectsCount >= requiredCount) {
    await awardAchievement(template, userId, openId, enterpriseId, {
        currentValue: completedSubjectsCount,
        targetValue: requiredCount,
        completedSubjects: allSubjectsProgress.filter(s => s.progress >= requiredProgress)
    });
}
```

### 4. 进度计算集成

```javascript
// 使用现有的考试资格计算方法
const { calculatePracticeQualification } = require('../controllers/wechatPracticeController');
const progress = await calculatePracticeQualification(userId, subjectId);
```

### 5. 连续天数统计

```javascript
// 检查连续学习天数
const consecutiveDays = await checkConsecutiveLearningDays(userId);
if (consecutiveDays >= targetDays) {
    // 触发学霸模式成就
}
```

## 配置说明

### 成就条件配置格式

```json
{
    "type": "成就类型",
    "rule": "成就名称",
    "progress": 50,              // 进度百分比（初入宝殿、知识探索、金牌毕业生）
    "count": 5,                  // 科目数量（知识探索）
    "days": 7,                   // 天数（学霸模式、早起鸟、夜猫子）
    "hours": 50,                 // 小时数（学无止境）
    "startTime1": 0,             // 时间范围1（碎片时间大师）
    "endTime1": 12,
    "startTime2": 12,            // 时间范围2（碎片时间大师）
    "endTime2": 18,
    "startTime3": 18,            // 时间范围3（碎片时间大师）
    "endTime3": 23,
    "startTime": 6,              // 时间范围（早起鸟、夜猫子）
    "endTime": 9,
    "passRate": 100              // 通过率（金牌毕业生）
}
```

## 使用说明

### 管理员操作

1. **创建成就模板**
   - 访问成就模板管理页面
   - 选择成就类型
   - 配置相关参数
   - 上传成就图标
   - 保存模板

2. **编辑成就模板**
   - 在列表中点击编辑
   - 修改配置参数
   - 更新保存

### 系统自动触发

1. **学习行为监听**
   - 用户完成练习时自动检测
   - 实时计算成就进度
   - 达成条件时自动颁发

2. **防重复机制**
   - 首次学习类成就只触发一次
   - 连续类成就按天数累计
   - 时间类成就按时间段检测

## 扩展性

### 新增成就类型

1. 在前端界面添加新的成就选项
2. 更新 `handleNameChange` 函数设置默认值
3. 更新 `generateDescription` 函数生成描述
4. 在中间件中添加相应的检测逻辑
5. 在事件监听器中添加处理函数

### 自定义触发条件

系统支持通过修改 `trigger_condition` JSON 配置来自定义各种触发条件，具有很强的扩展性。

## 技术特点

- **事件驱动**: 采用事件驱动架构，解耦业务逻辑
- **实时响应**: 用户行为实时触发成就检测
- **可配置化**: 所有成就参数均可通过界面配置
- **防重复**: 内置防重复触发机制
- **高性能**: 异步处理，不影响主业务流程
- **易扩展**: 模块化设计，易于添加新的成就类型

## 维护说明

### 日常维护

1. **监控成就触发频率**
2. **检查事件处理性能**
3. **定期清理过期数据**
4. **更新成就图标资源**

### 故障排查

1. **检查事件监听器状态**
2. **验证数据库连接**
3. **查看错误日志**
4. **测试成就触发逻辑**

---

*本文档记录了成就徽章系统的完整功能和实现细节，为系统维护和扩展提供参考。* 
