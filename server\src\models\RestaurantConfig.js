const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 餐烤师配置模型
 */
const RestaurantConfig = sequelize.define('RestaurantConfig', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  enterpriseId: {
    type: DataTypes.STRING(255),
    allowNull: false,
    defaultValue: '1',
    field: 'enterprise_id',
    comment: '企业ID'
  },
  positionBelongId: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'position_belong_id',
    comment: '前厅后厨分类ID'
  },
  avatar: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '头像'
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '名称'
  },
  initMessage: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field: 'init_message',
    comment: '打招呼消息'
  },
  createdBy: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'created_by',
    comment: '创建人ID'
  },
  updatedBy: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'updated_by',
    comment: '更新人ID'
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
    comment: '创建时间'
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
    comment: '更新时间'
  }
}, {
  tableName: 'restaurant_config',
  timestamps: true // 使用 createdAt 和 updatedAt
});

module.exports = RestaurantConfig; 