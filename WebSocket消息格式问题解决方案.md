# WebSocket 消息格式问题解决方案

## 🔍 问题分析

目前的问题是：
- openid 在 HTTP 请求中是 `oxiSG65bMvpNcF9TORr5mvW-HXo4`
- 但在 WebSocket 消息中显示为 `undefined`

这说明前端在发送 WebSocket 消息时没有正确包含 openid 字段。

## 🛠️ 解决方案

### 方案1：修改前端 WebSocket 消息格式（推荐）

前端发送消息时应该包含 openid，格式如下：

```javascript
// 正确的 WebSocket 消息格式
const message = {
  type: 'parse_answer',
  payload: {
    question: '题目内容',
    answer_yh: '用户答案', 
    tip: '提示信息',
    practice_id: '练习记录ID',
    time: '练习时间',
    openid: 'oxiSG65bMvpNcF9TORr5mvW-HXo4',  // 添加这个字段
    file_id: '文件ID',
    positionName: '岗位名称',
    positionLevel: '岗位等级'
  }
};

// 发送消息
websocket.send(JSON.stringify(message));
```

### 方案2：在 WebSocket 连接URL中传递 openid

修改 WebSocket 连接URL：

```javascript
// 前端连接 WebSocket 时包含 openid
const websocket = new WebSocket(`ws://localhost:3000?openid=oxiSG65bMvpNcF9TORr5mvW-HXo4`);
```

然后修改服务端代码来从 URL 参数中获取：

```javascript
// 在 server/src/app.js 中修改
wss.on('connection', (ws, req) => {
  const url = new URL(req.url, `http://${req.headers.host}`);
  const openid = url.searchParams.get('openid');
  
  // 将 openid 保存到 WebSocket 连接上
  ws.openid = openid;
  
  console.log('WebSocket客户端已连接, openid:', openid);
});
```

### 方案3：从 WebSocket 连接上下文获取 openid

修改 `parseAnswerWebSocket` 方法：

```javascript
// 在 parseAnswerWebSocket 方法中
exports.parseAnswerWebSocket = async (ws, data) => {
  try {
    // 从 WebSocket 连接上下文获取 openid
    const openid = ws.openid || data.openid || data.openId;
    
    console.log('[parseAnswerWebSocket] 从连接获取的 openid:', openid);
    
    // ... 其他逻辑
  } catch (error) {
    // 错误处理
  }
};
```

## 🎯 立即测试的方法

### 1. 检查当前 WebSocket 消息格式

重新发送练习解析请求，观察服务器日志：

```
[parseAnswerWebSocket] 原始 data: { "question": "...", "answer_yh": "...", ... }
[parseAnswerWebSocket] 提取的 openid: undefined
[parseAnswerWebSocket] 可用字段: ["question", "answer_yh", "tip", ...]
```

从日志中确认是否包含 openid 字段。

### 2. 临时修复方案

如果你无法立即修改前端，可以临时硬编码 openid 进行测试：

```javascript
// 在 parseAnswerWebSocket 中临时添加
let openid = data.openId || data.openid || data.headers?.openid;

// 临时硬编码用于测试
if (!openid) {
  openid = 'oxiSG65bMvpNcF9TORr5mvW-HXo4';  // 你的 openid
  console.log('[parseAnswerWebSocket] 使用临时 openid 进行测试');
}
```

## 📊 验证步骤

1. **查看详细日志**：观察 `[parseAnswerWebSocket] 原始 data:` 的输出
2. **确认字段存在**：检查是否包含 openid 相关字段
3. **测试成就触发**：确认成就检测不再显示 "缺少openId"

## 🔧 推荐的修复步骤

1. **立即使用临时修复**：硬编码 openid 进行测试，确认成就系统正常工作
2. **联系前端开发**：要求在 WebSocket 消息的 payload 中添加 openid 字段
3. **移除临时代码**：前端修复后移除硬编码的 openid

---

**先用临时方案测试，确认成就系统正常后再修复前端！** 🎯 