const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Announcement = sequelize.define('announcement', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  title: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '公告标题'
  },
  content: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: '公告内容'
  },
  status: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '发布状态：0-未发布，1-已发布'
  },
  sort: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排序，数字越小越靠前'
  },
  enterpriseId: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  createTime: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'announcement',
  timestamps: true,
  createdAt: 'createTime',
  updatedAt: 'updateTime',
  indexes: [
    {
      name: 'idx_status',
      fields: ['status']
    },
    {
      name: 'idx_sort',
      fields: ['sort']
    }
  ]
});

module.exports = Announcement; 