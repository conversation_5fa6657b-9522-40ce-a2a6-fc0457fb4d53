/**
 * 检查数据库表结构 - 验证 is_active 和 career_record_id 字段是否存在
 */

const mysql = require('mysql2/promise');

// 数据库配置 - 请根据实际情况修改
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'cankao_admin_dev'
};

// 需要检查的表和字段
const tablesToCheck = [
  'practice_record',
  'practice_record_detail', 
  'exam_records',
  'exam_review_applications',
  'certificate_records',
  'user_achievements',
  'org_employee_position',
  'org_employee_promotion'
];

const requiredFields = ['is_active', 'career_record_id'];

/**
 * 检查表结构
 */
async function checkTableStructure() {
  let connection;
  
  try {
    console.log('🔍 开始检查数据库表结构...');
    console.log('=' .repeat(80));
    
    connection = await mysql.createConnection(dbConfig);
    
    for (const tableName of tablesToCheck) {
      console.log(`\n📋 检查表: ${tableName}`);
      console.log('-'.repeat(50));
      
      try {
        // 获取表结构
        const [columns] = await connection.execute(
          `SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
           FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
           ORDER BY ORDINAL_POSITION`,
          [dbConfig.database, tableName]
        );
        
        if (columns.length === 0) {
          console.log(`❌ 表 ${tableName} 不存在`);
          continue;
        }
        
        console.log(`✅ 表 ${tableName} 存在，共 ${columns.length} 个字段`);
        
        // 检查必需字段
        const existingFields = columns.map(col => col.COLUMN_NAME);
        const missingFields = requiredFields.filter(field => !existingFields.includes(field));
        const presentFields = requiredFields.filter(field => existingFields.includes(field));
        
        if (presentFields.length > 0) {
          console.log(`✅ 已存在字段: ${presentFields.join(', ')}`);
        }
        
        if (missingFields.length > 0) {
          console.log(`❌ 缺失字段: ${missingFields.join(', ')}`);
          
          // 生成 ALTER TABLE 语句
          console.log(`💡 建议执行以下 SQL 语句:`);
          for (const field of missingFields) {
            if (field === 'is_active') {
              console.log(`   ALTER TABLE \`${tableName}\` ADD COLUMN \`is_active\` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）';`);
            } else if (field === 'career_record_id') {
              console.log(`   ALTER TABLE \`${tableName}\` ADD COLUMN \`career_record_id\` int DEFAULT NULL COMMENT '履历ID';`);
            }
          }
        }
        
        // 显示所有字段（仅显示字段名）
        console.log(`📝 当前字段列表: ${existingFields.join(', ')}`);
        
      } catch (error) {
        console.error(`❌ 检查表 ${tableName} 时出错:`, error.message);
      }
    }
    
    console.log('\n' + '=' .repeat(80));
    console.log('🎯 检查完成！');
    
    // 生成完整的修复脚本
    console.log('\n📝 如果需要添加缺失字段，请执行以下完整脚本:');
    console.log('-- 员工履历相关字段添加脚本');
    
    for (const tableName of tablesToCheck) {
      console.log(`\n-- ${tableName} 表`);
      console.log(`ALTER TABLE \`${tableName}\` ADD COLUMN \`is_active\` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃（1活跃 0非活跃）';`);
      console.log(`ALTER TABLE \`${tableName}\` ADD COLUMN \`career_record_id\` int DEFAULT NULL COMMENT '履历ID';`);
    }
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.log('\n💡 请检查数据库配置:');
    console.log('- 主机:', dbConfig.host);
    console.log('- 用户:', dbConfig.user);
    console.log('- 数据库:', dbConfig.database);
    console.log('\n如需修改配置，请编辑此文件顶部的 dbConfig 对象');
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * 检查特定表的详细结构
 */
async function checkSpecificTable(tableName) {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    console.log(`\n🔍 详细检查表: ${tableName}`);
    console.log('=' .repeat(60));
    
    const [columns] = await connection.execute(
      `SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
       FROM INFORMATION_SCHEMA.COLUMNS 
       WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
       ORDER BY ORDINAL_POSITION`,
      [dbConfig.database, tableName]
    );
    
    if (columns.length === 0) {
      console.log(`❌ 表 ${tableName} 不存在`);
      return;
    }
    
    console.log('字段详情:');
    columns.forEach((col, index) => {
      console.log(`${index + 1}. ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || '无注释'}`);
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 执行检查
if (process.argv[2]) {
  // 检查特定表
  checkSpecificTable(process.argv[2]);
} else {
  // 检查所有表
  checkTableStructure();
}
