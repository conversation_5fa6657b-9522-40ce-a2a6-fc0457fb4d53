// 企业数据模型
let enterpriseData = [
  {
    id: 1,
    name: '阿依来科技有限公司',
    code: 'AIL001',
    address: '北京市海淀区中关村南大街5号',
    contact: '张先生',
    phone: '13800138001',
    email: '<EMAIL>',
    status: true,
    createTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    name: '智慧未来科技有限公司',
    code: 'ZHW002',
    address: '上海市浦东新区张江高科技园区',
    contact: '李女士',
    phone: '13800138002',
    email: '<EMAIL>',
    status: true,
    createTime: '2024-01-20 14:30:00'
  },
  {
    id: 3,
    name: '数智云科技有限公司',
    code: 'SZY003',
    address: '深圳市南山区科技园',
    contact: '王先生',
    phone: '13800138003',
    email: '<EMAIL>',
    status: false,
    createTime: '2024-02-01 09:15:00'
  }
];

// 获取企业列表
exports.getEnterpriseList = (query = {}) => {
  let result = [...enterpriseData];
  
  // 支持按企业名称过滤
  if (query.name) {
    result = result.filter(item => 
      item.name.toLowerCase().includes(query.name.toLowerCase())
    );
  }
  
  // 支持按企业编码过滤
  if (query.code) {
    result = result.filter(item => 
      item.code.toLowerCase().includes(query.code.toLowerCase())
    );
  }
  
  // 支持按状态过滤
  if (query.status !== undefined) {
    const status = query.status === 'true' || query.status === true || query.status === 1;
    result = result.filter(item => item.status === status);
  }
  
  // 计算总数
  const total = result.length;
  
  // 分页处理
  const pageNum = parseInt(query.pageNum) || 1;
  const pageSize = parseInt(query.pageSize) || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = pageNum * pageSize;
  const list = result.slice(startIndex, endIndex);
  
  return {
    list,
    total
  };
};

// 获取企业详情
exports.getEnterpriseDetail = (id) => {
  const enterprise = enterpriseData.find(item => item.id === id);
  return enterprise || null;
};

// 创建企业
exports.createEnterprise = (data) => {
  const newEnterprise = {
    ...data,
    id: Date.now(),
    createTime: new Date().toLocaleString()
  };
  
  enterpriseData.push(newEnterprise);
  return newEnterprise;
};

// 更新企业
exports.updateEnterprise = (data) => {
  const index = enterpriseData.findIndex(item => item.id === data.id);
  if (index > -1) {
    enterpriseData[index] = { ...enterpriseData[index], ...data };
    return enterpriseData[index];
  }
  return null;
};

// 删除企业
exports.deleteEnterprise = (id) => {
  const index = enterpriseData.findIndex(item => item.id === id);
  if (index > -1) {
    const deleted = enterpriseData.splice(index, 1)[0];
    return deleted;
  }
  return null;
}; 