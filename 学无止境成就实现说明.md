# 学无止境成就实现说明

## 功能概述

学无止境成就是一种时间类成就，用于奖励累计学习时长超过指定小时数的用户。该成就属于时间类(category: 'time')，规则类型为'time'。

## 实现逻辑

### 1. 成就触发条件

- 用户累计学习时长达到配置的目标小时数（默认为50小时）
- 累计学习时长的计算基于所有练习记录的 `totalDuration` 字段

### 2. 检测流程

1. 从练习记录表(`practice_record`)中获取用户的所有练习记录
2. 计算所有记录的 `totalDuration` 总和（秒）
3. 将总秒数转换为小时
4. 更新用户的成就进度记录
5. 当累计时长达到目标时，颁发成就

### 3. 核心代码

```javascript
/**
 * 处理学无止境成就检测（累计学习时长超过xx小时）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 */
const processEndlessLearningAchievement = async (userId, openId, enterpriseId) => {
  try {
    // 查找学无止境成就模板
    const template = await AchievementTemplate.findOne({
      where: {
        name: '学无止境',
        category: 'time',
        ruleType: 'time',
        isActive: true
      }
    });

    // 获取目标学习时长（小时）
    const triggerCondition = JSON.parse(template.triggerCondition);
    const targetHours = triggerCondition.hours || 50;

    // 获取用户的所有练习记录
    const practiceRecords = await PracticeRecord.findAll({
      where: { openId },
      attributes: ['id', 'totalDuration'],
    });

    // 计算累计学习时长（秒）
    let totalDurationInSeconds = 0;
    practiceRecords.forEach(record => {
      if (record.totalDuration) {
        // 处理 MM:SS 格式
        const parts = record.totalDuration.split(':');
        if (parts.length === 2) {
          const minutes = parseInt(parts[0]) || 0;
          const seconds = parseInt(parts[1]) || 0;
          totalDurationInSeconds += minutes * 60 + seconds;
        }
        // 处理 HH:MM:SS 格式
        else if (parts.length === 3) {
          const hours = parseInt(parts[0]) || 0;
          const minutes = parseInt(parts[1]) || 0;
          const seconds = parseInt(parts[2]) || 0;
          totalDurationInSeconds += hours * 3600 + minutes * 60 + seconds;
        }
      }
    });

    // 转换为小时
    const totalDurationInHours = totalDurationInSeconds / 3600;

    // 如果达到目标学习时长，颁发成就
    if (totalDurationInHours >= targetHours) {
      await awardAchievement(template, userId, openId, enterpriseId, {
        currentValue: totalDurationInHours,
        targetValue: targetHours,
        formattedDuration: formatDuration(totalDurationInSeconds)
      });
    }
  } catch (error) {
    console.error('[学无止境] 检查累计学习时长成就失败:', error);
  }
};
```

### 4. 触发时机

学无止境成就在用户每次练习后触发检测，具体在 `processPracticeProgressAchievements` 函数中调用：

```javascript
// 分别调用独立的成就检测方法
await processFirstEntryAchievement(userId, openId, enterpriseId, subjectId, subjectName, progress);
await processKnowledgeExplorationAchievement(userId, openId, enterpriseId, positionName, positionLevel);
await processTimeMasterAchievement(userId, openId, enterpriseId, positionName, positionLevel);
await processStudyStreakAchievement(userId, openId, enterpriseId);
await processEndlessLearningAchievement(userId, openId, enterpriseId);
```

## 时长计算方法

### 1. 时长格式处理

系统支持两种时长格式：
- `MM:SS` 格式：如 "30:45"（30分钟45秒）
- `HH:MM:SS` 格式：如 "01:30:45"（1小时30分钟45秒）

### 2. 时长转换逻辑

```javascript
// 计算累计学习时长（秒）
let totalDurationInSeconds = 0;
practiceRecords.forEach(record => {
  if (record.totalDuration) {
    // 处理 MM:SS 格式
    const parts = record.totalDuration.split(':');
    if (parts.length === 2) {
      const minutes = parseInt(parts[0]) || 0;
      const seconds = parseInt(parts[1]) || 0;
      totalDurationInSeconds += minutes * 60 + seconds;
    }
    // 处理 HH:MM:SS 格式
    else if (parts.length === 3) {
      const hours = parseInt(parts[0]) || 0;
      const minutes = parseInt(parts[1]) || 0;
      const seconds = parseInt(parts[2]) || 0;
      totalDurationInSeconds += hours * 3600 + minutes * 60 + seconds;
    }
  }
});

// 转换为小时
const totalDurationInHours = totalDurationInSeconds / 3600;
```

### 3. 时长格式化

为了友好展示，提供了时长格式化函数：

```javascript
const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  let result = '';
  if (hours > 0) {
    result += `${hours}小时`;
  }
  if (minutes > 0 || (hours > 0 && remainingSeconds > 0)) {
    result += `${minutes}分钟`;
  }
  if (remainingSeconds > 0 && hours === 0) {
    result += `${remainingSeconds}秒`;
  }
  
  return result || '0分钟';
};
```

## 进度记录

系统会记录用户的学习时长进度：

1. 当用户还未达到目标时长时，会创建或更新进度记录
2. 进度记录包含当前累计时长、目标时长和完成百分比
3. 通过 `timeData` 字段存储详细的时长信息，包括：
   - `totalDurationInHours`: 累计小时数
   - `totalDurationInSeconds`: 累计秒数
   - `formattedDuration`: 格式化后的时长字符串
   - `lastUpdated`: 最后更新时间

## 测试方法

### 1. 测试脚本

我们创建了一个测试脚本 `test-endless-learning-achievement.js`，用于测试学无止境成就的检测逻辑。该脚本模拟用户的练习记录，并检测成就是否正确触发。

### 2. 测试场景

1. **场景1**: 累计学习20小时（不足50小时）
   - 预期结果：不触发成就，但创建进度记录(40%)

2. **场景2**: 累计学习60小时（超过50小时）
   - 预期结果：触发成就(100%)

### 3. 手动测试

1. 登录微信小程序
2. 进行多次练习，累计时长超过50小时
3. 检查成就列表，查看是否获得"学无止境"成就

## 配置说明

学无止境成就的配置存储在 `achievement_templates` 表中，关键字段如下：

```json
{
  "name": "学无止境",
  "description": "累计学习时长超过50小时",
  "icon": "/uploads/achievements/4.png",
  "category": "time",
  "rule_type": "time",
  "trigger_condition": "{\"type\":\"study_time\",\"rule\":\"学无止境\",\"hours\":50}",
  "reward_points": 40,
  "is_active": 1
}
```

可以通过管理后台修改 `trigger_condition` 中的 `hours` 参数来调整目标学习时长。

## 注意事项

1. 成就进度记录存储在 `achievement_progress` 表中，包含当前累计时长、目标时长等信息
2. 累计时长的计算基于 `practice_record` 表的 `total_duration` 字段
3. 该成就不考虑时间范围，只关注总累计时长
4. 一旦用户获得该成就，将不再重复颁发 