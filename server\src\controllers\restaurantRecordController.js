const RestaurantRecord = require('../models/RestaurantRecord');
const RestaurantRecordDetail = require('../models/RestaurantRecordDetail');
const Employee = require('../models/Employee');
const PositionType = require('../models/PositionType');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

/**
 * 分页查询餐烤师聊天记录
 */
exports.getRestaurantRecordList = async (req, res) => {
  try {
    const { 
      employeeName, 
      startTime, 
      endTime, 
      positionName,
      positionLevel,
      pageNum = 1, 
      pageSize = 10 
    } = req.query;

    // 构建查询条件
    const where = {};
    
    // 如果有员工名称，需要先查询员工ID
    if (employeeName) {
      const employees = await Employee.findAll(
        addEnterpriseFilter({
          where: {
            name: {
              [Op.like]: `%${employeeName}%`
            }
          },
          attributes: ['openId']
        })
      );
      
      const openIds = employees.map(emp => emp.openId);
      where.openId = {
        [Op.in]: openIds
      };
    }

    // 时间区间查询
    if (startTime && endTime) {
      // 解析时间字符串为Date对象
      const startDate = new Date(startTime);
      const endDate = new Date(endTime);
      
      // 设置startDate为当天的开始时间（00:00:00）
      startDate.setHours(0, 0, 0, 0);
      
      // 设置endDate为当天的结束时间（23:59:59.999）
      endDate.setHours(23, 59, 59, 999);
      
      where.createdAt = {
        [Op.between]: [startDate, endDate]
      };
    }

    // 岗位名称查询
    if (positionName) {
      where.positionName = positionName;
    }

    // 岗位等级查询
    if (positionLevel) {
      where.positionLevel = positionLevel;
    }

    // 执行分页查询，添加企业过滤
    const { count, rows } = await RestaurantRecord.findAndCountAll(
      addEnterpriseFilter({
        where,
        order: [['createdAt', 'DESC']],
        offset: (pageNum - 1) * pageSize,
        limit: parseInt(pageSize),
        include: [{
          model: Employee,
          as: 'employee',
          attributes: ['name'],
          required: false
        }, {
          model: PositionType,
          as: 'positionType',
          attributes: ['name', 'code'],
          required: false
        }]
      })
    );

    res.json({
      code: 200,
      msg: '查询成功',
      data: {
        total: count,
        records: rows,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('查询餐烤师聊天记录失败:', error);
    res.json({
      code: 500,
      msg: '查询失败',
      error: error.message
    });
  }
};

/**
 * 根据记录ID查询聊天详情
 */
exports.getRestaurantRecordDetail = async (req, res) => {
  try {
    const { id } = req.params;  // 修改这里，从recordId改为id

    const details = await RestaurantRecordDetail.findAll({
      where: {
        restaurantRecordId: id  // 使用id参数
      },
      order: [['createdAt', 'ASC']] // 按创建时间正序排序
    });

    res.json({
      code: 200,
      msg: '查询成功',
      data: details
    });
  } catch (error) {
    console.error('查询聊天详情失败:', error);
    res.json({
      code: 500,
      msg: '查询失败',
      error: error.message
    });
  }
};