# menu-permissions 接口修改说明

## 修改内容

修改了 `server/src/controllers/userController.js` 中的 `getUserMenuPermissions` 方法，实现以下功能：

### 1. menus 返回值变更
- **修改前**：返回所有类型的菜单（包括 type=2 的按钮菜单）
- **修改后**：过滤掉 type=2 的按钮菜单，只返回目录和页面菜单用于构建导航

### 2. permissions 返回值保持不变
- 仍然包含所有菜单的权限标识（包括按钮权限）
- 确保前端权限控制功能正常工作

## 技术实现

### 修改前的查询逻辑
```javascript
// 查询菜单信息
const menus = await Menu.findAll({
  where: {
    id: { [Op.in]: menuIds },
    status: true,
    hidden: false
  }
});

// menus 用于构建菜单树
const menuTree = buildMenuTree(menuList);

// menus 也用于提取权限
const permissions = [];
for (const menu of menus) {
  if (menu && menu.perms) {
    permissions.push(menu.perms);
  }
}
```

### 修改后的查询逻辑
```javascript
// 查询所有菜单信息（包含按钮菜单，用于提取权限）
const allMenus = await Menu.findAll({
  where: {
    id: { [Op.in]: menuIds },
    status: true,
    hidden: false
  }
});

// 查询显示菜单信息（排除按钮菜单，用于构建菜单树）
const displayMenus = await Menu.findAll({
  where: {
    id: { [Op.in]: menuIds },
    status: true,
    hidden: false,
    type: { [Op.ne]: 2 } // 排除type为2的按钮菜单
  }
});

// 使用显示菜单构建菜单树
const menuTree = buildMenuTree(displayMenuList);

// 使用所有菜单提取权限（包括按钮权限）
const permissions = [];
for (const menu of allMenus) {
  if (menu && menu.perms) {
    permissions.push(menu.perms);
  }
}
```

## 菜单类型说明

| Type | 类型名称 | 说明 | 在 menus 中显示 | 在 permissions 中包含 |
|------|---------|------|----------------|-------------------|
| 0 | 目录 | 菜单目录，用于分组 | ✅ 显示 | ✅ 包含权限 |
| 1 | 菜单 | 页面菜单，对应路由页面 | ✅ 显示 | ✅ 包含权限 |
| 2 | 按钮 | 操作按钮，用于权限控制 | ❌ 不显示 | ✅ 包含权限 |

## 接口返回值示例

### 修改前的返回值
```json
{
  "code": 200,
  "message": "获取用户菜单权限成功",
  "data": {
    "menus": [
      {
        "id": 1,
        "name": "系统管理",
        "type": 0,
        "children": [
          {
            "id": 2,
            "name": "用户管理",
            "type": 1,
            "children": [
              {
                "id": 3,
                "name": "新增用户",
                "type": 2,
                "perms": "user.add"
              },
              {
                "id": 4,
                "name": "编辑用户", 
                "type": 2,
                "perms": "user.edit"
              }
            ]
          }
        ]
      }
    ],
    "permissions": ["user.manage", "user.add", "user.edit"]
  }
}
```

### 修改后的返回值
```json
{
  "code": 200,
  "message": "获取用户菜单权限成功",
  "data": {
    "menus": [
      {
        "id": 1,
        "name": "系统管理",
        "type": 0,
        "children": [
          {
            "id": 2,
            "name": "用户管理",
            "type": 1
            // 注意：type=2的按钮菜单不再出现在这里
          }
        ]
      }
    ],
    "permissions": ["user.manage", "user.add", "user.edit"]
    // 注意：permissions仍然包含所有权限，包括按钮权限
  }
}
```

## 影响分析

### 正面影响
1. **菜单导航更清晰**：前端菜单树不再包含按钮项，导航结构更清晰
2. **权限控制不受影响**：所有权限标识仍然正常返回，按钮权限控制继续有效
3. **性能轻微提升**：减少了菜单树的节点数量

### 注意事项
1. **前端兼容性**：需要确认前端菜单渲染逻辑是否依赖于按钮菜单节点
2. **权限验证**：确保前端权限指令 `v-permission` 仍然正常工作
3. **菜单管理**：管理界面创建的按钮菜单仍然会正常保存，只是不在导航中显示

## 测试验证

### 1. 验证菜单显示
- 登录系统查看左侧导航菜单
- 确认只显示目录和页面菜单
- 确认按钮菜单不在导航中显示

### 2. 验证权限功能
- 查看页面中的按钮权限控制
- 确认 `v-permission` 指令正常工作
- 确认有权限的按钮正常显示，无权限的按钮被隐藏

### 3. 验证接口返回
```bash
# 调用接口查看返回值
curl -H "Authorization: Bearer your_token" \
     http://localhost:3000/api/system/user/menu-permissions
```

## 回滚方案

如果需要回滚到原来的逻辑，只需要恢复以下代码：

```javascript
// 恢复为原来的单一查询
const menus = await Menu.findAll({
  where: {
    id: { [Op.in]: menuIds },
    status: true,
    hidden: false
  }
});

// 使用 menus 构建菜单树和提取权限
const menuTree = buildMenuTree(menuList);
const permissions = [];
for (const menu of menus) {
  if (menu && menu.perms) {
    permissions.push(menu.perms);
  }
}
```

通过这次修改，实现了您要求的功能：menus 返回值去掉 type 为 2 的数据，permissions 返回值逻辑保持不变。 