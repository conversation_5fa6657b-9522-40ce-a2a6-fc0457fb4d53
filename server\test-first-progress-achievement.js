const { emitAchievementEvent, ACHIEVEMENT_EVENTS, setupAchievementEventListeners } = require('./src/utils/achievementEventListener');

/**
 * 测试首次学习进度成就
 */
async function testFirstProgressAchievement() {
  console.log('开始测试首次学习进度成就...');
  
  // 首先启动事件监听器
  setupAchievementEventListeners();
  
  // 等待一会儿让监听器初始化完成
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 模拟用户首次学习进度达到50%
  const eventData = {
    userId: 1, // 假设用户ID为1
    openId: 'test_open_id_001',
    enterpriseId: 1,
    subjectId: 1, // 假设科目ID为1
    subjectName: '测试科目',
    progress: 50, // 进度达到50%
    isFirstTime: true, // 首次学习
    positionName: 1,
    positionLevel: 1
  };
  
  console.log('发射首次学习进度事件:', eventData);
  
  // 发射事件
  emitAchievementEvent(ACHIEVEMENT_EVENTS.FIRST_SUBJECT_PROGRESS, eventData);
  
  // 等待一段时间让事件处理完成
  setTimeout(() => {
    console.log('首次学习进度成就测试完成');
    process.exit(0);
  }, 3000);
}

// 运行测试
testFirstProgressAchievement().catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
}); 