const { verifyToken } = require('../utils/jwt');
const { User, Role } = require('../models');

/**
 * 身份验证中间件
 * 从请求头的Authorization字段获取token并验证
 */
exports.requireLogin = async (req, res, next) => {
  try {
    // 检查是否是微信相关路径，如果是则跳过认证
    if (req.originalUrl.includes('/api/wechat') || req.path.includes('/wechat')) {
      return next();
    }
    // 获取请求头中的Authorization
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        code: 401,
        message: '未提供token，请先登录'
      });
    }
    
    // 解析token
    const token = authHeader.split(' ')[1]; // Bearer {token}
    const result = verifyToken(token);
    
    if (!result.valid) {
      return res.status(401).json({
        code: 401,
        message: 'token无效或已过期，请重新登录',
        error: result.error
      });
    }
    
    // 查询用户信息
    const user = await User.findByPk(result.data.userId, {
      include: [
        {
          model: Role,
          through: { attributes: [] } // 不包含中间表字段
        }
      ],
      attributes: { exclude: ['password'] }
    });
    
    if (!user) {
      return res.status(401).json({
        code: 401,
        message: '用户不存在或已被删除'
      });
    }
    
    // 将用户信息添加到请求对象
    req.user = user;
    
    // 继续执行下一个中间件或路由处理函数
    next();
  } catch (error) {
    console.error('验证token失败:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
}; 