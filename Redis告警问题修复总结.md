# Redis 告警问题修复总结

## 问题描述

系统监控显示Redis出现大量告警，主要表现为：
- 大量 "-ERR invalid command\r\n" 错误
- SETINFO命令执行失败
- 来源IP: ************
- 时间集中在2025-06-09

## 问题原因分析

### 1. Redis客户端版本过旧
- 项目使用的是 `redis@3.1.2` 版本（2017年发布）
- 该版本与现代Redis服务器存在兼容性问题
- 老版本客户端在连接时会发送不被支持的SETINFO命令

### 2. 代码中的兼容性问题
- 使用了过时的 `promisify` 方式包装Redis命令
- 直接访问 `redis.client` 对象的方法
- 配置参数名称不匹配新版本要求

## 修复方案

### 1. 更新Redis依赖版本
```json
// server/package.json
"redis": "^4.6.10"  // 从 "^3.1.2" 更新
```

### 2. 重构Redis客户端代码
**文件:** `server/src/utils/redisClient.js`

**主要改动:**
- 移除 `promisify` 包装，使用原生Promise API
- 更新配置参数：`db` → `database`
- 添加显式连接调用：`client.connect()`
- 使用新版本API方法名：
  - `setEx()` 替代 `set(..., 'EX', ...)`
  - `sAdd()` 替代 `sadd()`
  - `sMembers()` 替代 `smembers()`
  - `sRem()` 替代 `srem()`

### 3. 修复业务代码中的调用
**修复文件:**
- `server/src/controllers/weichat/wechatExamStartController.js`
- `server/src/controllers/weichat/wechatPracticeController.js`

**修复内容:**
- 将 `redis.client.incr()` 改为 `redis.incr()`
- 将 `redis.client.expire()` 改为 `redis.expire()`
- 移除手动的 `promisify` 包装

### 4. 新增辅助方法
在Redis客户端中新增：
```javascript
const incr = async (key) => {
  return await client.incr(key);
};

const expire = async (key, seconds) => {
  return await client.expire(key, seconds);
};
```

## 预期效果

1. **消除SETINFO错误**: 新版本客户端与Redis服务器完全兼容
2. **提升连接稳定性**: 使用现代化的连接管理机制
3. **改善性能**: 原生Promise支持，无需额外包装
4. **增强维护性**: 代码结构更清晰，符合最新最佳实践

## 验证步骤

1. 重启服务后观察Redis连接日志
2. 检查监控系统中的错误数量是否下降
3. 验证Redis相关功能正常运行：
   - 缓存读写
   - 集合操作  
   - 过期时间设置

## 注意事项

- 部署前建议在测试环境验证
- 监控服务重启后的Redis连接状态
- 如有异常，可快速回滚到之前版本

---
**修复时间:** 2025-01-16  
**影响范围:** Redis相关的所有功能模块  
**风险等级:** 低（向上兼容的升级） 