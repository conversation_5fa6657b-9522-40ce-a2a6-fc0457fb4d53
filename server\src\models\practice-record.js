const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const PositionType = require('./PositionType');
const PositionName = require('./PositionName');
const Level = require('./Level');
const KnowledgeBase = require('./knowledge-base');
const Employee = require('./Employee');

const PracticeRecord = sequelize.define('practice_record', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id'
  },
  openId: {
    type: DataTypes.STRING(255),
    field: 'open_id',
    allowNull: true,
    comment: 'open_id'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  questionNum: {
    type: DataTypes.INTEGER,
    field: 'question_num',
    allowNull: true,
    comment: '练习题目数'
  },
  totalDuration: {
    type: DataTypes.STRING(255),
    field: 'total_duration',
    allowNull: true,
    comment: '累计练习时长'
  },
  positionBelong: {
    type: DataTypes.STRING(255),
    field: 'position_belong',
    allowNull: true,
    comment: '岗位归属'
  },
  positionName: {
    type: DataTypes.STRING(255),
    field: 'position_name',
    allowNull: false,
    comment: '岗位名称'
  },
  positionLevel: {
    type: DataTypes.STRING(255),
    field: 'position_level',
    allowNull: false,
    comment: '岗位等级'
  },
  examSubject: {
    type: DataTypes.STRING(255),
    field: 'exam_subject',
    allowNull: false,
    comment: '练习科目'
  },
  status: {
    type: DataTypes.STRING(50),
    field: 'status',
    allowNull: true,
    defaultValue: '必练',
    comment: '练习状态：必练/必考'
  },
  employeeId: {
    type: DataTypes.INTEGER,
    field: 'employee_id',
    allowNull: true,
    comment: '员工ID'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    field: 'is_active',
    allowNull: true,
    defaultValue: true,
    comment: '是否有效(1有效 0无效)'
  },
  careerRecordId: {
    type: DataTypes.INTEGER,
    field: 'career_record_id',
    allowNull: true,
    comment: '员工履历记录ID'
  },
  positionBelongName: {
    type: DataTypes.STRING(100),
    field: 'position_belong_name',
    allowNull: true,
    comment: '岗位归属名称'
  },
  positionNameCn: {
    type: DataTypes.STRING(100),
    field: 'position_name_cn',
    allowNull: true,
    comment: '岗位名称'
  },
  positionLevelName: {
    type: DataTypes.STRING(100),
    field: 'position_level_name',
    allowNull: true,
    comment: '岗位等级名称'
  },
  examSubjectName: {
    type: DataTypes.STRING(100),
    field: 'exam_subject_name',
    allowNull: true,
    comment: '考试科目名称'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    allowNull: true,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    allowNull: true,
    comment: '更新时间'
  }
}, {
  tableName: 'practice_record',
  timestamps: false,
  comment: '练习记录'
});

// 添加关联关系
PracticeRecord.belongsTo(PositionType, {
  foreignKey: 'positionBelong',
  targetKey: 'id',
  as: 'positionTypeData'
});

PracticeRecord.belongsTo(PositionName, {
  foreignKey: 'positionName',
  targetKey: 'id',
  as: 'positionNameData'
});

// 岗位等级(positionLevel)关联到岗位等级表(Level)
PracticeRecord.belongsTo(Level, {
  as: 'level',
  foreignKey: 'positionLevel',
  targetKey: 'id',
  constraints: false
});

// 设置与KnowledgeBase的关联关系
PracticeRecord.belongsTo(KnowledgeBase, {
  foreignKey: 'examSubject',
  targetKey: 'id',
  // as: 'subjectKnowledge'
 as: 'knowledge'
});

// 添加与Employee的关联关系
PracticeRecord.belongsTo(Employee, {
  foreignKey: 'openId',
  targetKey: 'openId',
  as: 'employee'
});

module.exports = PracticeRecord;
