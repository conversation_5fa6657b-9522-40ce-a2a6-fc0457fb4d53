/**
 * 快速诊断脚本
 * 用于一键检测菜单点击无反应或加载不出数据的问题
 */

import { useUserStore } from '@/store/modules/user';
import { useMenuStore } from '@/store/modules/menu';
import { menuDiagnostic } from './menuDiagnostic';

export async function quickDiagnose() {
  console.group('🔍 开始快速诊断菜单问题');
  
  try {
    const userStore = useUserStore();
    const menuStore = useMenuStore();
    
    // 清除之前的诊断日志
    menuDiagnostic.clear();
    
    console.log('1️⃣ 检查基础环境...');
    
    // 检查1: Token是否存在
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('❌ 未找到登录token，请重新登录');
      return {
        success: false,
        error: '未登录',
        solution: '请重新登录系统'
      };
    }
    console.log('✅ Token存在');
    
    // 检查2: 用户信息是否加载
    if (!userStore.userInfo) {
      console.warn('⚠️ 用户信息未加载，尝试获取...');
      try {
        await userStore.getCurrentUserInfo();
        console.log('✅ 用户信息获取成功');
      } catch (error) {
        console.error('❌ 获取用户信息失败:', error.message);
        return {
          success: false,
          error: '用户信息获取失败',
          solution: '请检查网络连接或重新登录'
        };
      }
    } else {
      console.log('✅ 用户信息已存在');
    }
    
    console.log('2️⃣ 检查菜单数据...');
    
    // 检查3: 菜单数据是否加载
    if (!userStore.userMenus || userStore.userMenus.length === 0) {
      console.warn('⚠️ 菜单数据为空，尝试重新加载...');
      try {
        await userStore.getUserMenus();
        if (userStore.userMenus && userStore.userMenus.length > 0) {
          console.log(`✅ 菜单重新加载成功，共${userStore.userMenus.length}个菜单项`);
        } else {
          console.error('❌ 菜单重新加载后仍为空');
          return {
            success: false,
            error: '菜单数据为空',
            solution: '请检查用户权限设置或联系管理员'
          };
        }
      } catch (error) {
        console.error('❌ 菜单加载失败:', error.message);
        return {
          success: false,
          error: '菜单加载失败',
          solution: error.response?.status === 403 ? '权限不足，请联系管理员' : '网络错误，请检查网络连接'
        };
      }
    } else {
      console.log(`✅ 菜单数据已存在，共${userStore.userMenus.length}个菜单项`);
    }
    
    // 检查4: 权限数据
    console.log('3️⃣ 检查权限数据...');
    if (!userStore.permissions || userStore.permissions.length === 0) {
      console.warn('⚠️ 权限数据为空，可能影响菜单显示');
    } else {
      console.log(`✅ 权限数据正常，共${userStore.permissions.length}个权限`);
    }
    
    // 检查5: 网络连接
    console.log('4️⃣ 检查网络连接...');
    try {
      const response = await fetch('/api/health', { method: 'HEAD', timeout: 5000 });
      if (response.ok) {
        console.log('✅ 网络连接正常');
      } else {
        console.warn(`⚠️ 服务器响应异常: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ 网络连接失败:', error.message);
      return {
        success: false,
        error: '网络连接失败',
        solution: '请检查网络连接或联系技术支持'
      };
    }
    
    // 检查6: 浏览器兼容性
    console.log('5️⃣ 检查浏览器兼容性...');
    if (!window.localStorage) {
      console.error('❌ 浏览器不支持localStorage');
      return {
        success: false,
        error: '浏览器兼容性问题',
        solution: '请使用现代浏览器（Chrome、Firefox、Safari等）'
      };
    }
    
    if (!window.fetch) {
      console.error('❌ 浏览器不支持fetch API');
      return {
        success: false,
        error: '浏览器兼容性问题',
        solution: '请更新您的浏览器到最新版本'
      };
    }
    console.log('✅ 浏览器兼容性正常');
    
    // 生成详细报告
    const report = menuDiagnostic.generateReport();
    
    console.log('🎉 诊断完成，系统运行正常！');
    return {
      success: true,
      message: '系统运行正常',
      details: {
        userInfo: !!userStore.userInfo,
        menuCount: userStore.userMenus?.length || 0,
        permissionCount: userStore.permissions?.length || 0,
        token: !!token
      },
      report
    };
    
  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
    return {
      success: false,
      error: '诊断失败',
      solution: '请刷新页面重试或联系技术支持',
      details: error.message
    };
  } finally {
    console.groupEnd();
  }
}

// 创建一个便捷的全局诊断函数
export function diagnoseMenu() {
  return quickDiagnose().then(result => {
    if (result.success) {
      console.log('🟢 诊断结果: 系统正常');
    } else {
      console.log('🔴 诊断结果: 发现问题');
      console.log('问题:', result.error);
      console.log('解决方案:', result.solution);
    }
    return result;
  });
}

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  window.diagnoseMenu = diagnoseMenu;
  window.quickDiagnose = quickDiagnose;
} 