-- 创建员工岗位关联表
CREATE TABLE `org_employee_position` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `employee_id` int NOT NULL COMMENT '员工ID',
  `position_id` int NOT NULL COMMENT '岗位ID',
  `level_id` int NOT NULL COMMENT '岗位等级ID',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认岗位(1是 0否)',
  `enterprise_id` varchar(255) NOT NULL COMMENT '企业ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_position_level` (`employee_id`, `position_id`, `level_id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_position_id` (`position_id`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  CONSTRAINT `fk_emp_pos_employee` FOREIGN KEY (`employee_id`) REFERENCES `org_employee` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_emp_pos_position` FOREIGN KEY (`position_id`) REFERENCES `org_position` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_emp_pos_level` FOREIGN KEY (`level_id`) REFERENCES `org_level` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='员工岗位关联表';

-- 创建唯一索引确保每个员工只有一个默认岗位
CREATE UNIQUE INDEX `uk_employee_default_position` ON `org_employee_position` (`employee_id`, `is_default`) WHERE `is_default` = 1;

-- 数据迁移：将现有员工的岗位数据迁移到新表
INSERT INTO `org_employee_position` (
  `employee_id`, 
  `position_id`, 
  `level_id`, 
  `is_default`, 
  `enterprise_id`, 
  `create_time`, 
  `update_time`,
  `create_by`,
  `update_by`
)
SELECT 
  `id` as employee_id,
  `position_id`,
  `level_id`,
  1 as is_default,
  `enterprise_id`,
  `create_time`,
  `update_time`,
  `create_by`,
  `update_by`
FROM `org_employee` 
WHERE `position_id` IS NOT NULL AND `level_id` IS NOT NULL; 