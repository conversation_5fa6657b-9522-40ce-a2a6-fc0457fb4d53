const bcrypt = require('bcryptjs');

// 数据库中存储的密码哈希
const storedHash = '$2a$10$n9TE5gioCg1O8.Xd7p8M0.0NtmbC6h1z5psLOl8BmPZY2hnPbKA3m';

// 测试明文密码
const testPasswords = ['123456', 'admin123', 'password'];

console.log('当前bcrypt版本:', bcrypt.version);
console.log('数据库中存储的密码哈希:', storedHash);

// 生成新的哈希
console.log('\n生成新的密码哈希:');
testPasswords.forEach(password => {
  const newHash = bcrypt.hashSync(password, 10);
  console.log(`密码 "${password}" 的哈希值: ${newHash}`);
});

// 验证密码
console.log('\n验证密码:');
testPasswords.forEach(password => {
  const isMatch = bcrypt.compareSync(password, storedHash);
  console.log(`密码 "${password}" 匹配: ${isMatch}`);
});

// 手动创建用于登录的用户
console.log('\n创建用于登录的新哈希:');
const loginPassword = '123456';
const loginHash = bcrypt.hashSync(loginPassword, 10);
console.log(`登录密码 "${loginPassword}" 的哈希值: ${loginHash}`);
console.log(`验证: ${bcrypt.compareSync(loginPassword, loginHash)}`); 