const EmployeePromotion = require('../../models/EmployeePromotion');
const Employee = require('../../models/Employee');
const Position = require('../../models/Position');
const Level = require('../../models/Level');
const PositionType = require('../../models/PositionType');
const PositionName = require('../../models/PositionName');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../../utils/enterpriseFilter');

/**
 * 获取员工晋升时间记录列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeePromotionList = async (req, res) => {
  try {
    const { openId, positionTypeId, positionId, levelId, pageNum = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (openId) {
      where.openId = openId;
    }
    if (positionTypeId) {
      where.positionTypeId = positionTypeId;
    }
    if (positionId) {
      where.positionId = positionId;
    }
    if (levelId) {
      where.levelId = levelId;
    }
    
    // 分页查询
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);
    
    // 查询员工晋升数据，添加企业ID过滤
    const { count, rows } = await EmployeePromotion.findAndCountAll(
      addEnterpriseFilter({
        where,
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'name']
          },
          {
            model: Position,
            as: 'position',
            attributes: ['id', 'nameId'],
            include: [
              {
                model: PositionName,
                as: 'positionName',
                attributes: ['id', 'name']
              }
            ]
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name']
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name']
          }
        ],
        offset,
        limit,
        order: [
          ['createTime', 'DESC']
        ]
      })
    );
    
    // 处理数据
    const data = rows.map(promotion => {
      const item = promotion.toJSON();
      
      // 处理关联数据
      item.employeeName = item.employee ? item.employee.name : '';
      item.positionName = item.position && item.position.positionName ? item.position.positionName.name : '';
      item.levelName = item.level ? item.level.name : '';
      item.positionTypeName = item.positionType ? item.positionType.name : '';
      
      return item;
    });
    
    res.json({
      code: 200,
      data: {
        total: count,
        rows: data,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      },
      message: '获取员工晋升记录列表成功'
    });
  } catch (error) {
    console.error('获取员工晋升记录列表失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取员工晋升记录列表失败',
      error: error.message
    });
  }
};

/**
 * 获取员工晋升时间记录详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeePromotionDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询员工晋升记录详情，添加企业ID过滤
    const promotion = await EmployeePromotion.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'name']
          },
          {
            model: Position,
            as: 'position',
            attributes: ['id', 'nameId'],
            include: [
              {
                model: PositionName,
                as: 'positionName',
                attributes: ['id', 'name']
              }
            ]
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name']
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name']
          }
        ]
      })
    );
    
    if (!promotion) {
      return res.status(404).json({
        code: 404,
        message: '员工晋升记录不存在'
      });
    }
    
    // 处理数据
    const data = promotion.toJSON();
    
    // 处理关联数据
    data.employeeName = data.employee ? data.employee.name : '';
    data.positionName = data.position && data.position.positionName ? data.position.positionName.name : '';
    data.levelName = data.level ? data.level.name : '';
    data.positionTypeName = data.positionType ? data.positionType.name : '';
    
    res.json({
      code: 200,
      data,
      message: '获取员工晋升记录详情成功'
    });
  } catch (error) {
    console.error('获取员工晋升记录详情失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取员工晋升记录详情失败',
      error: error.message
    });
  }
};

/**
 * 添加员工晋升时间记录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.addEmployeePromotion = async (req, res) => {
  try {
    const { openId, positionTypeId, positionId, levelId } = req.body;
    
    // 验证必填字段
    if (!openId) {
      return res.status(400).json({
        code: 400,
        message: '员工openId不能为空'
      });
    }
    
    // 验证员工是否存在
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { openId }
      })
    );
    
    if (!employee) {
      return res.status(400).json({
        code: 400,
        message: '员工不存在'
      });
    }
    
    // 添加创建者信息
    const promotionData = {
      openId,
      positionTypeId,
      positionId,
      levelId,
      createBy: req.user ? req.user.username : 'system',
      updateBy: req.user ? req.user.username : 'system'
    };
    
    // 创建员工晋升记录，添加企业ID
    const promotion = await EmployeePromotion.create(
      addEnterpriseId(promotionData)
    );
    
    res.json({
      code: 200,
      data: promotion,
      message: '添加员工晋升记录成功'
    });
  } catch (error) {
    console.error('添加员工晋升记录失败：', error);
    res.status(500).json({
      code: 500,
      message: '添加员工晋升记录失败',
      error: error.message
    });
  }
};

/**
 * 更新员工晋升时间记录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateEmployeePromotion = async (req, res) => {
  try {
    const { id, openId, positionTypeId, positionId, levelId } = req.body;
    
    // 验证必填字段
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '晋升记录ID不能为空'
      });
    }
    
    // 验证员工晋升记录是否存在
    const existingPromotion = await EmployeePromotion.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!existingPromotion) {
      return res.status(404).json({
        code: 404,
        message: '员工晋升记录不存在'
      });
    }
    
    // 需要更新的数据
    const updateData = {};
    
    if (openId) {
      // 验证员工是否存在
      const employee = await Employee.findOne(
        addEnterpriseFilter({
          where: { openId }
        })
      );
      
      if (!employee) {
        return res.status(400).json({
          code: 400,
          message: '员工不存在'
        });
      }
      updateData.openId = openId;
    }
    
    if (positionTypeId !== undefined) updateData.positionTypeId = positionTypeId;
    if (positionId !== undefined) updateData.positionId = positionId;
    if (levelId !== undefined) updateData.levelId = levelId;
    
    // 添加更新者信息
    updateData.updateBy = req.user ? req.user.username : 'system';
    updateData.updateTime = new Date();
    
    // 更新员工晋升记录
    await existingPromotion.update(updateData);
    
    res.json({
      code: 200,
      data: await existingPromotion.reload(),
      message: '更新员工晋升记录成功'
    });
  } catch (error) {
    console.error('更新员工晋升记录失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新员工晋升记录失败',
      error: error.message
    });
  }
};

/**
 * 删除员工晋升时间记录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deleteEmployeePromotion = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 验证员工晋升记录是否存在
    const promotion = await EmployeePromotion.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!promotion) {
      return res.status(404).json({
        code: 404,
        message: '员工晋升记录不存在'
      });
    }
    
    // 删除员工晋升记录
    await promotion.destroy();
    
    res.json({
      code: 200,
      message: '删除员工晋升记录成功'
    });
  } catch (error) {
    console.error('删除员工晋升记录失败：', error);
    res.status(500).json({
      code: 500,
      message: '删除员工晋升记录失败',
      error: error.message
    });
  }
};

/**
 * 根据员工openId获取晋升记录历史
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeePromotionHistory = async (req, res) => {
  try {
    const { openId } = req.params;
    
    // 验证员工是否存在
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { openId }
      })
    );
    
    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }
    
    // 查询该员工的所有晋升记录，添加企业ID过滤
    const promotions = await EmployeePromotion.findAll(
      addEnterpriseFilter({
        where: { openId },
        include: [
          {
            model: Position,
            as: 'position',
            attributes: ['id', 'nameId'],
            include: [
              {
                model: PositionName,
                as: 'positionName',
                attributes: ['id', 'name']
              }
            ]
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name']
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name']
          }
        ],
        order: [
          ['createTime', 'DESC']
        ]
      })
    );
    
    // 处理数据
    const data = promotions.map(promotion => {
      const item = promotion.toJSON();
      
      // 处理关联数据
      item.positionName = item.position && item.position.positionName ? item.position.positionName.name : '';
      item.levelName = item.level ? item.level.name : '';
      item.positionTypeName = item.positionType ? item.positionType.name : '';
      
      return item;
    });
    
    res.json({
      code: 200,
      data,
      message: '获取员工晋升历史记录成功'
    });
  } catch (error) {
    console.error('获取员工晋升历史记录失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取员工晋升历史记录失败',
      error: error.message
    });
  }
}; 