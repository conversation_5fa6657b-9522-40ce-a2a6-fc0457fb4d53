#!/bin/bash

# 更新Docker镜像版本脚本
# 用于更新.env文件中的版本号

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 配置项
APP_DIR="/home/<USER>/enterprise/demo_enterprise"
ENV_FILE="${APP_DIR}/.env"
BACKUP_DIR="${APP_DIR}/backups/$(date +%Y%m%d_%H%M%S)"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -a, --api VERSION    设置后端API版本"
    echo "  -w, --web VERSION    设置前端Web版本"
    echo "  -b, --both VERSION   同时设置API和Web版本"
    echo "  -h, --help           显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --api v1.2.3      # 只更新API版本到v1.2.3"
    echo "  $0 --web v1.2.3      # 只更新Web版本到v1.2.3"
    echo "  $0 --both v1.2.3     # 同时更新API和Web版本到v1.2.3"
}

# 备份环境变量文件
backup_env() {
    log_info "备份环境变量文件..."
    mkdir -p "$BACKUP_DIR"
    cp "$ENV_FILE" "${BACKUP_DIR}/.env.bak" || {
        log_error "无法备份环境变量文件"
        exit 1
    }
    log_info "环境变量文件已备份到 ${BACKUP_DIR}/.env.bak"
}

# 更新版本号
update_version() {
    local component=$1
    local version=$2
    local var_name=""
    
    case "$component" in
        "api")
            var_name="ADMIN_DOCKER_VERSION"
            ;;
        "web")
            var_name="ADMIN_WEB_DOCKER_VERSION"
            ;;
        *)
            log_error "未知组件: $component"
            exit 1
            ;;
    esac
    
    # 检查.env文件是否存在
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境变量文件不存在: $ENV_FILE"
        exit 1
    fi
    
    # 检查变量是否存在
    if grep -q "^${var_name}=" "$ENV_FILE"; then
        # 更新已存在的变量
        sed -i "s/^${var_name}=.*/${var_name}=${version}/" "$ENV_FILE"
    else
        # 添加新变量
        echo "${var_name}=${version}" >> "$ENV_FILE"
    fi
    
    log_info "${component} 版本已更新为 ${version}"
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -a|--api)
                API_VERSION="$2"
                shift 2
                ;;
            -w|--web)
                WEB_VERSION="$2"
                shift 2
                ;;
            -b|--both)
                API_VERSION="$2"
                WEB_VERSION="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查是否提供了版本号
    if [ -z "$API_VERSION" ] && [ -z "$WEB_VERSION" ]; then
        log_error "请指定要更新的版本号"
        show_help
        exit 1
    fi
    
    # 备份环境变量文件
    backup_env
    
    # 更新版本号
    if [ -n "$API_VERSION" ]; then
        update_version "api" "$API_VERSION"
    fi
    
    if [ -n "$WEB_VERSION" ]; then
        update_version "web" "$WEB_VERSION"
    fi
    
    log_info "版本更新完成，请运行 ./graceful-update.sh 应用更改"
}

# 执行主函数
main "$@" 