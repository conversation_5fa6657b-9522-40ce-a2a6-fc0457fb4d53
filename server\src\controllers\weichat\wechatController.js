const {
  axios,
  bcrypt,
  generateToken,
  crypto,
  addEnterpriseId,
  addEnterpriseFilter,
  User,
  Employee,
  EmployeeEnterpriseApplication,
  ExamConfig,
  PracticeRecord,
  sequelize,
  KnowledgeBase,
  Level,
  Op,
  InfoConfig,
  createResponse,
  PositionType,
  Position,
  PositionName,
  SystemSetting,
  RestaurantConfig,
  RestaurantRecord,
  RestaurantRecordDetail,
} = require('../../utils/responseHelper');

// 导入Redis客户端
const redis = require('../../utils/redisClient');

const { safeJsonParse } = require('../../utils/jsonHelper');

// 导入WebSocket库
const WebSocket = require('ws');

// 导入公告模型
const Announcement = require('../../models/Announcement');

// 导入成就相关模型
const UserAchievement = require('../../models/UserAchievement');
const AchievementTemplate = require('../../models/AchievementTemplate');

/**
 * 获取企业信息（使用Redis缓存）
 * @param {string} enterpriseId - 企业ID
 * @returns {Promise<Object|null>} - 企业信息对象或null
 */
const getEnterpriseInfo = async (enterpriseId) => {
  if (!enterpriseId) {
    return null;
  }

  const cacheKey = `enterprise:${enterpriseId}`;

  try {
    // 先从Redis获取缓存
    const cachedEnterpriseInfo = await redis.get(cacheKey);

    if (cachedEnterpriseInfo) {
      console.log('从Redis缓存获取企业信息:', enterpriseId);
      return cachedEnterpriseInfo;
    } else {
      console.log('缓存中没有企业信息，请求API:', enterpriseId);
      // 缓存中没有，请求API
      const DIFY_URL = process.env.DIFY_URL;
      if (DIFY_URL) {
        const enterpriseApiUrl = `${DIFY_URL}/api/enterprise/uuid/${enterpriseId}`;

        try {
          const response = await axios.get(enterpriseApiUrl);

          if (response.data && response.data.success && response.data.code === 200) {
            const enterpriseInfo = response.data.data;

            // 保存企业名称和sign到system_settings
            if (enterpriseInfo && enterpriseInfo.name) {
              await upsertSystemSetting('company_name', enterpriseInfo.name, enterpriseId);
            }
            if (enterpriseInfo && enterpriseInfo.sign) {
              await upsertSystemSetting('company_sign', enterpriseInfo.sign, enterpriseId);
            }

            // 保存到Redis缓存，设置过期时间为1小时
            await redis.set(cacheKey, enterpriseInfo, 3600);
            console.log('企业信息已缓存到Redis:', enterpriseId);

            return enterpriseInfo;
          } else {
            console.log('获取企业信息失败:', response.data);
            return null;
          }
        } catch (apiError) {
          console.error('调用企业信息API失败:', apiError.message);
          return null;
        }
      } else {
        console.log('DIFY_URL未配置，无法获取企业信息');
        return null;
      }
    }
  } catch (redisError) {
    console.error('Redis操作失败:', redisError.message);
    // Redis失败时仍然尝试直接调用API
    const DIFY_URL = process.env.DIFY_URL;
    if (DIFY_URL) {
      try {
        const enterpriseApiUrl = `${DIFY_URL}/api/enterprise/uuid/${enterpriseId}`;
        const response = await axios.get(enterpriseApiUrl);

        if (response.data && response.data.success && response.data.code === 200) {
          return response.data.data;
        }
      } catch (apiError) {
        console.error('fallback调用企业信息API失败:', apiError.message);
      }
    }
    return null;
  }
};

// 清理文本内容的辅助函数
function cleanTextContent(rawText) {
    if (!rawText) return '';

    let cleanText = rawText;

    try {
        // 处理可能带有```json标记的字符串
        if (cleanText.startsWith('```json\n')) {
            cleanText = cleanText.substring('```json\n'.length);
        }
        if (cleanText.endsWith('\n```')) {
            cleanText = cleanText.substring(0, cleanText.length - 4);
        }

        // 尝试解析JSON并提取content
        const parsed = JSON.parse(cleanText);
        if (parsed.content) {
            // 直接返回content内容，不包含任何JSON结构
            return parsed.content.trim();
        }
    } catch (parseError) {
        // 如果不是JSON格式，继续清理
    }

    // 强硬清理result字段 - 多重过滤策略
    cleanText = cleanText
        // 1. 移除JSON结构字符
        .replace(/^\s*\{\s*/, '') // 开头的 {
        .replace(/\s*\}\s*$/, '') // 结尾的 }
        .replace(/^\s*\[\s*/, '') // 开头的 [
        .replace(/\s*\]\s*$/, '') // 结尾的 ]

        // 2. 强硬移除result字段 - 各种可能的格式
        .replace(/^"?result"?\s*:\s*"?/gi, '') // result: 或 "result":
        .replace(/"?result"?\s*:\s*/gi, '') // 去掉中间的result字段
        .replace(/\bresult\b/gi, '') // 强硬移除所有独立的result单词
        .replace(/result/gi, '') // 最强硬：移除所有result字符串

        // 3. 移除其他字段标识（更全面的匹配）
        .replace(/^"?(content|answer|response|text|message|data|output|value)"?\s*:\s*"?/gi, '')
        .replace(/"?(content|answer|response|text|message|data|output|value)"?\s*:\s*/gi, '')

        // 4. 移除布尔值和常见的无用词汇
        .replace(/^(true|false|null|undefined)$/gi, '')
        .replace(/\b(true|false|null|undefined)\b/gi, '')
        .replace(/^(content|answer|response|text|message|data|output|value)$/gi, '')

        // 5. 移除json相关标识
        .replace(/^json$/i, '')
        .replace(/^\s*json\s*$/i, '')
        .replace(/\bjson\b/gi, '')

        // 6. 清理转义字符
        .replace(/\\"/g, '"') // 转义的引号
        .replace(/\\n/g, '\n') // 转义的换行
        .replace(/\\r/g, '\r') // 转义的回车
        .replace(/\\t/g, '\t') // 转义的制表符
        .replace(/\\\\/g, '\\') // 转义的反斜杠

        // 7. 更彻底地移除双引号
        .replace(/^"*/, '') // 开头的所有双引号
        .replace(/"*$/, '') // 结尾的所有双引号
        .replace(/"+/g, '') // 中间的连续双引号

        // 8. 移除反引号符号
        .replace(/`/g, '') // 移除所有反引号

        // 9. 更彻底地移除标点符号
        .replace(/^[,:;!?]*/, '') // 开头的标点符号
        .replace(/[,:;!?]*$/, '') // 结尾的标点符号
        .replace(/\s*[:]\s*/g, ' ') // 中间的冒号替换为空格

        // 10. 特殊处理：移除中文句号后的逗号等
        .replace(/。\s*[,:;]*\s*$/, '。') // 中文句号后的标点
        .replace(/\.\s*[,:;]*\s*$/, '.') // 英文句号后的标点
        .replace(/！\s*[,:;]*\s*$/, '！') // 中文感叹号后的标点
        .replace(/？\s*[,:;]*\s*$/, '？') // 中文问号后的标点

        // 11. 强硬清理多余的空白字符
        .replace(/\s+/g, ' ') // 多个空格变成一个
        .trim(); // 去除首尾空白

    // 12. 二次强硬清理result残留
    cleanText = cleanText
        .replace(/^result\s*/gi, '') // 开头的result
        .replace(/\s*result$/gi, '') // 结尾的result
        .replace(/\s+result\s+/gi, ' ') // 中间的result用空格替换
        .replace(/result/gi, ''); // 最终强硬移除所有result

    // 13. 最终清理：再次移除末尾的标点符号（防止遗漏）
    cleanText = cleanText.replace(/[,:;]+$/, '');

    // 14. 如果清理后是空字符串或只是特殊字符，返回空
    if (!cleanText || /^[^\w\u4e00-\u9fa5]+$/.test(cleanText)) {
        return '';
    }

    // 15. 最后一次检查，确保没有result残留
    if (/result/i.test(cleanText)) {
        cleanText = cleanText.replace(/result/gi, '').trim();
    }

    return cleanText;
}

const { getFileType } = require('../../utils/fileUpload');
const { count } = require('../../models/knowledge-base');

/**
 * 获取微信OpenID
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getOpenId = async (req, res) => {
  try {
    const { jsCode } = req.query;

    if (!jsCode) {
      return res.status(400).json(createResponse(400, 'jsCode参数不能为空'));
    }

    // 获取配置
    const clientId = process.env.WECHAT_APPID;
    const clientSecret = process.env.WECHAT_SECRET;

    if (!clientId || !clientSecret) {
      return res.status(500).json(createResponse(500, '微信小程序配置未设置，请检查环境变量'));
    }

    // 构造请求参数
    const requestUrl = "https://api.weixin.qq.com/sns/jscode2session";
    const requestUrlParam = {
      appid: clientId,
      secret: clientSecret,
      js_code: jsCode,
      grant_type: "authorization_code"
    };
    // 发送请求获取openid
    const response = await axios.get(requestUrl, { params: requestUrlParam });

    if (response.data.errcode) {
      return res.status(400).json(createResponse(
        400,
        `微信接口返回错误: ${response.data.errmsg}(${response.data.errcode})`
      ));
    }

    // 只返回openid
    return res.json(createResponse(200, '获取OpenID成功', { openid: response.data.openid }));
  } catch (error) {
    console.error('获取OpenID失败:', error);
    return res.status(500).json(createResponse(500, '获取OpenID失败', { error: error.message }));
  }
};

/**
 * 微信小程序用户认证
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.authenticate = async (req, res) => {
  try {
    const { avatar, nickname, phone, openId } = req.body;

    // 验证必填参数
    // if (!avatar) {
    //   return res.status(400).json(createResponse(400, '微信头像不能为空'));
    // }

    if (!nickname) {
      return res.status(400).json(createResponse(400, '昵称不能为空'));
    }

    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId不能为空'));
    }

    console.log('认证请求参数:', { avatar, nickname, phone, openId });

    // 检查是否已存在该openId的用户，添加企业ID过滤
    let user = await User.findOne(
      addEnterpriseFilter({
        where: {
          [Op.or]: [
            { openId: openId },
            { username: `wx_${openId}` }
          ]
        }
      })
    );

    console.log('查询结果:', user ? `找到用户ID: ${user.id}` : '未找到用户');

    if (user) {
      // 如果用户已存在，更新信息
      console.log('更新现有用户信息');
      await user.update(
        addEnterpriseId({
          avatar,
          nickname,
          phone: phone || user.phone,
          openId: openId
        })
      );

      // 重新查询用户以确认更新结果
      user = await User.findByPk(user.id);
      console.log('更新后的用户信息:', {
        id: user.id,
        username: user.username,
        openId: user.openId,
        hasOpenId: !!user.openId
      });
    } else {
      // 如果用户不存在，创建新用户
      // 生成随机用户名和密码
      const username = `wx_${openId}`;  // 添加时间戳确保唯一性
      const randomPassword = crypto.randomBytes(8).toString('hex');
      const hashedPassword = bcrypt.hashSync(randomPassword, 10);

      console.log('创建新用户:', { username, hashedPassword: '已加密', openId });

      user = await User.create(
        addEnterpriseId({
          username,
          password: hashedPassword,
          nickname,
          avatar,
          phone,
          openId: openId,  // 使用 openId 而不是 open_id
          status: true,
          platform: 'miniapp'
        })
      );

      console.log('创建的用户信息:', {
        id: user.id,
        username: user.username,
        openId: user.openId,
        hasOpenId: !!user.openId
      });
    }

    // 如果用户的openId仍然未设置，使用原始SQL更新
    if (!user.openId) {
      console.log('尝试使用原始SQL更新openId');
      await User.sequelize.query(
        `UPDATE users SET open_id = ? WHERE id = ?`,
        {
          replacements: [openId, user.id]
        }
      );

      // 重新查询用户
      user = await User.findByPk(user.id);
      console.log('SQL更新后的用户信息:', {
        id: user.id,
        openId: user.openId,
        hasOpenId: !!user.openId
      });
    }

    // 生成token
    const token = openId;

    // 返回认证成功结果
    return res.json(createResponse(200, '认证成功', {
      success: true,
      token,
      userId: user.id
    }));
  } catch (error) {
    console.error('微信认证失败:', error);
    return res.status(500).json(createResponse(500, '微信认证失败', {
      success: false,
      error: error.message
    }));
  }
};

/**
 * 判断用户是否已授权
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.checkAuthorization = async (req, res) => {
  try {
    const { openId } = req.query;

    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId参数不能为空'));
    }

    console.log('检查授权状态，openId:', openId);

    // 检查用户是否存在，添加企业ID过滤
    const user = await User.findOne(
      addEnterpriseFilter({
        where: {
          openId: openId
        },
        attributes: ['id', 'username', 'nickname', 'avatar', 'phone', 'status', 'openId']
      })
    );

    console.log('查询结果:', user ? `找到用户ID: ${user.id}` : '未找到用户');

    if (!user) {
      return res.json(createResponse(200, '用户未授权', {
        authorized: false
      }));
    }

    // 用户存在且状态正常
    if (user.status) {
      return res.json(createResponse(200, '用户已授权', {
        authorized: true,
        user
      }));
    } else {
      // 用户存在但被禁用
      return res.json(createResponse(200, '用户已被禁用', {
        authorized: false,
        disabled: true
      }));
    }
  } catch (error) {
    console.error('检查授权状态失败:', error);
    return res.status(500).json(createResponse(500, '检查授权状态失败', {
      error: error.message
    }));
  }
};

/**
 * 微信小程序用户身份认证
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.verifyIdentity = async (req, res) => {
  try {
    const { realName, realPhone, idNumber, openId } = req.body;

    // 验证必填参数
    if (!realName) {
      return res.status(400).json(createResponse(400, '真实姓名不能为空'));
    }

    if (!realPhone) {
      return res.status(400).json(createResponse(400, '手机号不能为空'));
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(realPhone)) {
      return res.status(400).json(createResponse(400, '请输入正确的手机号格式'));
    }

    if (!idNumber) {
      return res.status(400).json(createResponse(400, '身份证号不能为空'));
    }

    // 验证身份证号格式
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardRegex.test(idNumber)) {
      return res.status(400).json(createResponse(400, '请输入正确的身份证号格式'));
    }

    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId不能为空'));
    }

    console.log('身份认证请求参数:', { realName, realPhone, idNumber, openId });

    // 查询员工信息
    const employee = await Employee.findOne({
      where: {
        phone: realPhone,
        id_card: idNumber,
        name: realName,
        status: '1' // 确保员工在职
      }
    });

    if (!employee) {
      return res.json(createResponse(400, '校验失败，请仔细检查信息是否填写正确哦~'));
    }

    // 更新员工的open_id字段和isActivated状态
    await employee.update({
      openId: openId,
      isActivated: true
    });

    // 检查是否已存在该openId的用户
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { openId: openId }
      })
    );

    if (!user) {
      return res.status(404).json(createResponse(404, '用户不存在，请先完成基本认证'));
    }

    // 更新用户身份信息
    await user.update(
      addEnterpriseId({
        realName,
        realPhone,
        idNumber
      })
    );

    // 查询企业名称
    const companySetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: {
          code: 'company_name'
        }
      })
    );

    // 获取企业名称，如果不存在则设为空字符串
    const companyName = companySetting ? companySetting.value : '';

    // 返回认证成功结果，包含企业名称
    return res.json(createResponse(200, '身份认证成功', {
      success: true,
      userId: user.id,
      companyName: companyName
    }));
  } catch (error) {
    console.error('身份认证失败:', error);
    return res.status(500).json(createResponse(500, '身份认证失败', {
      success: false,
      error: error.message
    }));
  }
};

/**
 * 根据openId获取用户信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserByOpenId = async (req, res) => {
  try {
    const { openId } = req.query;

    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId参数不能为空'));
    }

    console.log('查询用户信息，openId:', openId);

    // 查询用户信息，添加企业ID过滤
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { openId: openId },
        attributes: [
          'id',
          'username',
          'nickname',
          'avatar',
          'email',
          'phone',
          'realName',
          'realPhone',
          'idNumber',
          'status',
          'openId',
          'createTime',
          'updateTime',
          'currentPosition',
          'currentLevel'
        ]
      })
    );

    if (!user) {
      return res.status(404).json(createResponse(404, '用户不存在'));
    }

    // 检查用户状态
    if (!user.status) {
      return res.status(403).json(createResponse(403, '用户已被禁用'));
    }

    const employee = await Employee.findOne({
      where: {
        openId: openId,
        status: '1', // 确保员工在职,
        isActivated:'1' //确保员工已经小程序认证
      },
      attributes: ['positionTypeId','levelId', 'positionId', 'enterpriseId','status', 'entryTime'],
      include: [
        {
          model: PositionType,
          as: 'positionType',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name'],
          required: false
        }
      ]
    });

    // 获取企业信息（使用Redis缓存）
    let enterpriseInfo = null;
    let adminAccessUrl = null;
    let wsUrl = null;
    if (employee && employee.enterpriseId) {
      enterpriseInfo = await getEnterpriseInfo(employee.enterpriseId);
      adminAccessUrl = enterpriseInfo ? enterpriseInfo.adminAccessUrl : null;
      wsUrl = enterpriseInfo ? enterpriseInfo.miniAppUrl : null;
    }

    // 查询用户获得的徽章数量
    const achievementCount = await UserAchievement.count(
      addEnterpriseFilter({
        where: {
          openId: openId,
          status: 'achieved'
        }
      })
    );

    // 确定当前等级ID
    const currentLevelId = employee ? employee.levelId : '';

    // 获取当前等级信息
    const currentLevel = await Level.findOne(
      addEnterpriseFilter({
        where: {
          id: currentLevelId
        }
      })
    );

    // 查找下一个等级（orderNum更大的值，因为orderNum越大等级越高）
    let nextLevel = null;
    if (currentLevel) {
      nextLevel = await Level.findOne(
        addEnterpriseFilter({
          where: {
            orderNum: { [Op.gt]: currentLevel.orderNum }
          },
          order: [['orderNum', 'ASC']]
        })
      );
    }

    // 更新用户对象，包含当前等级和下一个等级以及岗位类型信息
    const userResponse = {
      ...user.toJSON(),
      currentLevel: nextLevel ? nextLevel.id : currentLevelId,
      currentLevelName: nextLevel ? nextLevel.name : employee?.level.name,
      currentPosition: (employee ? employee.positionId : ''),
      previewLevel: currentLevelId,
      // 添加岗位类型信息
      positionTypeId: employee?.positionTypeId || null,
      positionTypeName: employee?.positionType?.name || null,
      // 添加等级名称
      levelId: employee?.levelId || null,
      levelName: employee?.level?.name || null,
      // 添加职位名称
      positionId: employee?.positionId || null,
      positionName: employee?.positionName?.name || null,
      status: employee?.status || '0',
      // 添加入职时间
      entryTime: employee?.entryTime || null,
      // 添加获得徽章数量
      achievementCount: achievementCount || 0
    };

    // 查询企业名称
    const companySetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: {
          code: 'company_name'
        }
      })
    );

    // 获取企业名称，如果不存在则设为空字符串
    const companyName = companySetting ? companySetting.value : '';

    // 将企业名称添加到响应中
    userResponse.companyName = companyName;

    // 查询最新一条员工企业申请记录状态
    const latestApplication = await EmployeeEnterpriseApplication.findOne({
      where: {
        openId: openId
      },
      order: [['createTime', 'DESC']],
      attributes: ['id', 'auditStatus', 'createTime', 'enterpriseId']
    });

    // 将申请状态添加到响应中
    userResponse.auditStatus = latestApplication ? latestApplication.auditStatus : null;

    // 添加企业管理后台访问地址
    userResponse.adminAccessUrl = adminAccessUrl;
    userResponse.wsUrl = wsUrl ;

    // 返回用户信息
    return res.json(createResponse(200, '获取用户信息成功', { user: userResponse }));
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return res.status(500).json(createResponse(500, '获取用户信息失败', {
      error: error.message
    }));
  }
};

/**
 * 检查用户是否已完成身份认证
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.checkIdentityVerification = async (req, res) => {
  try {
    const { openId } = req.query;

    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId参数不能为空'));
    }

    console.log('检查身份认证状态，openId:', openId);

    // 查询用户信息，添加企业ID过滤
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { openId: openId },
        attributes: ['id', 'realName', 'realPhone', 'idNumber', 'status']
      })
    );

    if (!user) {
      return res.json(createResponse(200, '用户不存在', {
        verified: false,
        reason: 'user_not_exist',
        isNewUser: true
      }));
    }

    // 检查用户状态
    if (!user.status) {
      return res.json(createResponse(200, '用户已被禁用', {
        verified: false,
        reason: 'user_disabled',
        isNewUser: true
      }));
    }

    // 查询员工信息，通过openId获取
    const employee = await Employee.findOne({
      where: { openId: openId },
      attributes: ['id', 'name', 'phone', 'id_card', 'isActivated', 'status']
    });

    // 使用employee表的isActivated字段判断是否已完成身份认证
    const isVerified = employee && employee.isActivated && employee.status === '1';

    // 判断是否新用户
    let isNewUser = true;

    if (isVerified) {
      // 如果已认证，查询是否有练习记录
      const practiceCount = await PracticeRecord.count({
        where: {
          openId: openId
        }
      });

      // 有练习记录则为老用户
      isNewUser = practiceCount === 0;
    }

    return res.json(createResponse(200, isVerified ? '用户已完成身份认证' : '用户未完成身份认证', {
      verified: isVerified,
      userId: user.id,
      isNewUser: isNewUser,
      // 如果已认证，则返回部分身份信息
      userInfo: isVerified ? {
        realName: employee.name,
        // 隐藏部分手机号
        realPhone: employee.phone ?
          employee.phone.substring(0, 3) + '****' + employee.phone.substring(7) : null,
        // 隐藏部分身份证号
        idNumber: employee.id_card ?
          employee.id_card.substring(0, 4) + '**********' + employee.id_card.substring(14) : null
      } : null
    }));
  } catch (error) {
    console.error('检查身份认证状态失败:', error);
    return res.status(500).json(createResponse(500, '检查身份认证状态失败', {
      verified: false,
      error: error.message,
      isNewUser: true
    }));
  }
};

/**
 * 获取微信小程序用户手机号
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
/**
 * 获取微信小程序access_token
 * @returns {Promise<string>} access_token
 */
const getAccessToken = async () => {
  try {
    const appId = process.env.WECHAT_APPID;
    const appSecret = process.env.WECHAT_SECRET;

    if (!appId || !appSecret) {
      throw new Error('微信小程序配置未设置，请检查环境变量');
    }

    const requestUrl = 'https://api.weixin.qq.com/cgi-bin/token';
    const params = {
      grant_type: 'client_credential',
      appid: appId,
      secret: appSecret
    };

    const response = await axios.get(requestUrl, { params });

    if (response.data.errcode) {
      throw new Error(`获取access_token失败: ${response.data.errmsg}(${response.data.errcode})`);
    }

    return response.data.access_token;
  } catch (error) {
    console.error('获取access_token失败:', error);
    throw error;
  }
};

// 修改 getPhoneNumber 方法，使用新的 getAccessToken 函数
exports.getPhoneNumber = async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json(createResponse(400, 'code参数不能为空'));
    }

    // 获取 access_token
    const accessToken = await getAccessToken();

    // 构造请求URL
    const requestUrl = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${accessToken}`;

    // 发送请求获取手机号
    const response = await axios.post(requestUrl, {
      code: code
    });

    if (response.data.errcode !== 0) {
      return res.status(400).json(createResponse(
        400,
        `微信接口返回错误: ${response.data.errmsg}(${response.data.errcode})`
      ));
    }

    // 返回手机号信息
    return res.json(createResponse(200, '获取手机号成功', response.data.phone_info));

  } catch (error) {
    console.error('获取手机号失败:', error);
    return res.status(500).json(createResponse(500, '获取手机号失败', {
      error: error.message
    }));
  }
};


/**
 * 上传图片
 * 用于上传Logo、前厅头像、后厨头像等图片
 */
exports.uploadConfigImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传的文件'
      });
    }

    const { originalname, filename, path: filePath, size } = req.file;

    // 确保文件名是正确解码的
    let decodedFileName = originalname;
    if (typeof decodedFileName === 'string') {
      try {
        // 尝试检测是否需要转换编码
        const testDecode = decodeURIComponent(escape(decodedFileName));
        // 如果解码成功但与原字符串不同，说明需要转换
        if (testDecode !== decodedFileName && /[\u4e00-\u9fa5]/.test(testDecode)) {
          decodedFileName = testDecode;
        }
      } catch (e) {
        // 如果解码失败，尝试直接从latin1转换
        decodedFileName = Buffer.from(decodedFileName, 'latin1').toString('utf8');
      }
    }

    // 获取文件类型
    const fileType = getFileType(decodedFileName);


    // 构建文件访问路径，添加时间戳
    const fileUrl = `/uploads/user/${filename}`;

    res.json({
      code: 200,
      message: '图片上传成功',
      data: {
        fileName: decodedFileName,
        fileType,
        fileSize: size,
        fileUrl
      }
    });
  } catch (error) {
    console.error('图片上传失败:', error);
    res.status(500).json({
      code: 500,
      message: '图片上传失败',
      error: error.message
    });
  }
};

/**
 * 根据openId获取员工所属企业的信息配置
 * 先通过openId查找员工所属的企业ID，再根据企业ID查询对应的信息配置
 */
exports.getInfoConfigByOpenId = async (req, res) => {
  try {
    // 从请求头中获取openId，而不是从query参数
    const openId = req.headers.openid;

    // 验证参数
    if (!openId) {
      return res.status(400).json({
        code: 400,
        message: 'openId请求头不能为空'
      });
    }

    // 查找员工信息，获取企业ID
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { openId }
      })
    );

    // 如果没有找到员工信息
    if (!employee) {
      return res.status(404).json({
        code: 200,
        message: '未找到对应员工信息'
      });
    }

    const enterpriseId = employee.enterpriseId;

    // 使用企业ID查询信息配置
    const infoConfig = await InfoConfig.findOne(
      addEnterpriseFilter({
        where: { enterpriseId }
      })
    );

    // 如果没有找到企业特定配置，则返回默认配置
    if (!infoConfig) {
      const defaultConfig = await InfoConfig.findOne(
        addEnterpriseFilter({
          where: { isDefault: 1 }
        })
      );

      // 如果默认配置也不存在，返回404
      if (!defaultConfig) {
        return res.status(200).json({
          code: 200,
          message: '未找到信息配置'
        });
      }

      return res.status(200).json({
        code: 200,
        message: '获取信息配置成功(默认配置)',
        data: defaultConfig
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取信息配置成功',
      data: infoConfig
    });

  } catch (error) {
    console.error('根据openId获取信息配置出错:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 根据openId获取用户岗位对应的餐厅配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRestaurantConfigByOpenId = async (req, res) => {
  try {
    const  openId  = req.headers.openid;

    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId参数不能为空'));
    }

    console.log('查询餐厅配置，openId:', openId);

    // 查询员工信息，获取岗位类型ID
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: {
          openId: openId,
          status: '1' // 确保员工在职
        },
        attributes: ['positionTypeId', 'enterpriseId'],
        include: [
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name'],
            required: false
          }
        ]
      })
    );

    if (!employee) {
      return res.status(404).json(createResponse(404, '未找到员工信息'));
    }

    const positionTypeId = employee.positionTypeId;

    if (!positionTypeId) {
      return res.status(404).json(createResponse(404, '员工未设置岗位类型'));
    }

    // 根据岗位类型ID查询餐厅配置
    const restaurantConfig = await RestaurantConfig.findOne(
      addEnterpriseFilter({
        where: {
          enterpriseId: employee.enterpriseId
        },
        limit: 1
      })
    );

    if (!restaurantConfig) {
      return res.status(404).json(createResponse(404, '未找到对应的餐厅配置'));
    }




    // 构造返回结果
    const result = {
      ...restaurantConfig.toJSON(),
      positionTypeName: employee.positionType ? employee.positionType.name : null
    };

    // 返回餐厅配置信息
    return res.json(createResponse(200, '获取餐厅配置成功', { config: result }));

  } catch (error) {
    console.error('获取餐厅配置失败:', error);
    return res.status(500).json(createResponse(500, '获取餐厅配置失败', {
      error: error.message
    }));
  }
};

/**
 * 根据岗位类型调用餐烤师接口
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.callCankaoshi = async (req, res) => {
  try {
    const  openId = req.headers.openid;
    let id = req.body.id;
    const question = req.body.question;
    console.log('调用餐烤师接口，question:', question);
    const DIFY_URL = process.env.DIFY_URL;
    const DEFAULT_ENTERPRISE_ID = process.env.DEFAULT_ENTERPRISE_ID;
    // 验证必填参数
    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId参数不能为空'));
    }

    if (!question) {
      return res.status(400).json(createResponse(400, '问题内容不能为空'));
    }

    // 查找员工信息，获取企业ID、岗位归属ID、岗位ID和等级ID
    const employee = await Employee.findOne({
      where: {
        openId: openId,
        status: '1', // 确保员工在职
        enterpriseId: DEFAULT_ENTERPRISE_ID
      },
      attributes: ['id', 'enterpriseId', 'positionTypeId', 'positionId', 'levelId']
    });

    if (!employee) {
      return res.status(404).json(createResponse(404, '未找到对应员工信息'));
    }

    const positionBelongId = employee.positionTypeId;
    const positionName = employee.positionId; // 直接使用岗位ID
    const positionLevel = employee.levelId;   // 直接使用等级ID

    // 根据positionTypeId查询PositionType获取code
    const positionType = await PositionType.findOne({
      where: {
        id: employee.positionTypeId,
        enterpriseId: DEFAULT_ENTERPRISE_ID
      }
    });

    if (!positionType || !positionType.code) {
      return res.status(404).json(createResponse(404, '未找到岗位类型信息或code未设置'));
    }

    const positionTypeCode = positionType.code;

    // 构建WebSocket连接URL
    const wsUrl = `${DIFY_URL.replace('http', 'ws')}/api/agent/workflow/run/${DEFAULT_ENTERPRISE_ID}/${positionTypeCode}`;

    // 构建请求数据
    const requestData = {
      inputs: {
        "question": question,
      }
    };
    console.log('餐烤师WebSocket请求数据:', requestData);

    // 使用WebSocket发送请求到AI接口
    const response = await new Promise((resolve, reject) => {
      const ws = new WebSocket(wsUrl, {
        headers: {
          'Authorization': `Bearer ${process.env.DIFY_API_KEY}`,
          'Content-Type': 'application/json',
          'Enterprise-Id': DEFAULT_ENTERPRISE_ID
        }
      });

      let responseData = '';
      let hasError = false;

      ws.on('open', () => {
        console.log('WebSocket连接已建立');
        ws.send(JSON.stringify(requestData));
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          if (message.event === 'workflow_finished') {
            responseData = message.data.outputs.text;
            ws.close();
            resolve({ data: { data: { outputs: { text: responseData } } } });
          } else if (message.event === 'error') {
            hasError = true;
            ws.close();
            reject(new Error(message.data.message || '接口返回错误'));
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
          responseData += data.toString();
        }
      });

      ws.on('error', (error) => {
        console.error('WebSocket连接错误:', error);
        if (!hasError) {
          reject(error);
        }
      });

      ws.on('close', () => {
        console.log('WebSocket连接已关闭');
        if (!hasError && !responseData) {
          reject(new Error('WebSocket连接意外关闭'));
        }
      });

      // 设置超时
      setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close();
          reject(new Error('WebSocket请求超时'));
        }
      }, 30000); // 30秒超时
    });

    // 返回AI接口的响应结果

    let record;

    if (!id) {
      // 如果id为空，创建新记录
      record = await RestaurantRecord.create({
        enterpriseId: DEFAULT_ENTERPRISE_ID,
        positionBelongId,
        positionName,
        positionLevel,
        openId,
        count: 1,
        createdBy: employee.id
      });

      id = record.id;
      console.log('新记录创建成功:', record.toJSON());


    } else {
      // 如果id不为空，更新现有记录
      record = await RestaurantRecord.findByPk(id);

      if (!record) {
        return res.status(404).json(createResponse(404, '未找到对应聊天记录'));
      }
       // 更新记录数量
       if (record.count !== undefined) {
        await record.update({
          count: (record.count || 0) + 1
        });
      }
    }

    // 添加用户消息记录
    const detail = await RestaurantRecordDetail.create({
      restaurantRecordId: id,
      enterpriseId: DEFAULT_ENTERPRISE_ID,
      positionBelongId,
      positionName,
      positionLevel,
      openId,
      role: 'user',
      content: question,
      createdBy: employee.id
    });
    // 添加系统回复消息记录
    const detail1 = await RestaurantRecordDetail.create({
      restaurantRecordId: id,
      enterpriseId: DEFAULT_ENTERPRISE_ID,
      positionBelongId,
      positionName,
      positionLevel,
      openId,
      role: 'system',
      content: response.data.data.outputs.text,
      createdBy: employee.id
    });
    return res.json(createResponse(200, '操作成功', {...response.data,id: record.id }));
  } catch (error) {
    // console.error('调用餐烤师失败:', error);
    return res.status(500).json(createResponse(500, '调用餐烤师失败', {
      error: error.message
    }));
  }
};

/**
 * WebSocket版本的餐烤师对话接口
 * @param {WebSocket} ws - WebSocket连接对象
 * @param {Object} payload - 请求数据
 */
exports.callCankaoshiWebSocket = async (ws, payload) => {
  try {
    const { openId, id, question } = payload;
    const DEFAULT_ENTERPRISE_ID = process.env.DEFAULT_ENTERPRISE_ID;

    console.log('参考师调用:',question);

    // 验证必填参数
    if (!openId) {
      ws.send(JSON.stringify({
        event: 'error',
        data: { success: false, error: 'openId参数不能为空' }
      }));
      return;
    }

    if (!question) {
      ws.send(JSON.stringify({
        event: 'error',
        data: { success: false, error: '问题内容不能为空' }
      }));
      return;
    }

    // 查找员工信息，获取企业ID、岗位归属ID、岗位ID和等级ID
    const employee = await Employee.findOne({
      where: {
        openId: openId,
        status: '1', // 确保员工在职
        enterpriseId: DEFAULT_ENTERPRISE_ID
      },
      attributes: ['id', 'enterpriseId', 'positionTypeId', 'positionId', 'levelId']
    });

    if (!employee) {
      ws.send(JSON.stringify({
        event: 'error',
        data: { success: false, error: '未找到对应员工信息' }
      }));
      return;
    }

    const positionBelongId = employee.positionTypeId;
    const positionName = employee.positionId; // 直接使用岗位ID
    const positionLevel = employee.levelId;   // 直接使用等级ID

    // 查询岗位名称
    const positionNameRecord = await PositionName.findOne({
      where: {
        id: employee.positionId,
        enterpriseId: DEFAULT_ENTERPRISE_ID
      },
      attributes: ['name']
    });

    // 查询等级名称
    const levelRecord = await Level.findOne({
      where: {
        id: employee.levelId,
        enterpriseId: DEFAULT_ENTERPRISE_ID
      },
      attributes: ['name']
    });

    if (!positionNameRecord) {
      ws.send(JSON.stringify({
        event: 'error',
        data: { success: false, error: '未找到岗位名称信息' }
      }));
      return;
    }

    if (!levelRecord) {
      ws.send(JSON.stringify({
        event: 'error',
        data: { success: false, error: '未找到等级信息' }
      }));
      return;
    }

    // 组合岗位信息，格式如：服务员-P1
    const position = `${positionNameRecord.name}-${levelRecord.name}`;

    // 直接使用写死的code值
    const positionTypeCode = 'T1747218642845';
    const DIFY_URL = process.env.DIFY_URL;

    // 构建HTTP API URL（不是WebSocket）
    const apiUrl = `${DIFY_URL}/api/agent/workflow/run/${DEFAULT_ENTERPRISE_ID}/${positionTypeCode}`;

    // 构建请求体
    const requestBody = {
      inputs: {
        question: question,
        position: position
      },
      stream: true
    };

    console.log('餐烤师请求URL:', apiUrl);
    console.log('餐烤师请求体:', requestBody);

    // 创建或更新聊天记录
    let record;
    let recordId = id;

    if (!recordId) {
      // 如果id为空，创建新记录
      record = await RestaurantRecord.create({
        enterpriseId: DEFAULT_ENTERPRISE_ID,
        positionBelongId,
        positionName,
        positionLevel,
        openId,
        count: 1,
        createdBy: employee.id
      });

      recordId = record.id;
      console.log('新记录创建成功:', record.toJSON());
    } else {
      // 如果id不为空，更新现有记录
      record = await RestaurantRecord.findByPk(recordId);

      if (!record) {
        ws.send(JSON.stringify({
          event: 'error',
          data: { success: false, error: '未找到对应聊天记录' }
        }));
        return;
      }

      // 更新记录数量
      if (record.count !== undefined) {
        await record.update({
          count: (record.count || 0) + 1
        });
      }
    }

    // 添加用户消息记录
    await RestaurantRecordDetail.create({
      restaurantRecordId: recordId,
      enterpriseId: DEFAULT_ENTERPRISE_ID,
      positionBelongId,
      positionName,
      positionLevel,
      openId,
      role: 'user',
      content: question,
      createdBy: employee.id
    });

    // 发送开始处理消息
    ws.send(JSON.stringify({
      event: 'processing',
      data: {
        success: true,
        message: '正在处理您的问题...',
        id: recordId
      }
    }));

    let completeContent = '';

    try {
      // 调用Dify API - 流式请求
      const response = await axios.post(apiUrl, requestBody, {
        headers: {
          'Accept': 'text/event-stream',
          'Content-Type': 'application/json'
        },
        responseType: 'stream',
        timeout: 60000 // 设置60秒超时时间
      });

      // 用于累积不完整的数据行
      let buffer = '';

      // 处理流式响应
      response.data.on('data', (chunk) => {
        // 将新chunk添加到缓冲区
        buffer += chunk.toString();

        // 按行分割，但保留最后一个可能不完整的行
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保存最后一行（可能不完整）到缓冲区

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              let jsonStr = line.slice(6);
              let streamData;

              // 处理JSON断裂的情况
              streamData = safeJsonParse(jsonStr);
              if (!streamData) {
                  console.error('JSON解析失败:', jsonStr);
                  continue; // 跳过这行，继续处理下一行
              }

              // 过滤掉不需要发送的事件
              if (streamData.event === 'node_finished' ||
                  streamData.event === 'node_started') {
                continue; // 跳过这些事件，不发送给前端
              }

              // 检查是否为workflow_finished事件
              if (streamData.event === 'workflow_finished') {
                // 处理最终结果，但不发送流式内容
                if (streamData.data && streamData.data.outputs && streamData.data.outputs.text) {
                  completeContent = streamData.data.outputs.text;
                }
                continue; // 跳过workflow_finished事件的流式内容发送
              }

              // 只提取并发送纯文本内容
              let contentToSend = '';

              // 根据不同的事件类型提取content
              if (streamData.event === 'text_chunk' && streamData.data && streamData.data.text) {
                // contentToSend = cleanTextContent(streamData.data.text);
                contentToSend = streamData.data.text
              } else if (streamData.event === 'agent_message' && streamData.data && streamData.data.answer) {
                // contentToSend = cleanTextContent(streamData.data.answer);
                contentToSend = streamData.data.text
              } else if (streamData.data && streamData.data.outputs && streamData.data.outputs.text) {
                let rawText = streamData.data.outputs.text;

                // 清理文本内容
                // contentToSend = cleanTextContent(rawText);
                contentToSend = rawText
              }

              // 如果有content内容且不为空，发送纯文本给前端
              if (contentToSend && contentToSend.trim() && ws.readyState === ws.OPEN) {
                // 额外检查：只过滤纯字段名和布尔值，保留正常内容
                const finalContent = contentToSend.trim();

                // 过滤纯字段名和布尔值，其他内容都发送
                  ws.send(JSON.stringify({
                    event: 'stream_content',
                    data: finalContent
                  }));
              }

            } catch (error) {
              console.error('解析流式数据失败:', {
                error: error.message,
                line: line,
                jsonStr: line.slice(6).trim(),
                timestamp: new Date().toISOString()
              });
              // 继续处理其他数据，不中断整个流
            }
          }
        }
      });

      response.data.on('end', async () => {
        try {
          // 处理缓冲区中的最后一行数据
          if (buffer.trim() && buffer.startsWith('data: ')) {
            try {
              const jsonStr = buffer.slice(6).trim();
              if (jsonStr && jsonStr !== '[DONE]') {
                const streamData = JSON.parse(jsonStr);
                if (streamData.event === 'workflow_finished' &&
                    streamData.data && streamData.data.outputs && streamData.data.outputs.text) {
                  completeContent = streamData.data.outputs.text;
                }
              }
            } catch (error) {
              console.error('处理缓冲区最后数据失败:', error);
            }
          }

          // 清空缓冲区
          buffer = '';

          // 保存系统回复记录
          await RestaurantRecordDetail.create({
            restaurantRecordId: recordId,
            enterpriseId: DEFAULT_ENTERPRISE_ID,
            positionBelongId,
            positionName,
            positionLevel,
            openId,
            role: 'system',
            content: completeContent,
            createdBy: employee.id
          });

          // 发送最终完成事件
          if (ws.readyState === ws.OPEN) {
            ws.send(JSON.stringify({
              event: 'completed',
              data: {
                success: true,
                message: '处理完成',
                id: recordId,
                response: completeContent
              }
            }));
          }
        } catch (error) {
          console.error('保存系统回复记录失败:', error);
          if (ws.readyState === ws.OPEN) {
            ws.send(JSON.stringify({
              event: 'error',
              data: {
                success: false,
                error: '保存回复记录失败'
              }
            }));
          }
        }
      });

      response.data.on('error', (error) => {
        console.error('流式响应错误:', error);
        if (ws.readyState === ws.OPEN) {
          ws.send(JSON.stringify({
            event: 'error',
            data: {
              success: false,
              error: '流式响应错误'
            }
          }));
        }
      });

    } catch (error) {
      console.error('调用Dify API失败:', error.response?.data || error.message);
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({
          event: 'error',
          data: {
            success: false,
            error: '调用Dify API失败'
          }
        }));
      }
    }

  } catch (error) {
    console.error('WebSocket调用餐烤师失败:', error);
    ws.send(JSON.stringify({
      event: 'error',
      data: { success: false, error: error.message }
    }));
  }
};

/**
 * 获取当前时间段有效的公告列表（小程序专用）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCurrentAnnouncements = async (req, res) => {
  try {
    const {
      pageNum = 1,
      pageSize = 10
    } = req.query;

    // 从请求头获取openId
    const openId = req.headers.openid;

    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId请求头不能为空'));
    }

    console.log('小程序查询公告，openId:', openId);

    // 验证用户身份并获取企业ID
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { openId: openId },
        attributes: ['id', 'status', 'enterpriseId']
      })
    );

    if (!user) {
      return res.status(404).json(createResponse(404, '用户不存在，请先完成认证'));
    }

    if (!user.status) {
      return res.status(403).json(createResponse(403, '用户已被禁用'));
    }

    // 获取用户企业ID（用于精确的企业过滤）
    const userEnterpriseId = user.enterpriseId || process.env.DEFAULT_ENTERPRISE_ID;

    console.log('查询企业公告，企业ID:', userEnterpriseId);

    // 构建查询条件
    const where = {
      status: 1, // 只查询启用状态的公告
      enterpriseId: userEnterpriseId // 明确指定企业ID
    };

    // 验证分页参数
    const pageNumber = Math.max(1, parseInt(pageNum));
    const size = Math.min(Math.max(1, parseInt(pageSize)), 20); // 小程序限制更小，减少流量

    // 查询公告（不使用addEnterpriseFilter，因为已经明确指定了enterpriseId）
    const { count, rows } = await Announcement.findAndCountAll({
      where,
      order: [
        ['sort', 'ASC'],        // 按排序字段升序
        ['createTime', 'DESC']   // 按创建时间降序
      ],
      offset: (pageNumber - 1) * size,
      limit: size,
      attributes: [
        'id',
        'title',
        'content',
        'sort',
        'createTime'
        // 移除updateTime等后台管理字段，减少数据传输
      ]
    });

    // 为小程序优化数据处理
    const processedRows = rows.map(announcement => {
      const item = announcement.toJSON();

      return {
        id: item.id,
        title: item.title,
        content: item.content,
        sort: item.sort,
        createTime: new Date(item.createTime).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }),
        // 判断是否为置顶公告（sort值小的为置顶）
        isTop: item.sort <= 10
      };
    });

    return res.json(createResponse(200, '获取公告成功', {
      total: count,
      list: processedRows,
      pageNum: pageNumber,
      pageSize: size,
      totalPages: Math.ceil(count / size),
      hasNext: pageNumber < Math.ceil(count / size),
      hasPrev: pageNumber > 1
      // 移除currentTime等调试信息，减少响应体积
    }));

  } catch (error) {
    console.error('获取公告失败:', error);
    return res.status(500).json(createResponse(500, '获取公告失败', {
      error: process.env.NODE_ENV === 'development' ? error.message : '服务异常，请稍后重试'
    }));
  }
};

/**
 * 根据openId判断是否绑定企业
 * 条件：org_employee表中有该用户且未离职、已认证状态
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.checkEnterpriseBinding = async (req, res) => {
  try {
    const { openId } = req.query;

    // 验证必填参数
    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId参数不能为空'));
    }

    console.log('检查企业绑定状态，openId:', openId);

    // 查询员工信息，添加企业ID过滤
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: {
          openId: openId,
          status: '1',        // 未离职状态
          isActivated: true   // 已认证状态
        },
        attributes: ['id', 'name', 'phone', 'status', 'isActivated', 'enterpriseId']
      })
    );

    // 判断是否绑定企业
    const isBound = !!employee;

    console.log('企业绑定检查结果:', isBound ? '已绑定' : '未绑定');

     // 判断是否新用户
     let isNewUser = true;

    if (isBound) {
      // 如果已认证，查询是否有练习记录
      const practiceCount = await PracticeRecord.count({
        where: {
          openId: openId
        }
      });
      // 有练习记录则为老用户
      isNewUser = practiceCount === 0;
      // 已绑定企业，返回基本员工信息
      return res.json(createResponse(200, '已绑定企业', {
        isBound: true,
        isNewUser: isNewUser,
        employee: {
          id: employee.id,
          name: employee.name,
          phone: employee.phone,
          enterpriseId: employee.enterpriseId
        }
      }));
    } else {
      // 未绑定企业
      return res.json(createResponse(200, '未绑定企业', {
        isBound: false,
        isNewUser:true,
        reason: '用户未在企业员工表中找到，或状态不符合要求（未离职且已认证）'
      }));
    }

  } catch (error) {
    console.error('检查企业绑定状态失败:', error);
    return res.status(500).json(createResponse(500, '检查企业绑定状态失败', {
      isBound: false,
      isNewUser:true,
      error: error.message
    }));
  }
};

/**
 * 员工企业申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.applyToEnterprise = async (req, res) => {
  try {
    const {
      enterpriseInviteCode,
      realName,
      idCard,
      phone,
      departmentId,
      positionId,
      levelId,
      positionTypeId,
      gender,
      entryTime,
      enterpriseId
    } = req.body;

    // 从请求头获取openId
    const openId = req.headers.openid;

    // 验证必填参数
    if (!openId) {
      return res.status(400).json(createResponse(400, 'openId请求头不能为空'));
    }

    if (!enterpriseInviteCode) {
      return res.status(400).json(createResponse(400, '企业邀请码不能为空'));
    }

    if (!realName) {
      return res.status(400).json(createResponse(400, '真实姓名不能为空'));
    }

    if (!idCard) {
      return res.status(400).json(createResponse(400, '身份证号不能为空'));
    }

    if (!phone) {
      return res.status(400).json(createResponse(400, '手机号不能为空'));
    }

    if (!enterpriseId) {
      return res.status(400).json(createResponse(400, '企业ID不能为空'));
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json(createResponse(400, '请输入正确的手机号格式'));
    }

    // 验证身份证号格式
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardRegex.test(idCard)) {
      return res.status(400).json(createResponse(400, '请输入正确的身份证号格式'));
    }

    console.log('员工企业申请请求参数:', {
      openId,
      enterpriseInviteCode,
      realName,
      idCard,
      phone,
      departmentId,
      positionId,
      levelId,
      positionTypeId,
      gender,
      entryTime,
      enterpriseId
    });

    // 检查员工表中是否已存在该openId的记录
    const existingEmployee = await Employee.findOne({
      where: {
        openId,
        enterpriseId
      }
    });

    // 如果员工已存在且为在职状态，不允许重复申请
    if (existingEmployee && existingEmployee.status === '1') {
      return res.status(400).json(createResponse(400, '您当前为在职状态，无需重复申请'));
    }

    // 如果员工存在但为离职状态，允许重新申请，但需要检查是否有审核中的申请
    if (existingEmployee && existingEmployee.status === '0') {
      console.log('检测到离职员工重新申请:', { openId, enterpriseId, employeeStatus: existingEmployee.status });

      // 检查是否有审核中的申请
      const pendingApplication = await EmployeeEnterpriseApplication.findOne({
        where: {
          openId,
          enterpriseId,
          auditStatus: '审核中'
        }
      });

      if (pendingApplication) {
        return res.status(400).json(createResponse(400, '您已提交过重新加入申请，请等待审核结果'));
      }
    } else {
      // 对于新员工，检查是否已存在相同的申请（同一个openId和企业ID）
      const existingApplication = await EmployeeEnterpriseApplication.findOne({
        where: {
          openId,
          enterpriseId,
          auditStatus: {
            [Op.in]: ['审核中', '通过']
          }
        }
      });

      if (existingApplication) {
        if (existingApplication.auditStatus === '通过') {
          return res.status(400).json(createResponse(400, '您已成功加入该企业，无需重复申请'));
        } else {
          return res.status(400).json(createResponse(400, '您已提交过申请，请等待审核结果'));
        }
      }
    }

    // 检查手机号和身份证是否已被其他用户申请过该企业
    // 如果是离职员工重新申请，则排除当前openId的检查
    const duplicateCheckCondition = {
      [Op.or]: [
        { phone, enterpriseId },
        { idCard, enterpriseId }
      ],
      auditStatus: {
        [Op.in]: ['审核中', '通过']
      }
    };

    // 如果是离职员工重新申请，排除自己的记录
    if (existingEmployee && existingEmployee.status === '0') {
      duplicateCheckCondition.openId = { [Op.ne]: openId };
    }

    const duplicateCheck = await EmployeeEnterpriseApplication.findOne({
      where: duplicateCheckCondition
    });

    if (duplicateCheck) {
      return res.status(400).json(createResponse(400, '该手机号或身份证已有其他用户申请过该企业'));
    }

    // 创建申请记录
    const applicationData = addEnterpriseId({
      openId,
      enterpriseInviteCode,
      realName,
      idCard,
      phone,
      departmentId: departmentId || null,
      positionId: positionId || null,
      levelId: levelId || null,
      positionTypeId: positionTypeId || null,
      gender: gender || null,
      entryTime: entryTime || null,
      enterpriseId,
      auditStatus: '审核中'
    });

    // 如果是离职员工重新申请，添加备注
    if (existingEmployee && existingEmployee.status === '0') {
      applicationData.remark = '离职员工重新申请';
      console.log('离职员工重新申请，员工ID:', existingEmployee.id);
    }

    const application = await EmployeeEnterpriseApplication.create(applicationData);

    console.log('员工企业申请创建成功:', application.id, '申请类型:', existingEmployee && existingEmployee.status === '0' ? '离职员工重新申请' : '新员工申请');

    return res.json(createResponse(200, '申请提交成功，请等待管理员审核', {
      success: true,
      applicationId: application.id,
      auditStatus: application.auditStatus
    }));

  } catch (error) {
    console.error('员工企业申请失败:', error);
    return res.status(500).json(createResponse(500, '申请提交失败', {
      success: false,
      error: error.message
    }));
  }
};

/**
 * 根据企业邀请码获取企业信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getEnterpriseByInviteCode = async (req, res) => {
  try {
    const { inviteCode } = req.params;

    // 验证必填参数
    if (!inviteCode) {
      return res.status(400).json(createResponse(400, '企业邀请码不能为空'));
    }

    // 获取配置
    const DIFY_URL = process.env.DIFY_URL;

    if (!DIFY_URL) {
      return res.status(500).json(createResponse(500, 'DIFY_URL配置未设置，请检查环境变量'));
    }

    console.log('查询企业信息，邀请码:', inviteCode);

    // 构造请求URL
    const requestUrl = `${DIFY_URL}/api/enterprise/invite/${inviteCode}`;

    // 发送GET请求获取企业信息
    const response = await axios.get(requestUrl);

    // 检查响应状态
    if (!response.data.success || response.data.code !== 200) {
      return res.status(400).json(createResponse(
        400,
        response.data.message || '获取企业信息失败'
      ));
    }

    const enterpriseData = response.data.data;

    // 提取需要的字段
    const result = {
      name: enterpriseData.name,
      inviteCode: enterpriseData.inviteCode,
      uuid: enterpriseData.uuid,
      adminAccessUrl: enterpriseData.adminAccessUrl,
      miniAppUrl: enterpriseData.miniAppUrl
    };

    // 新增：保存企业名称和sign到system_settings
    const enterpriseId = enterpriseData.uuid;
    if (enterpriseData.name) {
      await upsertSystemSetting('company_name', enterpriseData.name, enterpriseId);
    }
    if (enterpriseData.sign) {
      await upsertSystemSetting('company_sign', enterpriseData.sign, enterpriseId);
    }

    console.log('企业信息查询成功:', result);

    // 返回成功结果
    return res.json(createResponse(200, '获取企业信息成功', result));

  } catch (error) {
    console.error('获取企业信息失败:', error);

    // 处理网络请求错误
    if (error.response) {
      const statusCode = error.response.status;
      const message = error.response.data?.message || '企业信息查询失败';

      if (statusCode === 404) {
        return res.status(404).json(createResponse(404, '未找到对应的企业信息，请检查邀请码是否正确'));
      }

      return res.status(statusCode).json(createResponse(statusCode, message));
    }

    return res.status(500).json(createResponse(500, '获取企业信息失败', {
      error: error.message
    }));
  }
};


/**
 * 生成餐烤证书主方法
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.generateCertificate = async (req, res) => {
  try {
    const { certificateId } = req.body;

    // 验证必填参数
    if (!certificateId) {
      return res.status(400).json(createResponse(400, '证书ID不能为空'));
    }

    // 查询证书记录，包含关联的岗位名称和等级信息
    const CertificateRecord = require('../../models/CertificateRecord');
    const certificateRecord = await CertificateRecord.findOne({
      where: {
        id: certificateId,
        delFlag: 0
      },
      include: [
        {
          model: PositionName,
          as: 'positionNameInfo',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: Level,
          as: 'positionLevelInfo',
          attributes: ['id', 'name'],
          required: false
        }
      ]
    });

    if (!certificateRecord) {
      return res.status(404).json(createResponse(404, '未找到对应的证书记录'));
    }

    // 检查是否已存在证书文件URL
    if (certificateRecord.certificateFileUrl) {
      console.log('证书文件已存在，直接返回:', certificateRecord.certificateFileUrl);
      return res.json(createResponse(200, '证书已存在', {
        certificateUrl: certificateRecord.certificateFileUrl,
        certificateId: certificateId,
        certificateNo: certificateRecord.certificateNo,
        isExisting: true
      }));
    }

    // 如果不存在证书文件，则创建证书
    const certificateResult = await exports.createCertificateFile(certificateRecord);

    // 返回成功响应
    return res.json(createResponse(200, '证书生成成功', {
      certificateUrl: certificateResult.certificateUrl,
      fileName: certificateResult.fileName,
      certificateId: certificateId,
      certificateNo: certificateRecord.certificateNo,
      certificateInfo: certificateResult.certificateInfo,
      isExisting: false
    }));

  } catch (error) {
    console.error('生成证书失败:', error);
    return res.status(500).json(createResponse(500, '生成证书失败', {
      error: error.message
    }));
  }
};

/**
 * 创建证书文件的具体实现方法
 * @param {Object} certificateRecord - 证书记录对象
 * @returns {Object} - 包含证书URL和相关信息的对象
 */
exports.createCertificateFile = async (certificateRecord) => {
  try {
    const { createCanvas, loadImage, registerFont } = require('canvas');
    const fs = require('fs');
    const path = require('path');

    // 处理员工姓名，在每个字符间加空格
    const formatName = (name) => {
      if (!name) return '';
      return name.split('').join(' ');
    };

    // 格式化日期为 YYYY-MM-DD 格式
    const formatDate = (date) => {
      if (!date) return '';
      const d = new Date(date);
      return d.toISOString().split('T')[0];
    };

    // 格式化obtainTime为文件名用的日期格式（YYYY-MM-DD）
    const formatDateForFileName = (date) => {
      if (!date) return new Date().toISOString().split('T')[0];
      const d = new Date(date);
      return d.toISOString().split('T')[0];
    };

    // 提取数据
    const name = formatName(certificateRecord.employeeName);           // 员工姓名（格式化）
    const courseName = certificateRecord.certificateName;             // 课程名称
    const certificateNo = certificateRecord.certificateNo;            // 证书编号
    const issueDate = formatDate(certificateRecord.obtainTime);       // 颁发日期
    const validDate = formatDate(certificateRecord.validUntil);       // 有效期至
    const obtainDateString = formatDateForFileName(certificateRecord.obtainTime); // 获取日期字符串

    // 组合职位信息：岗位名称（等级名称）
    const positionNameStr = certificateRecord.positionNameInfo ? certificateRecord.positionNameInfo.name : '';
    const levelNameStr = certificateRecord.positionLevelInfo ? certificateRecord.positionLevelInfo.name : '';
    const position = `${positionNameStr}（${levelNameStr}级）`;

    // 验证必要数据
    if (!name) {
      throw new Error('员工姓名不能为空');
    }
    if (!courseName) {
      throw new Error('课程名称不能为空');
    }
    if (!position || !positionNameStr || !levelNameStr) {
      throw new Error('岗位信息不完整');
    }
    if (!certificateNo) {
      throw new Error('证书编号不能为空');
    }
    if (!issueDate) {
      throw new Error('颁发日期不能为空');
    }
    if (!validDate) {
      throw new Error('有效期至不能为空');
    }

    // 注册中文字体 - 解决Linux系统中文乱码问题
    const fontPaths = [
      // 用户指定的字体路径（优先使用）
      path.join(__dirname, '../../../uploads/fonts/wqy-microhei.ttc'),
      // Ubuntu/Debian 系统字体路径
      '/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc',
      '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
      '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',

      // 文泉驿字体路径
      '/usr/share/fonts/wenquanyi/wqy-microhei/wqy-microhei.ttc',
      '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
      '/usr/share/fonts/wqy-microhei/wqy-microhei.ttc',

      // CentOS/RHEL 系统字体路径
      '/usr/share/fonts/google-noto-cjk/NotoSansCJK-Regular.ttc',
      '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',

      // macOS 系统字体路径
      '/System/Library/Fonts/PingFang.ttc',
      '/System/Library/Fonts/Helvetica.ttc',

      // Windows 系统字体路径（如果在 WSL 中运行）
      '/mnt/c/Windows/Fonts/msyh.ttc',
      '/mnt/c/Windows/Fonts/simhei.ttf'
    ];

    let fontRegistered = false;
    let registeredFontPath = '';

    for (const fontPath of fontPaths) {
      try {
        if (fs.existsSync(fontPath)) {
          registerFont(fontPath, { family: 'NotoSansCJK' });
          console.log('中文字体注册成功:', fontPath);
          fontRegistered = true;
          registeredFontPath = fontPath;
          break;
        }
      } catch (fontError) {
        console.log('字体注册失败，尝试下一个字体文件:', fontError.message);
      }
    }

    if (!fontRegistered) {
      console.log('未找到可用的中文字体文件，将使用系统默认字体');
      console.log('建议执行以下命令安装中文字体:');
      console.log('Ubuntu/Debian: sudo apt-get install fonts-noto-cjk fonts-wqy-microhei');
      console.log('CentOS/RHEL: sudo yum install google-noto-sans-cjk-fonts');
      console.log('或者将字体文件放置到:', fontPaths[0]);
    } else {
      console.log('使用字体文件:', registeredFontPath);
    }

    // 背景图片路径 - 使用Buffer处理中文路径
    const backgroundImagePath = path.join(__dirname, '../../../uploads/餐烤证书背景图.jpg');

    // 处理中文路径编码问题
    const normalizedPath = path.normalize(backgroundImagePath);

    // 检查背景图片是否存在
    if (!fs.existsSync(normalizedPath)) {
      throw new Error('证书背景图片不存在');
    }

    // 加载背景图片 - 使用Buffer读取避免编码问题
    const imageBuffer = fs.readFileSync(normalizedPath);
    const backgroundImage = await loadImage(imageBuffer);

    // 创建画布，使用背景图片的尺寸
    const canvas = createCanvas(backgroundImage.width, backgroundImage.height);
    const ctx = canvas.getContext('2d');

    // 绘制背景图片
    ctx.drawImage(backgroundImage, 0, 0);

    //--------------别删，调试用的----------------
    // // 绘制调试网格线
    // ctx.strokeStyle = 'rgba(255, 0, 0, 0.3)';
    // ctx.lineWidth = 1;

    // // 绘制垂直网格线（每50像素）
    // for (let x = 0; x < canvas.width; x += 50) {
    //   ctx.beginPath();
    //   ctx.moveTo(x, 0);
    //   ctx.lineTo(x, canvas.height);
    //   ctx.stroke();

    //   // 每100像素标注坐标
    //   if (x % 100 === 0) {
    //     ctx.fillStyle = 'red';
    //     ctx.font = '12px Arial';
    //     ctx.fillText(x.toString(), x + 2, 15);
    //   }
    // }

    // // 绘制水平网格线（每50像素
    // for (let y = 0; y < canvas.height; y += 50) {
    //   ctx.beginPath();
    //   ctx.moveTo(0, y);
    //   ctx.lineTo(canvas.width, y);
    //   ctx.stroke();

    //   // 每100像素标注坐标
    //   if (y % 100 === 0) {
    //     ctx.fillStyle = 'red';
    //     ctx.font = '12px Arial';
    //     ctx.fillText(y.toString(), 2, y - 2);
    //   }
    // }


    // 设置字体样式和绘制文字
    // 根据效果图调整位置坐标
    const centerX = 249;

    // 1. 姓名 - 黑色字体粗体字体比较大 (在证书中央偏上位置)
    ctx.fillStyle = '#5B5756';
    ctx.font = 'bold 40px "NotoSansCJK", Arial, "Microsoft YaHei", "SimHei", "WenQuanYi Micro Hei", sans-serif';
    ctx.textAlign = 'center';
    const nameY = 330;
    ctx.fillText(name, centerX, nameY);


    // 2. 恭喜词 - 黑色字体粗体字体较小 (在姓名下方) - 减小字体
    const congratsText = `恭喜你已顺利通过餐烤餐考《${courseName}》课程学习，考核成绩合格，特发此证。`;
    ctx.fillStyle = '#5B5756';
    ctx.font = 'bold 16px "NotoSansCJK", Arial, "Microsoft YaHei", "SimHei", "WenQuanYi Micro Hei", sans-serif';
    ctx.textAlign = 'center';

    // 分行显示恭喜词 - 在课程名那里切分
    const lineHeight = 25;
    let y = 380;
    let congratsStartY = y;

    // 第一行：恭喜你已顺利通过餐烤餐考《课程名》
    const firstLine = `恭喜你已顺利通过餐烤餐考《${courseName}》`;
    ctx.fillText(firstLine, centerX, y);

    // 第二行：课程学习，考核成绩合格，特发此证
    y += lineHeight;
    const secondLine = '课程学习，考核成绩合格，特发此证';
    ctx.fillText(secondLine, centerX, y);


    // 3. 职位信息 - 修改颜色为#FBEBD1，添加书名号和课程名称
    ctx.fillStyle = '#FBEBD1';
    ctx.font = 'bold 16px "NotoSansCJK", Arial, "Microsoft YaHei", "SimHei", "WenQuanYi Micro Hei", sans-serif';
    ctx.textAlign = 'center';
    const positionY = 495;
    const positionText = `《${courseName}》   ${position}`;
    ctx.fillText(positionText, centerX, positionY);

    ctx.fillStyle = '#5B5756';
    ctx.font = 'bold 14px "NotoSansCJK", Arial, "Microsoft YaHei", "SimHei", "WenQuanYi Micro Hei", sans-serif';
    ctx.textAlign = 'left';
    const certNoX = 230; // 左上角X坐标
    const certNoY = 525;
    ctx.fillText(certificateNo, certNoX, certNoY);


    // 5. 颁发日期 - #9c8464颜色 (改为左对齐，从第一个字的左上角作为坐标)
    ctx.fillStyle = '#9c8464';
    ctx.font = 'bold 12px "NotoSansCJK", Arial, "Microsoft YaHei", "SimHei", "WenQuanYi Micro Hei", sans-serif';
    ctx.textAlign = 'left';
    const issueDateX = 345;
    const issueDateY = 643;
    ctx.fillText(`${issueDate}`, issueDateX, issueDateY);


    // 6. 有效期至 - #9c8464颜色 (改为左对齐，从第一个字的左上角作为坐标)
    ctx.fillStyle = '#9c8464';
    ctx.font = 'bold 12px "NotoSansCJK", Arial, "Microsoft YaHei", "SimHei", "WenQuanYi Micro Hei", sans-serif';
    ctx.textAlign = 'left';
    const validDateX = 345;
    const validDateY = 663;
    ctx.fillText(`${validDate}`, validDateX, validDateY);

    // 7. 添加企业Logo
    try {
      const InfoConfig = require('../../models/InfoConfig');
      const DEFAULT_ENTERPRISE_ID = process.env.DEFAULT_ENTERPRISE_ID || 1;

      // 查询企业logo
      const infoConfig = await InfoConfig.findOne({
        where: { enterprise_id: DEFAULT_ENTERPRISE_ID }
      });

      if (infoConfig && infoConfig.enterpriseLogo) {
        const logoPath = path.join(__dirname, '../../../', infoConfig.enterpriseLogo);
        if (fs.existsSync(logoPath)) {
          const logoBuffer = fs.readFileSync(logoPath);
          const logoImage = await loadImage(logoBuffer);

          // Logo位置和尺寸
          const logoX = 214; // 中心点X
          const logoY = 614; // Y坐标
          const logoSize = 30; // 50x50像素

          // 绘制logo (居中显示)
          ctx.drawImage(logoImage, logoX - logoSize/2, logoY - logoSize/2, logoSize, logoSize);

        } else {
          console.log('Logo文件不存在:', logoPath);
        }
      } else {
        console.log('未找到企业Logo配置');
      }
    } catch (logoError) {
      console.error('加载Logo失败:', logoError);
    }

    // 将画布转换为Buffer
    const buffer = canvas.toBuffer('image/png');

    // 生成文件名 - 使用obtainTime的日期格式（YYYY-MM-DD）
    const fileName = `certificate_${certificateNo}_${obtainDateString}.png`;
    const outputPath = path.join(__dirname, '../../../uploads/certificates', fileName);


 // 压缩画布尺寸（例如缩小50%）
//  const compressedWidth = Math.floor(backgroundImage.width * 0.5);
//  const compressedHeight = Math.floor(backgroundImage.height * 0.5);
//   // 创建临时画布用于压缩
//   const tempCanvas = createCanvas(compressedWidth, compressedHeight);
//   const tempCtx = tempCanvas.getContext('2d');
//  // 将原始画布内容绘制到临时画布（压缩）
//  tempCtx.drawImage(canvas, 0, 0, canvas.width, canvas.height, 
//   0, 0, compressedWidth, compressedHeight);
// const thumBuffer = tempCanvas.toBuffer('image/png');


    // 确保证书目录存在
    const certificateDir = path.dirname(outputPath);
    if (!fs.existsSync(certificateDir)) {
      fs.mkdirSync(certificateDir, { recursive: true });
    }

    // 保存证书图片
    fs.writeFileSync(outputPath, buffer);
    // fs.writeFileSync(outputPath, thumBuffer);

    // 构建证书URL
    const certificateUrl = `/uploads/certificates/${fileName}`;

    // 更新证书记录，保存证书URL
    const CertificateRecord = require('../../models/CertificateRecord');
    await CertificateRecord.update(
      { certificateFileUrl: certificateUrl },
      { where: { id: certificateRecord.id } }
    );

    console.log('证书文件创建成功:', certificateUrl);

    // 返回证书信息
    return {
      certificateUrl: certificateUrl,
      fileName: fileName,
      certificateInfo: {
        name: name,
        courseName: courseName,
        position: position,
        issueDate: issueDate,
        validDate: validDate
      }
    };

  } catch (error) {
    console.error('创建证书文件失败:', error);
    throw error;
  }
};

/**
 * 保存或更新系统设置
 * @param {string} code - 设置项code
 * @param {string} value - 设置项value
 * @param {string|number} enterpriseId - 企业ID
 */
async function upsertSystemSetting(code, value, enterpriseId) {
  let setting = await SystemSetting.findOne({
    where: { code, enterpriseId }
  });
  if (setting) {
    await setting.update({ value });
  } else {
    await SystemSetting.create({
      code,
      name: code,
      value,
      enterpriseId
    });
  }
}

/**
 * 获取用户未弹出的成就
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getUnpoppedAchievements = async (req, res) => {
  try {
    const openId = req.headers.openid;

    if (!openId) {
      return res.status(400).json({
        code: 400,
        message: 'openId参数不能为空'
      });
    }

    // 获取用户信息
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { openId }
      })
    );

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 查询用户未弹出的成就（is_pop = 0）
    const unpoppedAchievements = await UserAchievement.findAll(
      addEnterpriseFilter({
        where: {
          openId,
          isPop: 0,
          status: 'achieved'
        },
        include: [
          {
            model: AchievementTemplate,
            as: 'template',
            attributes: ['id', 'name', 'description', 'icon', 'category', 'rewardPoints']
          }
        ],
        order: [['achievedAt', 'DESC']]
      })
    );

    if (unpoppedAchievements.length > 0) {
      // 将这些成就标记为已弹出（is_pop = 1）
      const achievementIds = unpoppedAchievements.map(achievement => achievement.id);

      await UserAchievement.update(
        { isPop: 1 },
        addEnterpriseFilter({
          where: {
            id: {
              [Op.in]: achievementIds
            }
          }
        })
      );

      console.log(`[成就弹出] 用户${openId}获取了${unpoppedAchievements.length}个未弹出成就，已标记为已弹出`);
    }

    // 格式化返回数据
    const formattedAchievements = unpoppedAchievements.map(achievement => ({
      id: achievement.id,
      templateId: achievement.templateId,
      achievementName: achievement.achievementName,
      achievementIcon: achievement.achievementIcon,
      category: achievement.category,
      status: achievement.status,
      achievedAt: achievement.achievedAt,
      rewardPoints: achievement.rewardPoints,
      achievementData: achievement.achievementData,
      template: achievement.template
    }));

    res.json({
      code: 200,
      data: {
        achievements: formattedAchievements,
        total: formattedAchievements.length
      },
      message: '获取未弹出成就成功'
    });

  } catch (error) {
    console.error('[成就弹出] 获取未弹出成就失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取未弹出成就失败',
      error: error.message
    });
  }
};

/**
 * 获取所有徽章模板并标记用户是否已获得
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getAllAchievementsWithUserStatus = async (req, res) => {
  try {
    const openId = req.headers.openid;

    if (!openId) {
      return res.status(400).json({
        code: 400,
        message: 'openId参数不能为空'
      });
    }

    // 获取用户信息验证
    const user = await User.findOne(
      addEnterpriseFilter({
        where: { openId }
      })
    );

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 获取所有启用的成就模板
    const achievementTemplates = await AchievementTemplate.findAll(
      addEnterpriseFilter({
        where: {
          isActive: true
        },
        attributes: [
          'id',
          'name',
          'description',
          'icon',
          'category',
          'ruleType',
          'triggerCondition',
          'rewardPoints',
          'sort'
        ],
        order: [
          ['category', 'ASC'],
          ['sort', 'ASC'],
          ['id', 'ASC']
        ]
      })
    );

    // 获取用户已获得的成就
    const userAchievements = await UserAchievement.findAll(
      addEnterpriseFilter({
        where: {
          openId,
          status: 'achieved'
        },
        attributes: ['templateId', 'achievedAt', 'rewardPoints']
      })
    );

    // 创建用户已获得成就的映射表
    const userAchievementMap = new Map();
    userAchievements.forEach(achievement => {
      userAchievementMap.set(achievement.templateId, {
        achievedAt: achievement.achievedAt,
        rewardPoints: achievement.rewardPoints
      });
    });

    // 组合数据，为每个模板添加用户获得状态
    const achievementsWithStatus = achievementTemplates.map(template => {
      const userAchievement = userAchievementMap.get(template.id);
      const isAchieved = !!userAchievement;

      return {
        id: template.id,
        name: template.name,
        description: template.description,
        icon: template.icon,
        category: template.category,
        ruleType: template.ruleType,
        triggerCondition: template.triggerCondition,
        rewardPoints: template.rewardPoints,
        sort: template.sort,
        // 用户获得状态
        isAchieved: isAchieved,
        achievedAt: userAchievement?.achievedAt || null,
        actualRewardPoints: userAchievement?.rewardPoints || null
      };
    });

    // 统计信息
    const totalAchievements = achievementTemplates.length;
    const achievedCount = userAchievements.length;
    const achievementRate = totalAchievements > 0 ? Math.round((achievedCount / totalAchievements) * 100) : 0;

    // 按类别分组统计
    const categoryStats = {};
    achievementsWithStatus.forEach(achievement => {
      if (!categoryStats[achievement.category]) {
        categoryStats[achievement.category] = {
          total: 0,
          achieved: 0
        };
      }
      categoryStats[achievement.category].total++;
      if (achievement.isAchieved) {
        categoryStats[achievement.category].achieved++;
      }
    });

    res.json({
      code: 200,
      data: {
        achievements: achievementsWithStatus,
        statistics: {
          totalAchievements,
          achievedCount,
          achievementRate,
          categoryStats
        }
      },
      message: '获取徽章列表成功'
    });

  } catch (error) {
    console.error('[徽章列表] 获取徽章列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取徽章列表失败',
      error: error.message
    });
  }
};



