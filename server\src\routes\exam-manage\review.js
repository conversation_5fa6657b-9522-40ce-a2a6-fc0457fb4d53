const express = require('express');
const router = express.Router();
const reviewController = require('../../controllers/exam-manage/reviewController');
const { requireLogin } = require('../../middleware/auth');

// 考试审核申请相关路由
// 创建申请
router.post('/application', requireLogin, reviewController.createApplication);
// 获取申请列表（分页）
router.get('/applications', requireLogin, reviewController.getApplicationList);
// 获取申请详情
router.get('/application/:id', requireLogin, reviewController.getApplicationDetail);
// 更新申请
router.put('/application/:id', requireLogin, reviewController.updateApplication);
// 删除申请
router.delete('/application/:id', requireLogin, reviewController.deleteApplication);
// 审核申请
router.post('/application/:id/review', requireLogin, reviewController.reviewApplication);

// 批量操作路由
// 批量审核申请
router.post('/batch/review', requireLogin, reviewController.batchReviewApplication);
// 批量更新成绩确认状态
router.post('/batch/score/confirm', requireLogin, reviewController.batchUpdateScoreConfirmStatus);

// 小程序接口
// 小程序考试申请
router.post('/miniapp/application', reviewController.createApplicationFromMiniapp);

// 考试成绩相关路由
// 更新成绩确认状态
router.post('/score/confirm', requireLogin, reviewController.updateScoreConfirmStatus);
// 添加考试成绩
router.post('/score/:applicationId', requireLogin, reviewController.addExamScore);
// 获取成绩列表
router.get('/scores', requireLogin, reviewController.getExamScoreList);
// 获取考试统计数据
router.get('/statistics', requireLogin, reviewController.getExamStatistics);

// 考试题目相关路由
// 添加考试题目数据
router.post('/questions/:applicationId', requireLogin, reviewController.addExamQuestions);
// 更新考试题目数据
router.put('/questions/:applicationId', requireLogin, reviewController.updateExamQuestions);
// 获取考试题目统计信息
router.get('/questions/statistics/:applicationId', requireLogin, reviewController.getExamQuestionsStatistics);

module.exports = router; 