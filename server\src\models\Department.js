const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 部门模型
 */
const Department = sequelize.define('Department', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '部门ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '部门名称'
  },
  parentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'parent_id',
    comment: '上级部门ID'
  },
  orderNum: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'order_num',
    comment: '排序号'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态（1正常 0停用）'
  },
  remark: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '备注'
  },
  createBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'create_by',
    comment: '创建者'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'update_by',
    comment: '更新者'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'org_department',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

// 自关联（部门与子部门）
Department.hasMany(Department, { 
  as: 'children', 
  foreignKey: 'parentId' 
});
Department.belongsTo(Department, { 
  as: 'parent', 
  foreignKey: 'parentId' 
});

module.exports = Department; 