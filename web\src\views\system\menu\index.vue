<template>
  <div class="page-container">
    <page-header>
      <!-- <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="searchFormItems"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template> -->
      <template #actions>
        <a-button type="primary" @click="handleAdd">
        <template #icon><plus-outlined /></template>
        新增菜单
      </a-button>
      </template>
    </page-header>

    <!-- 表格区域 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      row-key="id"
      @change="handleTableChange"
      @edit="handleEdit"
      @delete="handleDelete"
      :action-config="actionConfig"
      :delete-title="deleteTitle"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'icon'">
          <component :is="record.icon" />
        </template>
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'success' : 'error'">
            {{ record.status ? '正常' : '禁用' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
            <a-button type="link" size="small" class="custom-button" @click="handleAdd(record)">
              <plus-outlined />新增</a-button>
        </template>
      </template>
    </base-table>

    <!-- 菜单表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-item label="上级菜单">
          <a-tree-select
            v-model:value="formData.parentId"
            :tree-data="menuTreeData"
            placeholder="请选择上级菜单"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="菜单类型" name="type">
          <a-radio-group v-model:value="formData.type">
            <a-radio :value="0">目录</a-radio>
            <a-radio :value="1">菜单</a-radio>
            <a-radio :value="2">按钮</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="菜单名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入菜单名称" />
        </a-form-item>
        <template v-if="formData.type !== 2">
          <a-form-item label="图标" name="icon">
            <a-input v-model:value="formData.icon" placeholder="请输入图标" />
          </a-form-item>
          <a-form-item label="路由地址" name="path">
            <a-input v-model:value="formData.path" placeholder="请输入路由地址" />
          </a-form-item>
          <a-form-item 
            label="组件路径" 
            name="component" 
            v-if="formData.type === 1"
            extra="以views开头，如：views/exam-manage/config/index">
            <a-input 
              v-model:value="formData.component" 
              placeholder="请输入组件路径，如：views/exam-manage/config/index" 
            />
          </a-form-item>
        </template>
        <template v-else>
          <a-form-item label="权限标识" name="permission">
            <a-input v-model:value="formData.permission" placeholder="请输入权限标识" />
          </a-form-item>
        </template>
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formData.sort" :min="0" style="width: 100%" />
        </a-form-item>
        <a-form-item 
          label="权限标识" 
          name="permission"
          :extra="formData.type !== 2 ? '菜单权限标识，例如：exam.manage.config，用于控制路由访问权限' : '按钮权限标识'">
          <a-input v-model:value="formData.permission" placeholder="请输入权限标识，例如：exam.manage.config" />
        </a-form-item>
        <a-form-item label="可见状态" name="visible">
          <a-switch v-model:checked="formData.visible" />
        </a-form-item>
        <a-form-item label="菜单状态" name="status">
          <a-switch v-model:checked="formData.status" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import { getMenuList, createMenu, updateMenu, deleteMenu } from '@/api/system/menu'
import { useTablePagination } from '@/utils/common'
import { SearchFormCard } from '@/components/SearchForm'

// 加载状态
const loading = ref(false)

const deleteTitle = '确定删除该菜单吗?';

const actionConfig={
  edit:true,
  delete:true,
}

// 搜索表单
const searchForm = reactive({
  name: '',
  path: '',
  status: undefined,
  sortField: '',
  sortOrder: ''
});

// 搜索表单配置
const searchFormItems = computed(() => [
  {
    label: '菜单名称',
    field: 'name',
    type: 'input',
    placeholder: '请输入菜单名称',
    width: '180px'
  },
  {
    label: '路由地址',
    field: 'path',
    type: 'input',
    placeholder: '请输入路由地址',
    width: '180px'
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    placeholder: '请选择状态',
    width: '120px',
    options: [
      { label: '全部', value: undefined },
      { label: '正常', value: true },
      { label: '禁用', value: false }
    ]
  }
]);

// 表格列定义
const columns = [
  {
    title: '菜单名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '图标',
    dataIndex: 'icon',
    key: 'icon',
    width: 80
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '权限标识',
    dataIndex: 'perms',
    key: 'permission'
  },
  {
    title: '路由地址',
    dataIndex: 'path',
    key: 'path'
  },
  {
    title: '组件路径',
    dataIndex: 'component',
    key: 'component'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 240
  }
]

// 表格数据
const tableData = ref([])

// 弹窗控制
const modalVisible = ref(false)
const modalTitle = ref('新增菜单')
const formRef = ref()

// 表单数据
const formData = reactive({
  id: undefined,
  parentId: undefined,
  name: '',
  type: 0,
  icon: '',
  path: '',
  component: '',
  permission: '',
  sort: 0,
  status: true,
  visible: true,
  hidden: false
})

// 菜单树数据
const menuTreeData = ref([])

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入菜单名称' },
    { max: 50, message: '菜单名称最多50个字符' }
  ],
  type: [{ required: true, message: '请选择菜单类型' }],
  path: [{ required: true, message: '请输入路由地址' }],
  sort: [{ required: true, message: '请输入排序号' }],
  component: [
    { 
      required: false, 
      message: '请输入组件路径',
      validator: (rule, value, callback) => {
        if (formData.type === 1 && !value) {
          callback(new Error('菜单类型为菜单时，组件路径为必填'));
        } else {
          callback();
        }
      } 
    }
  ],
  permission: [
    { 
      validator: (rule, value, callback) => {
        if (formData.type === 2 && !value) {
          callback(new Error('菜单类型为按钮时，权限标识为必填'));
        } else if (formData.type === 1 && !value) {
          // 菜单类型建议填写权限标识，但不强制
          callback(new Error('建议填写权限标识，用于控制路由访问权限'));
        } else {
          callback();
        }
      } 
    }
  ]
}

// 搜索处理
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values);
  }
  resetPagination();
  fetchMenuList();
};

// 重置搜索
const resetSearch = () => {
  searchForm.name = '';
  searchForm.path = '';
  searchForm.status = undefined;
  searchForm.sortField = '';
  searchForm.sortOrder = '';
  resetPagination();
  fetchMenuList();
};

// 方法定义
const handleAdd = (record) => {
  modalTitle.value = '新增菜单'
  formData.id = undefined
  formData.parentId = record ? record.id : undefined
  formData.name = ''
  formData.type = 0 // 目录
  formData.icon = ''
  formData.path = ''
  formData.component = '' // 确保重置组件路径
  formData.permission = ''
  formData.sort = 0
  formData.status = true
  formData.visible = true
  formData.hidden = false
  modalVisible.value = true
}

const handleEdit = (record) => {
  modalTitle.value = '编辑菜单'
  
  // 直接重置所有表单数据
  formRef.value?.resetFields();
  
  // 重新赋值，保持原始值
  formData.id = record.id;
  formData.parentId = record.parentId;
  formData.name = record.name;
  formData.type = record.type;
  formData.icon = record.icon || '';
  formData.path = record.path || '';
  formData.component = record.component || '';
  formData.permission = record.perms || '';
  formData.sort = record.sort || 0;
  formData.visible = !record.hidden;
  formData.status = record.status !== false;
  
  console.log('编辑菜单，原始数据:', record);
  console.log('组件路径(原始):', record.component);
  console.log('组件路径(表单):', formData.component);
  
  modalVisible.value = true
}

const handleDelete = async (record) => {
  try {
    loading.value = true
    await deleteMenu(record.id)
    message.success('删除成功')
    fetchMenuList()
  } catch (error) {
    message.error('删除失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    
    // 处理表单数据 - 浅拷贝即可
    const submitData = { ...formData };
    
    console.log('表单提交前数据:', submitData);
    console.log('组件路径提交前(原样):', submitData.component);
    
    // 处理visible到hidden的转换
    submitData.hidden = !submitData.visible;
    delete submitData.visible;
    
    // 组件路径保持原样
    
    // 处理权限标识字段映射
    if (submitData.permission) {
      submitData.perms = submitData.permission;
      delete submitData.permission;
    }
    
    console.log('准备提交数据:', submitData);
    
    loading.value = true;
    
    if (submitData.id) {
      // 编辑
      await updateMenu(submitData);
      message.success('更新成功');
    } else {
      // 新增
      await createMenu(submitData);
      message.success('创建成功');
    }
    
    modalVisible.value = false;
    fetchMenuList();
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

// 格式化菜单树
const formatMenuTree = (menus) => {
  return menus.map(menu => ({
    title: menu.name,
    value: menu.id,
    key: menu.id,
    children: menu.children ? formatMenuTree(menu.children) : undefined
  }))
}

// 加载菜单列表
const fetchMenuList = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      name: searchForm.name,
      path: searchForm.path,
      status: searchForm.status,
      sortField: searchForm.sortField,
      sortOrder: searchForm.sortOrder
    };
    
    // 使用setTimeout减少界面阻塞感
    setTimeout(async () => {
      try {
        const res = await getMenuList(params)
        
        // 添加缓存优化 - 避免不必要的重新渲染
        if (JSON.stringify(tableData.value) !== JSON.stringify(res)) {
          tableData.value = res
          menuTreeData.value = formatMenuTree(res)
        }
        
        loading.value = false
      } catch (error) {
        message.error('获取菜单列表失败: ' + error.message)
        tableData.value = []
        menuTreeData.value = []
        loading.value = false
      }
    }, 100);
  } catch (error) {
    console.error('参数处理失败:', error);
    message.error('获取菜单列表失败');
    loading.value = false;
  }
}

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchMenuList,
  initialPagination: false, // 菜单表格不使用分页
  searchForm
});

// 添加对菜单类型变化的监听
watch(() => formData.type, (newVal, oldVal) => {
  console.log(`菜单类型从 ${oldVal} 变为 ${newVal}`);
  
  // 只有在新建菜单时才自动清空组件路径
  if (!formData.id) {
    if (newVal === 0) { // 目录
      formData.component = '';
    } else if (newVal === 2) { // 按钮
      formData.component = '';
    }
  } else {
    // 在编辑模式下，提示用户
    console.log('编辑模式下菜单类型变化，组件路径保持不变:', formData.component);
  }
});

// 初始化获取菜单数据
onMounted(() => {
  fetchMenuList()
})
</script>

<style scoped>
.table-toolbar {
  margin-bottom: 16px;
}

/* 固定列样式优化 */
:deep(.ant-table-cell-fix-right) {
  background-color: #fff;
  padding: 8px 4px !important;
}

:deep(.ant-table-cell-fix-right .action-btns .ant-divider) {
  margin: 0 4px;
}
</style>