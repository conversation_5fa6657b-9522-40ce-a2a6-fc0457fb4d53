/**
 * 菜单诊断工具
 * 用于排查菜单点击无反应或加载不出数据的问题
 */

export class MenuDiagnostic {
  constructor() {
    this.logs = [];
    this.startTime = Date.now();
  }

  log(level, message, data = null) {
    const timestamp = Date.now() - this.startTime;
    const logEntry = {
      level,
      message,
      data,
      timestamp,
      time: new Date().toISOString()
    };
    
    this.logs.push(logEntry);
    console.log(`[菜单诊断-${level}] ${message}`, data || '');
  }

  info(message, data) {
    this.log('INFO', message, data);
  }

  warn(message, data) {
    this.log('WARN', message, data);
  }

  error(message, data) {
    this.log('ERROR', message, data);
  }

  // 检查菜单数据结构
  checkMenuStructure(menus) {
    this.info('开始检查菜单数据结构', { menuCount: menus?.length });
    
    if (!menus) {
      this.error('菜单数据为空');
      return false;
    }

    if (!Array.isArray(menus)) {
      this.error('菜单数据不是数组类型', typeof menus);
      return false;
    }

    if (menus.length === 0) {
      this.warn('菜单数组为空');
      return false;
    }

    // 检查每个菜单项的必要字段
    for (let i = 0; i < menus.length; i++) {
      const menu = menus[i];
      if (!menu.id) {
        this.error(`菜单项${i}缺少id字段`, menu);
      }
      if (!menu.name) {
        this.error(`菜单项${i}缺少name字段`, menu);
      }
      if (!menu.path && menu.type === 1) {
        this.error(`菜单项${i}是页面类型但缺少path字段`, menu);
      }
    }

    this.info('菜单数据结构检查完成');
    return true;
  }

  // 检查用户权限
  checkUserPermissions(permissions) {
    this.info('检查用户权限', { permissionCount: permissions?.length });
    
    if (!permissions || !Array.isArray(permissions)) {
      this.warn('用户权限数据无效', typeof permissions);
      return false;
    }

    this.info(`用户拥有${permissions.length}个权限`, permissions);
    return true;
  }

  // 检查路由配置
  checkRouteConfig(route) {
    this.info('检查当前路由配置', route.path);
    
    if (!route.matched || route.matched.length === 0) {
      this.error('路由未匹配到任何组件', route);
      return false;
    }

    this.info('路由配置正常', {
      path: route.path,
      name: route.name,
      matched: route.matched.length
    });
    return true;
  }

  // 检查网络连接
  async checkNetworkConnection() {
    this.info('检查网络连接');
    
    try {
      const response = await fetch('/api/health', { 
        method: 'GET',
        timeout: 5000
      });
      
      if (response.ok) {
        this.info('网络连接正常');
        return true;
      } else {
        this.warn('服务器响应异常', response.status);
        return false;
      }
    } catch (error) {
      this.error('网络连接失败', error.message);
      return false;
    }
  }

  // 生成诊断报告
  generateReport() {
    const report = {
      summary: {
        totalLogs: this.logs.length,
        errors: this.logs.filter(log => log.level === 'ERROR').length,
        warnings: this.logs.filter(log => log.level === 'WARN').length,
        duration: Date.now() - this.startTime
      },
      logs: this.logs,
      recommendations: this.getRecommendations()
    };

    console.group('菜单诊断报告');
    console.table(report.summary);
    console.log('详细日志:', report.logs);
    console.log('建议解决方案:', report.recommendations);
    console.groupEnd();

    return report;
  }

  // 获取解决建议
  getRecommendations() {
    const recommendations = [];
    const errors = this.logs.filter(log => log.level === 'ERROR');
    const warnings = this.logs.filter(log => log.level === 'WARN');

    if (errors.length > 0) {
      recommendations.push('发现错误，请检查控制台日志并修复相关问题');
    }

    if (warnings.length > 0) {
      recommendations.push('发现警告，建议检查相关配置');
    }

    if (errors.some(log => log.message.includes('菜单数据'))) {
      recommendations.push('菜单数据有问题，建议检查后端接口返回数据格式');
    }

    if (errors.some(log => log.message.includes('权限'))) {
      recommendations.push('权限配置有问题，建议检查用户角色和权限设置');
    }

    if (errors.some(log => log.message.includes('网络'))) {
      recommendations.push('网络连接有问题，建议检查网络设置和后端服务状态');
    }

    if (recommendations.length === 0) {
      recommendations.push('未发现明显问题，建议查看浏览器开发者工具的网络面板');
    }

    return recommendations;
  }

  // 清除日志
  clear() {
    this.logs = [];
    this.startTime = Date.now();
  }
}

// 创建全局诊断实例
export const menuDiagnostic = new MenuDiagnostic();

// 在开发环境下将诊断工具暴露到全局
if (import.meta.env.DEV) {
  window.menuDiagnostic = menuDiagnostic;
} 