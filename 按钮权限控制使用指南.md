# 按钮权限控制使用指南

## 概述

本系统提供了完整的按钮权限控制方案，包括：
- 权限工具函数
- Vue指令（v-permission）
- 计算属性方式
- 实际使用示例

## 1. 权限工具函数 (`/src/utils/permission.js`)

### 基础函数

#### `hasPermission(permission)`
检查当前用户是否拥有指定权限
```javascript
import { hasPermission } from '@/utils/permission'

// 检查单个权限
hasPermission('user.add') // 返回 true/false

// 检查多个权限（任意一个）
hasPermission(['user.add', 'user.edit']) // 返回 true/false
```

#### `hasAllPermissions(permissions)`
检查当前用户是否拥有所有指定权限
```javascript
import { hasAllPermissions } from '@/utils/permission'

// 必须同时拥有所有权限
hasAllPermissions(['user.add', 'user.edit']) // 返回 true/false
```

#### `hasAnyPermission(permissions)`
检查当前用户是否拥有任意一个指定权限
```javascript
import { hasAnyPermission } from '@/utils/permission'

// 拥有其中任意一个权限即可
hasAnyPermission(['user.add', 'user.edit']) // 返回 true/false
```

#### `getUserPermissions()`
获取当前用户的所有权限
```javascript
import { getUserPermissions } from '@/utils/permission'

const permissions = getUserPermissions() // 返回权限数组
```

## 2. Vue指令 (`v-permission`)

### 基础用法

#### 单个权限
```vue
<template>
  <!-- 只有拥有 'user.add' 权限的用户才能看到此按钮 -->
  <a-button v-permission="'user.add'">新增用户</a-button>
</template>
```

#### 多个权限（任意一个）
```vue
<template>
  <!-- 拥有 'user.edit' 或 'user.update' 权限的用户可以看到 -->
  <a-button v-permission="['user.edit', 'user.update']">编辑用户</a-button>
</template>
```

#### 多个权限（全部拥有）
```vue
<template>
  <!-- 必须同时拥有 'user.delete' 和 'user.admin' 权限 -->
  <a-button v-permission:all="['user.delete', 'user.admin']">删除用户</a-button>
</template>
```

### 高级用法

#### 在表格操作列中使用
```vue
<template>
  <a-table :columns="columns" :data-source="data">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <a-space>
          <!-- 查看按钮 - 所有人可见 -->
          <a-button type="link" @click="handleView(record)">查看</a-button>
          
          <!-- 编辑按钮 - 需要编辑权限 -->
          <a-button 
            type="link" 
            v-permission="'user.edit'"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          
          <!-- 删除按钮 - 需要删除权限 -->
          <a-button 
            type="link" 
            danger
            v-permission="'user.delete'"
            @click="handleDelete(record)"
          >
            删除
          </a-button>
        </a-space>
      </template>
    </template>
  </a-table>
</template>
```

## 3. 计算属性方式

当需要更复杂的权限控制逻辑时，建议使用计算属性：

```vue
<template>
  <div>
    <!-- 禁用按钮而不是隐藏 -->
    <a-button 
      type="primary" 
      :disabled="!canAdd"
      @click="handleAdd"
    >
      新增用户
    </a-button>

    <!-- 根据权限显示不同样式 -->
    <a-button 
      :type="canDelete ? 'danger' : 'default'"
      :disabled="!canDelete"
      @click="handleDelete"
    >
      {{ canDelete ? '删除' : '无删除权限' }}
    </a-button>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { hasPermission } from '@/utils/permission'

const canAdd = computed(() => hasPermission('user.add'))
const canDelete = computed(() => hasPermission('user.delete'))

const handleAdd = () => {
  // 新增逻辑
}

const handleDelete = () => {
  // 删除逻辑
}
</script>
```

## 4. 权限标识命名规范

建议使用以下命名规范：

```javascript
// 模块.操作 格式
'user.add'          // 用户新增
'user.edit'         // 用户编辑
'user.delete'       // 用户删除
'user.list'         // 用户列表查看

// 模块.子模块.操作 格式
'system.role.add'   // 系统角色新增
'system.menu.edit'  // 系统菜单编辑

// 特殊权限
'admin'             // 管理员权限
'super.admin'       // 超级管理员权限
```

## 5. 在菜单管理中配置按钮权限

### 菜单类型说明
- **目录（0）**：不需要权限标识
- **菜单（1）**：页面访问权限，如 `user.manage`
- **按钮（2）**：操作权限，如 `user.add`、`user.delete`

### 配置步骤

1. **创建按钮权限菜单**
   - 菜单类型：选择"按钮"
   - 权限标识：填写具体的操作权限，如 `user.add`
   - 父级菜单：选择对应的页面菜单

2. **分配权限给角色**
   - 在角色管理中选择角色
   - 点击"权限"按钮
   - 勾选对应的按钮权限

3. **用户获得权限**
   - 给用户分配对应角色
   - 用户登录后自动获得角色对应的权限

## 6. 实际使用示例

### 用户管理页面完整示例

```vue
<template>
  <div class="page-container">
    <page-header>
      <template #actions>
        <!-- 新增按钮 - 需要新增权限 -->
        <a-button 
          type="primary" 
          v-permission="'user.add'"
          @click="handleAdd"
        >
          <template #icon><plus-outlined /></template>
          新增用户
        </a-button>

        <!-- 批量删除 - 需要删除权限 -->
        <a-button 
          danger
          v-permission="'user.delete'"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          批量删除
        </a-button>

        <!-- 导出 - 需要导出权限 -->
        <a-button 
          v-permission="'user.export'"
          @click="handleExport"
        >
          导出数据
        </a-button>
      </template>
    </page-header>

    <!-- 表格 -->
    <a-table 
      :columns="columns" 
      :data-source="tableData"
      :row-selection="rowSelection"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-space>
            <!-- 查看详情 - 无权限限制 -->
            <a-button type="link" @click="handleView(record)">
              查看
            </a-button>

            <!-- 编辑 - 需要编辑权限 -->
            <a-button 
              type="link" 
              v-permission="'user.edit'"
              @click="handleEdit(record)"
            >
              编辑
            </a-button>

            <!-- 重置密码 - 需要重置密码权限 -->
            <a-button 
              type="link" 
              v-permission="'user.reset.password'"
              @click="handleResetPassword(record)"
            >
              重置密码
            </a-button>

            <!-- 删除 - 需要删除权限 -->
            <a-button 
              type="link" 
              danger
              v-permission="'user.delete'"
              @click="handleDelete(record)"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'

const selectedRowKeys = ref([])
const tableData = ref([])

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 事件处理函数
const handleAdd = () => {
  message.success('打开新增用户弹窗')
}

const handleEdit = (record) => {
  message.success(`编辑用户: ${record.name}`)
}

const handleDelete = (record) => {
  message.success(`删除用户: ${record.name}`)
}

const handleBatchDelete = () => {
  message.success(`批量删除 ${selectedRowKeys.value.length} 个用户`)
}

const handleResetPassword = (record) => {
  message.success(`重置用户密码: ${record.name}`)
}

const handleExport = () => {
  message.success('导出用户数据')
}

const handleView = (record) => {
  message.info(`查看用户详情: ${record.name}`)
}
</script>
```

## 7. 常见问题

### Q: 权限不生效怎么办？
A: 
1. 检查用户是否正确分配了角色
2. 检查角色是否包含对应权限
3. 检查权限标识是否正确
4. 检查浏览器控制台是否有错误信息

### Q: 如何调试权限问题？
A:
```javascript
// 在组件中添加调试代码
import { getUserPermissions } from '@/utils/permission'

console.log('当前用户权限:', getUserPermissions())
console.log('是否有user.add权限:', hasPermission('user.add'))
```

### Q: 权限更新后不生效？
A: 需要重新登录或刷新页面，让用户重新获取权限信息

## 8. 最佳实践

1. **权限粒度要合适**：不要过于细化，也不要过于粗糙
2. **命名要规范**：使用统一的命名规范
3. **及时更新**：角色权限变更后要及时通知用户
4. **异常处理**：权限检查要有异常处理机制
5. **性能考虑**：避免在循环中频繁调用权限检查函数

## 9. 后端配置要求

确保后端满足以下要求：
1. 用户登录后返回用户权限列表
2. 菜单管理支持按钮类型菜单
3. 角色权限分配接口正常工作
4. 企业字段正确配置（满足后端要求）

通过以上配置，您就可以在项目中完整地实现按钮权限控制了。 