const express = require('express');
const router = express.Router();
const departmentController = require('../controllers/organization/departmentController');
const employeeController = require('../controllers/organization/employeeController');
const positionController = require('../controllers/organization/positionController');
const levelController = require('../controllers/organization/levelController');
const positionTypeController = require('../controllers/organization/positionTypeController');
const positionNameController = require('../controllers/organization/positionNameController');
const employeePromotionController = require('../controllers/organization/employeePromotionController');
const employeePositionController = require('../controllers/organization/employeePositionController');
const employeeStatisticsRoutes = require('./organization/employeeStatisticsRoutes');
const { requireLogin } = require('../middleware/auth');

// 部门相关路由
router.get('/department/tree', departmentController.getDepartmentTree);
router.get('/department/list', departmentController.getDepartmentList);
router.get('/department/:id', departmentController.getDepartmentDetail);
router.post('/department', departmentController.addDepartment);
router.put('/department', departmentController.updateDepartment);
router.delete('/department/:id', departmentController.deleteDepartment);
router.put('/department/status', departmentController.updateDepartmentStatus);

// 员工相关路由
router.get('/employee/list', employeeController.getEmployeeList);
router.get('/employee/department/:departmentId', employeeController.getEmployeeByDepartment);
router.get('/employee/template', employeeController.downloadEmployeeTemplate);
router.get('/employee/export', employeeController.exportEmployees);

// 员工统计相关路由（必须在动态路由/:id之前）
router.use('/employee/statistics', employeeStatisticsRoutes);

router.get('/employee/:id', employeeController.getEmployeeDetail);
router.post('/employee', employeeController.addEmployee);
router.put('/employee', employeeController.updateEmployee);
router.delete('/employee/:id', employeeController.deleteEmployee);
router.put('/employee/status', employeeController.updateEmployeeStatus);
router.put('/employee/activation', employeeController.updateEmployeeActivation);
router.post('/employee/import', employeeController.importEmployees);

// 员工企业申请审核相关路由
router.get('/employee/application/list', employeeController.getEmployeeApplicationList);
router.get('/employee/application/:id', employeeController.getEmployeeApplicationDetail);
router.put('/employee/application/:id/audit', employeeController.auditEmployeeApplication);
router.put('/employee/application/batch-audit', employeeController.batchAuditEmployeeApplication);

// 员工岗位配置相关路由
router.get('/employee/:employeeId/positions', employeePositionController.getEmployeePositions);
router.put('/employee/:employeeId/positions', employeePositionController.updateEmployeePositions);
router.post('/employee/position', employeePositionController.addEmployeePosition);
router.delete('/employee/position/:id', employeePositionController.deleteEmployeePosition);

// 岗位相关路由
router.get('/position/name/options', positionController.getPositionNameOptions);
router.get('/position/list', positionController.getPositionList);
router.get('/position/options', positionController.getPositionOptions);
router.get('/position/structure/tree', positionController.getPositionStructureTree);
router.get('/position/:positionId/levels', positionController.getLevelsByPosition);
router.get('/position/:id', positionController.getPositionDetail);
router.post('/position', positionController.addPosition);
router.put('/position', positionController.updatePosition);
router.delete('/position/:id', positionController.deletePosition);

// 岗位等级相关路由
router.get('/level/list', requireLogin, levelController.getLevelList);
router.get('/level/options', levelController.getLevelOptions);
router.get('/level/:id', requireLogin, levelController.getLevelDetail);
router.post('/level', requireLogin, levelController.addLevel);
router.put('/level', requireLogin, levelController.updateLevel);
router.delete('/level/:id', requireLogin, levelController.deleteLevel);

// 岗位类型相关路由
router.get('/position/type/list', positionTypeController.getPositionTypeList);
router.get('/position/type/options', positionTypeController.getPositionTypeOptions);
router.post('/position/type', positionTypeController.addPositionType);
router.put('/position/type', positionTypeController.updatePositionType);
router.delete('/position/type/:id', positionTypeController.deletePositionType);

// 岗位名称相关路由
router.get('/position/name/list', positionNameController.getPositionNameList);
router.post('/position/name', positionNameController.addPositionName);
router.put('/position/name', positionNameController.updatePositionName);
router.delete('/position/name/:id', positionNameController.deletePositionName);

// 员工晋升时间表相关路由
router.get('/employee/promotion/list', employeePromotionController.getEmployeePromotionList);
router.get('/employee/promotion/:id', employeePromotionController.getEmployeePromotionDetail);
router.post('/employee/promotion', employeePromotionController.addEmployeePromotion);
router.put('/employee/promotion', employeePromotionController.updateEmployeePromotion);
router.delete('/employee/promotion/:id', employeePromotionController.deleteEmployeePromotion);
router.get('/employee/promotion/history/:openId', employeePromotionController.getEmployeePromotionHistory);

module.exports = router;
