@echo off
setlocal

echo =================================
echo 阿依来后台系统 Git 提交工具
echo =================================

REM 检查是否已初始化Git仓库
if not exist .git (
    echo Git仓库未初始化，正在初始化...
    git init
    if errorlevel 1 (
        echo Git初始化失败，请检查Git是否正确安装
        goto :end
    )
    
    echo 请输入远程仓库地址(例如: https://gitlab.com/username/project.git):
    set /p remote_url=
    git remote add origin %remote_url%
    if errorlevel 1 (
        echo 添加远程仓库失败，请检查URL是否正确
        goto :end
    )
)

REM 获取分支名称
for /f "tokens=*" %%a in ('git branch --show-current') do set current_branch=%%a
if "%current_branch%"=="" set current_branch=main

REM 显示当前状态
git status
echo.

REM 添加所有文件
echo 正在添加所有文件...
git add .
if errorlevel 1 (
    echo 添加文件失败
    goto :end
)

REM 提交信息
echo 请输入提交信息:
set /p commit_msg=
if "%commit_msg%"=="" set commit_msg="更新提交"

REM 提交更改
echo 正在提交更改...
git commit -m "%commit_msg%"
if errorlevel 1 (
    echo 提交失败
    goto :end
)

REM 推送到远程
echo 是否推送到远程? (Y/N)
set /p push_choice=
if /i "%push_choice%"=="Y" (
    echo 正在推送到远程仓库...
    git push -u origin %current_branch%
    if errorlevel 1 (
        echo 推送失败，尝试拉取最新代码...
        git pull --rebase origin %current_branch%
        git push -u origin %current_branch%
    ) else (
        echo 推送成功!
    )
)

:end
echo.
echo 操作完成!
pause
