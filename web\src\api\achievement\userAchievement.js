import request from '@/utils/request';

/**
 * 用户成就相关API
 */

// 获取用户成就列表
export function getUserAchievements(params) {
  return request({
    url: '/user-achievement/achievements',
    method: 'get',
    params
  });
}

// 获取用户成就进度列表
export function getUserAchievementProgress(params) {
  return request({
    url: '/user-achievement/progress',
    method: 'get',
    params
  });
}

// 获取用户成就统计
export function getUserAchievementStats(params) {
  return request({
    url: '/user-achievement/stats',
    method: 'get',
    params
  });
}

// 获取可用成就列表（包含进度）
export function getAvailableAchievements(params) {
  return request({
    url: '/user-achievement/available',
    method: 'get',
    params
  });
}

// 获取成就详情（包含进度信息）
export function getAchievementDetail(templateId, params) {
  return request({
    url: `/user-achievement/detail/${templateId}`,
    method: 'get',
    params
  });
}

// 手动触发成就检测（调试用）
export function triggerAchievementCheck(data) {
  return request({
    url: '/user-achievement/trigger',
    method: 'post',
    data
  });
} 