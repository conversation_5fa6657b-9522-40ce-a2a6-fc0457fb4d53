/**
 * 学霸模式成就测试脚本
 * 该脚本用于测试连续学习天数成就
 */

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.DEFAULT_ENTERPRISE_ID = '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32';

// 设置数据库连接信息
process.env.DB_HOST = 'localhost';
process.env.DB_USER = 'root';
process.env.DB_PASSWORD = '';
process.env.DB_NAME = 'ayl_admin';
process.env.DB_PORT = '3306';

// 导入依赖
const { sequelize } = require('./server/src/config/database');
const { processStudyStreakAchievement } = require('./server/src/utils/achievementUtils');
const PracticeRecord = require('./server/src/models/practice-record');
const User = require('./server/src/models/user');
const { addEnterpriseId } = require('./server/src/utils/enterpriseFilter');

// 测试用户信息
const TEST_OPEN_ID = 'oxiSG65bMvpNcF9TORr5mvW-HXo4'; // 替换为实际测试用户的openId
const TEST_POSITION_NAME = '1'; // 替换为实际岗位ID
const TEST_POSITION_LEVEL = '1'; // 替换为实际等级ID

/**
 * 创建模拟练习记录
 * @param {string} openId - 用户openId
 * @param {number} days - 要创建的天数
 * @param {boolean} consecutive - 是否连续
 */
async function createMockPracticeRecords(openId, days, consecutive = true) {
  console.log(`开始创建${days}天${consecutive ? '连续' : '不连续'}练习记录...`);
  
  const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
  const today = new Date();
  
  // 删除之前的测试记录
  await PracticeRecord.destroy(
    addEnterpriseId({
      where: { openId }
    }, enterpriseId)
  );
  
  // 创建练习记录
  for (let i = 0; i < days; i++) {
    // 如果是连续的，每天减1；如果不连续，每天减2
    const daysToSubtract = consecutive ? i : i * 2;
    const recordDate = new Date(today);
    recordDate.setDate(today.getDate() - daysToSubtract);
    
    await PracticeRecord.create(
      addEnterpriseId({
        openId,
        questionNum: 10,
        totalDuration: '00:30:00',
        positionBelong: '1',
        positionName: TEST_POSITION_NAME,
        positionLevel: TEST_POSITION_LEVEL,
        examSubject: '1',
        status: '必练',
        createTime: recordDate,
        updateTime: recordDate
      }, enterpriseId)
    );
    
    console.log(`创建练习记录: 日期=${recordDate.toISOString().split('T')[0]}`);
  }
  
  console.log(`成功创建${days}天练习记录`);
}

/**
 * 运行测试
 */
async function runTest() {
  try {
    console.log('开始测试学霸模式成就...');
    
    // 获取测试用户
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
    const user = await User.findOne(
      addEnterpriseId({
        where: { openId: TEST_OPEN_ID }
      }, enterpriseId)
    );
    
    if (!user) {
      console.error(`未找到测试用户: openId=${TEST_OPEN_ID}`);
      return;
    }
    
    console.log(`测试用户: id=${user.id}, username=${user.username}, openId=${user.openId}`);
    
    // 测试场景1: 连续学习2天（不足3天）
    await createMockPracticeRecords(TEST_OPEN_ID, 2, true);
    console.log('测试场景1: 连续学习2天（不足3天）');
    await processStudyStreakAchievement(user.id, TEST_OPEN_ID, enterpriseId);
    
    // 测试场景2: 连续学习4天（超过3天）
    await createMockPracticeRecords(TEST_OPEN_ID, 4, true);
    console.log('测试场景2: 连续学习4天（超过3天）');
    await processStudyStreakAchievement(user.id, TEST_OPEN_ID, enterpriseId);
    
    // 测试场景3: 不连续学习5天
    await createMockPracticeRecords(TEST_OPEN_ID, 5, false);
    console.log('测试场景3: 不连续学习5天');
    await processStudyStreakAchievement(user.id, TEST_OPEN_ID, enterpriseId);
    
    console.log('测试完成');
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 运行测试
runTest().then(() => {
  console.log('测试脚本执行完毕');
  process.exit(0);
}).catch(error => {
  console.error('测试脚本执行失败:', error);
  process.exit(1);
}); 