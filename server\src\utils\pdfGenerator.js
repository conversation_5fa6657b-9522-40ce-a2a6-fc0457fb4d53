const { createCanvas, loadImage } = require('canvas');
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

/**
 * 生成证书PDF
 * @param {Object} certificate - 证书记录对象
 * @returns {Promise<Buffer>} - PDF文件的Buffer
 */
async function generateCertificatePDF(certificate) {
  return new Promise(async (resolve, reject) => {
    try {
      // 创建PDF文档
      const doc = new PDFDocument({
        size: 'A4',
        layout: 'landscape',
        margin: 0
      });
      
      // 收集数据到Buffer
      const buffers = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // 设置背景色
      doc.rect(0, 0, doc.page.width, doc.page.height)
         .fill('#f6f7f8');
         
      // 绘制证书边框
      doc.rect(20, 20, doc.page.width - 40, doc.page.height - 40)
         .lineWidth(2)
         .stroke('#a18cd1');
      
      // 添加标题
      doc.font('Helvetica-Bold')
         .fontSize(36)
         .fillColor('#a18cd1')
         .text('餐烤餐考', doc.page.width / 2, 60, { align: 'center' });
      
      doc.font('Helvetica-Bold')
         .fontSize(28)
         .fillColor('#333333')
         .text(certificate.certificateName, doc.page.width / 2, 110, { align: 'center' });
      
      // 添加证书内容
      doc.font('Helvetica')
         .fontSize(16)
         .fillColor('#333333')
         .text(
           `兹证明 ${certificate.employeeName} 在岗位 ${certificate.positionName}（${certificate.positionLevel}） 中表现优秀，通过所有相关考核，特颁发此证书。`,
           doc.page.width / 2, 180, 
           { align: 'center', width: 500 }
         );
      
      // 添加证书信息
      doc.fontSize(14)
         .text(`证书编号：CERT-${String(certificate.id).padStart(6, '0')}`, 150, 260);
      
      // 格式化日期
      const obtainDate = new Date(certificate.obtainTime);
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };
      
      doc.text(`颁发日期：${formatDate(obtainDate)}`, 150, 290);
      
      // 计算有效期（一年后）
      const expiryDate = new Date(obtainDate);
      expiryDate.setFullYear(expiryDate.getFullYear() + 1);
      
      doc.text(`有效期至：${formatDate(expiryDate)}`, 150, 320);
      
      // 添加印章和签名
      // 印章
      doc.circle(150, 400, 40)
         .lineWidth(1)
         .stroke('#a18cd1');
      
      doc.fontSize(10)
         .text('餐烤餐考', 150, 400, { align: 'center' });
      
      // 签名线
      doc.moveTo(600, 410)
         .lineTo(700, 410)
         .stroke('#333333');
      
      doc.fontSize(12)
         .text('餐烤餐考企业负责人', 650, 430, { align: 'center' });
      
      // 结束文档
      doc.end();
      
    } catch (error) {
      reject(error);
    }
  });
}

module.exports = {
  generateCertificatePDF
}; 