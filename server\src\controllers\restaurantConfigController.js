const RestaurantConfig = require('../models/RestaurantConfig');
const PositionType = require('../models/PositionType');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');
const { getFileType } = require('../utils/fileUpload');

/**
 * 获取餐烤师配置列表
 */
exports.getRestaurantConfigList = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, name } = req.query;
    const offset = (page - 1) * pageSize;
    const where = {};

    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }

    const { count, rows } = await RestaurantConfig.findAndCountAll(
      addEnterpriseFilter({
        where,
        order: [['id', 'DESC']],
        limit: parseInt(pageSize),
        offset: parseInt(offset),
        include: [
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name']
          }
        ]
      }, req.user.enterpriseId)
    );

    res.json({
      code: 200,
      data: {
        total: count,
        items: rows,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取餐烤师配置列表失败:', error);
    res.status(500).json({ code: 500, message: '获取餐烤师配置列表失败', error: error.message });
  }
};

/**
 * 获取餐烤师配置详情
 */
exports.getRestaurantConfigDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const restaurantConfig = await RestaurantConfig.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name']
          }
        ]
      }, req.user.enterpriseId)
    );

    if (!restaurantConfig) {
      return res.status(404).json({ code: 404, message: '餐烤师配置不存在' });
    }

    res.json({
      code: 200,
      data: restaurantConfig
    });
  } catch (error) {
    console.error('获取餐烤师配置详情失败:', error);
    res.status(500).json({ code: 500, message: '获取餐烤师配置详情失败', error: error.message });
  }
};

/**
 * 创建餐烤师配置
 */
exports.createRestaurantConfig = async (req, res) => {
  try {
    const { positionBelongId, name, initMessage } = req.body;
    const userId = req.user ? req.user.id : null;
    const enterpriseId = req.user.enterpriseId;

    // 检查是否已存在相同岗位分类的餐烤师配置
    const existingConfig = await RestaurantConfig.findOne({
      where: {
        enterpriseId,
        positionBelongId
      }
    });

    if (existingConfig) {
      return res.status(400).json({
        code: 400,
        message: '该岗位分类下已存在餐烤师配置，不能重复创建'
      });
    }

    const newRestaurantConfig = await RestaurantConfig.create(
      addEnterpriseId({
        positionBelongId,
        name,
        avatar: req.body.avatar || null,
        initMessage,
        createdBy: userId,
        updatedBy: userId
      }, enterpriseId)
    );

    res.status(201).json({
      code: 200,
      data: newRestaurantConfig,
      message: '创建餐烤师配置成功'
    });
  } catch (error) {
    console.error('创建餐烤师配置失败:', error);
    res.status(500).json({ code: 500, message: '创建餐烤师配置失败', error: error.message });
  }
};

/**
 * 更新餐烤师配置
 */
exports.updateRestaurantConfig = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, avatar, initMessage } = req.body;
    const userId = req.user ? req.user.id : null;
    const enterpriseId = req.user.enterpriseId;

    const restaurantConfig = await RestaurantConfig.findOne(
      addEnterpriseFilter({
        where: { id }
      }, enterpriseId)
    );

    if (!restaurantConfig) {
      return res.status(404).json({ code: 404, message: '餐烤师配置不存在' });
    }

    await restaurantConfig.update({
      name,
      avatar,
      initMessage,
      updatedBy: userId
    });

    res.json({
      code: 200,
      data: restaurantConfig,
      message: '更新餐烤师配置成功'
    });
  } catch (error) {
    console.error('更新餐烤师配置失败:', error);
    res.status(500).json({ code: 500, message: '更新餐烤师配置失败', error: error.message });
  }
};

/**
 * 删除餐烤师配置
 */
exports.deleteRestaurantConfig = async (req, res) => {
  try {
    const { id } = req.params;

    const restaurantConfig = await RestaurantConfig.findOne(
      addEnterpriseFilter({
        where: { id }
      }, req.user.enterpriseId)
    );

    if (!restaurantConfig) {
      return res.status(404).json({ code: 404, message: '餐烤师配置不存在' });
    }

    await restaurantConfig.destroy();

    res.json({ 
      code: 200,
      data: restaurantConfig,
      message: '餐烤师配置删除成功' 
    });
  } catch (error) {
    console.error('删除餐烤师配置失败:', error);
    res.status(500).json({ code: 500, message: '删除餐烤师配置失败', error: error.message });
  }
};

/**
 * 上传餐烤师头像
 */
exports.uploadAvatar = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传的文件'
      });
    }

    const { originalname, filename, path: filePath, size } = req.file;
    
    // 确保文件名是正确解码的
    let decodedFileName = originalname;
    if (typeof decodedFileName === 'string') {
      try {
        // 尝试检测是否需要转换编码
        const testDecode = decodeURIComponent(escape(decodedFileName));
        // 如果解码成功但与原字符串不同，说明需要转换
        if (testDecode !== decodedFileName && /[\u4e00-\u9fa5]/.test(testDecode)) {
          decodedFileName = testDecode;
        }
      } catch (e) {
        // 如果解码失败，尝试直接从latin1转换
        decodedFileName = Buffer.from(decodedFileName, 'latin1').toString('utf8');
      }
    }
    
    // 获取文件类型
    const fileType = getFileType(decodedFileName);
    
    // 构建文件访问路径
    const fileUrl = `/uploads/restaurant/${filename}`;
    
    res.json({
      code: 200,
      message: '头像上传成功',
      data: {
        fileName: decodedFileName,
        fileType,
        fileSize: size,
        fileUrl
      }
    });
  } catch (error) {
    console.error('上传餐烤师头像失败:', error);
    res.status(500).json({ 
      code: 500, 
      message: '上传餐烤师头像失败', 
      error: error.message 
    });
  }
};

/**
 * 获取岗位类型列表（用于下拉框选择）
 */
exports.getPositionTypeList = async (req, res) => {
  try {
    const positionTypes = await PositionType.findAll(
      addEnterpriseFilter({
        where: {
          status: true // 只获取启用状态的岗位类型
        },
        attributes: ['id', 'name'],
        order: [['sort', 'ASC']]
      }, req.user.enterpriseId)
    );
    
    res.json({
      code: 200,
      data: positionTypes
    });
  } catch (error) {
    console.error('获取岗位类型列表失败:', error);
    res.status(500).json({ code: 500, message: '获取岗位类型列表失败', error: error.message });
  }
}; 