const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const PositionType = require('./PositionType');
const Employee = require('./Employee');
const { count } = require('./knowledge-base');

/**
 * 餐烤师聊天记录模型
 */
const RestaurantRecord = sequelize.define('RestaurantRecord', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  enterpriseId: {
    type: DataTypes.STRING(255),
    allowNull: false,
    defaultValue: '1',
    field: 'enterprise_id',
    comment: '企业ID'
  },
  positionBelongId: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'position_belong_id',
    comment: '前厅后厨分类ID'
  },
  positionName: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_name',
    comment: '岗位名称'
  },
  positionLevel: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_level',
    comment: '岗位等级'
  },
  openId: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field: 'open_id',
    comment: 'open_id'
  },
  count: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '聊天次数'
  },
  createdBy: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'created_by',
    comment: '创建人ID'
  },
  updatedBy: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'updated_by',
    comment: '更新人ID'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: true,
    defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
    field: 'created_at',
    comment: '创建时间'
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
    field: 'updated_at',
    comment: '更新时间'
  }
}, {
  tableName: 'restaurant_record',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 关联前厅后厨分类
RestaurantRecord.belongsTo(PositionType, {
  foreignKey: 'positionBelongId',
  targetKey: 'id',
  as: 'positionType'
});

// 通过openId关联员工
RestaurantRecord.belongsTo(Employee, {
  foreignKey: 'openId',
  targetKey: 'openId',
  as: 'employee'
});

module.exports = RestaurantRecord;