/**
 * 增强的日志工具
 * 支持结构化日志和详细错误追踪
 */

// 定义日志级别
const LogLevel = {
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG'
};

// 获取环境配置
const isDevelopment = process.env.NODE_ENV !== 'production';

/**
 * 格式化错误对象
 * @param {Error} error - 错误对象
 * @returns {Object} 格式化后的错误信息
 */
const formatError = (error) => {
  if (!error) return null;
  
  return {
    message: error.message,
    name: error.name,
    stack: isDevelopment ? error.stack : undefined,
    code: error.code,
    // 提取额外的错误信息字段
    details: error.details || undefined,
    sqlMessage: error.sqlMessage || undefined,
    sqlState: error.sqlState || undefined,
  };
};

/**
 * 记录日志的通用函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {any} data - 额外数据
 */
const log = (level, message, data = null) => {
  const timestamp = new Date().toISOString();
  let logObject = {
    timestamp,
    level,
    message
  };

  // 处理不同类型的数据
  if (data) {
    if (data instanceof Error) {
      logObject.error = formatError(data);
    } else if (typeof data === 'object') {
      logObject.data = data;
    } else {
      logObject.data = { value: data };
    }
  }
  
  // 生产环境下移除敏感信息
  if (!isDevelopment && logObject.data) {
    // 删除可能包含的敏感信息
    const sensitiveFields = ['password', 'token', 'secret', 'credential'];
    for (const field of sensitiveFields) {
      if (logObject.data[field]) {
        logObject.data[field] = '[REDACTED]';
      }
    }
  }

  // 根据不同的日志级别使用不同的控制台方法
  const logString = JSON.stringify(logObject);
  switch (level) {
    case LogLevel.ERROR:
      console.error(logString);
      break;
    case LogLevel.WARN:
      console.warn(logString);
      break;
    case LogLevel.INFO:
      console.info(logString);
      break;
    case LogLevel.DEBUG:
      if (isDevelopment) {
        console.debug(logString);
      }
      break;
    default:
      console.log(logString);
  }
};

// 导出日志函数
module.exports = {
  error: (message, data) => log(LogLevel.ERROR, message, data),
  warn: (message, data) => log(LogLevel.WARN, message, data),
  info: (message, data) => log(LogLevel.INFO, message, data),
  debug: (message, data) => log(LogLevel.DEBUG, message, data),
  // 添加一个包含请求信息的日志方法
  request: (req, message, data) => {
    const requestInfo = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      ...(req.headers.openid ? { openid: req.headers.openid } : {}),
    };
    log(LogLevel.INFO, `${message} - ${req.method} ${req.originalUrl}`, { ...data, request: requestInfo });
  }
}; 