<template>
  <div class="page-container">
    <page-header
    >
      <template #search>
        <!-- 操作区域 -->
        <search-form-card
          v-model="queryParams"
          :items="searchFormItems"
          @search="handleQuery"
          @reset="handleReset"
        />
      </template>
      <template #actions>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增配置
        </a-button>
      </template>
    </page-header>

    <!-- 表格区域 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      @change="handleTableChange"
      row-key="id"
      :action-config="actionConfig"
      @edit="handleEdit"
      @delete="handleDelete"
      >
      </base-table>

    <!-- 新增/编辑表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="formTitle"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item name="code" label="设置代码" v-if="!isEdit">
          <a-input
            v-model:value="formData.code"
            placeholder="请输入设置代码"
            :disabled="isEdit"
          />
        </a-form-item>
        <a-form-item name="name" label="设置名称">
          <a-input
            v-model:value="formData.name"
            placeholder="请输入设置名称"
          />
        </a-form-item>
        <a-form-item name="value" label="设置值">
          <a-textarea
            v-model:value="formData.value"
            placeholder="请输入设置值"
            :rows="4"
          />
        </a-form-item>
        <a-form-item name="description" label="设置描述">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入设置描述"
            :rows="2"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, createVNode } from 'vue';
import {
  getSystemSettings,
  createSystemSetting,
  updateSystemSetting,
  deleteSystemSetting
} from '@/api/system/setting';
import { message, Modal } from 'ant-design-vue';
import { SearchFormCard } from '@/components/SearchForm';
import { PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';

import { useTablePagination } from '@/utils/common';

export default defineComponent({
  name: 'SystemSetting',
  components: {
    SearchFormCard
  },
  setup() {
    // 表格配置
    const actionConfig = ref({
      edit: true,
      delete: true
    });

    // 表格相关
    const loading = ref(false);
    const tableData = ref([]);
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '设置代码',
        dataIndex: 'code',
        width: 200,
      },
      {
        title: '设置名称',
        dataIndex: 'name',
        width: 200,
      },
      {
        title: '设置值',
        dataIndex: 'value',
        ellipsis: true,
      },
      {
        title: '设置描述',
        dataIndex: 'description',
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        width: 180,
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        width: 180,
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 180,
      },
    ];

    // 查询参数
    const queryParams = reactive({
      keyword: '',
    });

    // 搜索表单配置
    const searchFormItems = [
      {
        label: '关键词',
        field: 'keyword',
        type: 'input',
        placeholder: '请输入代码/名称/描述',
        width: '200px'
      }
    ];


    // 表单相关
    const formRef = ref(null);
    const isEdit = ref(false);
    const modalVisible = ref(false);
    const modalLoading = ref(false);
    const formData = reactive({
      id: undefined,
      code: '',
      name: '',
      value: '',
      description: '',
    });
    const formRules = {
      code: [{ required: true, message: '请输入设置代码', trigger: 'blur' }],
      name: [{ required: true, message: '请输入设置名称', trigger: 'blur' }],
    };
    const formTitle = computed(() => isEdit.value ? '编辑系统设置' : '新增系统设置');

    // 加载数据
    const loadData = async () => {
      try {
        loading.value = true;
        const res = await getSystemSettings({
          keyword: queryParams.keyword,
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
        });

        // 使用request封装后的响应结构
        tableData.value = res.list || [];
        // 更新分页信息
        updatePagination({
          total: res.total || 0,
          pageNum: res.pageNum || pagination.current,
          pageSize: res.pageSize || pagination.pageSize
        });
      } catch (error) {
        console.error('获取系统设置列表失败:', error);
        message.error('获取系统设置列表失败');
        // 发生错误时重置分页
        updatePagination({ total: 0, pageNum: 1 });
      } finally {
        loading.value = false;
      }
    };

    // 使用表格分页组合式函数
    const {
      pagination,
      handleTableChange,
      updatePagination,
      resetPagination
    } = useTablePagination({
      fetchData: loadData,
      initialPagination: { current: 1, pageSize: 10, total: 0 },
      searchForm: queryParams
    });

    // 查询
    const handleQuery = (values) => {
      if (values) {
        Object.assign(queryParams, values);
      }
      // 重置到第一页
      resetPagination();
      loadData();
    };

    // 重置查询
    const handleReset = () => {
      queryParams.keyword = '';
      // 重置到第一页
      resetPagination();
      loadData();
    };

    // 新增
    const handleAdd = () => {
      isEdit.value = false;
      resetForm();
      modalVisible.value = true;
    };

    // 编辑
    const handleEdit = (record) => {
      isEdit.value = true;
      resetForm();
      Object.assign(formData, record);
      modalVisible.value = true;
    };

    // 删除
    const handleDelete = (record) => {
      Modal.confirm({
        title: '确认删除',
        icon: createVNode(ExclamationCircleOutlined),
        content: `确认删除代码为 "${record.code}" 的系统设置吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          try {
            await deleteSystemSetting(record.id);
            message.success('删除成功');
            // 重新加载数据
            loadData();
          } catch (error) {
            console.error('删除系统设置失败:', error);
            message.error('删除失败');
          }
        },
      });
    };

    // 表单确认
    const handleModalOk = () => {
      formRef.value.validate().then(async () => {
        try {
          modalLoading.value = true;

          if (isEdit.value) {
            // 编辑
            await updateSystemSetting(formData.id, {
              name: formData.name,
              value: formData.value,
              description: formData.description,
            });

            message.success('更新成功');
            modalVisible.value = false;
            loadData();
          } else {
            // 新增
            await createSystemSetting(formData);

            message.success('创建成功');
            modalVisible.value = false;
            loadData();
          }
        } catch (error) {
          console.error('保存系统设置失败:', error);
          message.error('保存失败');
        } finally {
          modalLoading.value = false;
        }
      }).catch(error => {
        console.log('表单验证失败:', error);
      });
    };

    // 重置表单
    const resetForm = () => {
      formData.id = undefined;
      formData.code = '';
      formData.name = '';
      formData.value = '';
      formData.description = '';
      if (formRef.value) {
        formRef.value.resetFields();
      }
    };

    // 初始化
    onMounted(() => {
      loadData();
    });

    return {
      // 表格相关
      loading,
      tableData,
      columns,

      // 查询相关
      queryParams,
      handleQuery,
      handleTableChange,

      // 操作相关
      handleAdd,
      handleEdit,
      handleDelete,

      // 表单相关
      formRef,
      isEdit,
      modalVisible,
      modalLoading,
      formData,
      formRules,
      formTitle,
      handleModalOk,
      // 搜索表单相关
      searchFormItems,
      handleReset,
      actionConfig
    };
  },
});
</script>

<style scoped>

.table-area {
  overflow-x: auto;
}

.action-btns {
  display: flex;
  justify-content: space-around;
  width: 100%;
  flex-wrap: nowrap;
}

/* 固定列样式优化 */
:deep(.ant-table-cell-fix-right) {
  background-color: #fff;
  padding: 8px 4px !important;
}

:deep(.ant-table-cell-fix-right .action-btns .ant-divider) {
  margin: 0 4px;
}
</style>
