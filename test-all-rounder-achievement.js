/**
 * 测试全能力者成就功能
 */
require('dotenv').config();
const { processAllRounderAchievement } = require('./server/src/utils/achievementUtils');

// 模拟用户信息
const userId = 1;  // 替换为实际用户ID
const openId = 'test_openid';  // 替换为实际openId
const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID || '1';

async function testAllRounderAchievement() {
  console.log('开始测试全能力者成就功能...');
  
  try {
    // 调用全能力者成就检测函数
    await processAllRounderAchievement(userId, openId, enterpriseId);
    console.log('测试完成！');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 执行测试
testAllRounderAchievement(); 