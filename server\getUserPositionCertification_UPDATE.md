# getUserPositionCertification 方法更新说明

## 修改内容
为 `getUserPositionCertification` 方法添加 `positionId` 参数，并基于该岗位ID查询相关信息。

## 修改位置
- **文件**: `server/src/controllers/weichat/wechatExamListController.js`
- **方法**: `getUserPositionCertification`
- **路由**: `/api/wechat/exam/position-certification?positionId=xxx`

## 核心变化

### 1. 新增参数验证
```javascript
const { positionId } = req.query; // 从查询参数获取positionId

if (!positionId) {
    return res.status(400).json({
        code: 400,
        message: '参数错误：缺少positionId',
    });
}
```

### 2. 验证岗位归属
```javascript
// 验证positionId是否为员工的晋升岗位
const employeePosition = await EmployeePosition.findOne({
    where: {
        employeeId: employee.id,
        enterpriseId
    },
    include: [
        {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name', 'code', 'typeId'],
            where: {
                id: positionId  // 通过关联查询验证岗位ID
            },
            required: true  // 必须匹配，否则返回null
        },
        {
            model: Level,
            as: 'level',
            attributes: ['id', 'name', 'code', 'orderNum'],
            required: false
        }
    ]
});

if (!employeePosition) {
    return res.status(400).json({
        code: 400,
        message: '该岗位不是员工的晋升岗位'
    });
}
```

**注意**：由于 `EmployeePosition` 表没有直接的 `positionNameId` 字段，我们通过关联查询 `PositionName` 表来验证岗位ID是否匹配。

## 修改后的查询逻辑

### 1. 基于positionId获取岗位和等级信息
```javascript
// 修改前：基于员工默认岗位
const { currentLevel, targetLevelId, targetLevelName, positionName } =
    await getPositionAndLevelInfo(employee, enterpriseId);

// 修改后：基于指定的positionId
const positionName = employeePosition.positionName?.name || '';
const currentLevel = employeePosition.level;
const targetLevelId = employeePosition.levelId;
const targetLevelName = currentLevel?.name || '';
```

### 2. 基于positionId查询考试配置
```javascript
// 修改前：基于员工默认岗位
const examConfigs = await getExamConfigs(employee, targetLevelId, enterpriseId);

// 修改后：基于指定的positionId
const examConfigs = await ExamConfig.findAll({
    where: {
        enterpriseId,
        positionName: positionId, // 使用传入的positionId
        positionLevel: targetLevelId.toString(),
        status: '必考'
    }
});
```

### 3. 基于positionId查询晋升时间记录
```javascript
// 修改前：基于员工默认岗位
const promotionRecord = await findEmployeePromotionTime(employee, enterpriseId);

// 修改后：基于指定的positionId
const promotionRecord = await EmployeePromotion.findOne({
    where: {
        employeeId: employee.id,
        enterpriseId,
        positionId: positionId, // 使用传入的positionId
        levelId: targetLevelId,
        positionTypeId: employeePosition.positionTypeId
    },
    attributes: ['id', 'promotionTime', 'positionId', 'levelId', 'positionTypeId', 'createTime'],
    order: [['promotionTime', 'DESC']]
});
```

### 4. 基于positionId查询证书数量
```javascript
// 修改前：基于员工默认岗位
const certificateCount = await CertificateRecord.count({
    where: {
        enterpriseId,
        openId,
        positionName: employee.positionId, // 员工默认岗位
        positionLevel: targetLevelId,
        delFlag: 0
    }
});

// 修改后：基于指定的positionId
const certificateCount = await CertificateRecord.count({
    where: {
        enterpriseId,
        openId,
        positionName: positionId, // 使用传入的positionId
        positionLevel: targetLevelId,
        delFlag: 0
    }
});
```

### 5. 基于positionId获取考试科目信息和状态
```javascript
// 修改前：基于员工默认岗位
const { examResults } = await getExamSubjectsAndStatus(examConfigs, openId, enterpriseId, employee.positionId, targetLevelId);

// 修改后：基于指定的positionId
const { examResults } = await getExamSubjectsAndStatus(finalExamConfigs, openId, enterpriseId, positionId, targetLevelId);
```

## 请求参数变化

### 修改前
```
GET /api/wechat/exam/position-certification
Headers: { "openid": "用户openId" }
```

### 修改后
```
GET /api/wechat/exam/position-certification?positionId=岗位ID
Headers: { "openid": "用户openId" }
```

## 错误处理

### 新增错误码
- `400`: 参数错误：缺少positionId
- `400`: 该岗位不是员工的晋升岗位
- `400`: 岗位等级信息不完整
- `404`: 未找到员工信息或员工未激活

## 业务逻辑优势

### 1. 精确性
- 基于具体的岗位ID查询，而不是员工的默认岗位
- 确保查询结果与指定岗位完全匹配

### 2. 安全性
- 验证岗位ID必须是员工的晋升岗位
- 防止查询其他员工的岗位信息

### 3. 灵活性
- 支持员工查询任意一个自己的晋升岗位认证信息
- 适配多岗位员工的需求

## 使用场景
前端可以通过传入具体的岗位ID，获取该岗位的认证信息，包括考试配置、证书数量、晋升记录等，适用于员工有多个晋升岗位的场景。 