-- 成就模板初始化数据
-- 注意：请根据实际的企业ID替换 enterprise_id 字段的值

INSERT INTO achievement_templates (enterprise_id, name, description, icon, category, rule_type, trigger_condition, reward_points, sort, is_active, created_time, updated_time) VALUES
-- 1. 初入宝殿
(1, '初入宝殿', '首次学习一门科目的进度达到100%', '/icons/achievement/first_complete.png', 'learning', 'progress', '{"type":"first_complete","rule":"初入宝殿","progress":100}', 10, 1, 1, NOW(), NOW()),

-- 2. 知识探索
(1, '知识探索', '完成5门科目的进度达到100%', '/icons/achievement/multiple_complete.png', 'learning', 'progress', '{"type":"multiple_complete","rule":"知识探索","count":5,"progress":100}', 20, 2, 1, NOW(), NOW()),

-- 3. 学霸模式
(1, '学霸模式', '连续学习超过10天', '/icons/achievement/study_streak.png', 'time', 'streak', '{"type":"study_streak","rule":"学霸模式","days":10}', 30, 3, 1, NOW(), NOW()),

-- 4. 学无止境
(1, '学无止境', '累计学习时长超过50小时', '/icons/achievement/study_time.png', 'time', 'time', '{"type":"study_time","rule":"学无止境","hours":50}', 40, 4, 1, NOW(), NOW()),

-- 5. 碎片时间大师
(1, '碎片时间大师', '一天内0～12点、12～18点、18～24点三个时间范围都有练习记录', '/icons/achievement/time_master.png', 'time', 'time', '{"type":"time_master","rule":"碎片时间大师","start1":0,"end1":12,"start2":12,"end2":18,"start3":18,"end3":24}', 50, 5, 1, NOW(), NOW()),

-- 6. 全能力者
(1, '全能力者', '所有岗位都有练习记录', '/icons/achievement/all_position.png', 'practice', 'progress', '{"type":"all_position_practice","rule":"全能力者"}', 60, 6, 1, NOW(), NOW()),

-- 7. 金牌毕业生
(1, '金牌毕业生', '所有考试都是通过率100%', '/icons/achievement/all_pass.png', 'exam', 'progress', '{"type":"all_pass","rule":"金牌毕业生","passRate":100}', 70, 7, 1, NOW(), NOW()),

-- 8. 早起鸟
(1, '早起鸟', '4点~7点之间有练习记录，连续5天', '/icons/achievement/early_bird.png', 'time', 'streak', '{"type":"early_bird","rule":"早起鸟","startHour":4,"endHour":7,"days":5}', 25, 8, 1, NOW(), NOW()),

-- 9. 夜猫子
(1, '夜猫子', '22点~2点之间有练习记录，连续5天', '/icons/achievement/night_owl.png', 'time', 'streak', '{"type":"night_owl","rule":"夜猫子","startHour":22,"endHour":2,"days":5}', 25, 9, 1, NOW(), NOW()),

-- 10. 旗开得胜
(1, '旗开得胜', '获得第一个考试满分', '/icons/achievement/first_perfect.png', 'exam', 'count', '{"type":"first_perfect_score","rule":"旗开得胜"}', 80, 10, 1, NOW(), NOW()); 