<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchForm" 
          :items="formItems"
          :key="formKey"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <template #actions>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><plus-outlined /></template>
            新增岗位等级
          </a-button>
        </a-space>
      </template>
    </page-header>
    
    <!-- 列表区域 -->
    <base-table
      :columns="columns"
      :data-source="levelList"
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      @edit="handleEdit"
      @delete="handleDelete"
      :action-config="actionConfig"
      :delete-title="deleteTitle"
    >
      <template #bodyCell="{ column, record }">
        <!-- 可以在这里添加自定义列内容 -->
        <template v-if="column.key === 'orderNum'">
          <a-tag color="blue">{{ record.orderNum }}</a-tag>
        </template>
      </template>
    </base-table>
    
    <!-- 添加/编辑岗位等级名称弹窗 -->
    <a-modal
      :title="modalType === 'add' ? '新增岗位等级' : '编辑岗位等级'"
      :visible="modalVisible"
      :confirm-loading="modalLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      width="500px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="岗位等级名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入岗位等级名称" />
        </a-form-item>
        <a-form-item label="英文缩写" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入英文缩写" />
        </a-form-item>
        <a-form-item label="等级顺序" name="orderNum">
          <a-input-number
            v-model:value="formData.orderNum"
            placeholder="请输入等级顺序"
            style="width: 100%"
            :min="0"
            :precision="0"
            class="custom-number-input"
          />
          <div class="help-text">数值越大越靠前显示</div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { getLevelList, getLevelDetail, addLevel, updateLevel, deleteLevel } from '@/api/organization/level';
import { SearchFormCard } from '@/components/SearchForm';

import BaseTable from '@/components/BaseTable';
import { useTablePagination } from '@/utils/common';

// 查询表单
const searchForm = reactive({
  name: '',
  pageNum: 1,
  pageSize: 10
});

// 表格操作配置
const actionConfig = {
  edit: true,
  delete: true
};

// 删除确认标题
const deleteTitle = '确定删除该岗位等级名称吗?';

// 添加key值用于强制重新渲染搜索表单
const formKey = ref(0);

// 岗位等级名称选项
const levelOptions = ref([]);

// 搜索表单配置
const formItems = computed(() => {
  return [
    {
      label: '岗位等级名称',
      field: 'name',
      type: 'select',
      placeholder: '请选择岗位等级名称',
      width: '200px',
      options: levelOptions.value,
      selectLabel: 'name',
      selectValue: 'name'
    }
  ];
});

// 表格列定义
const columns = [
  // {
  //   title: '序号',
  //   dataIndex: 'index',
  //   key: 'index',
  //   width: '80px',
  //   customRender: ({ index }) => {
  //     return index + 1 + (pagination.current - 1) * pagination.pageSize;
  //   }
  // },
  {
    title: '岗位等级名称',
    dataIndex: 'name',
    key: 'name',
    width: '15%',
    ellipsis: true
  },
  {
    title: '岗位等级缩写',
    dataIndex: 'code',
    key: 'code',
    width: '15%',
    ellipsis: true
  },
  {
    title: '等级顺序',
    dataIndex: 'orderNum',
    key: 'orderNum',
    width: '15%',
    ellipsis: true
  },
  {
    title: '操作人',
    dataIndex: ['operator', 'nickname'],
    key: 'operator',
    width: '10%',
    ellipsis: true,
    customRender: ({ record }) => {
      return record.operator?.nickname || '-';
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: '15%',
    sorter: true
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: '15%',
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: '150px'
  }
];

// 数据与状态
const loading = ref(false);
const levelList = ref([]);
const total = ref(0);

// 获取岗位等级名称列表
const fetchLevelList = async () => {
  try {
    loading.value = true;
    // 构造查询参数
    const params = {
      name: searchForm.name,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    };
    
    const result = await getLevelList(params);
    levelList.value = result.rows;
    total.value = result.total;
    
    // 更新分页信息
    updatePagination({
      total: result.total,
      current: pagination.current,
      pageSize: pagination.pageSize
    });
    
    // 更新下拉选项
    levelOptions.value = result.rows;
    
    // 更新key值强制重新渲染表单
    formKey.value += 1;
    
  } catch (error) {
    console.error('获取岗位等级名称列表失败:', error);
    message.error('获取岗位等级名称列表失败');
  } finally {
    loading.value = false;
  }
};

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchLevelList,
  initialPagination: { 
    current: 1, 
    pageSize: 10, 
    total: 0,
    showTotal: (total) => `共 ${total} 条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
  },
  searchForm
});

// 查询
const handleSearch = (values) => {
  // 如果有values参数，更新form
  if (values) {
    Object.assign(searchForm, values);
  }
  // 重置到第一页并加载数据
  resetPagination();
  fetchLevelList();
};

// 重置查询
const resetSearch = (values) => {
  // 如果有values参数，更新form
  if (values) {
    Object.assign(searchForm, values);
  } else {
    searchForm.name = '';
  }
  // 重置分页并加载数据
  resetPagination();
  fetchLevelList();
};

// 模态框相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const modalType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formData = reactive({
  id: null,
  name: '',
  code: '',
  orderNum: 0,
  remark: ''
});
const formRules = {
  name: [
    { required: true, message: '请输入岗位等级名称名称', trigger: 'blur' },
    { max: 50, message: '岗位等级名称名称不能超过50个字符', trigger: 'blur' }
  ],
  code: [
    { max: 30, message: '等级编码不能超过30个字符', trigger: 'blur' }
  ],
  orderNum: [
    { type: 'number', message: '等级顺序必须为数字', trigger: 'blur' }
  ]
};

// 显示添加模态框
const showAddModal = () => {
  modalType.value = 'add';
  formData.id = null;
  formData.name = '';
  formData.code = '';
  formData.orderNum = 0;
  formData.remark = '';
  modalVisible.value = true;
};

// 编辑
const handleEdit = async (record) => {
  try {
    loading.value = true;
    const result = await getLevelDetail(record.id);
    modalType.value = 'edit';
    formData.id = result.id;
    formData.name = result.name;
    formData.code = result.code;
    formData.orderNum = result.orderNum;
    formData.remark = result.remark;
    modalVisible.value = true;
  } catch (error) {
    console.error('获取岗位等级名称详情失败:', error);
    message.error('获取岗位等级名称详情失败');
  } finally {
    loading.value = false;
  }
};

// 删除
const handleDelete = async (record) => {
  try {
    loading.value = true;
    await deleteLevel(record.id);
    message.success('删除成功');
    if (levelList.value.length === 1 && pagination.current > 1) {
      pagination.current -= 1;
    }
    fetchLevelList();
  } catch (error) {
    console.error('删除失败:', error);
    // 检查是否是因为关联员工导致的删除失败
    if (error.response && error.response.status === 400) {
      if (error.response.data && error.response.data.message) {
        message.error(error.response.data.message);
      } else {
        message.error('该岗位等级名称下存在员工，请先解除关联后再删除');
      }
    } else {
      message.error('删除失败');
    }
  } finally {
    loading.value = false;
  }
};

// 确认添加/编辑
const handleOk = () => {
  formRef.value.validate().then(async () => {
    try {
      modalLoading.value = true;
      if (modalType.value === 'add') {
        // 添加岗位等级名称
        await addLevel({
          name: formData.name,
          code: formData.code,
          orderNum: formData.orderNum,
        });
        message.success('添加成功');
      } else {
        // 编辑岗位等级名称
        await updateLevel({
          id: formData.id,
          name: formData.name,
          code: formData.code,
          orderNum: formData.orderNum,
        });
        message.success('编辑成功');
      }
      modalVisible.value = false;
      fetchLevelList();
    } catch (error) {
      console.error(`${modalType.value === 'add' ? '添加' : '编辑'}失败:`, error);
      if (error.response && error.response.data && error.response.data.message) {
        message.error(error.response.data.message);
      } else {
        message.error(`${modalType.value === 'add' ? '添加' : '编辑'}失败`);
      }
    } finally {
      modalLoading.value = false;
    }
  }).catch(error => {
    console.log('表单验证失败', error);
  });
};

// 取消添加/编辑
const handleCancel = () => {
  modalVisible.value = false;
};

// 组件挂载后加载数据
onMounted(() => {
  fetchLevelList();
});
</script>

<style scoped>

/* 添加自定义表单项样式 */
.custom-form-item {
  margin-right: 16px;
}

:deep(.ant-select) {
  min-width: 200px;
}

.custom-number-input {
  width: 100%;
}

.help-text {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

:deep(.ant-table-fixed-right) {
  background-color: #fff;
}

/* 添加响应式布局样式 */
@media (max-width: 768px) {
  .page-container {
    padding: 8px;
  }
  
  :deep(.ant-select) {
    min-width: 120px;
  }
}
</style>