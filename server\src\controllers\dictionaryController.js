const { DictionaryType, DictionaryData } = require('../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

/**
 * 获取字典类型列表
 */
exports.getDictionaryTypeList = async (req, res) => {
  try {
    const { typeCode, typeName, status, pageNum = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const whereCondition = {};
    if (typeCode) {
      whereCondition.typeCode = {
        [Op.like]: `%${typeCode}%`
      };
    }
    if (typeName) {
      whereCondition.typeName = {
        [Op.like]: `%${typeName}%`
      };
    }
    if (status !== undefined && status !== '') {
      whereCondition.status = status === 'true';
    }
    
    // 查询数据，应用企业ID过滤
    const { count, rows } = await DictionaryType.findAndCountAll(
      addEnterpriseFilter({
        where: whereCondition,
        order: [['createTime', 'DESC']],
        offset: (pageNum - 1) * pageSize,
        limit: parseInt(pageSize)
      })
    );
    
    res.json({
      code: 200,
      message: '获取字典类型列表成功',
      data: {
        total: count,
        rows
      }
    });
  } catch (error) {
    console.error('获取字典类型列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取字典类型列表失败',
      error: error.message
    });
  }
};

/**
 * 获取字典类型详情
 */
exports.getDictionaryTypeDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 应用企业ID过滤
    const dictType = await DictionaryType.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!dictType) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在'
      });
    }
    
    res.json({
      code: 200,
      message: '获取字典类型详情成功',
      data: dictType
    });
  } catch (error) {
    console.error('获取字典类型详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取字典类型详情失败',
      error: error.message
    });
  }
};

/**
 * 创建字典类型
 */
exports.createDictionaryType = async (req, res) => {
  try {
    const { typeCode, typeName, status, remark } = req.body;
    
    // 检查字典类型编码是否存在，应用企业ID过滤
    const existType = await DictionaryType.findOne(
      addEnterpriseFilter({
        where: { typeCode }
      })
    );
    
    if (existType) {
      return res.status(400).json({
        code: 400,
        message: '字典类型编码已存在'
      });
    }
    
    // 创建字典类型，添加企业ID
    const dictType = await DictionaryType.create(
      addEnterpriseId({
        typeCode,
        typeName,
        status: status === undefined ? true : status,
        remark,
        createBy: req.user?.username || 'system',
        createTime: new Date()
      })
    );
    
    res.json({
      code: 200,
      message: '创建字典类型成功',
      data: dictType
    });
  } catch (error) {
    console.error('创建字典类型失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建字典类型失败',
      error: error.message
    });
  }
};

/**
 * 更新字典类型
 */
exports.updateDictionaryType = async (req, res) => {
  try {
    const { id, typeCode, typeName, status, remark } = req.body;
    
    // 检查字典类型是否存在，应用企业ID过滤
    const dictType = await DictionaryType.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!dictType) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在'
      });
    }
    
    // 如果修改了typeCode，检查是否与其他记录冲突，应用企业ID过滤
    if (typeCode !== dictType.typeCode) {
      const existType = await DictionaryType.findOne(
        addEnterpriseFilter({
          where: { 
            typeCode,
            id: { [Op.ne]: id } 
          }
        })
      );
      
      if (existType) {
        return res.status(400).json({
          code: 400,
          message: '字典类型编码已存在'
        });
      }
    }
    
    // 更新字典类型
    await dictType.update({
      typeCode,
      typeName,
      status,
      remark,
      updateBy: req.user?.username || 'system',
      updateTime: new Date()
    });
    
    res.json({
      code: 200,
      message: '更新字典类型成功',
      data: dictType
    });
  } catch (error) {
    console.error('更新字典类型失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新字典类型失败',
      error: error.message
    });
  }
};

/**
 * 删除字典类型
 */
exports.deleteDictionaryType = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查字典类型是否存在，应用企业ID过滤
    const dictType = await DictionaryType.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!dictType) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在'
      });
    }
    
    // 检查是否有关联的字典数据，添加企业ID过滤
    const dataCount = await DictionaryData.count(
      addEnterpriseFilter({
        where: { typeId: id }
      })
    );
    
    if (dataCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '该字典类型下有关联的字典数据，无法删除'
      });
    }
    
    // 删除字典类型
    await dictType.destroy();
    
    res.json({
      code: 200,
      message: '删除字典类型成功'
    });
  } catch (error) {
    console.error('删除字典类型失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除字典类型失败',
      error: error.message
    });
  }
};

/**
 * 获取字典数据列表
 */
exports.getDictionaryDataList = async (req, res) => {
  try {
    const { typeId, dictLabel, dictValue, status, pageNum = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const whereCondition = {};
    if (typeId) {
      whereCondition.typeId = typeId;
    }
    if (dictLabel) {
      whereCondition.dictLabel = {
        [Op.like]: `%${dictLabel}%`
      };
    }
    if (dictValue) {
      whereCondition.dictValue = {
        [Op.like]: `%${dictValue}%`
      };
    }
    if (status !== undefined && status !== '') {
      whereCondition.status = status === 'true';
    }
    
    // 查询数据，添加企业ID过滤
    const { count, rows } = await DictionaryData.findAndCountAll(
      addEnterpriseFilter({
        where: whereCondition,
        order: [['dictSort', 'ASC'], ['createTime', 'DESC']],
        offset: (pageNum - 1) * pageSize,
        limit: parseInt(pageSize),
        include: [{
          model: DictionaryType,
          attributes: ['typeCode', 'typeName']
        }]
      })
    );
    
    res.json({
      code: 200,
      message: '获取字典数据列表成功',
      data: {
        total: count,
        rows
      }
    });
  } catch (error) {
    console.error('获取字典数据列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取字典数据列表失败',
      error: error.message
    });
  }
};

/**
 * 通过字典类型编码获取字典数据
 */
exports.getDictionaryDataByTypeCode = async (req, res) => {
  try {
    const { typeCode } = req.params;
    
    // 获取字典类型，添加企业ID过滤
    const dictType = await DictionaryType.findOne(
      addEnterpriseFilter({
        where: { typeCode }
      })
    );
    
    if (!dictType) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在'
      });
    }
    
    // 获取字典数据，添加企业ID过滤
    const dictData = await DictionaryData.findAll(
      addEnterpriseFilter({
        where: { 
          typeId: dictType.id,
          status: true 
        },
        order: [['dictSort', 'ASC'], ['createTime', 'DESC']]
      })
    );
    
    res.json({
      code: 200,
      message: '获取字典数据成功',
      data: dictData
    });
  } catch (error) {
    console.error('获取字典数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取字典数据失败',
      error: error.message
    });
  }
};

/**
 * 获取字典数据详情
 */
exports.getDictionaryDataDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 添加企业ID过滤
    const dictData = await DictionaryData.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [{
          model: DictionaryType,
          attributes: ['typeCode', 'typeName']
        }]
      })
    );
    
    if (!dictData) {
      return res.status(404).json({
        code: 404,
        message: '字典数据不存在'
      });
    }
    
    res.json({
      code: 200,
      message: '获取字典数据详情成功',
      data: dictData
    });
  } catch (error) {
    console.error('获取字典数据详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取字典数据详情失败',
      error: error.message
    });
  }
};

/**
 * 创建字典数据
 */
exports.createDictionaryData = async (req, res) => {
  try {
    const { 
      typeId, 
      dictLabel, 
      dictValue, 
      dictSort, 
      cssClass, 
      listClass, 
      isDefault, 
      status, 
      remark 
    } = req.body;
    
    // 检查字典类型是否存在，添加企业ID过滤
    const dictType = await DictionaryType.findOne(
      addEnterpriseFilter({
        where: { id: typeId }
      })
    );
    if (!dictType) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在'
      });
    }
    
    // 创建字典数据，添加企业ID
    const dictData = await DictionaryData.create(
      addEnterpriseId({
        typeId,
        dictLabel,
        dictValue,
        dictSort: dictSort || 0,
        cssClass,
        listClass,
        isDefault: isDefault === undefined ? false : isDefault,
        status: status === undefined ? true : status,
        remark,
        createBy: req.user?.username || 'system',
        createTime: new Date()
      })
    );
    
    res.json({
      code: 200,
      message: '创建字典数据成功',
      data: dictData
    });
  } catch (error) {
    console.error('创建字典数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建字典数据失败',
      error: error.message
    });
  }
};

/**
 * 更新字典数据
 */
exports.updateDictionaryData = async (req, res) => {
  try {
    const { 
      id,
      typeId, 
      dictLabel, 
      dictValue, 
      dictSort, 
      cssClass, 
      listClass, 
      isDefault, 
      status, 
      remark 
    } = req.body;
    
    // 检查字典数据是否存在，添加企业ID过滤
    const dictData = await DictionaryData.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    if (!dictData) {
      return res.status(404).json({
        code: 404,
        message: '字典数据不存在'
      });
    }
    
    // 如果修改了typeId，检查类型是否存在，添加企业ID过滤
    if (typeId !== dictData.typeId) {
      const dictType = await DictionaryType.findOne(
        addEnterpriseFilter({
          where: { id: typeId }
        })
      );
      if (!dictType) {
        return res.status(404).json({
          code: 404,
          message: '字典类型不存在'
        });
      }
    }
    
    // 更新字典数据
    await dictData.update({
      typeId,
      dictLabel,
      dictValue,
      dictSort: dictSort || 0,
      cssClass,
      listClass,
      isDefault,
      status,
      remark,
      updateBy: req.user?.username || 'system',
      updateTime: new Date()
    });
    
    res.json({
      code: 200,
      message: '更新字典数据成功',
      data: dictData
    });
  } catch (error) {
    console.error('更新字典数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新字典数据失败',
      error: error.message
    });
  }
};

/**
 * 删除字典数据
 */
exports.deleteDictionaryData = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查字典数据是否存在，添加企业ID过滤
    const dictData = await DictionaryData.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    if (!dictData) {
      return res.status(404).json({
        code: 404,
        message: '字典数据不存在'
      });
    }
    
    // 删除字典数据
    await dictData.destroy();
    
    res.json({
      code: 200,
      message: '删除字典数据成功'
    });
  } catch (error) {
    console.error('删除字典数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除字典数据失败',
      error: error.message
    });
  }
};

/**
 * 批量更新字典类型状态
 */
exports.batchUpdateDictionaryStatus = async (req, res) => {
  try {
    const { ids, status } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请选择要操作的字典类型'
      });
    }
    
    // 更新字典类型状态
    await DictionaryType.update(
      { 
        status, 
        updateBy: req.user?.username || 'system',
        updateTime: new Date() 
      },
      { 
        where: { id: { [Op.in]: ids } } 
      }
    );
    
    res.json({
      code: 200,
      message: `批量${status ? '启用' : '禁用'}成功`
    });
  } catch (error) {
    console.error('批量更新字典类型状态失败:', error);
    res.status(500).json({
      code: 500,
      message: '批量更新字典类型状态失败',
      error: error.message
    });
  }
}; 