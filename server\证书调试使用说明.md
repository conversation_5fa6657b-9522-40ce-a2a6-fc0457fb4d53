# 餐烤证书生成与调试说明

## 可用接口

现在提供了三个证书生成接口：

### 1. 正常版本（推荐正式使用）
- **接口**: `POST /api/wechat/certificate/generate`
- **用途**: 生成正式的证书，无调试信息

### 2. 调试版本（用于查看位置）
- **接口**: `POST /api/wechat/certificate/generate-debug`  
- **用途**: 生成带网格线和坐标标注的证书，方便查看文字位置

### 3. 配置版本（用于精确调整）
- **接口**: `POST /api/wechat/certificate/generate-config`
- **用途**: 使用配置文件生成证书，方便动态调整坐标

## 调试步骤

### 第一步：生成调试版证书
```bash
# 运行调试版本
curl -X POST http://localhost:3000/api/wechat/certificate/generate-debug \
  -H "Content-Type: application/json" \
  -d '{
    "name": "姜子介",
    "courseName": "预定流程标准",
    "position": "服务员（p4级）",
    "certificateNo": "C2505493172",
    "issueDate": "2025-05-28",
    "validDate": "2025-08-26"
  }'
```

### 第二步：查看调试信息
调试版证书包含：
- ✅ **红色网格线**：每50像素一条，方便定位
- ✅ **坐标标注**：每100像素显示X/Y坐标值
- ✅ **文字位置标注**：蓝色/黄色/绿色文字显示每个元素的坐标
- ✅ **画布尺寸**：左上角显示总尺寸和中心点

### 第三步：根据效果图调整坐标
打开 `certificate_positions.json` 文件，修改坐标：

```json
{
  "certificatePositions": {
    "name": {
      "y": 580,  // 修改这里调整姓名的垂直位置
      "fontSize": 60  // 修改字体大小
    },
    "congratsText": {
      "y": 680,  // 修改恭喜词起始位置
      "maxWidth": 650,  // 修改最大宽度
      "lineHeight": 40  // 修改行间距
    }
    // ... 其他配置
  }
}
```

### 第四步：使用配置文件生成
```bash
# 使用配置文件生成
curl -X POST http://localhost:3000/api/wechat/certificate/generate-config \
  -H "Content-Type: application/json" \
  -d '{
    "name": "姜子介",
    "courseName": "预定流程标准",
    "position": "服务员（p4级）",
    "certificateNo": "C2505493172",
    "issueDate": "2025-05-28",
    "validDate": "2025-08-26"
  }'
```

## 坐标系统说明

- **原点**: 左上角 (0, 0)
- **X轴**: 向右递增
- **Y轴**: 向下递增
- **centerX**: 自动计算的画布中心点X坐标

## 主要元素位置（当前配置）

| 元素 | X坐标 | Y坐标 | 字体大小 | 颜色 | 说明 |
|------|-------|-------|----------|------|------|
| 姓名 | centerX | 580 | 60px | 黑色 | 居中显示 |
| 恭喜词 | centerX | 680 | 26px | 黑色 | 自动换行 |
| 职位 | centerX | 900 | 22px | 白色 | 棕色区域 |
| 证书编号 | centerX | 1000 | 28px | 黑色 | 居中显示 |
| 颁发日期 | 580 | 1150 | 18px | #988A79 | 左对齐 |
| 有效期 | 580 | 1180 | 18px | #988A79 | 左对齐 |

## 文件位置

- **背景图**: `server/uploads/餐烤证书背景图.jpg`
- **效果图**: `server/uploads/餐烤证书效果图.jpg`
- **配置文件**: `server/certificate_positions.json`
- **生成证书**: `server/uploads/certificates/`

## 调试技巧

1. **对比效果图**：将生成的调试版证书与效果图对比
2. **使用网格线**：根据红色网格线确定目标位置
3. **逐步调整**：每次只调整一个元素的位置
4. **保存备份**：调整前备份配置文件
5. **测试不同内容**：用不同长度的姓名和课程名测试

## 常见问题

**Q: 文字位置不准确怎么办？**
A: 使用调试版接口查看当前坐标，然后在配置文件中调整

**Q: 恭喜词换行不合适怎么办？**
A: 调整 `maxWidth` 和 `lineHeight` 参数

**Q: 颜色显示不正确怎么办？**
A: 检查颜色值格式，确保使用正确的十六进制颜色代码

**Q: 修改配置后不生效怎么办？**
A: 使用配置版接口 `/certificate/generate-config`，它会重新读取配置文件 