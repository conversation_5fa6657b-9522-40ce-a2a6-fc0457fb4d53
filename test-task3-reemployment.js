/**
 * 任务3：员工在职功能测试脚本
 * 
 * 测试场景：
 * 输入：在职接口：/api/organization/employee，传参：{"id":69,"status":"1"}
 * 输出：employee_career_record 新增一条记录
 */

const axios = require('axios');

// 配置
const config = {
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    // 这里需要添加实际的认证token
    'Authorization': 'Bearer your-token-here'
  }
};

// 测试数据
const testData = {
  employeeId: 69,  // 测试员工ID
  status: "1"      // 在职状态
};

/**
 * 测试员工在职功能
 */
async function testEmployeeReemployment() {
  console.log('🚀 开始测试任务3：员工在职功能');
  console.log('='.repeat(60));
  
  try {
    console.log('\n📋 测试参数:');
    console.log(`员工ID: ${testData.employeeId}`);
    console.log(`状态: ${testData.status} (在职)`);
    
    console.log('\n📡 发送在职请求...');
    const response = await axios.put(
      `${config.baseURL}/organization/employee`,
      {
        id: testData.employeeId,
        status: testData.status
      },
      {
        headers: config.headers,
        timeout: config.timeout
      }
    );
    
    if (response.status === 200) {
      console.log('\n✅ 在职请求成功!');
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
      console.log('\n📊 预期结果:');
      console.log('1. ✅ 员工状态更新为在职(1)');
      console.log('2. ✅ employee_career_record 新增一条记录');
      console.log('   - status: 1 (在职)');
      console.log('   - entry_time: 员工入职时间或当前时间');
      console.log('   - departure_time: null');
      console.log('   - departure_reason: null');
      
      console.log('\n🔍 建议验证步骤:');
      console.log('1. 检查数据库中 org_employee 表的员工状态');
      console.log('2. 验证 employee_career_record 表是否新增记录');
      console.log('3. 确认履历记录的字段值是否正确');
      console.log('4. 检查事务是否正确提交');
      
    } else {
      console.log('\n❌ 在职请求失败');
      console.log('状态码:', response.status);
      console.log('响应数据:', response.data);
    }
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', error.response.data);
    }
    
    console.log('\n🔧 可能的问题:');
    console.log('1. 员工ID不存在或已经是在职状态');
    console.log('2. 认证token无效或过期');
    console.log('3. 服务器连接问题');
    console.log('4. 数据库连接或事务问题');
  }
}

/**
 * 显示SQL验证查询
 */
function showVerificationQueries() {
  console.log('\n📝 数据库验证查询:');
  console.log(`
-- 检查员工状态
SELECT id, name, status, entry_time FROM org_employee WHERE id = ${testData.employeeId};

-- 检查履历记录
SELECT * FROM employee_career_record WHERE employee_id = ${testData.employeeId} ORDER BY create_time DESC;

-- 检查最新履历记录详情
SELECT 
  id,
  employee_id,
  status,
  entry_time,
  departure_time,
  departure_reason,
  create_time,
  create_by
FROM employee_career_record 
WHERE employee_id = ${testData.employeeId} 
ORDER BY create_time DESC 
LIMIT 1;
  `);
}

// 运行测试
async function runTest() {
  await testEmployeeReemployment();
  showVerificationQueries();
  
  console.log('\n' + '='.repeat(60));
  console.log('📋 任务3测试完成');
  console.log('请根据上述验证查询检查数据库中的实际结果');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = {
  testEmployeeReemployment,
  showVerificationQueries,
  testData
};
