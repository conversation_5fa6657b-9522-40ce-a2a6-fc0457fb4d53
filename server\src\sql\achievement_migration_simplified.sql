-- 简化版成就模板表
-- 只保留核心字段：成就名称、成就条件、成就图标
CREATE TABLE IF NOT EXISTS `achievement_templates_simple` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '成就模板ID',
  `enterprise_id` int NOT NULL DEFAULT '1' COMMENT '企业ID',
  `name` varchar(100) NOT NULL COMMENT '成就名称',
  `description` text COMMENT '成就条件描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '成就图标路径',
  `category` varchar(50) NOT NULL DEFAULT 'learning' COMMENT '成就分类：learning-学习类, time-时间类, exam-考试类, practice-练习类',
  `rule_type` varchar(50) NOT NULL COMMENT '规则类型：progress-进度类, time-时间类, count-计数类, streak-连续类',
  `trigger_condition` text NOT NULL COMMENT '触发条件JSON字符串',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='简化版成就模板表';

-- 如果你想要修改现有表，可以执行以下ALTER语句删除不需要的字段
-- ALTER TABLE `achievement_templates` DROP COLUMN `reward_points`;
-- ALTER TABLE `achievement_templates` DROP COLUMN `is_active`;
-- ALTER TABLE `achievement_templates` DROP COLUMN `sort`;
-- ALTER TABLE `achievement_templates` DROP INDEX `idx_is_active`;
-- ALTER TABLE `achievement_templates` DROP INDEX `idx_sort`; 