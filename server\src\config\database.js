const { Sequelize } = require('sequelize');
require('dotenv').config();

// 数据库配置
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || 3306;
const DB_NAME = process.env.DB_NAME || 'ayl_admin';
const DB_USER = process.env.DB_USER || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || '';

// 创建Sequelize实例
const sequelize = new Sequelize(DB_NAME, DB_USER, DB_PASSWORD, {
  host: DB_HOST,
  port: DB_PORT,
  dialect: 'mysql',
  timezone: '+08:00', // 设置时区
  dialectOptions: {
    dateStrings: true,
    typeCast: true,
    // MySQL2支持的连接超时配置
    connectTimeout: 60000, // 连接超时 60秒
  },
  pool: {
    max: 15,        // 增加最大连接数
    min: 2,         // 设置最小连接数，保持预热连接
    acquire: 60000, // 增加获取连接超时时间到60秒
    idle: 30000,    // 增加空闲时间到30秒
    evict: 1000,    // 每1秒检查一次空闲连接
    handleDisconnects: true, // 自动处理断开连接
  },
  retry: {
    match: [
      /ETIMEDOUT/,
      /EHOSTUNREACH/,
      /ECONNRESET/,
      /ECONNREFUSED/,
      /ETIMEDOUT/,
      /ESOCKETTIMEDOUT/,
      /EHOSTUNREACH/,
      /EPIPE/,
      /EAI_AGAIN/,
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/
    ],
    max: 3 // 最大重试次数
  },
  define: {
    timestamps: false, // 默认不使用时间戳字段
    freezeTableName: true, // 表名默认不会被复数化
    charset: 'utf8mb4',
    collate: 'utf8mb4_0900_ai_ci'
  },
  logging: process.env.NODE_ENV === 'development' ? console.log : false
});

// 测试数据库连接
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error);
    // 增加重试逻辑
    setTimeout(async () => {
      console.log('尝试重新连接数据库...');
      await testConnection();
    }, 5000);
    return false;
  }
};

// 优雅关闭数据库连接
const closeConnection = async () => {
  try {
    await sequelize.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('关闭数据库连接时出错:', error);
  }
};

module.exports = sequelize;
module.exports.testConnection = testConnection;
module.exports.closeConnection = closeConnection;
