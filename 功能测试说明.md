# 岗位名称多选等级功能测试说明

## 功能概述
在岗位层级管理页面中，新增和编辑岗位名称时可以多选等级，选择的等级将自动创建对应的Position记录存入数据库。

## 主要功能点

### 1. 新增岗位名称
- 支持选择岗位类型
- 支持输入岗位名称
- 支持多选岗位等级（必选，至少选择一个）
- 自动为每个选择的等级创建Position记录

### 2. 编辑岗位名称
- 支持修改岗位类型
- 支持修改岗位名称  
- 支持重新选择岗位等级
- 自动删除旧的Position记录并创建新的记录

### 3. 岗位名称列表展示
- 显示岗位名称
- 显示岗位类型
- 显示关联的等级标签（按等级顺序排序）
- 支持编辑和删除操作

### 4. 删除岗位名称
- 检查是否有员工、知识库、考试配置等关联
- 自动删除关联的Position记录
- 使用事务确保数据一致性

## 数据库变更

### Position表记录生成规则
- 每个岗位名称+等级组合创建一条Position记录
- Position.nameId: 关联岗位名称ID
- Position.levelId: 关联等级ID  
- Position.typeId: 关联岗位类型ID
- Position.code: 格式为 `{岗位名称编码}_{等级ID}`

## 测试步骤

1. **访问页面**: 进入岗位层级管理页面
2. **新增岗位名称**:
   - 点击"新增岗位名称"按钮
   - 选择岗位类型
   - 输入岗位名称
   - 多选岗位等级
   - 点击确定保存
3. **验证结果**:
   - 查看列表中是否显示新增的岗位名称
   - 查看是否正确显示关联的等级标签
   - 检查数据库Position表是否生成了对应记录
4. **编辑测试**:
   - 点击编辑按钮
   - 修改等级选择
   - 保存并验证结果
5. **删除测试**:
   - 点击删除按钮
   - 验证是否正确删除相关记录

## 技术实现要点

### 前端
- 使用Vue3 Composition API
- 等级选择使用a-select的multiple模式
- 表格中使用a-tag显示等级信息
- 支持等级按orderNum排序显示

### 后端  
- 使用Sequelize事务确保数据一致性
- 支持批量创建Position记录
- 包含完整的关联关系检查
- 企业ID过滤确保数据隔离

## 注意事项
- 所有操作都包含企业ID过滤
- 删除时会检查各种关联关系
- 使用事务确保数据一致性
- 等级选择为必填项，至少选择一个 