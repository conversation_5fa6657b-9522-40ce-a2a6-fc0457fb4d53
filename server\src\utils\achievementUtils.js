const { User, AchievementTemplate, UserAchievement, AchievementProgress,PracticeRecord,ExamConfig ,KnowledgeBase} = require('../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('./enterpriseFilter');
const { calculatePracticeQualification, getPracticeRecords } = require('../controllers/weichat/wechatPracticeController');

/**
 * 颁发成就（从achievementEventListener迁移过来）
 * @param {Object} template - 成就模板
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 * @param {Object} progress - 进度信息
 */
const awardAchievement = async (template, userId, openId, enterpriseId, progress) => {
  try {
    const UserAchievement = require('../models/UserAchievement');
    const { addEnterpriseId } = require('./enterpriseFilter');

    console.log(`[成就颁发] 开始颁发成就: ${template.name}, 用户ID: ${userId}, openId: ${openId}`);

    // 创建成就记录
    const achievement = await UserAchievement.create(
      addEnterpriseId({
        userId,
        openId,
        templateId: template.id,
        achievementName: template.name,
        achievementIcon: template.icon,
        category: template.category,
        status: 'achieved',
        achievedAt: new Date(),
        rewardPoints: template.rewardPoints || 10,
        triggerCondition: template.triggerCondition,
        achievementData: {
          finalValue: progress.currentValue,
          targetValue: progress.targetValue,
          completedAt: progress.completedAt,
          description: template.description || `获得成就：${template.name}`,
          ...progress
        }
      }, enterpriseId)
    );

    console.log(`🎉 [成就获得] 用户${userId}获得成就: ${template.name}`);
    console.log(`📝 [成就描述] ${template.description || '无描述'}`);

    // TODO: 可以在这里添加成就获得通知逻辑，如WebSocket推送、邮件通知等
    // 不再使用事件发射器，改为直接处理通知逻辑

    return achievement;

  } catch (error) {
    console.error('[成就颁发] 颁发成就失败:', error);
    throw error;
  }
};

/**
 * 检查所有科目的学习进度（去掉必考条件和岗位限制）
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 * @returns {Array} 所有科目的进度信息
 */
const checkAllSubjectsProgressAcrossPositions = async (openId, enterpriseId) => {
  try {
    const ExamConfig = require('../models/ExamConfigModel');
    const KnowledgeBase = require('../models/knowledge-base');
    const PracticeRecord = require('../models/practice-record');
    const { calculatePracticeQualification, getPracticeRecords } = require('../controllers/weichat/wechatPracticeController');
    const { addEnterpriseFilter } = require('./enterpriseFilter');

    console.log(`[知识探索] 检查所有岗位的所有科目进度: 用户${openId}`);

    // 1. 首先获取用户的所有练习记录，按岗位和等级分组
    const userPracticeRecords = await PracticeRecord.findAll(
      addEnterpriseFilter({
        where: { openId },
        attributes: ['positionName', 'positionLevel', 'examSubject'],
        group: ['positionName', 'positionLevel', 'examSubject'],
      }, enterpriseId)
    );

    // 提取用户的所有岗位和等级组合
    const positionCombinations = userPracticeRecords.map(record => ({
      positionName: record.positionName,
      positionLevel: record.positionLevel
    }));

    console.log(`[知识探索] 用户有${positionCombinations.length}个岗位组合`);

    // 存储所有科目的进度信息
    const allSubjectsProgress = [];
    const processedSubjects = new Set(); // 用于去重

    // 2. 遍历每个岗位组合，获取该岗位下的科目进度
    for (const position of positionCombinations) {
      const { positionName, positionLevel } = position;
      console.log(`[知识探索] 检查岗位: ${positionName}-${positionLevel}`);

      // 获取该岗位级别的所有考试配置
      const examConfigs = await ExamConfig.findAll(
        addEnterpriseFilter({
          where: {
            positionName,
            positionLevel,
          }
        }, enterpriseId)
      );

      if (!examConfigs || examConfigs.length === 0) {
        console.log(`[知识探索] 未找到岗位${positionName}-${positionLevel}的考试配置`);
        continue;
      }

      // 获取练习记录统计
      const practiceRecords = await getPracticeRecords(openId, positionName, positionLevel);

      for (const examConfig of examConfigs) {
        try {
          const subjectId = examConfig.examSubject;

          // 如果已经处理过这个科目，跳过
          if (processedSubjects.has(subjectId)) {
            continue;
          }

          // 找到对应科目的练习记录
          const practiceRecord = practiceRecords.find(record =>
            record.examSubject == subjectId
          );

          if (practiceRecord) {
            // 计算真实进度
            const progress = calculatePracticeQualification(examConfig, practiceRecord);

            // 获取科目名称
            const knowledgeBase = await KnowledgeBase.findOne(
              addEnterpriseFilter({
                where: { id: subjectId },
                attributes: ['id', 'name']
              }, enterpriseId)
            );

            const subjectName = knowledgeBase ? knowledgeBase.name : `科目${subjectId}`;

            allSubjectsProgress.push({
              subjectId,
              subjectName,
              progress,
              status: practiceRecord.status || '可选',
              positionName,
              positionLevel
            });

            // 标记为已处理
            processedSubjects.add(subjectId);

            console.log(`[知识探索] 科目${subjectName}: ${progress}%`);
          }
        } catch (error) {
          console.error(`[知识探索] 处理科目${examConfig.examSubject}失败:`, error);
        }
      }
    }

    console.log(`[知识探索] 总共检查了${allSubjectsProgress.length}个科目`);
    return allSubjectsProgress;

  } catch (error) {
    console.error('[知识探索] 检查所有科目进度失败:', error);
    return [];
  }
};

/**
 * 处理初入宝殿成就检测
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 * @param {string} subjectId - 科目ID
 * @param {string} subjectName - 科目名称
 * @param {number} progress - 当前进度
 */
const processFirstEntryAchievement = async (userId, openId, enterpriseId, positionName, positionLevel,subjectId) => {
  try {
    const AchievementTemplate = require('../models/AchievementTemplate');
    const UserAchievement = require('../models/UserAchievement');
    const { addEnterpriseFilter } = require('./enterpriseFilter');


    const existingRecords = await PracticeRecord.findAll(
      addEnterpriseFilter({
        where: {
          openId,
          examSubject: subjectId,
          positionName,
          positionLevel
        }
      }, enterpriseId)
    );

    console.log(`[成就检测] 科目${subjectId}的练习记录数: ${existingRecords.length}`);

    // 如果已经有超过3条记录，说明可能不是首次学习了

    // 获取考试配置
    const examConfig = await ExamConfig.findOne(
      addEnterpriseFilter({
        where: {
          positionName,
          positionLevel,
          examSubject: subjectId,
        }
      }, enterpriseId)
    );

    if (!examConfig) {
      console.log(`[成就检测] 未找到考试配置`);
      return;
    }

    // 获取练习记录统计（去掉必考条件）
    const practiceRecords = await getPracticeRecords(openId, positionName, positionLevel);
    console.log(`[成就检测] 所有练习记录:`, practiceRecords);
    console.log(`[成就检测] 查找科目ID: ${subjectId}`);

    const practiceRecord = practiceRecords.find(record => {
      console.log(`[成就检测] 比较记录: examSubject=${record.examSubject}`);
      return record.examSubject == subjectId;
    });

    if (!practiceRecord) {
      console.log(`[成就检测] 未找到练习记录，科目ID: ${subjectId}`);
      console.log(`[成就检测] 可用记录:`, practiceRecords.map(r => ({
        examSubject: r.examSubject,
        status: r.status
      })));
      return;
    }

    // 使用 calculatePracticeQualification 方法计算进度
    const progress = calculatePracticeQualification(examConfig, practiceRecord);

    console.log(`[成就检测] 当前科目学习进度: ${progress}%`);

    // 获取科目名称
    const knowledgeBase = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { id: subjectId },
        attributes: ['id', 'name']
      }, enterpriseId)
    );

    const subjectName = knowledgeBase ? knowledgeBase.name : `科目${subjectId}`;

    // 获取初入宝殿成就模板
    const firstEntryTemplates = await AchievementTemplate.findAll(
      addEnterpriseFilter({
        where: {
          isActive: true,
          ruleType: 'progress'
        }
      }, enterpriseId)
    );

    for (const template of firstEntryTemplates) {
      try {
        const triggerCondition = JSON.parse(template.triggerCondition || '{}');

        // 只处理初入宝殿类型的成就
        if (triggerCondition.type === 'first_complete') {
          const requiredProgress = triggerCondition.progress || 1;

          console.log(`[初入宝殿成就] 检查成就${template.name}, 需要进度${requiredProgress}%, 当前进度${progress}%`);

          if (progress >= requiredProgress) {
            // 检查是否已经获得过这个成就
            const existingAchievement = await UserAchievement.findOne(
              addEnterpriseFilter({
                where: {
                  userId,
                  templateId: template.id
                }
              }, enterpriseId)
            );

            if (!existingAchievement) {
              console.log(`[初入宝殿成就] 触发成就: ${template.name}, 科目${subjectName}, 进度达到${progress}%`);

              await awardAchievement(template, userId, openId, enterpriseId, {
                currentValue: progress,
                targetValue: requiredProgress,
                completedAt: new Date(),
                subjectId,
                subjectName
              });
            } else {
              console.log(`[初入宝殿成就] 成就${template.name}已获得，跳过`);
            }
          } else {
            console.log(`[初入宝殿成就] 进度${progress}%未达到要求的${requiredProgress}%`);
          }
        }
      } catch (error) {
        console.error(`[初入宝殿成就] 处理成就模板${template.id}失败:`, error);
      }
    }
  } catch (error) {
    console.error('[初入宝殿成就] 检测初入宝殿成就失败:', error);
  }
};

/**
 * 处理知识探索成就检测
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 * @param {string} positionName - 岗位名称（可选，不再使用）
 * @param {string} positionLevel - 岗位级别（可选，不再使用）
 */
const processKnowledgeExplorationAchievement = async (userId, openId, enterpriseId, positionName, positionLevel) => {
  try {
    const AchievementTemplate = require('../models/AchievementTemplate');
    const UserAchievement = require('../models/UserAchievement');
    const { addEnterpriseFilter } = require('./enterpriseFilter');

    console.log(`[知识探索成就] 开始检测知识探索成就，用户ID: ${userId}`);

    // 获取知识探索成就模板
    const knowledgeExplorationTemplates = await AchievementTemplate.findAll(
      addEnterpriseFilter({
        where: {
          isActive: true,
          ruleType: 'progress'
        }
      }, enterpriseId)
    );

    for (const template of knowledgeExplorationTemplates) {
      try {
        const triggerCondition = JSON.parse(template.triggerCondition || '{}');

        // 只处理知识探索类型的成就
        if (triggerCondition.type === 'multiple_complete') {
          // 【统一】使用 subjectCount 参数名（和数据库配置保持一致）
          const requiredCount = triggerCondition.subjectCount || triggerCondition.count || 5;
          const requiredProgress = triggerCondition.progress || 100;

          console.log(`[知识探索成就] 检查成就${template.name}, 需要${requiredCount}门科目达到${requiredProgress}%`);

          // 【改进】使用跨岗位的科目进度检查，不再限制在当前岗位
          const allSubjectsProgress = await checkAllSubjectsProgressAcrossPositions(openId, enterpriseId);

          // 统计达到要求进度的科目数量
          const completedSubjectsCount = allSubjectsProgress.filter(subject => subject.progress >= requiredProgress).length;

          console.log(`[知识探索成就] 当前已完成${completedSubjectsCount}门科目（需要${requiredCount}门）`);

          if (completedSubjectsCount >= requiredCount) {
            // 检查是否已经获得过这个成就
            const existingAchievement = await UserAchievement.findOne(
              addEnterpriseFilter({
                where: {
                  userId,
                  templateId: template.id
                }
              }, enterpriseId)
            );

            if (!existingAchievement) {
              console.log(`[知识探索成就] 触发成就: ${template.name}, 已完成${completedSubjectsCount}门科目`);

              // 【统一】直接调用awardAchievement，和碎片时间大师一样的方式
              await awardAchievement(template, userId, openId, enterpriseId, {
                currentValue: completedSubjectsCount,
                targetValue: requiredCount,
                completedAt: new Date(),
                completedSubjects: allSubjectsProgress.filter(s => s.progress >= requiredProgress).map(s => ({
                  subjectId: s.subjectId,
                  subjectName: s.subjectName,
                  progress: s.progress,
                  positionName: s.positionName,
                  positionLevel: s.positionLevel
                }))
              });
            } else {
              console.log(`[知识探索成就] 成就${template.name}已获得，跳过`);
            }
          } else {
            console.log(`[知识探索成就] 已完成${completedSubjectsCount}门科目，未达到要求的${requiredCount}门`);
          }
        }
      } catch (error) {
        console.error(`[知识探索成就] 处理成就模板${template.id}失败:`, error);
      }
    }
  } catch (error) {
    console.error('[知识探索成就] 检测知识探索成就失败:', error);
  }
};

/**
 * 处理碎片时间大师成就检测（一天内三个时间范围都有练习记录）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 * @param {string} positionName - 岗位名称
 * @param {string} positionLevel - 岗位级别
 */
const processTimeMasterAchievement = async (userId, openId, enterpriseId, positionName, positionLevel) => {
  try {
    const AchievementTemplate = require('../models/AchievementTemplate');
    const UserAchievement = require('../models/UserAchievement');
    const AchievementProgress = require('../models/AchievementProgress');
    const { addEnterpriseFilter, addEnterpriseId } = require('./enterpriseFilter');
    const { Op } = require('sequelize');

    console.log(`[碎片时间大师成就] 开始检测碎片时间大师成就: 用户${userId}, 岗位${positionName}-${positionLevel}`);

    // 获取今天的开始和结束时间（使用中国时区 UTC+8）
    const now = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
    // 使用本地日期格式而不是ISO格式，避免时差问题
    const today = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;

    // 设置今天的开始和结束时间
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);

    console.log(`[碎片时间大师成就] 当前本地日期: ${today}`);
    console.log(`[碎片时间大师成就] 检查时间范围: ${todayStart.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' })} 到 ${todayEnd.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' })}`);

    // 获取碎片时间大师成就模板
    const template = await AchievementTemplate.findOne(
      addEnterpriseFilter({
        where: {
          name:"碎片时间大师",
          isActive: true,
          ruleType: 'time_based'
        }
      }, enterpriseId)
    );
    if (!template) {
      console.log('[碎片时间大师] 未找到碎片时间大师成就模板');
      return;
    }
    // 检查用户是否已获得该成就
    const existingAchievement = await UserAchievement.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id,
          status: 'achieved'
        }
      }, enterpriseId)
    );

    if (existingAchievement) {
      console.log('[碎片时间大师] 用户已获得该成就');
      return;
    }

    // 1. 获取triggerCondition中的三个时间段数据
    let triggerCondition;
    try {
      triggerCondition = JSON.parse(template.triggerCondition);
      console.log('[碎片时间大师] 触发条件:', triggerCondition);

      // 获取三个时间段
      const timeSlot1 = {
        start: triggerCondition.startTime1 || triggerCondition.start1 || 0,
        end: triggerCondition.endTime1 || triggerCondition.end1 || 12
      };

      const timeSlot2 = {
        start: triggerCondition.startTime2 || triggerCondition.start2 || 12,
        end: triggerCondition.endTime2 || triggerCondition.end2 || 18
      };

      const timeSlot3 = {
        start: triggerCondition.startTime3 || triggerCondition.start3 || 18,
        end: triggerCondition.endTime3 || triggerCondition.end3 || 24
      };

      console.log(`[碎片时间大师] 时间段1: ${timeSlot1.start}-${timeSlot1.end}`);
      console.log(`[碎片时间大师] 时间段2: ${timeSlot2.start}-${timeSlot2.end}`);
      console.log(`[碎片时间大师] 时间段3: ${timeSlot3.start}-${timeSlot3.end}`);

      // 2. 获取当前用户的成就进度记录
      let progressRecord = await AchievementProgress.findOne(
        addEnterpriseFilter({
          where: {
            userId,
            openId,
            templateId: template.id
          }
        }, enterpriseId)
      );

      // 如果没有进度记录，创建一个新的
      if (!progressRecord) {
        progressRecord = await AchievementProgress.create(
          addEnterpriseId({
            userId,
            openId,
            templateId: template.id,
            achievementName: template.name,
            ruleType: 'time_based',
            currentValue: 1, // 初始值为1，表示没有时间段完成
            targetValue: 3, // 目标值为3，表示需要完成3个时间段
            progressPercentage: 0,
            status: 'in_progress',
            isCompleted: false,
            timeData: {
              timeSlots: [false, false, false] // 三个时间段的完成状态
            },
            lastActivityDate: today
          }, enterpriseId)
        );
        console.log('[碎片时间大师] 创建新的进度记录');
      }

      // 3. 判断lastActivityDate是否为今天
      const lastActivityDate = progressRecord.lastActivityDate;
      console.log(`[碎片时间大师] 最后活动日期: ${lastActivityDate}, 今天: ${today}`);

      if (lastActivityDate === today) {
        // 如果是今天，检查当前时间属于哪个时间段
        const currentHour = now.getHours();
        console.log(`[碎片时间大师] 当前小时: ${currentHour}`);

        // 获取当前的timeData，如果不存在则初始化
        let timeData = progressRecord.timeData || { timeSlots: [false, false, false] };
        console.log(`[碎片时间大师] 时间段完成状态:`, timeData.timeSlots);

        // 判断当前时间属于哪个时间段
        let currentTimeSlot = null;
        if (currentHour >= timeSlot1.start && currentHour < timeSlot1.end) {
          currentTimeSlot = 0; // 第一个时间段
        } else if (currentHour >= timeSlot2.start && currentHour < timeSlot2.end) {
          currentTimeSlot = 1; // 第二个时间段
        } else if (currentHour >= timeSlot3.start && currentHour < timeSlot3.end) {
          currentTimeSlot = 2; // 第三个时间段
        }
        console.log(`[碎片时间大师] 时间段currentTimeSlot:`, currentTimeSlot);

        if (currentTimeSlot !== null) {
          // 标记当前时间段为已完成
          timeData.timeSlots[currentTimeSlot] = true;
          console.log(timeData)

          console.log(`[碎片时间大师] 标记时间段${currentTimeSlot}为已完成`);

          // 计算已完成的时间段数量
          const completedSlots = timeData.timeSlots.filter(slot => slot).length;

          // 检查是否需要颁发成就（在更新数据库之前检查）
          const shouldAwardAchievement = completedSlots === 3 ;
          console.log(`[碎片时间大师] 检查成就颁发条件: completedSlots=${completedSlots}, progressRecord.isCompleted=${progressRecord.isCompleted}, shouldAward=${shouldAwardAchievement}`);

          // 更新进度记录
          console.log(`[碎片时间大师] 准备更新数据库，timeData:`, JSON.stringify(timeData));

          // 先更新 progressRecord 实例的属性
          progressRecord.currentValue = completedSlots;
          progressRecord.progressPercentage = (completedSlots / 3) * 100;
          progressRecord.isCompleted = completedSlots === 3;
          progressRecord.completedAt = completedSlots === 3 ? new Date() : null;
          progressRecord.status = completedSlots === 3 ? 'completed' : 'in_progress';
          progressRecord.timeData = timeData;
          progressRecord.lastActivityDate = today;

          // 标记 timeData 字段已修改（对于 JSON 字段很重要）
          progressRecord.changed('timeData', true);

          // 保存到数据库
          const updateResult = await progressRecord.save();

          console.log(`[碎片时间大师] 数据库更新结果:`, updateResult ? '成功' : '失败');

          // 重新查询数据库以验证更新是否成功
          await progressRecord.reload();
          console.log(`[碎片时间大师] 重新查询后的timeData:`, JSON.stringify(progressRecord.timeData));

          console.log(`[碎片时间大师] 更新进度: ${completedSlots}/3 时间段已完成`);

          // 如果所有时间段都已完成，颁发成就
          if (shouldAwardAchievement) {
            console.log('[碎片时间大师] 所有时间段已完成，颁发成就');

            await awardAchievement(template, userId, openId, enterpriseId, {
              currentValue: 3,
              targetValue: 3,
              completedAt: new Date(),
              timeSlots: timeData.timeSlots,
              description: `在一天内的三个不同时间段（${timeSlot1.start}-${timeSlot1.end}，${timeSlot2.start}-${timeSlot2.end}，${timeSlot3.start}-${timeSlot3.end}）都有学习记录！`
            });
          } else {
            console.log(`[碎片时间大师] 不满足成就颁发条件: completedSlots=${completedSlots}, 原isCompleted=${!shouldAwardAchievement && completedSlots === 3}`);
          }
        }
      } else {
        // 如果不是今天，重置进度记录
        const currentHour = now.getHours();
        let timeData = { timeSlots: [false, false, false] };

        // 判断当前时间属于哪个时间段
        let currentTimeSlot = null;
        if (currentHour >= timeSlot1.start && currentHour < timeSlot1.end) {
          currentTimeSlot = 0; // 第一个时间段
        } else if (currentHour >= timeSlot2.start && currentHour < timeSlot2.end) {
          currentTimeSlot = 1; // 第二个时间段
        } else if (currentHour >= timeSlot3.start && currentHour < timeSlot3.end) {
          currentTimeSlot = 2; // 第三个时间段
        }

        if (currentTimeSlot !== null) {
          // 标记当前时间段为已完成
          timeData.timeSlots[currentTimeSlot] = true;

          // 更新进度记录
          console.log(`[碎片时间大师] 准备重置进度，timeData:`, JSON.stringify(timeData));

          // 先更新 progressRecord 实例的属性
          progressRecord.currentValue = 1; // 重置为1，因为只有当前时间段完成
          progressRecord.progressPercentage = (1 / 3) * 100;
          progressRecord.isCompleted = false;
          progressRecord.completedAt = null;
          progressRecord.status = 'in_progress';
          progressRecord.timeData = timeData;
          progressRecord.lastActivityDate = today;

          // 标记 timeData 字段已修改（对于 JSON 字段很重要）
          progressRecord.changed('timeData', true);

          // 保存到数据库
          const updateResult = await progressRecord.save();

          console.log(`[碎片时间大师] 重置数据库更新结果:`, updateResult ? '成功' : '失败');

          // 重新查询数据库以验证更新是否成功
          await progressRecord.reload();
          console.log(`[碎片时间大师] 重置后重新查询的timeData:`, JSON.stringify(progressRecord.timeData));

          console.log(`[碎片时间大师] 重置进度: 1/3 时间段已完成`);
        }
      }

    } catch (error) {
      console.error('[碎片时间大师] 解析触发条件失败:', error);
      return;
    }
  } catch (error) {
    console.error('[碎片时间大师成就] 检测碎片时间大师成就失败:', error);
  }
};

/**
 * 处理旗开得胜成就检测（获得第一个考试满分）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 * @param {number} examRecordId - 考试记录ID
 * @param {number} score - 考试得分
 * @param {number} questionCount - 题目总数
 */
const processFirstPerfectScoreAchievement = async (userId, openId, enterpriseId, examRecordId, score, questionCount) => {
  try {
    const AchievementTemplate = require('../models/AchievementTemplate');
    const UserAchievement = require('../models/UserAchievement');
    const { addEnterpriseFilter } = require('./enterpriseFilter');

    console.log(`[旗开得胜成就] 开始检测旗开得胜成就: 考试ID${examRecordId}, 得分${score}, 总题数${questionCount}`);

    // 检查是否为满分（得分等于题目总数）
    if (score !== questionCount) {
      console.log(`[旗开得胜成就] 得分${score}不等于总题数${questionCount}，不是满分，跳过检测`);
      return;
    }

    console.log(`[旗开得胜成就] 检测到满分！开始检查旗开得胜成就...`);

    // 获取旗开得胜成就模板
    const templates = await AchievementTemplate.findAll(
      addEnterpriseFilter({
        where: {
          isActive: true,
          ruleType: 'count'
        }
      }, enterpriseId)
    );

    for (const template of templates) {
      try {
        const triggerCondition = JSON.parse(template.triggerCondition || '{}');

        // 只处理旗开得胜类型的成就
        if (triggerCondition.type === 'first_perfect_score') {
          console.log(`[旗开得胜成就] 检查成就${template.name}`);

          // 检查是否已经获得过这个成就
          const existingAchievement = await UserAchievement.findOne(
            addEnterpriseFilter({
              where: {
                userId,
                templateId: template.id
              }
            }, enterpriseId)
          );

          if (!existingAchievement) {
            console.log(`[旗开得胜成就] 触发成就: ${template.name}, 第一次考试满分！`);

            await awardAchievement(template, userId, openId, enterpriseId, {
              currentValue: score,
              targetValue: questionCount,
              completedAt: new Date(),
              examRecordId,
              score,
              questionCount,
              description: `考试得分${score}/${questionCount}，获得满分！`
            });
          } else {
            console.log(`[旗开得胜成就] 成就${template.name}已获得，跳过`);
          }
        }
      } catch (error) {
        console.error(`[旗开得胜成就] 处理成就模板${template.id}失败:`, error);
      }
    }
  } catch (error) {
    console.error('[旗开得胜成就] 检测旗开得胜成就失败:', error);
  }
};

/**
 * 处理学霸模式成就检测（连续学习天数）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 */
const processStudyStreakAchievement = async (userId, openId, enterpriseId) => {
  try {
    console.log(`[学霸模式] 开始检查连续学习天数成就: 用户${userId}`);

    // 查找学霸模式成就模板
    const template = await AchievementTemplate.findOne(
      addEnterpriseFilter({
        where: {
          name: '学霸模式',
          category: 'learning',
          ruleType: 'consecutive_days',
          isActive: true
        }
      }, enterpriseId)
    );

    if (!template) {
      console.log('[学霸模式] 未找到学霸模式成就模板');
      return;
    }

    // 解析触发条件
    let triggerCondition;
    try {
      triggerCondition = JSON.parse(template.triggerCondition);
    } catch (error) {
      console.error('[学霸模式] 解析触发条件失败:', error);
      return;
    }

    // 获取目标连续天数
    const targetDays = triggerCondition.days || 3; // 默认为3天
    console.log(`[学霸模式] 目标连续天数: ${targetDays}天`);

    // 检查用户是否已获得该成就
    const existingAchievement = await UserAchievement.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id,
          status: 'achieved'
        }
      }, enterpriseId)
    );

    if (existingAchievement) {
      console.log('[学霸模式] 用户已获得该成就');
      return;
    }

    // 获取用户的练习记录
    const PracticeRecord = require('../models/practice-record');
    const practiceRecords = await PracticeRecord.findAll(
      addEnterpriseFilter({
        where: { openId },
        attributes: ['id', 'createTime'],
        order: [['createTime', 'DESC']]
      }, enterpriseId)
    );

    if (!practiceRecords || practiceRecords.length === 0) {
      console.log('[学霸模式] 用户没有练习记录');
      return;
    }

    // 获取用户练习的所有日期（按日期分组）
    const practiceDates = practiceRecords.map(record => {
      const date = new Date(record.createTime);
      return date.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
    });

    // 去重并排序
    const uniqueDates = [...new Set(practiceDates)].sort();
    console.log(`[学霸模式] 用户练习天数: ${uniqueDates.length}天`);
    console.log(`[学霸模式] 练习日期: ${uniqueDates.join(', ')}`);

    // 计算最大连续天数
    let maxConsecutiveDays = 0;
    let currentStreak = 1;

    for (let i = 1; i < uniqueDates.length; i++) {
      const prevDate = new Date(uniqueDates[i-1]);
      const currDate = new Date(uniqueDates[i]);

      // 计算日期差
      const diffTime = Math.abs(currDate - prevDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) {
        // 连续天数
        currentStreak++;
      } else {
        // 重置连续天数
        currentStreak = 1;
      }

      // 更新最大连续天数
      maxConsecutiveDays = Math.max(maxConsecutiveDays, currentStreak);
    }

    console.log(`[学霸模式] 最大连续学习天数: ${maxConsecutiveDays}天`);

    // 查找或创建成就进度记录
    let progressRecord = await AchievementProgress.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id
        }
      }, enterpriseId)
    );

    if (!progressRecord) {
      progressRecord = await AchievementProgress.create(
        addEnterpriseId({
          userId,
          openId,
          templateId: template.id,
          achievementName: template.name,
          ruleType: template.ruleType,
          currentValue: maxConsecutiveDays,
          targetValue: targetDays,
          progressPercentage: Math.min(100, (maxConsecutiveDays / targetDays) * 100),
          status: 'in_progress',
          isCompleted: false,
          lastActivityDate: new Date().toISOString().split('T')[0],
          streakData: JSON.stringify({
            currentStreak: maxConsecutiveDays,
            targetStreak: targetDays,
            lastPracticeDates: uniqueDates.slice(-10)
          })
        }, enterpriseId)
      );
    } else {
      // 更新进度记录
      await progressRecord.update(
        addEnterpriseId({
          currentValue: maxConsecutiveDays,
          targetValue: targetDays,
          progressPercentage: Math.min(100, (maxConsecutiveDays / targetDays) * 100),
          lastActivityDate: new Date().toISOString().split('T')[0],
          streakData: JSON.stringify({
            currentStreak: maxConsecutiveDays,
            targetStreak: targetDays,
            lastPracticeDates: uniqueDates.slice(-10)
          })
        }, enterpriseId)
      );
    }

    // 如果达到目标连续天数，颁发成就
    if (maxConsecutiveDays >= targetDays) {
      console.log(`[学霸模式] 达成连续学习${maxConsecutiveDays}天，颁发成就`);
      await awardAchievement(template, userId, openId, enterpriseId, {
        currentValue: maxConsecutiveDays,
        targetValue: targetDays,
        practiceDates: uniqueDates
      });
    } else {
      console.log(`[学霸模式] 当前连续学习${maxConsecutiveDays}天，目标${targetDays}天，继续努力`);
    }

  } catch (error) {
    console.error('[学霸模式] 检查连续学习天数成就失败:', error);
  }
};

/**
 * 处理学无止境成就检测（累计学习时长超过xx小时）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 */
const processEndlessLearningAchievement = async (userId, openId, enterpriseId) => {
  try {
    console.log(`[学无止境] 开始检查累计学习时长成就: 用户${userId}`);

    // 查找学无止境成就模板
    const template = await AchievementTemplate.findOne(
      addEnterpriseFilter({
        where: {
          name: '学无止境',
          category: 'learning',
          ruleType: 'study_time',
          isActive: true
        }
      }, enterpriseId)
    );

    if (!template) {
      console.log('[学无止境] 未找到学无止境成就模板');
      return;
    }

    // 解析触发条件
    let triggerCondition;
    try {
      triggerCondition = JSON.parse(template.triggerCondition);
    } catch (error) {
      console.error('[学无止境] 解析触发条件失败:', error);
      return;
    }

    // 获取目标学习时长（小时）
    const targetHours = triggerCondition.hours || 1; // 默认为1小时
    console.log(`[学无止境] 目标学习时长: ${targetHours}小时`);

    // 检查用户是否已获得该成就
    const existingAchievement = await UserAchievement.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id,
          status: 'achieved'
        }
      }, enterpriseId)
    );

    if (existingAchievement) {
      console.log('[学无止境] 用户已获得该成就');
      return;
    }

    // 获取用户的所有练习记录
    const PracticeRecord = require('../models/practice-record');
    const practiceRecords = await PracticeRecord.findAll(
      addEnterpriseFilter({
        where: { openId },
        attributes: ['id', 'totalDuration'],
      }, enterpriseId)
    );

    if (!practiceRecords || practiceRecords.length === 0) {
      console.log('[学无止境] 用户没有练习记录');
      return;
    }

    // 计算累计学习时长（秒）
    let totalDurationInSeconds = 0;
    practiceRecords.forEach(record => {
      if (record.totalDuration) {
        // 处理 MM:SS 格式
        const parts = record.totalDuration.split(':');
        if (parts.length === 2) {
          const minutes = parseInt(parts[0]) || 0;
          const seconds = parseInt(parts[1]) || 0;
          totalDurationInSeconds += minutes * 60 + seconds;
        }
        // 处理 HH:MM:SS 格式
        else if (parts.length === 3) {
          const hours = parseInt(parts[0]) || 0;
          const minutes = parseInt(parts[1]) || 0;
          const seconds = parseInt(parts[2]) || 0;
          totalDurationInSeconds += hours * 3600 + minutes * 60 + seconds;
        }
      }
    });

    // 转换为小时
    const totalDurationInHours = totalDurationInSeconds / 3600;
    console.log(`[学无止境] 累计学习时长: ${totalDurationInHours.toFixed(2)}小时`);

    // 查找或创建成就进度记录
    let progressRecord = await AchievementProgress.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id
        }
      }, enterpriseId)
    );

    if (!progressRecord) {
      progressRecord = await AchievementProgress.create(
        addEnterpriseId({
          userId,
          openId,
          templateId: template.id,
          achievementName: template.name,
          ruleType: template.ruleType,
          currentValue: totalDurationInHours,
          targetValue: targetHours,
          progressPercentage: Math.min(100, (totalDurationInHours / targetHours) * 100),
          status: 'in_progress',
          isCompleted: false,
          lastActivityDate: new Date().toISOString().split('T')[0],
          timeData: JSON.stringify({
            totalDurationInHours,
            totalDurationInSeconds,
            formattedDuration: formatDuration(totalDurationInSeconds),
            lastUpdated: new Date().toISOString()
          })
        }, enterpriseId)
      );
    } else {
      // 更新进度记录
      await progressRecord.update(
        addEnterpriseId({
          currentValue: totalDurationInHours,
          targetValue: targetHours,
          progressPercentage: Math.min(100, (totalDurationInHours / targetHours) * 100),
          lastActivityDate: new Date().toISOString().split('T')[0],
          timeData: JSON.stringify({
            totalDurationInHours,
            totalDurationInSeconds,
            formattedDuration: formatDuration(totalDurationInSeconds),
            lastUpdated: new Date().toISOString()
          })
        }, enterpriseId)
      );
    }

    // 如果达到目标学习时长，颁发成就
    if (totalDurationInHours >= targetHours) {
      console.log(`[学无止境] 达成累计学习${totalDurationInHours.toFixed(2)}小时，颁发成就`);
      await awardAchievement(template, userId, openId, enterpriseId, {
        currentValue: totalDurationInHours,
        targetValue: targetHours,
        formattedDuration: formatDuration(totalDurationInSeconds)
      });
    } else {
      console.log(`[学无止境] 当前累计学习${totalDurationInHours.toFixed(2)}小时，目标${targetHours}小时，继续努力`);
    }

  } catch (error) {
    console.error('[学无止境] 检查累计学习时长成就失败:', error);
  }
};

/**
 * 处理练习成就触发（用于WebSocket调用）
 * @param {Object} req - 模拟的请求对象
 * @param {Object} responseBody - 响应体
 */
const handlePracticeAchievementTrigger = async (req, responseBody) => {
  try {
    console.log('[成就触发] 处理练习成就触发，请求体:', JSON.stringify(req.body, null, 2));
    console.log('[成就触发] 请求头openId:', req.headers.openid);

    // 兼容WebSocket payload格式和HTTP请求格式
    let requestData = req.body;
    if (req.body.payload) {
      // WebSocket格式：{"type": "parse_answer", "payload": {...}}
      requestData = req.body.payload;
      console.log('[成就触发] 检测到WebSocket payload格式，提取数据:', JSON.stringify(requestData, null, 2));
    }


    // 主要关注practice_id和time字段
    const practiceId = requestData.practice_id || requestData.practiceId;
    const time = requestData.time || requestData.duration || requestData.timeSpent;
    const openId = req.headers.openid; // 从请求头获取openId

    console.log('[成就触发] 参数解析结果:');
    console.log('  - practiceId:', practiceId);
    console.log('  - time:', time);
    console.log('  - openId:', openId);

    if (!openId) {
      console.warn('[成就触发] 练习记录缺少openId');
      return;
    }

    // 获取用户信息
    const { addEnterpriseFilter } = require('./enterpriseFilter');
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID || 1;

    const user = await User.findOne(
      addEnterpriseFilter({ where: { openId } }, enterpriseId)
    );
    if (!user) {
      console.warn('[成就触发] 未找到openId对应的用户:', openId);
      return;
    }


    if (practiceId) {
      try {
        // 通过practice_id查询练习记录获取岗位和科目信息
        const PracticeRecord = require('../models/practice-record');
        const { addEnterpriseFilter } = require('./enterpriseFilter');

        const practiceRecord = await PracticeRecord.findOne(
          addEnterpriseFilter({
            where: { id: practiceId },
            attributes: ['id', 'openId', 'positionName', 'positionLevel', 'examSubject', 'createTime']
          }, enterpriseId)
        );

        if (practiceRecord) {
          console.log('[成就触发] 从练习记录获取到信息:');
          console.log('  - positionName:', practiceRecord.positionName);
          console.log('  - positionLevel:', practiceRecord.positionLevel);
          console.log('  - examSubject:', practiceRecord.examSubject);
          console.log('  - openId:', practiceRecord.openId);

          // 验证openId是否匹配
          if (practiceRecord.openId === openId) {
            const positionName = practiceRecord.positionName;
            const positionLevel = practiceRecord.positionLevel;
            const subjectId = practiceRecord.examSubject;

            // 初入宝殿
            await processFirstEntryAchievement(user.id, openId, enterpriseId,positionName, positionLevel, subjectId);
            // 知识探索
            await processKnowledgeExplorationAchievement(user.id, openId, enterpriseId);
            // 碎片时间大师
            await processTimeMasterAchievement(user.id, openId, enterpriseId, positionName, positionLevel);
            // 学霸模式
            await processStudyStreakAchievement(user.id, openId, enterpriseId);
            // 学无止境
            await processEndlessLearningAchievement(user.id, openId, enterpriseId);
            // 早起鸟
            await processEarlyBirdAchievement(user.id, openId, enterpriseId);
            // 夜猫子
            await processNightOwlAchievement(user.id, openId, enterpriseId);
            // 全能力者
            await processAllRounderAchievement(user.id, openId, enterpriseId);
          } else {
            console.warn('[成就触发] 练习记录的openId与当前用户不匹配');
          }
        } else {
          console.warn('[成就触发] 未找到对应的练习记录:', practiceId);
        }
      } catch (error) {
        console.error('[成就触发] 查询练习记录失败:', error);
      }
    } else {
      console.warn('[成就触发] 缺少practice_id，无法进行成就检测');
    }
  } catch (error) {
    console.error('[成就触发] 处理练习成就触发失败:', error);
  }
};

/**
 * 格式化时长（秒）为可读字符串
 * @param {number} seconds - 总秒数
 * @returns {string} - 格式化后的时长字符串
 */
const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  let result = '';
  if (hours > 0) {
    result += `${hours}小时`;
  }
  if (minutes > 0 || (hours > 0 && remainingSeconds > 0)) {
    result += `${minutes}分钟`;
  }
  if (remainingSeconds > 0 && hours === 0) {
    result += `${remainingSeconds}秒`;
  }

  return result || '0分钟';
};

/**
 * 处理早起鸟成就检测（4点～7点之间有练习记录，连续5天）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 */
const processEarlyBirdAchievement = async (userId, openId, enterpriseId) => {
  try {
    console.log(`[早起鸟] 开始检查早起鸟成就: 用户${userId}`);

    // 查找早起鸟成就模板
    const template = await AchievementTemplate.findOne(
      addEnterpriseFilter({
        where: {
          name: '早起鸟',
          category: 'learning',
          ruleType: 'time_based',
          isActive: true
        }
      }, enterpriseId)
    );

    if (!template) {
      console.log('[早起鸟] 未找到早起鸟成就模板');
      return;
    }

    // 解析触发条件
    let triggerCondition;
    try {
      triggerCondition = JSON.parse(template.triggerCondition);
    } catch (error) {
      console.error('[早起鸟] 解析触发条件失败:', error);
      return;
    }

    // 获取时间范围和目标连续天数
    const startHour = triggerCondition.startTime || triggerCondition.startHour || 4; // 默认为4点
    const endHour = triggerCondition.endTime || triggerCondition.endHour || 7; // 默认为7点
    const targetDays = triggerCondition.days || 5; // 默认为5天

    console.log(`[早起鸟] 目标时间范围: ${startHour}:00-${endHour}:00, 连续${targetDays}天`);

    // 检查用户是否已获得该成就
    const existingAchievement = await UserAchievement.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id,
          status: 'achieved'
        }
      }, enterpriseId)
    );

    if (existingAchievement) {
      console.log('[早起鸟] 用户已获得该成就');
      return;
    }

    // 获取当前时间（使用中国时区 UTC+8）
    const now = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
    const currentHour = now.getHours();
    // 使用本地日期格式而不是ISO格式，避免时差问题
    const today = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    console.log(`[早起鸟] 当前本地日期: ${today}, 小时: ${currentHour}`);

    // 检查当前时间是否在早起时间范围内（4-7点）
    const isEarlyMorning = currentHour >= startHour && currentHour < endHour;
    console.log(`[早起鸟] 当前时间: ${today}, 小时: ${currentHour}, 是否早起时间: ${isEarlyMorning}`);

    if (!isEarlyMorning) {
      console.log('[早起鸟] 当前不在早起时间范围内，跳过检测');
      return;
    }

    // 查找或创建成就进度记录
    const AchievementProgress = require('../models/AchievementProgress');
    let progressRecord = await AchievementProgress.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id
        }
      }, enterpriseId)
    );

    // 如果没有进度记录，创建一个新的
    if (!progressRecord) {
      console.log('[早起鸟] 创建新的进度记录');
      progressRecord = await AchievementProgress.create(
        addEnterpriseId({
          userId,
          openId,
          templateId: template.id,
          achievementName: template.name,
          ruleType: template.ruleType,
          currentValue: 1, // 今天是第一天
          targetValue: targetDays,
          progressPercentage: Math.min(100, (1 / targetDays) * 100),
          status: 'in_progress',
          isCompleted: false,
          lastActivityDate: today,

        }, enterpriseId)
      );
      console.log('[早起鸟] 第1天早起记录');
      return;
    }



    const lastCheckedDate = progressRecord.lastActivityDate || '';
    let currentStreak = progressRecord.currentValue || 0;

    // 如果今天已经检查过了，跳过
    if (lastCheckedDate === today) {
      console.log('[早起鸟] 今天已经检查过了，跳过');
      return;
    }

    // 检查是否连续（昨天的日期）
    const yesterday = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
    yesterday.setDate(yesterday.getDate() - 1);
    // 使用本地日期格式
    const yesterdayStr = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;
    console.log(`[早起鸟] 昨天日期: ${yesterdayStr}`);

    // 获取数据库中记录的最后活动日期
    const dbLastActivityDate = progressRecord.lastActivityDate;
    console.log(`[早起鸟] 数据库记录的最后活动日期: ${dbLastActivityDate}`);

    // 如果最后检查的日期是昨天，则连续+1，否则重置为1
    if (lastCheckedDate == yesterdayStr ) {
      currentStreak++;
      console.log(`[早起鸟] 连续早起第${currentStreak}天，目标${targetDays}天`);

      // 更新进度记录
      await progressRecord.update(
        addEnterpriseId({
          currentValue: currentStreak,
          targetValue: targetDays,
          progressPercentage: Math.min(100, (currentStreak / targetDays) * 100),
          lastActivityDate: today,
        }, enterpriseId)
      );

      // 如果达到目标连续天数，颁发成就
      if (currentStreak >= targetDays) {
        console.log(`[早起鸟] 达成连续早起${currentStreak}天，颁发成就`);
        await awardAchievement(template, userId, openId, enterpriseId, {
          currentValue: currentStreak,
          targetValue: targetDays,
          timeRange: `${startHour}:00-${endHour}:00`,
          completedAt: new Date()
        });
      } else {
        console.log(`[早起鸟] 当前连续早起${currentStreak}天，目标${targetDays}天，继续努力`);
      }
    } else {
      console.log(`[早起鸟] 连续中断，上次记录日期: ${lastCheckedDate}, 数据库日期: ${dbLastActivityDate}, 需要: ${yesterdayStr}`);
      currentStreak = 1;

      // 重置后也需要更新进度记录
      await progressRecord.update(
        addEnterpriseId({
          currentValue: currentStreak,
          targetValue: targetDays,
          progressPercentage: Math.min(100, (currentStreak / targetDays) * 100),
          lastActivityDate: today,
        }, enterpriseId)
      );

      console.log('[早起鸟] 连续中断，重新开始计数');
    }

  } catch (error) {
    console.error('[早起鸟] 检查早起鸟成就失败:', error);
  }
};

/**
 * 处理夜猫子成就检测（22点～2点之间有练习记录，连续7天）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 */
const processNightOwlAchievement = async (userId, openId, enterpriseId) => {
  try {
    console.log(`[夜猫子] 开始检查夜猫子成就: 用户${userId}`);

    // 查找夜猫子成就模板
    const template = await AchievementTemplate.findOne(
      addEnterpriseFilter({
        where: {
          name: '夜猫子',
          category: 'learning',
          ruleType: 'time_based',
          isActive: true
        }
      }, enterpriseId)
    );

    if (!template) {
      console.log('[夜猫子] 未找到夜猫子成就模板');
      return;
    }

    // 解析触发条件
    let triggerCondition;
    try {
      triggerCondition = JSON.parse(template.triggerCondition);
    } catch (error) {
      console.error('[夜猫子] 解析触发条件失败:', error);
      return;
    }

    // 获取时间范围和目标连续天数
    const startHour = triggerCondition.startTime || 22; // 默认为22点
    const endHour = triggerCondition.endTime || 2; // 默认为2点
    const targetDays = triggerCondition.days || 7; // 默认为7天

    console.log(`[夜猫子] 目标时间范围: ${startHour}:00-${endHour}:00, 连续${targetDays}天`);

    // 检查用户是否已获得该成就
    const existingAchievement = await UserAchievement.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id,
          status: 'achieved'
        }
      }, enterpriseId)
    );

    if (existingAchievement) {
      console.log('[夜猫子] 用户已获得该成就');
      return;
    }

    // 获取当前时间（使用本地时间，避免时差问题）
    const now = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
    const currentHour = now.getHours();
    // 使用本地日期格式而不是ISO格式，避免时差问题
    const today = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    console.log(`[夜猫子] 当前本地日期: ${today}, 小时: ${currentHour}`);

    // 检查当前时间是否在夜间时间范围内（22-2点）
    // 注意：夜猫子时间跨越了午夜，需要特殊处理
    const isNightTime = (currentHour >= startHour) || (currentHour < endHour);
    console.log(`[夜猫子] 当前时间: ${today}, 小时: ${currentHour}, 是否夜间时间: ${isNightTime}`);

    if (!isNightTime) {
      console.log('[夜猫子] 当前不在夜间时间范围内，跳过检测');
      return;
    }

    // 确定当前日期（如果是0-2点，应该算作前一天）
    let dateKey = today;
    if (currentHour < endHour) {
      // 0-2点，使用前一天的日期
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      // 使用本地日期格式
      dateKey = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;
      console.log(`[夜猫子] 当前是凌晨时段，使用前一天日期: ${dateKey}`);
    }

    // 查找或创建成就进度记录
    const AchievementProgress = require('../models/AchievementProgress');
    let progressRecord = await AchievementProgress.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id
        }
      }, enterpriseId)
    );

    // 如果没有进度记录，创建一个新的
    if (!progressRecord) {
      console.log('[夜猫子] 创建新的进度记录');
      progressRecord = await AchievementProgress.create(
        addEnterpriseId({
          userId,
          openId,
          templateId: template.id,
          achievementName: template.name,
          ruleType: template.ruleType,
          currentValue: 1, // 今天是第一天
          targetValue: targetDays,
          progressPercentage: Math.min(100, (1 / targetDays) * 100),
          status: 'in_progress',
          isCompleted: false,
          lastActivityDate: dateKey,
        }, enterpriseId)
      );
      console.log('[夜猫子] 第1天夜间记录');
      return;
    }


    const lastCheckedDate = progressRecord.lastActivityDate || '';
    let currentStreak = progressRecord.currentValue || 0;

    // 如果今天已经检查过了，跳过
    if (lastCheckedDate === dateKey) {
      console.log('[夜猫子] 今天已经检查过了，跳过');
      return;
    }

    // 检查是否连续（昨天的日期）
    let yesterdayStr;
    if (currentHour < endHour) {
      // 如果是凌晨0-2点，昨天应该是前天
      const dayBeforeYesterday = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
      dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);
      // 使用本地日期格式
      yesterdayStr = `${dayBeforeYesterday.getFullYear()}-${String(dayBeforeYesterday.getMonth() + 1).padStart(2, '0')}-${String(dayBeforeYesterday.getDate()).padStart(2, '0')}`;
      console.log(`[夜猫子] 前天日期(凌晨模式): ${yesterdayStr}`);
    } else {
      // 如果是22-23点，昨天就是前一天
      const yesterday = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
      yesterday.setDate(yesterday.getDate() - 1);
      // 使用本地日期格式
      yesterdayStr = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;
      console.log(`[夜猫子] 昨天日期(晚上模式): ${yesterdayStr}`);
    }

    // 获取数据库中记录的最后活动日期
    const dbLastActivityDate = progressRecord.lastActivityDate;
    console.log(`[夜猫子] 数据库记录的最后活动日期: ${dbLastActivityDate}`);

    // 如果最后检查的日期是昨天，则连续+1，否则重置为1
    if (lastCheckedDate === yesterdayStr ) {
      currentStreak++;
      console.log(`[夜猫子] 连续夜间活动第${currentStreak}天，目标${targetDays}天`);

      // 更新进度记录
      await progressRecord.update(
        addEnterpriseId({
          currentValue: currentStreak,
          targetValue: targetDays,
          progressPercentage: Math.min(100, (currentStreak / targetDays) * 100),
          lastActivityDate: dateKey,
        }, enterpriseId)
      );

      // 如果达到目标连续天数，颁发成就
      if (currentStreak >= targetDays) {
        console.log(`[夜猫子] 达成连续夜间活动${currentStreak}天，颁发成就`);
        await awardAchievement(template, userId, openId, enterpriseId, {
          currentValue: currentStreak,
          targetValue: targetDays,
          timeRange: `${startHour}:00-${endHour}:00`,
          completedAt: new Date()
        });
      } else {
        console.log(`[夜猫子] 当前连续夜间活动${currentStreak}天，目标${targetDays}天，继续努力`);
      }
    } else {
      console.log(`[夜猫子] 连续中断，上次记录日期: ${lastCheckedDate}, 数据库日期: ${dbLastActivityDate}, 需要: ${yesterdayStr}`);
      currentStreak = 1;

      // 重置后也需要更新进度记录
      await progressRecord.update(
        addEnterpriseId({
          currentValue: currentStreak,
          targetValue: targetDays,
          progressPercentage: Math.min(100, (currentStreak / targetDays) * 100),
          lastActivityDate: dateKey,
        }, enterpriseId)
      );

      console.log('[夜猫子] 连续中断，重新开始计数');
    }

    // 如果达到目标连续天数，颁发成就
    if (currentStreak >= targetDays) {
      console.log(`[夜猫子] 达成连续夜间活动${currentStreak}天，颁发成就`);
      await awardAchievement(template, userId, openId, enterpriseId, {
        currentValue: currentStreak,
        targetValue: targetDays,
        timeRange: `${startHour}:00-${endHour}:00`,
        completedAt: new Date()
      });
    } else {
      console.log(`[夜猫子] 当前连续夜间活动${currentStreak}天，目标${targetDays}天，继续努力`);
    }

  } catch (error) {
    console.error('[夜猫子] 检查夜猫子成就失败:', error);
  }
};

/**
 * 处理金牌毕业生成就检测（通过所有必考科目的考试）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 */
const processGoldGraduateAchievement = async (userId, openId, enterpriseId) => {
  try {
    console.log(`[金牌毕业生成就] 开始检测金牌毕业生成就: 用户${userId}`);

    const { ExamRecord, ExamConfig, User } = require('../models');
    const { addEnterpriseFilter } = require('./enterpriseFilter');

    // 查找金牌毕业生成就模板
    const AchievementTemplate = require('../models/AchievementTemplate');
    const UserAchievement = require('../models/UserAchievement');

    const template = await AchievementTemplate.findOne(
      addEnterpriseFilter({
        where: {
          category: 'exam',
          ruleType: 'progress',
          isActive: true,
          name:"金牌毕业生"
        }
      }, enterpriseId)
    );

    if (!template) {
      console.log('[金牌毕业生成就] 未找到金牌毕业生成就模板');
      return;
    }

    // 解析触发条件
    let triggerCondition;
    try {
      triggerCondition = JSON.parse(template.triggerCondition);
    } catch (error) {
      console.error('[金牌毕业生成就] 解析触发条件失败:', error);
      return;
    }

    // 检查用户是否已获得该成就
    const existingAchievement = await UserAchievement.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id,
          status: 'achieved'
        }
      }, enterpriseId)
    );

    if (existingAchievement) {
      console.log('[金牌毕业生成就] 用户已获得该成就');
      return;
    }

    // 获取用户信息
    const user = await User.findByPk(userId);
    if (!user) {
      console.log(`[金牌毕业生成就] 未找到用户ID:${userId}`);
      return;
    }

    // 获取所有必考科目配置
    const requiredExamConfigs = await ExamConfig.findAll(
      addEnterpriseFilter({
        where: {
          status: '必考'
        }
      }, enterpriseId)
    );

    if (requiredExamConfigs.length === 0) {
      console.log('[金牌毕业生成就] 未找到必考科目配置');
      return;
    }

    console.log(`[金牌毕业生成就] 找到${requiredExamConfigs.length}个必考科目配置`);

    // 对每个必考科目，检查用户是否有通过的考试记录
    const passedExams = [];
    const failedExams = [];

    for (const config of requiredExamConfigs) {
      // 查找用户在该科目的考试记录，按时间倒序排列
      // 注意：ExamRecord表中使用positionId和levelId，而不是positionName和positionLevel
      const examRecords = await ExamRecord.findAll(
        addEnterpriseFilter({
          where: {
            openId,
            positionId: config.positionName, // ExamConfig.positionName实际存储的是PositionName的ID
            levelId: config.positionLevel,   // ExamConfig.positionLevel实际存储的是Level的ID
            kbId: config.examSubject,
            examStatus: 'completed',
            confirmStatus: '2' // 已通过
          },
          order: [['examTime', 'DESC']]
        })
      );
      console.log(config)

      if (examRecords.length > 0) {
        // 有通过的考试记录
        passedExams.push({
          config,
          record: examRecords[0] // 最新的考试记录
        });
      } else {
        // 没有通过的考试记录
        failedExams.push(config);
      }
    }

    console.log(`[金牌毕业生成就] 通过的科目: ${passedExams.length}, 未通过的科目: ${failedExams.length}`);

    // 如果所有必考科目都通过了，颁发成就
    if (failedExams.length === 0 && passedExams.length > 0) {
      console.log(`[金牌毕业生成就] 用户${userId}通过了所有${passedExams.length}个必考科目，触发金牌毕业生成就`);

      await awardAchievement(template, userId, openId, enterpriseId, {
        currentValue: passedExams.length,
        targetValue: passedExams.length,
        completedAt: new Date(),
        passedExams: passedExams.map(item => ({
          subject: item.config.examSubject,
          score: item.record.score,
          examTime: item.record.examTime
        })),
        description: `通过了所有${passedExams.length}个必考科目的考试`
      });
    } else {
      console.log(`[金牌毕业生成就] 用户${userId}还有${failedExams.length}个必考科目未通过，无法获得金牌毕业生成就`);
    }
  } catch (error) {
    console.error('[金牌毕业生成就] 检测金牌毕业生成就失败:', error);
  }
};

/**
 * 处理考试成就触发（用于考试完成和审核通过时调用）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 * @param {number} examRecordId - 考试记录ID
 * @param {number} score - 考试得分
 * @param {number} questionCount - 题目总数
 */
const handleExamAchievementTrigger = async (userId, openId, enterpriseId, examRecordId, score, questionCount) => {
  try {
    console.log('[成就触发] 处理考试成就触发：', { userId, openId, examRecordId, score, questionCount });

    if (!userId || !openId) {
      console.warn('[成就触发] 缺少用户信息，无法进行成就检测');
      return;
    }

    // 旗开得胜成就（第一次考试满分）
    await processFirstPerfectScoreAchievement(userId, openId, enterpriseId, examRecordId, score, questionCount);

    // 金牌毕业生成就（通过所有必考科目）
    await processGoldGraduateAchievement(userId, openId, enterpriseId);

  } catch (error) {
    console.error('[成就触发] 处理考试成就触发失败:', error);
  }
};

/**
 * 处理全能力者成就检测（所有岗位都有练习记录）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 */
const processAllRounderAchievement = async (userId, openId, enterpriseId) => {
  try {
    console.log(`[全能力者成就] 开始检测全能力者成就: 用户${userId}`);

    const PracticeRecord = require('../models/practice-record');
    const { addEnterpriseFilter } = require('./enterpriseFilter');

    // 查找全能力者成就模板
    const AchievementTemplate = require('../models/AchievementTemplate');
    const UserAchievement = require('../models/UserAchievement');

    const template = await AchievementTemplate.findOne(
      addEnterpriseFilter({
        where: {
          name: '全能力者',
          category: 'learning',
          ruleType: 'progress',
          isActive: true
        }
      }, enterpriseId)
    );

    if (!template) {
      console.log('[全能力者成就] 未找到全能力者成就模板');
      return;
    }

    // 检查用户是否已获得该成就
    const existingAchievement = await UserAchievement.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id,
          status: 'achieved'
        }
      }, enterpriseId)
    );

    if (existingAchievement) {
      console.log('[全能力者成就] 用户已获得该成就');
      return;
    }

    // 获取系统中所有的岗位配置
    // 使用Position模型来获取所有岗位和等级组合
    const Position = require('../models/Position');
    const PositionName = require('../models/PositionName');
    const Level = require('../models/Level');

    const allPositions = await Position.findAll(
      addEnterpriseFilter({
        where: { status: true }, // 只获取启用的岗位
        include: [
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['name']
          },
          {
            model: Level,
            as: 'level',
            attributes: ['name']
          }
        ]
      }, enterpriseId)
    );

    if (allPositions.length === 0) {
      console.log('[全能力者成就] 未找到任何岗位配置');
      return;
    }

    // 生成岗位名称-等级的组合
    const allPositionCombinations = allPositions.map(p => ({
      positionName: p.positionName?.name || `岗位${p.nameId}`,
      positionLevel: p.level?.name || `等级${p.levelId}`,
      combination: `${p.positionName?.name || `岗位${p.nameId}`}-${p.level?.name || `等级${p.levelId}`}`
    }));

    console.log(`[全能力者成就] 系统中共有${allPositionCombinations.length}个岗位组合:`);
    allPositionCombinations.forEach(p => console.log(`  - ${p.combination}`));

    // 获取用户有练习记录的岗位组合（通过关联查询获取名称）
    const userPracticePositions = await PracticeRecord.findAll(
      addEnterpriseFilter({
        where: { openId },
        attributes: ['positionName', 'positionLevel'],
        include: [
          {
            model: PositionName,
            as: 'positionNameData',
            attributes: ['name']
          },
          {
            model: Level,
            as: 'level',
            attributes: ['name']
          }
        ],
        group: ['positionName', 'positionLevel', 'positionNameData.name', 'level.name']
      }, enterpriseId)
    );

    const userPositionCombinations = userPracticePositions.map(p => ({
      positionNameId: p.positionName,
      positionLevelId: p.positionLevel,
      positionName: p.positionNameData?.name || `岗位${p.positionName}`,
      positionLevel: p.level?.name || `等级${p.positionLevel}`,
      combination: `${p.positionNameData?.name || `岗位${p.positionName}`}-${p.level?.name || `等级${p.positionLevel}`}`
    }));

    console.log(`[全能力者成就] 用户在${userPositionCombinations.length}个岗位组合有练习记录:`);
    userPositionCombinations.forEach(p => console.log(`  - ${p.combination}`));

    // 查找用户没有练习记录的岗位组合
    const allCombinationStrings = allPositionCombinations.map(p => p.combination);
    const userCombinationStrings = userPositionCombinations.map(p => p.combination);
    const missingPositions = allCombinationStrings.filter(p => !userCombinationStrings.includes(p));

    console.log(`[全能力者成就] 系统岗位组合: ${allCombinationStrings.join(', ')}`);
    console.log(`[全能力者成就] 用户岗位组合: ${userCombinationStrings.join(', ')}`);
    console.log(`[全能力者成就] 缺少岗位组合: ${missingPositions.join(', ')}`);

    // 查找或创建成就进度记录
    const AchievementProgress = require('../models/AchievementProgress');
    let progressRecord = await AchievementProgress.findOne(
      addEnterpriseFilter({
        where: {
          userId,
          templateId: template.id
        }
      }, enterpriseId)
    );

    const progressPercentage = Math.min(100, (userPositionCombinations.length / allPositionCombinations.length) * 100);

    if (!progressRecord) {
      progressRecord = await AchievementProgress.create(
        addEnterpriseId({
          userId,
          openId,
          templateId: template.id,
          achievementName: template.name,
          ruleType: template.ruleType,
          currentValue: userPositionCombinations.length,
          targetValue: allPositionCombinations.length,
          progressPercentage,
          status: 'in_progress',
          isCompleted: false,
          lastActivityDate: new Date().toISOString().split('T')[0],
          positionData: JSON.stringify({
            completedPositions: userCombinationStrings,
            totalPositions: allCombinationStrings,
            missingPositions
          })
        }, enterpriseId)
      );
    } else {
      // 更新进度记录
      await progressRecord.update(
        addEnterpriseId({
          currentValue: userPositionCombinations.length,
          targetValue: allPositionCombinations.length,
          progressPercentage,
          lastActivityDate: new Date().toISOString().split('T')[0],
          positionData: JSON.stringify({
            completedPositions: userCombinationStrings,
            totalPositions: allCombinationStrings,
            missingPositions
          })
        }, enterpriseId)
      );
    }

    // 如果用户在所有岗位都有练习记录，颁发成就
    if (missingPositions.length === 0 && userPositionCombinations.length > 0) {
      console.log(`[全能力者成就] 用户${userId}在所有${allPositionCombinations.length}个岗位组合都有练习记录，触发全能力者成就`);

      await awardAchievement(template, userId, openId, enterpriseId, {
        currentValue: userPositionCombinations.length,
        targetValue: allPositionCombinations.length,
        completedAt: new Date(),
        completedPositions: userCombinationStrings,
        description: `在所有${allPositionCombinations.length}个岗位组合都有练习记录`
      });
    } else {
      console.log(`[全能力者成就] 用户${userId}还有${missingPositions.length}个岗位组合没有练习记录，无法获得全能力者成就`);
      console.log(`[全能力者成就] 缺少的岗位组合: ${missingPositions.join(', ')}`);
    }
  } catch (error) {
    console.error('[全能力者成就] 检测全能力者成就失败:', error);
  }
};

module.exports = {
  processFirstEntryAchievement,
  processKnowledgeExplorationAchievement,
  processTimeMasterAchievement,
  processFirstPerfectScoreAchievement,
  processStudyStreakAchievement,
  processEndlessLearningAchievement,
  handlePracticeAchievementTrigger,
  handleExamAchievementTrigger,
  checkAllSubjectsProgressAcrossPositions,
  awardAchievement,
  processEarlyBirdAchievement,
  processNightOwlAchievement,
  processGoldGraduateAchievement,
  processAllRounderAchievement
};
