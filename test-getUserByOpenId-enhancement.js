/**
 * 测试 getUserByOpenId 方法增强功能
 * 验证返回员工入职时间和获得徽章数量
 */
require('dotenv').config();

console.log('getUserByOpenId 方法增强说明');
console.log('='.repeat(60));

console.log('\n新增功能：');
console.log('1. 返回员工的入职时间（employee.entryTime）');
console.log('2. 返回用户获得的徽章数量（user_achievements 表统计）');

console.log('\n修改内容：');

console.log('\n1. Employee 查询字段增加：');
console.log('原来：attributes: ["positionTypeId","levelId", "positionId", "enterpriseId","status"]');
console.log('修改后：attributes: ["positionTypeId","levelId", "positionId", "enterpriseId","status", "entryTime"]');

console.log('\n2. 新增徽章数量查询：');
console.log(`
// 查询用户获得的徽章数量
const achievementCount = await UserAchievement.count(
  addEnterpriseFilter({
    where: {
      openId: openId,
      status: 'achieved'
    }
  })
);
`);

console.log('\n3. 返回数据增强：');
console.log(`
const userResponse = {
  ...user.toJSON(),
  // ... 其他字段
  // 添加入职时间
  entryTime: employee?.entryTime || null,
  // 添加获得徽章数量
  achievementCount: achievementCount || 0
};
`);

console.log('\n返回数据格式：');
console.log(`{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "user": {
      "id": 1,
      "username": "user123",
      "nickname": "张三",
      "avatar": "/uploads/avatar.jpg",
      "realName": "张三",
      "phone": "13800138000",
      "openId": "wx_openid_123",
      "currentLevel": 2,
      "currentLevelName": "中级",
      "positionTypeId": 1,
      "positionTypeName": "服务员",
      "levelId": 1,
      "levelName": "初级",
      "positionId": 1,
      "positionName": "前厅服务员",
      "status": "1",
      "entryTime": "2024-01-15T08:00:00.000Z",  // 新增：入职时间
      "achievementCount": 5,                     // 新增：徽章数量
      "companyName": "示例餐厅",
      "auditStatus": "approved",
      "adminAccessUrl": "https://admin.example.com",
      "wsUrl": "wss://ws.example.com"
    }
  }
}`);

console.log('\n字段说明：');
console.log('- entryTime: 员工入职时间，来自 employee 表的 entryTime 字段');
console.log('- achievementCount: 用户获得的徽章总数，统计 user_achievements 表中 status="achieved" 的记录');

console.log('\n查询逻辑：');
console.log('1. 通过 openId 查询 Employee 表，获取入职时间');
console.log('2. 通过 openId 统计 UserAchievement 表中已获得的成就数量');
console.log('3. 使用企业过滤确保数据隔离');
console.log('4. 如果员工不存在，入职时间返回 null');
console.log('5. 如果没有成就记录，徽章数量返回 0');

console.log('\n使用场景：');
console.log('- 微信小程序个人信息页面显示入职时间');
console.log('- 显示用户获得的徽章总数');
console.log('- 用户成就展示和激励');

console.log('\n测试建议：');
console.log('1. 测试有员工记录且有入职时间的用户');
console.log('2. 测试有员工记录但入职时间为空的用户');
console.log('3. 测试没有员工记录的用户');
console.log('4. 测试有成就记录的用户');
console.log('5. 测试没有成就记录的用户');
console.log('6. 验证企业过滤是否正常工作');

console.log('\n注意事项：');
console.log('- 入职时间字段可能为 null，前端需要处理空值情况');
console.log('- 徽章数量只统计 status="achieved" 的成就');
console.log('- 使用 addEnterpriseFilter 确保数据安全');
console.log('- 保持向后兼容性，新字段不影响现有功能');
