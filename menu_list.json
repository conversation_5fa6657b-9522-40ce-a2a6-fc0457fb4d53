{"code": 200, "message": "获取菜单列表成功", "data": [{"component": "dashboard/index", "id": 1, "parentId": 0, "name": "控制台", "path": "/dashboard", "redirect": null, "icon": "dashboard", "sort": 1, "hidden": false, "type": 1, "perms": "dashboard", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": null, "id": 2, "parentId": 0, "name": "企业管理", "path": "/enterprise", "redirect": null, "icon": "team", "sort": 2, "hidden": false, "type": 0, "perms": null, "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33", "children": [{"component": "enterprise/list/index", "id": 21, "parentId": 2, "name": "企业列表", "path": "/enterprise/list", "redirect": null, "icon": null, "sort": 1, "hidden": false, "type": 1, "perms": "enterprise.list", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "enterprise/agent/index", "id": 22, "parentId": 2, "name": "智能体列表", "path": "/enterprise/agent", "redirect": null, "icon": null, "sort": 2, "hidden": false, "type": 1, "perms": "enterprise.agent", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "enterprise/knowledge/index", "id": 23, "parentId": 2, "name": "知识库列表", "path": "/enterprise/knowledge", "redirect": null, "icon": null, "sort": 3, "hidden": false, "type": 1, "perms": "enterprise.knowledge", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}]}, {"component": null, "id": 3, "parentId": 0, "name": "组织管理", "path": "/organization", "redirect": null, "icon": "apartment", "sort": 3, "hidden": false, "type": 0, "perms": null, "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33", "children": [{"component": "organization/structure/index", "id": 31, "parentId": 3, "name": "组织架构", "path": "/organization/structure", "redirect": null, "icon": null, "sort": 1, "hidden": false, "type": 1, "perms": "organization.structure", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "organization/employee/index", "id": 32, "parentId": 3, "name": "员工管理", "path": "/organization/employee", "redirect": null, "icon": null, "sort": 2, "hidden": false, "type": 1, "perms": "organization.employee", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "organization/statistics/index", "id": 33, "parentId": 3, "name": "员工统计", "path": "/organization/statistics", "redirect": null, "icon": null, "sort": 3, "hidden": false, "type": 1, "perms": "organization.statistics", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "organization/role/index", "id": 34, "parentId": 3, "name": "角色管理", "path": "/organization/role", "redirect": null, "icon": null, "sort": 4, "hidden": false, "type": 1, "perms": "organization.role", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "organization/dictionary/index", "id": 35, "parentId": 3, "name": "字典管理", "path": "/organization/dictionary", "redirect": null, "icon": null, "sort": 5, "hidden": false, "type": 1, "perms": "organization.dictionary", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}]}, {"component": "knowledge-base/index", "id": 4, "parentId": 0, "name": "知识库管理", "path": "/knowledge-base", "redirect": null, "icon": "book", "sort": 4, "hidden": false, "type": 1, "perms": "knowledge.base", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "views/exam-manage/index", "id": 5, "parentId": 0, "name": "练考管理", "path": "/exam-manage", "redirect": null, "icon": "form", "sort": 5, "hidden": false, "type": 1, "perms": "exam.manage", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 16:20:35", "children": [{"component": "exam-manage/config/index", "id": 103, "parentId": 5, "name": "练考配置", "path": "/exam-manage/config", "redirect": null, "icon": "", "sort": 0, "hidden": false, "type": 1, "perms": "exam.manage", "status": true, "createTime": "2025-04-11 16:08:55", "updateTime": "2025-04-11 16:19:30"}, {"component": "views/exam-manage/review/index", "id": 104, "parentId": 5, "name": "考试审核", "path": "exam-manage/review/index", "redirect": null, "icon": "", "sort": 0, "hidden": false, "type": 1, "perms": "exam.manage", "status": true, "createTime": "2025-04-11 16:09:19", "updateTime": "2025-04-11 16:13:44"}, {"component": "views/exam-manage/practice/index", "id": 105, "parentId": 5, "name": "练习记录", "path": "exam-manage/practice/index", "redirect": null, "icon": "", "sort": 0, "hidden": false, "type": 1, "perms": "exam.manage", "status": true, "createTime": "2025-04-11 16:09:40", "updateTime": "2025-04-11 16:13:49"}, {"component": "views/exam-manage/exam/index", "id": 106, "parentId": 5, "name": "考试记录", "path": "exam-manage/exam/index", "redirect": null, "icon": "", "sort": 0, "hidden": false, "type": 0, "perms": "exam.manage", "status": true, "createTime": "2025-04-11 16:09:53", "updateTime": "2025-04-11 16:13:57"}, {"component": "exam-manage/certificate/index", "id": 107, "parentId": 5, "name": "证书记录", "path": "exam-manage/certificate/index", "redirect": null, "icon": "", "sort": 0, "hidden": false, "type": 0, "perms": "exam.manage", "status": true, "createTime": "2025-04-11 16:10:08", "updateTime": "2025-04-11 16:18:11"}]}, {"component": null, "id": 6, "parentId": 0, "name": "餐考师", "path": "/catering-examiner", "redirect": null, "icon": "customer-service", "sort": 6, "hidden": false, "type": 0, "perms": null, "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33", "children": [{"component": "catering-examiner/front-hall/index", "id": 61, "parentId": 6, "name": "前厅餐考师", "path": "/catering-examiner/front-hall", "redirect": null, "icon": null, "sort": 1, "hidden": false, "type": 1, "perms": "catering.examiner.front", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "catering-examiner/back-kitchen/index", "id": 62, "parentId": 6, "name": "后厨餐考师", "path": "/catering-examiner/back-kitchen", "redirect": null, "icon": null, "sort": 2, "hidden": false, "type": 1, "perms": "catering.examiner.back", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}]}, {"component": "feedback/index", "id": 7, "parentId": 0, "name": "意见反馈", "path": "/feedback", "redirect": null, "icon": "message", "sort": 7, "hidden": false, "type": 1, "perms": "feedback", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": null, "id": 8, "parentId": 0, "name": "系统管理", "path": "/system", "redirect": null, "icon": "setting", "sort": 8, "hidden": false, "type": 0, "perms": null, "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33", "children": [{"component": "system/user/index", "id": 81, "parentId": 8, "name": "用户管理", "path": "/system/user", "redirect": null, "icon": null, "sort": 1, "hidden": false, "type": 1, "perms": "system.user", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "system/role/index", "id": 82, "parentId": 8, "name": "角色管理", "path": "/system/role", "redirect": null, "icon": null, "sort": 2, "hidden": false, "type": 1, "perms": "system.role", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "system/menu/index", "id": 83, "parentId": 8, "name": "菜单管理", "path": "/system/menu", "redirect": null, "icon": null, "sort": 3, "hidden": false, "type": 1, "perms": "system.menu", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}]}, {"component": null, "id": 9, "parentId": 0, "name": "岗位管理", "path": "/position", "redirect": null, "icon": "user", "sort": 9, "hidden": false, "type": 0, "perms": null, "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33", "children": [{"component": "position/level/index", "id": 91, "parentId": 9, "name": "岗位等级", "path": "/position/level", "redirect": null, "icon": null, "sort": 1, "hidden": false, "type": 1, "perms": "position.level", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "position/management/index", "id": 92, "parentId": 9, "name": "岗位管理", "path": "/position/management", "redirect": null, "icon": null, "sort": 2, "hidden": false, "type": 1, "perms": "position.management", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "position/promotion/index", "id": 93, "parentId": 9, "name": "晋升配置", "path": "/position/promotion", "redirect": null, "icon": null, "sort": 3, "hidden": false, "type": 1, "perms": "position.promotion", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}]}, {"component": null, "id": 10, "parentId": 0, "name": "系统配置", "path": "/config", "redirect": null, "icon": "file", "sort": 10, "hidden": false, "type": 0, "perms": null, "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33", "children": [{"component": "config/info/index", "id": 101, "parentId": 10, "name": "信息配置", "path": "/config/info", "redirect": null, "icon": null, "sort": 1, "hidden": false, "type": 1, "perms": "config.info", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}, {"component": "config/announcement/index", "id": 102, "parentId": 10, "name": "公告配置", "path": "/config/announcement", "redirect": null, "icon": null, "sort": 2, "hidden": false, "type": 1, "perms": "config.announcement", "status": true, "createTime": "2025-04-11 10:07:33", "updateTime": "2025-04-11 10:07:33"}]}, {"component": "views/test/index", "id": 108, "parentId": 0, "name": "测试菜单", "path": "/test", "redirect": null, "icon": null, "sort": 99, "hidden": false, "type": 1, "perms": "test:menu", "status": true, "createTime": "2025-04-11 16:23:20", "updateTime": "2025-04-11 16:23:20"}]}