# 菜单故障排除指南

当你遇到"菜单点击无反应"或"数据加载不出来"的问题时，请按照以下步骤进行排查。

## 🚀 快速诊断

### 方法1: 使用控制台一键诊断

1. 打开浏览器开发者工具（F12）
2. 切换到 Console 面板
3. 输入以下命令并回车：

```javascript
// 快速诊断
diagnoseMenu()

// 或者更详细的诊断
quickDiagnose().then(console.log)
```

### 方法2: 手动检查步骤

## 🔍 详细排查步骤

### 1. 检查基础环境

#### ✅ 登录状态检查
```javascript
// 检查是否有token
console.log('Token:', localStorage.getItem('token'))

// 检查用户信息
import { getUserInfo } from '@/utils/auth'
console.log('用户信息:', getUserInfo())
```

**常见问题:**
- Token过期 → 重新登录
- 用户信息丢失 → 刷新页面

#### ✅ 网络连接检查
```javascript
// 检查API连接
fetch('/api/health').then(res => console.log('API状态:', res.status))
```

**常见问题:**
- 网络断开 → 检查网络连接
- 服务器错误 → 联系技术支持

### 2. 检查菜单数据

#### ✅ 菜单加载状态
```javascript
// 检查菜单store
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
console.log('菜单数据:', userStore.userMenus)
console.log('权限数据:', userStore.permissions)
```

**常见问题:**
- 菜单数组为空 → 权限不足或接口错误
- 菜单数据格式错误 → 后端接口返回格式问题

#### ✅ 强制重新加载菜单
```javascript
// 强制重新加载
const userStore = useUserStore()
userStore.getUserMenus().then(menus => {
  console.log('重新加载的菜单:', menus)
})
```

### 3. 检查路由配置

#### ✅ 当前路由状态
```javascript
// 检查当前路由
import { useRoute } from 'vue-router'
const route = useRoute()
console.log('当前路由:', route.path)
console.log('路由匹配:', route.matched)
```

#### ✅ 权限检查
```javascript
// 检查路由权限
import { checkPermission } from '@/utils/permission'
const hasPermission = checkPermission(route, userStore.permissions)
console.log('是否有权限:', hasPermission)
```

### 4. 检查组件状态

#### ✅ MenuTree组件状态
打开浏览器开发者工具，查看Console面板中的菜单相关日志：

- `[MenuTree]` 开头的日志：菜单组件状态
- `[UserStore]` 开头的日志：用户数据状态
- `[Layout]` 开头的日志：布局组件状态

## 🛠️ 常见问题解决方案

### 问题1: 菜单完全不显示

**可能原因:**
- 用户未登录或token过期
- 菜单数据获取失败
- 权限不足

**解决方案:**
1. 重新登录
2. 检查网络连接
3. 联系管理员检查权限配置

### 问题2: 菜单显示但点击无反应

**可能原因:**
- 路由配置错误
- 组件加载失败
- JavaScript错误

**解决方案:**
1. 检查浏览器控制台是否有错误
2. 检查路由配置是否正确
3. 刷新页面重试

### 问题3: 部分菜单不显示

**可能原因:**
- 权限不足
- 菜单配置中设置了hidden
- 路由权限检查失败

**解决方案:**
1. 检查用户角色和权限
2. 联系管理员检查菜单配置
3. 检查路由meta中的permission字段

### 问题4: 数据加载缓慢或失败

**可能原因:**
- 网络连接不稳定
- 服务器响应慢
- 接口错误

**解决方案:**
1. 检查网络连接
2. 等待一段时间后重试
3. 查看Network面板检查接口响应

## 🔧 高级调试技巧

### 启用详细日志

在开发环境下，系统会自动输出详细的调试日志。如果需要在生产环境启用，可以：

```javascript
// 临时启用调试模式
localStorage.setItem('DEBUG_MENU', 'true')
// 刷新页面生效
```

### 查看请求详情

打开浏览器开发者工具，切换到Network面板：

1. 刷新页面
2. 查看是否有失败的请求（红色）
3. 点击失败的请求查看详细错误信息

### 检查浏览器兼容性

确保浏览器支持以下特性：
- ES6语法
- fetch API
- localStorage
- Promise

推荐使用：
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## 📞 获取帮助

如果以上方法都无法解决问题，请：

1. 运行 `diagnoseMenu()` 获取诊断报告
2. 截图保存控制台错误信息
3. 记录复现步骤
4. 联系技术支持并提供以上信息

## 🔄 版本更新

定期检查是否有系统更新：
- 清除浏览器缓存
- 刷新页面
- 检查是否有新的功能或修复 