/**
 * 统一错误处理
 * @param {Object} res - 响应对象
 * @param {Error} error - 错误对象
 */
exports.handleError = (res, error) => {
  console.error('操作失败:', error);
  
  // 区分错误类型
  if (error.name === 'SequelizeValidationError' || error.name === 'SequelizeUniqueConstraintError') {
    // 数据验证错误
    return res.status(400).json({
      code: 400,
      message: '数据验证错误',
      errors: error.errors.map(err => ({
        field: err.path,
        message: err.message
      }))
    });
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    // 外键约束错误
    return res.status(400).json({
      code: 400,
      message: '数据关联错误，请检查关联数据是否存在'
    });
  } else if (error.name === 'SequelizeDatabaseError') {
    // 数据库错误
    return res.status(500).json({
      code: 500,
      message: '数据库操作错误',
      error: process.env.NODE_ENV === 'production' ? undefined : error.message
    });
  }
  
  // 默认错误处理
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'production' ? undefined : error.message
  });
}; 