const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 考试审核申请模型
 * 用于管理企业提交的考试审核申请信息和考试分数
 */
const ExamReviewApplication = sequelize.define('ExamReviewApplication', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },

  applicationNo: {
    type: DataTypes.STRING(50),
    field: 'application_no',
    allowNull: false,
    unique: true,
    comment: '申请编号'
  },

  enterpriseId: {
    type: DataTypes.BIGINT,
    field: 'enterprise_id',
    allowNull: false,
    comment: '企业ID'
  },

  applicantName: {
    type: DataTypes.STRING(50),
    field: 'applicant_name',
    allowNull: false,
    comment: '申请人姓名'
  },

  contactPhone: {
    type: DataTypes.STRING(20),
    field: 'contact_phone',
    allowNull: false,
    comment: '联系电话'
  },

  examType: {
    type: DataTypes.STRING(20),
    field: 'exam_type',
    allowNull: false,
    comment: '考试类型：理论考试、实操考试、综合考试'
  },

  examTitle: {
    type: DataTypes.STRING(100),
    field: 'exam_title',
    allowNull: false,
    comment: '考试名称'
  },

  positionLevel: {
    type: DataTypes.STRING(20),
    field: 'position_level',
    allowNull: true,
    comment: '岗位等级：初级、中级、高级等'
  },
  positionName: {
    type: DataTypes.STRING(20),
    field: 'position_name',
    allowNull: true,
    comment: '岗位名称'
  },

  kbId: {
    type: DataTypes.STRING(50),
    field: 'kb_id',
    allowNull: true,
    comment: '知识库id'
  },
  openId: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'open_id',
    comment: '微信openId'
  },
  examDate: {
    type: DataTypes.DATE,
    field: 'exam_date',
    allowNull: true,
    comment: '考试日期'
  },

  reasonForApplication: {
    type: DataTypes.TEXT,
    field: 'reason_for_application',
    allowNull: false,
    comment: '申请原因'
  },

  examQuestions: {
    type: DataTypes.JSON,
    field: 'exam_questions',
    comment: '考试题目信息，包含题目内容、回答和正确结果的JSON数组'
  },

  examScores: {
    type: DataTypes.JSON,
    field: 'exam_scores',
    comment: '考试成绩信息，JSON格式存储考生信息、分数、等级等'
  },
  
  examConfigInfo: {
    type: DataTypes.JSON,
    field: 'exam_config_info',
    allowNull: true,
    comment: '练考配置信息，存储ExamConfigModel的数据'
  },

  status: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: '待审核',
    comment: '申请状态：1待审核、2通过、3不通过'
  },

  scoreConfirmStatus: {
    type: DataTypes.STRING(20),
    field: 'score_confirm_status',
    allowNull: true,
    defaultValue: '1', // 默认为待审核状态
    comment: '成绩确认状态：1-待审核、2-已通过、3-已驳回'
  },

  reviewInfo: {
    type: DataTypes.JSON,
    field: 'review_info',
    comment: '审核信息，包含审核人ID、审核时间、审核意见等'
  },
  confirmInfo: {
    type: DataTypes.JSON,
    field: 'confirm_info',
    comment: '确认成绩审核信息，包含审核人ID、审核时间、审核意见等'
  },
  certificateRecordId: {
    type: DataTypes.BIGINT,
    field: 'certificate_record_id',
    allowNull: true,
    comment: '关联的证书记录ID'
  },
  delFlag: {
    type: DataTypes.BOOLEAN,
    field: 'del_flag',
    allowNull: false,
    defaultValue: 0,
    comment: '逻辑删除标志：0-未删除，1-已删除'
  },

  createdBy: {
    type: DataTypes.BIGINT,
    field: 'created_by',
    allowNull: false,
    comment: '创建人ID'
  },

  updatedBy: {
    type: DataTypes.BIGINT,
    field: 'updated_by',
    allowNull: false,
    comment: '更新人ID'
  },
  employeeId: {
    type: DataTypes.INTEGER,
    field: 'employee_id',
    allowNull: true,
    comment: '员工ID'
  },
  openId: {
    type: DataTypes.STRING(255),
    field: 'open_id',
    allowNull: true,
    comment: '微信openId'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    field: 'is_active',
    allowNull: true,
    defaultValue: true,
    comment: '是否有效(1有效 0无效)'
  },
  careerRecordId: {
    type: DataTypes.INTEGER,
    field: 'career_record_id',
    allowNull: true,
    comment: '员工履历记录ID'
  }
}, {
  tableName: 'exam_review_applications',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'uk_application_no',
      unique: true,
      fields: ['application_no']
    },
    {
      name: 'idx_enterprise_status',
      fields: ['enterprise_id', 'status']
    },
    {
      name: 'idx_enterprise_exam_date',
      fields: ['enterprise_id', 'exam_date']
    },
    {
      name: 'idx_enterprise_applicant',
      fields: ['enterprise_id', 'applicant_name']
    },
    {
      name: 'idx_del_flag',
      fields: ['del_flag']
    }
  ]
});

module.exports = ExamReviewApplication;
