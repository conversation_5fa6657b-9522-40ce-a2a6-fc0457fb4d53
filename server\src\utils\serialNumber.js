/**
 * 生成序列号工具
 * 用于生成申请编号等唯一标识
 */

/**
 * 生成序列号 
 * @param {String} prefix - 前缀，如 'EX'表示考试申请
 * @param {Number} enterpriseId - 企业ID
 * @returns {String} 生成的序列号
 */
exports.generateSerialNumber = async (prefix, enterpriseId) => {
  // 生成日期部分：年月日，如220415表示2022年4月15日
  const date = new Date();
  const dateStr = date.getFullYear().toString().slice(2) + 
                 (date.getMonth() + 1).toString().padStart(2, '0') + 
                 date.getDate().toString().padStart(2, '0');
  
  // 生成4位随机数
  const randomNum = Math.floor(1000 + Math.random() * 9000);
  
  // 组合成序列号：前缀 + 企业ID + 日期 + 随机数
  // 例如：EX01220415XXXX
  const serialNumber = `${prefix}${enterpriseId.toString().padStart(2, '0')}${dateStr}${randomNum}`;
  
  return serialNumber;
};

/**
 * 解析序列号
 * @param {String} serialNumber - 序列号
 * @returns {Object} 解析后的数据，包含前缀、企业ID、日期等
 */
exports.parseSerialNumber = (serialNumber) => {
  if (!serialNumber || serialNumber.length < 10) {
    return null;
  }
  
  try {
    // 假设序列号格式为：前缀(2) + 企业ID(2) + 年(2) + 月(2) + 日(2) + 随机数(4)
    const prefix = serialNumber.substring(0, 2);
    const enterpriseId = parseInt(serialNumber.substring(2, 4));
    const year = `20${serialNumber.substring(4, 6)}`;
    const month = serialNumber.substring(6, 8);
    const day = serialNumber.substring(8, 10);
    const randomNum = serialNumber.substring(10);
    
    return {
      prefix,
      enterpriseId,
      date: `${year}-${month}-${day}`,
      randomNum
    };
  } catch (error) {
    console.error('解析序列号失败:', error);
    return null;
  }
}; 