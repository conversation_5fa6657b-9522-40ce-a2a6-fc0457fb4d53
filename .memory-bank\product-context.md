# 阿依来后台管理系统 (Ayilai Admin System)

## 项目信息
- **项目名称**: 阿依来后台管理系统
- **描述**: 企业级后台管理系统，支持多企业架构
- **技术栈**: Node.js + Express + MySQL + Sequelize
- **开发环境**: Windows 10 (10.0.26100)
- **工作目录**: D:\workspace\ayilai\cankao-admin
- **Shell**: PowerShell

## 技术架构
### 后端 (server/)
- **框架**: Express.js 4.21.2
- **数据库**: MySQL + Sequelize 6.37.7
- **认证**: JWT (jsonwebtoken 9.0.2)
- **文件处理**: Multer 1.4.5-lts.2
- **其他依赖**: 
  - bcryptjs (密码加密)
  - canvas (图像处理)
  - redis (缓存)
  - axios (HTTP客户端)
  - uuid (唯一标识符)

### 前端 (web/)
- **框架**: 待确认
- **构建工具**: 待确认

## 项目结构
```
cankao-admin/
├── server/           # 后端服务
│   ├── src/
│   │   ├── app.js           # 主应用文件
│   │   ├── config/          # 配置文件
│   │   │   └── database.js  # 数据库配置
│   │   ├── controllers/     # 控制器
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由
│   │   ├── middleware/      # 中间件
│   │   └── utils/           # 工具函数
│   ├── uploads/             # 文件上传目录
│   ├── public/              # 静态文件
│   ├── Dockerfile           # Docker配置
│   └── package.json         # 依赖配置
└── web/              # 前端应用
```

## 核心特性
- **多企业架构**: 支持企业级多租户
- **用户管理**: 完整的用户认证和授权
- **考试系统**: 在线考试功能
- **知识库**: 知识管理系统
- **证书管理**: 证书生成和管理
- **文件上传**: 支持多种文件类型
- **餐厅配置**: 餐厅相关配置管理

## 数据库设计原则
- 所有模型必须包含企业字段 (organization_id)
- 查询时自动拼接企业ID过滤条件
- 支持多租户数据隔离

## API设计
- **前缀**: /api
- **认证**: JWT Token
- **响应格式**: JSON
- **错误处理**: 统一错误响应格式

## 部署配置
- **容器化**: Docker + Docker Compose
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **反向代理**: 待配置
- **监控**: 健康检查端点 /health

## 开发规范
- 遵循企业级开发标准
- 所有接口需要企业ID验证
- 统一的错误处理机制
- 完整的日志记录
- 代码注释和文档 