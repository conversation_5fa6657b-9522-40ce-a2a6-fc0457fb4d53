server {
    listen 80;
    server_name ck-demo-api.lingmiaoai.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name ck-demo-api.lingmiaoai.com;

    ssl_certificate /etc/nginx/ssl/lingmiaoai.crt;
    ssl_certificate_key /etc/nginx/ssl/lingmiaoai.key;

    location /ws/ {
        proxy_pass http://***********:31494;  # 改为这个IP
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }   

    location / {
        proxy_pass http://***********:31494;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
