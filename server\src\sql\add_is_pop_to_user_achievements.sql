-- 为 user_achievements 表添加 is_pop 字段
-- 用于标记成就是否已弹出显示给用户

-- 检查字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'user_achievements'
    AND COLUMN_NAME = 'is_pop'
);

-- 如果字段不存在，则添加字段
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `user_achievements` ADD COLUMN `is_pop` tinyint(1) NOT NULL DEFAULT 0 COMMENT "是否弹出：0-未弹出，1-已弹出" AFTER `update_by`',
    'SELECT "字段 is_pop 已存在，跳过添加" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示添加结果
SELECT 
    CASE 
        WHEN @column_exists = 0 THEN '✅ 成功添加 is_pop 字段到 user_achievements 表'
        ELSE '⚠️  is_pop 字段已存在，无需添加'
    END as result;

-- 查看表结构确认
DESCRIBE user_achievements;
