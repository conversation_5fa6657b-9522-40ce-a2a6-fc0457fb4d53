/**
 * 标准响应处理
 * @param {Object} res - Express响应对象
 * @param {Object} data - 响应数据
 * @param {String} message - 响应消息
 * @param {Number} code - 状态码，默认200
 */
exports.handleResponse = (res, options) => {
  const { data, message = '操作成功', code = 200 } = options;
  
  return res.status(code).json({
    code,
    message,
    data
  });
};

/**
 * 错误响应处理
 * @param {Object} res - Express响应对象
 * @param {Error|String} error - 错误对象或错误消息
 * @param {Number} code - 错误码，默认500
 */
exports.handleError = (res, error, code = 500) => {
  console.error('请求错误:', error);
  
  // 如果是字符串，则作为错误消息
  const message = typeof error === 'string' ? error : error.message || '服务器内部错误';
  
  return res.status(code).json({
    code,
    message,
    // 非生产环境返回详细错误
    error: process.env.NODE_ENV === 'production' ? undefined : (error.stack || error)
  });
}; 