const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 岗位类型模型
 */
const PositionType = sequelize.define('PositionType', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '类型名称'
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '类型编码'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态（1正常 0停用）'
  },
  sort: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '排序'
  },
  remark: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '备注'
  },
  createBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'create_by',
    comment: '创建者'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'update_by',
    comment: '更新者'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'org_position_type',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

module.exports = PositionType; 