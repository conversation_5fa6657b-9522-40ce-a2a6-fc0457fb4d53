const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const User = sequelize.define('users', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  enterpriseId: {
    type: DataTypes.STRING(255),
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '用户名'
  },
  password: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '密码'
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '昵称'
  },
  avatar: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '头像'
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '邮箱'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '手机号'
  },
  realName: {
    type: DataTypes.STRING(255),
    field: 'real_name',
    allowNull: true,
    comment: '真实姓名'
  },
  realPhone: {
    type: DataTypes.STRING(255),
    field: 'real_phone',
    allowNull: true,
    comment: '实名手机号'
  },
  idNumber: {
    type: DataTypes.STRING(20),
    field: 'id_number',
    allowNull: true,
    comment: '身份证号'
  },
  openId: {
    type: DataTypes.STRING(255),
    field: 'open_id',
    allowNull: true,
    unique: true,
    comment: '微信OpenID'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态：0禁用，1正常'
  },
  platform: {
    type: DataTypes.STRING(20),
    allowNull: true,
    defaultValue: 'admin',
    comment: '用户平台：admin-后台管理系统，miniapp-小程序'
  },
  currentPosition: {
    type: DataTypes.INTEGER,
    field: 'current_position',
    allowNull: true,
    comment: '当前岗位'
  },
  currentLevel: {
    type: DataTypes.INTEGER,
    field: 'current_level',
    allowNull: true,
    comment: '当前等级'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'users'
});

module.exports = User;
