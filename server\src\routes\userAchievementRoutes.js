const express = require('express');
const router = express.Router();
const userAchievementController = require('../controllers/userAchievementController');
const { requireLogin } = require('../middleware/auth');

// 应用认证中间件到所有路由
router.use(requireLogin);

/**
 * 用户成就相关路由
 */

// 获取用户成就列表
router.get('/achievements', userAchievementController.getUserAchievements);

// 获取用户成就进度列表
router.get('/progress', userAchievementController.getUserAchievementProgress);

// 获取用户成就统计
router.get('/stats', userAchievementController.getUserAchievementStats);

// 获取可用成就列表（包含进度）
router.get('/available', userAchievementController.getAvailableAchievements);

// 获取成就详情（包含进度信息）
router.get('/detail/:templateId', userAchievementController.getAchievementDetail);

// 手动触发成就检测（调试用）
router.post('/trigger', userAchievementController.triggerAchievementCheck);

module.exports = router; 