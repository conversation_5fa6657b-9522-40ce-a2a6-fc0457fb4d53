<template>
  <div ref="tableCardRef">
  <a-card class="base-table-card table-card" :bordered="false" v-bind="cardProps" >
    <slot name="header"></slot>
    <a-table
      :columns="processedColumns"
      :data-source="dataSource"
      :row-key="rowKey"
      :pagination="paginationConfig"
      :loading="loading"
      :scroll="scrollConfig"
      v-bind="$attrs"
      @change="handleTableChange"
      :row-selection="rowSelection"
    >
      <!-- 处理自定义插槽 -->
      <template v-for="(_, name) in $slots" :key="name" v-if="name !== 'bodyCell'" #[name]="data">
        <slot :name="name" v-bind="data || {}"></slot>
      </template>
      
      <!-- 默认的操作列 -->
      <template #bodyCell="props">
         <!-- 使用自定义的bodyCell插槽处理 -->
        <slot name="bodyCell" v-bind="props"></slot>
        <!-- 如果当前列是action，且没有被自定义处理过，则显示默认按钮 -->
        <template v-if="props.column && props.column.key === 'action' && showDefaultAction" class="action-buttons" >
          <div class="action-buttons">
            <slot name="actionBefore" :record="props.record"></slot>
            <a-button 
              v-if="actionConfig.detail" 
              class="custom-button" 
              type="link"
              size="small"  
              @click="handleDetail(props.record)"
            >
            <template #icon><eye-outlined /></template>
              详情
            </a-button>
            <a-button 
              v-if="actionConfig.edit" 
              class="custom-button" 
              type="link"
              size="small"
              @click="handleEdit(props.record)"
            >
            <template #icon><edit-outlined /></template>
              编辑
            </a-button>
            <a-popconfirm
              :title="deleteTitle"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(props.record)"
            >
            <a-button 
              v-if="actionConfig.delete" 
              class="custom-button" 
              type="link"
              size="small"
            >
              <template #icon><delete-outlined /></template>
              删除
            </a-button>
            </a-popconfirm>
            <slot name="actionAfter" :record="props.record"></slot>
          </div>
        </template>
      </template>
    </a-table>
    <slot name="footer"></slot>
  </a-card>
</div>
</template>

<script>
import { defineComponent, computed, ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'BaseTable',
  components: {
    EyeOutlined,
    EditOutlined,
    DeleteOutlined
  },
  inheritAttrs: false,
  props: {
    // 表格列配置
    columns: {
      type: Array,
      required: true
    },
    // 表格数据源
    dataSource: {
      type: Array,
      default: () => []
    },
    // 行选择配置
    rowSelection: {
      type: Object,
      default: null
    },
    // 行键值
    rowKey: {
      type: [String, Function],
      default: 'id'
    },
    // 分页配置
    pagination: {
      type: [Object, Boolean],
      default: () => ({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条记录`
      })
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 表格滚动配置
    scroll: {
      type: Object,
      default: () => ({ })
    },
    // 是否显示默认操作按钮
    showDefaultAction: {
      type: Boolean,
      default: true
    },
    // 操作按钮配置
    actionConfig: {
      type: Object,
      default: () => ({})
    },
    // 操作按钮样式
    actionButtonStyle: {
      type: Object,
      default: () => ({ color: '#a18cd1' })
    },
    // 卡片属性
    cardProps: {
      type: Object,
      default: () => ({})
    },
    // 删除确认标题
    deleteTitle: {
      type: String,
      default: '确定删除吗?'
    },
  },
  emits: ['change', 'detail', 'edit', 'delete'],
  setup(props, { emit }) {
    // 表格容器引用
    const tableCardRef = ref(null);
    
    // 表格高度状态
    const tableHeight = ref(750); // 默认高度
    
    // 调整表格高度
    const adjustTableHeight = () => {
      if (!tableCardRef.value) return;
      
      // 获取容器高度
      const parentElement = tableCardRef.value.parentElement;
      const parentHeight = parentElement ? parentElement.offsetHeight : 0;
      
      const paginationHeight = props.pagination === false ? 0 : 77;
      const pageHeaderElement = document.querySelector('.page-header');
      const pageHeaderHeight = pageHeaderElement ? pageHeaderElement.offsetHeight : 80;

      // 72是边距，增加最小高度
      if(parentHeight > 350){
        tableHeight.value = parentHeight - pageHeaderHeight - 72 - paginationHeight;
      }else{
        tableHeight.value = 350; // 增加最小高度到600px
      }
    };
    
    // 监听窗口大小变化
    const handleResize = () => {
      adjustTableHeight();
    };
    
    // 组件挂载后调整表格高度
    onMounted(() => {
      // 延迟执行以确保DOM已完全渲染
      // setTimeout(() => {
        adjustTableHeight();
      // }, 900);
      
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize);
    });
    
    // 组件卸载前移除监听
    onBeforeUnmount(() => {
      window.removeEventListener('resize', handleResize);
    });

    // 处理分页配置
    const paginationConfig = computed(() => {
      if (props.pagination === false) return false;
      
      const defaultConfig = {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条记录`,
        pageSizeOptions: ['10', '20', '50', '100']
      };
      
      // 如果传入了pagination配置，则优先使用传入的配置
      if (typeof props.pagination === 'object') {
        const result = {
          ...defaultConfig,
          ...props.pagination
        };
        return result;
      }
      
      // 如果没有传入pagination配置，则使用dataSource长度作为total
      const result = {
        ...defaultConfig,
        total: props.dataSource?.length || 0
      };
      return result;
    });
    
    // 滚动配置，动态计算y值
    const scrollConfig = computed(() => {
      return {
        // x: props.scroll.x || 1500,
        y: tableHeight.value
      };
    });
    
    // 处理columns配置，确保action列有固定属性和宽度
    const processedColumns = computed(() => {
      return props.columns.map(column => {
        // 如果是action列，添加默认的fixed和width属性
        if (column.key === 'action') {
          return {
            ...column,
            fixed: column.fixed || 'right',
            width: column.width || 180
          };
        }
        return column;
      });
    });

    // 表格变化处理
    const handleTableChange = (pagination, filters, sorter, extra) => {
      emit('change', { pagination, filters, sorter, extra });
    };

    // 详情按钮点击
    const handleDetail = (record) => {
      emit('detail', record);
    };

    // 编辑按钮点击
    const handleEdit = (record) => {
      emit('edit', record);
    };

    // 删除按钮点击
    const handleDelete = (record) => {
      emit('delete', record);
    };


    return {
      tableCardRef,
      tableHeight,
      paginationConfig,
      scrollConfig,
      processedColumns,
      handleTableChange,
      handleDetail,
      handleEdit,
      handleDelete
    };
  }
});
</script>

<style lang="scss" scoped>
.base-table-card {
  // height: 100%;
  box-shadow:none;
  
  :deep(.ant-card-body) {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.ant-table-wrapper) {
    padding: 0px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.ant-table) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.ant-table-container) {
    display: flex;
    flex-direction: column;
    flex: 1;
  }
  
  :deep(.ant-table-body) {
    flex: 1;
    overflow-y: auto;
    min-height:300px;
  }
}
</style> 