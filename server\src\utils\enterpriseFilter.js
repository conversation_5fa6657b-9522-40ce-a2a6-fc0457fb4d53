const { Op } = require('sequelize');
require('dotenv').config();

// 获取环境变量中的默认企业ID
const DEFAULT_ENTERPRISE_ID = process.env.DEFAULT_ENTERPRISE_ID || 1;

/**
 * 添加企业ID过滤条件到查询参数
 * @param {Object} options - Sequelize查询选项
 * @param {number} [enterpriseId] - 可选的企业ID，默认使用环境变量中的企业ID
 * @returns {Object} 添加了企业ID过滤条件的查询选项
 */
const addEnterpriseFilter = (options = {}, enterpriseId = DEFAULT_ENTERPRISE_ID) => {
  const newOptions = { ...options };
  
  // 初始化where条件
  if (!newOptions.where) {
    newOptions.where = {};
  }
  
  // 添加企业ID过滤条件
  newOptions.where.enterpriseId = enterpriseId;
  
  return newOptions;
};

/**
 * 创建时自动添加企业ID
 * @param {Object} data - 创建数据对象
 * @param {number} [enterpriseId] - 可选的企业ID，默认使用环境变量中的企业ID
 * @returns {Object} 添加了企业ID的数据对象
 */
const addEnterpriseId = (data = {}, enterpriseId = DEFAULT_ENTERPRISE_ID) => {
  return {
    ...data,
    enterpriseId
  };
};

/**
 * 批量添加企业ID到数据数组
 * @param {Array} dataArray - 数据对象数组
 * @param {number} [enterpriseId] - 可选的企业ID，默认使用环境变量中的企业ID
 * @returns {Array} 添加了企业ID的数据对象数组
 */
const addEnterpriseIdToArray = (dataArray = [], enterpriseId = DEFAULT_ENTERPRISE_ID) => {
  return dataArray.map(item => ({
    ...item,
    enterpriseId
  }));
};

module.exports = {
  addEnterpriseFilter,
  addEnterpriseId,
  addEnterpriseIdToArray,
  DEFAULT_ENTERPRISE_ID
}; 