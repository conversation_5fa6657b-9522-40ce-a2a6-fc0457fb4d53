-- 成就管理菜单插入语句
-- 设置企业ID变量
SET @ENTERPRISE_ID =  '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32';

-- 插入成就管理主菜单
INSERT INTO `menus` (
  `id`,
  `parent_id`,
  `name`,
  `path`,
  `component`,
  `redirect`,
  `icon`,
  `sort`,
  `hidden`,
  `type`,
  `perms`,
  `status`,
  `create_time`,
  `update_time`,
  `enterprise_id`
) VALUES (
  NULL,
  0,
  '成就管理',
  '/achievement',
  NULL,
  NULL,
  'trophy',
  7,
  0,
  0,
  'achievement',
  1,
  NOW(),
  NOW(),
  @ENTERPRISE_ID
);

-- 获取刚插入的成就管理主菜单ID
SET @achievement_menu_id = LAST_INSERT_ID();

-- 插入成就模板子菜单
INSERT INTO `menus` (
  `id`,
  `parent_id`,
  `name`,
  `path`,
  `component`,
  `redirect`,
  `icon`,
  `sort`,
  `hidden`,
  `type`,
  `perms`,
  `status`,
  `create_time`,
  `update_time`,
  `enterprise_id`
) VALUES (
  NULL,
  @achievement_menu_id,
  '成就模板',
  '/achievement/template',
  'achievement/template/index',
  NULL,
  '',
  1,
  0,
  1,
  'achievement.template',
  1,
  NOW(),
  NOW(),
  @ENTERPRISE_ID
);

-- 获取成就模板菜单ID
SET @template_menu_id = LAST_INSERT_ID();

-- 插入成就模板相关的按钮权限
INSERT INTO `menus` (
  `id`,
  `parent_id`,
  `name`,
  `path`,
  `component`,
  `redirect`,
  `icon`,
  `sort`,
  `hidden`,
  `type`,
  `perms`,
  `status`,
  `create_time`,
  `update_time`,
  `enterprise_id`
) VALUES
-- 查看权限
(
  NULL,
  @template_menu_id,
  '查看成就模板',
  '',
  '',
  NULL,
  '',
  1,
  1,
  2,
  'achievement.template.view',
  1,
  NOW(),
  NOW(),
  @ENTERPRISE_ID
),
-- 新增权限
(
  NULL,
  @template_menu_id,
  '新增成就模板',
  '',
  '',
  NULL,
  '',
  2,
  1,
  2,
  'achievement.template.add',
  1,
  NOW(),
  NOW(),
  @ENTERPRISE_ID
),
-- 编辑权限
(
  NULL,
  @template_menu_id,
  '编辑成就模板',
  '',
  '',
  NULL,
  '',
  3,
  1,
  2,
  'achievement.template.edit',
  1,
  NOW(),
  NOW(),
  @ENTERPRISE_ID
),
-- 删除权限
(
  NULL,
  @template_menu_id,
  '删除成就模板',
  '',
  '',
  NULL,
  '',
  4,
  1,
  2,
  'achievement.template.delete',
  1,
  NOW(),
  NOW(),
  @ENTERPRISE_ID
),
-- 状态切换权限
(
  NULL,
  @template_menu_id,
  '切换成就模板状态',
  '',
  '',
  NULL,
  '',
  5,
  1,
  2,
  'achievement.template.status',
  1,
  NOW(),
  NOW(),
  @ENTERPRISE_ID
),
-- 初始化默认模板权限
(
  NULL,
  @template_menu_id,
  '初始化默认模板',
  '',
  '',
  NULL,
  '',
  6,
  1,
  2,
  'achievement.template.init',
  1,
  NOW(),
  NOW(),
  @ENTERPRISE_ID
);

-- 为超级管理员角色分配成就管理权限
INSERT INTO `role_menus` (
  `id`,
  `role_id`,
  `menu_id`,
  `create_time`,
  `enterprise_id`
) VALUES
(NULL, 1, @achievement_menu_id, NOW(), @ENTERPRISE_ID),
(NULL, 1, @template_menu_id, NOW(), @ENTERPRISE_ID);

-- 查询验证插入结果
SELECT
  m1.id as main_menu_id,
  m1.name as main_menu_name,
  m2.id as sub_menu_id,
  m2.name as sub_menu_name,
  m2.perms as sub_permission
FROM menus m1
LEFT JOIN menus m2 ON m1.id = m2.parent_id
WHERE m1.name = '成就管理' AND m1.enterprise_id = @ENTERPRISE_ID
ORDER BY m1.sort, m2.sort;
