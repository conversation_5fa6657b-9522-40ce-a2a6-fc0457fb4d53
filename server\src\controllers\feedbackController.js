const Feedback = require('../models/Feedback');
const { DictionaryType, DictionaryData } = require('../models/dictionary');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

// 创建反馈
exports.createFeedback = async (req, res) => {
    try {
        const { contactPerson, contactInfo, feedbackTypeId, feedbackContent, feedbackImgs } = req.body;
        const createdBy = req.user.id; // 从请求中获取当前用户ID

        const feedback = await Feedback.create(addEnterpriseId({
            createdBy,
            contactPerson,
            contactInfo,
            feedbackTypeId, // 这里仍使用feedbackTypeId，但实际上是字典数据的id
            feedbackContent,
            feedbackImgs,
            feedbackTime: new Date()
        }));

        return res.status(200).json({
            code: 200,
            message: '创建成功',
            data: feedback
        });
    } catch (error) {
        console.error('创建反馈失败:', error);
        return res.status(500).json({
            code: 500,
            message: '服务器内部错误',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

// 获取反馈列表
exports.listFeedback = async (req, res) => {
    try {
        const { pageNum = 1, pageSize = 10, feedbackTypeId, startTime, endTime } = req.query;
        const offset = (pageNum - 1) * pageSize;

        const where = {};
        if (feedbackTypeId) {
            where.feedbackTypeId = feedbackTypeId;
        }
        if (startTime && endTime) {
            where.feedbackTime = {
                [Op.between]: [new Date(startTime), new Date(endTime)]
            };
        }

        // 添加企业ID过滤
        const { count, rows } = await Feedback.findAndCountAll(
            addEnterpriseFilter({
                where,
                include: [{
                    model: DictionaryData,
                    as: 'feedbackType',
                    attributes: ['id', 'dictLabel', 'dictValue'],
                    include: [{
                        model: DictionaryType,
                        attributes: ['typeCode', 'typeName'],
                        where: { typeCode: 'feedback_type' }
                    }]
                }],
                order: [['feedbackTime', 'DESC']],
                offset,
                limit: parseInt(pageSize)
            })
        );

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: {
                total: count,
                list: rows,
                pageNum: parseInt(pageNum),
                pageSize: parseInt(pageSize)
            }
        });
    } catch (error) {
        console.error('获取反馈列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: '服务器内部错误',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

// 获取反馈详情
exports.detailFeedback = async (req, res) => {
    try {
        const { id } = req.params;

        // 添加企业ID过滤
        const feedback = await Feedback.findOne(
            addEnterpriseFilter({
                where: { id },
                include: [{
                    model: DictionaryData,
                    as: 'feedbackType',
                    attributes: ['id', 'dictLabel', 'dictValue'],
                    include: [{
                        model: DictionaryType,
                        attributes: ['typeCode', 'typeName'],
                        where: { typeCode: 'feedback_type' }
                    }]
                }]
            })
        );

        if (!feedback) {
            return res.status(404).json({
                code: 404,
                message: '反馈不存在'
            });
        }

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: feedback
        });
    } catch (error) {
        console.error('获取反馈详情失败:', error);
        return res.status(500).json({
            code: 500,
            message: '服务器内部错误',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

// 回复反馈
exports.replyFeedback = async (req, res) => {
    try {
        const { id } = req.params;
        const { replyContent } = req.body;
        const replyBy = req.user.id; // 从请求中获取当前用户ID
        
        // 添加企业ID过滤查找反馈
        const feedback = await Feedback.findOne(
            addEnterpriseFilter({
                where: { id }
            })
        );

        if (!feedback) {
            return res.status(404).json({
                code: 404,
                message: '反馈不存在'
            });
        }

        // 更新反馈，添加回复内容和回复时间
        await feedback.update({
            replyContent,
            replyTime: new Date(),
            replyBy
        });

        return res.status(200).json({
            code: 200,
            message: '回复成功',
            data: feedback
        });
    } catch (error) {
        console.error('回复反馈失败:', error);
        return res.status(500).json({
            code: 500,
            message: '服务器内部错误',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

// 获取反馈类型列表
exports.getFeedbackTypes = async (req, res) => {
    try {
        // 查找feedback_type的字典类型
        const dictType = await DictionaryType.findOne(
            addEnterpriseFilter({
                where: { typeCode: 'feedback_type' }
            })
        );
        
        if (!dictType) {
            return res.status(400).json({
                code: 400,
                message: '反馈类型字典不存在'
            });
        }
        
        // 获取所有反馈类型字典数据
        const feedbackTypes = await DictionaryData.findAll(
            addEnterpriseFilter({
                where: { 
                    typeId: dictType.id,
                    status: true 
                },
                attributes: ['id', 'dictLabel', 'dictValue', 'dictSort'],
                order: [['dictSort', 'ASC']]
            })
        );
        
        return res.status(200).json({
            code: 200,
            message: '获取反馈类型成功',
            data: feedbackTypes
        });
    } catch (error) {
        console.error('获取反馈类型失败:', error);
        return res.status(500).json({
            code: 500,
            message: '服务器内部错误',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};
