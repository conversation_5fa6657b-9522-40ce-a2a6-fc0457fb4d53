const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const SystemSetting = sequelize.define('SystemSetting', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  code: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '系统设置代码，唯一标识',
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '系统设置名称',
  },
  value: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '系统设置值',
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '系统设置描述',
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
  },
}, {
  tableName: 'system_settings',
  timestamps: true,
  underscored: true,
});

module.exports = SystemSetting; 