variables:
  GIT_CEILING_DIRECTORIES: /home/<USER>/builds/
  GIT_STRATEGY: "fetch"  # 使用fetch策略获取代码

.template: &template
  tags:
    - erupt

stages:
  - build
  - notify

build-job:
  <<: *template
  stage: build
  only:
    - /^master/
    - /^dev/
  script:
    - echo "开始构建...."
    - chown -R $USER:$USER .  # 将所有文件的所有权更改为当前用户
    - chmod -R u+w .          # 确保所有文件对当前用户有写权限
    - git config --global --add safe.directory $PWD
    - echo '当前工作目录:' "$PWD"
    - echo "直接在本地执行构建..."
    - sh build-backend.sh $DOCKER_REGISTRY_PWD
    - sh build-frontend.sh $DOCKER_REGISTRY_PWD
