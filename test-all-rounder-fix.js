/**
 * 测试修复后的全能力者成就功能
 * 验证ID和名称匹配问题的修复
 */
require('dotenv').config();
const { processAllRounderAchievement } = require('./server/src/utils/achievementUtils');

// 模拟用户信息 - 使用实际的用户ID
const userId = 77;  // 使用日志中的用户ID
const openId = 'test_openid_77';  // 对应的openId
const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID || '1';

async function testAllRounderAchievementFix() {
  console.log('开始测试修复后的全能力者成就功能...');
  console.log('修复内容：解决ID和名称匹配问题');
  console.log('='.repeat(60));

  try {
    // 调用修复后的全能力者成就检测函数
    await processAllRounderAchievement(userId, openId, enterpriseId);

    console.log('='.repeat(60));
    console.log('测试完成！');
    console.log('\n修复验证要点：');
    console.log('1. 用户练习记录应该显示岗位名称-等级名称组合（如：烧烤师-P2）');
    console.log('   而不是ID组合（如：6-19）');
    console.log('2. 系统岗位组合和用户岗位组合应该使用相同的格式进行比较');
    console.log('3. 如果格式一致，缺少的岗位组合数量应该正确计算');
    console.log('4. 查看新增的调试日志，确认两边的组合格式是否一致');

  } catch (error) {
    console.error('测试失败:', error);
    console.error('\n可能的问题：');
    console.error('1. PracticeRecord与PositionName/Level的关联关系问题');
    console.error('2. 关联查询的as别名不正确');
    console.error('3. GROUP BY子句包含关联字段导致的问题');
  }
}

// 执行测试
testAllRounderAchievementFix();
