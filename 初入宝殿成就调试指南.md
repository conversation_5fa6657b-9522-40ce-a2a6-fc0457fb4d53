# 初入宝殿成就调试指南

## 问题描述
用户练习后没有触发"初入宝殿"成就，需要系统性地排查问题。

## 修复内容

### 1. 事件类型修正
- 将 `FIRST_SUBJECT_PROGRESS` 改为 `FIRST_COMPLETE`
- 匹配数据库中的配置：`"type":"first_complete"`

### 2. 规则类型修正
- 代码中查找 `ruleType: 'progress'`（匹配数据库配置）
- 而不是 `ruleType: 'first_progress'`

### 3. 中间件配置
在 `server/src/routes/wechatRoutes.js` 中添加成就中间件：
```javascript
const { achievementPracticeMiddleware } = require('../middleware/achievementMiddleware');

// 在练习相关路由上添加中间件
router.post('/practice/question', achievementPracticeMiddleware, wechatPracticeController.getQuestion);
router.post('/practice/analysis', achievementPracticeMiddleware, wechatPracticeController.parseAnswer);
```

## 调试步骤

### 第一步：检查数据库配置
确认成就模板在数据库中存在且配置正确：
```sql
SELECT * FROM achievement_templates 
WHERE enterprise_id = '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32' 
AND name = '初入宝殿' 
AND is_active = 1;
```

期望结果：
- `rule_type`: 'progress'
- `trigger_condition`: '{"type":"first_complete","rule":"初入宝殿","progress":1}'

### 第二步：检查练习记录
确认用户有练习记录：
```sql
SELECT * FROM practice_record 
WHERE open_id = '你的openId' 
AND exam_subject = '科目ID';
```

### 第三步：检查考试配置
确认有对应的考试配置：
```sql
SELECT * FROM exam_config 
WHERE position_name = '岗位ID' 
AND position_level = '等级ID' 
AND exam_subject = '科目ID' 
AND status = '必考';
```

### 第四步：启用详细日志
在控制器中添加更多日志查看触发情况：

#### 检查中间件是否被调用
在 `server/src/middleware/achievementMiddleware.js` 的 `handlePracticeAchievementTrigger` 函数开头添加：
```javascript
console.log('[成就触发] 中间件被调用，请求体:', req.body);
console.log('[成就触发] 请求头openId:', req.headers.openid);
```

#### 检查事件监听器是否被触发
在 `server/src/utils/achievementEventListener.js` 的 `handleFirstSubjectProgressEvent` 函数开头添加：
```javascript
console.log('[成就检测] 接收到首次完成事件:', eventData);
```

### 第五步：模拟测试
运行测试文件：
```bash
node test-first-entry-achievement.js
```

## 常见问题排查

### 问题1：中间件未被调用
**症状**: 没有看到 `[成就触发]` 相关日志
**解决**: 确认路由配置正确，重启服务器

### 问题2：事件监听器未初始化
**症状**: 有中间件日志但没有事件处理日志
**解决**: 确认在服务器启动时调用了 `setupAchievementEventListeners()`

### 问题3：找不到成就模板
**症状**: 显示 "找到 0 个进度类成就模板"
**解决**: 
- 检查数据库中的 `enterprise_id` 是否匹配
- 检查 `rule_type` 是否为 'progress'
- 检查 `is_active` 是否为 1

### 问题4：找不到练习记录
**症状**: 显示 "未找到练习记录"
**解决**:
- 检查 `getPracticeRecords` 函数的参数
- 确认练习记录的 `exam_subject` 字段类型匹配
- 检查练习记录的 `status` 字段是否为 '必考'

### 问题5：进度计算错误
**症状**: 进度始终为 0%
**解决**:
- 检查 `calculatePracticeQualification` 函数
- 确认练习记录有有效的时长或题目数数据
- 检查考试配置中的 `practiceDuration` 或 `practiceQuestionCount`

## 完整的日志流程
正常情况下应该看到以下日志顺序：

```
[成就触发] 中间件被调用，请求体: { file_id: '1', positionName: '1', positionLevel: '1', time: '5:30' }
[成就触发] 练习事件: 用户1, 岗位1-1, 时长5.5分钟, 时间14点
[成就检测] 检查首次学习进度: 用户1, 科目1, 岗位1-1
[成就检测] 科目1练习记录数: 1, 是否首次学习: true
[成就检测] 所有练习记录: [{ examSubject: '1', status: '必考', totalStudyDuration: 5.5 }]
[成就检测] 接收到首次完成事件: { userId: 1, openId: 'xxx', enterpriseId: 1, subjectId: '1', ... }
[成就检测] 找到 1 个进度类成就模板
[成就检测] 检查成就初入宝殿, 需要进度1%, 当前进度某个百分比%
[成就触发] 初入宝殿成就: 初入宝殿, 科目测试科目, 进度达到某个百分比%
```

## 验证成就获得
检查用户是否成功获得成就：
```sql
SELECT * FROM user_achievements 
WHERE user_id = '用户ID' 
AND template_id = 21;  -- 初入宝殿的模板ID
```

---

*按照此指南逐步排查，应该能找到并解决问题所在。* 