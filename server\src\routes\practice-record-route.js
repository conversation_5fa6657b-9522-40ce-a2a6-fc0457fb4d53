const express = require('express');
const router = express.Router();
const practiceRecordController = require('../controllers/practiceRecordController');

// 获取练习记录列表
router.get('/practice/records', practiceRecordController.getPracticeRecordList);

// 获取练习记录详情
router.get('/practice/records/:id', practiceRecordController.getPracticeRecordDetail);

// 获取用户练习词汇统计数据
router.get('/practice/word-stats', practiceRecordController.getUserPracticeWordStats);

// 获取用户练习词汇列表(改为POST方法接收JSON请求体)
router.post('/practice/words', practiceRecordController.getUserPracticeWordList);

module.exports = router;