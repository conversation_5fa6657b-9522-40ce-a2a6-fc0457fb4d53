const express = require('express');
const router = express.Router();
const feedbackController = require('../controllers/feedbackController.js');
const { requireLogin } = require('../middleware/auth');

// 创建反馈
router.post('/create', requireLogin, feedbackController.createFeedback);

// 获取反馈列表
router.get('/list', requireLogin, feedbackController.listFeedback);

// 获取反馈详情
router.get('/detail/:id', requireLogin, feedbackController.detailFeedback);

// 回复反馈
router.post('/reply/:id', requireLogin, feedbackController.replyFeedback);

// 获取反馈类型列表
router.get('/types', feedbackController.getFeedbackTypes);

module.exports = router;
