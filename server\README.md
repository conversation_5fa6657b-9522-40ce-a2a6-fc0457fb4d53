# 阿依来后台管理系统 - 后端服务

这是阿依来后台管理系统的后端服务，提供了用户、角色、菜单等模块的API接口。

## 技术栈

- Node.js
- Express.js
- dotenv (环境变量管理)
- cors (跨域资源共享)
- body-parser (请求体解析)

## 目录结构

```
server/
  ├── src/                # 源代码目录
  │   ├── app.js          # 应用程序入口
  │   ├── data/           # 数据模型(模拟数据库)
  │   ├── controllers/    # 控制器
  │   └── routes/         # 路由
  ├── .env                # 环境变量配置
  ├── package.json        # 项目依赖
  └── README.md           # 项目说明
```

## 环境变量
复制一份`.env.example` 文件为`.env`文件
在`.env`文件中配置：

```
PORT=3000            # 服务器运行端口
API_PREFIX=/api      # API前缀
# 企业配置
DEFAULT_ENTERPRISE_ID=1
```

## 安装与运行

### 安装依赖

```bash
npm install
```

### 开发模式运行

```bash
npm run dev
```

### 生产模式运行

```bash
npm start
```

## API接口

### 用户模块

- `POST /api/system/user/login` - 用户登录
- `GET /api/system/user/info` - 获取用户信息
- `GET /api/system/user/list` - 获取用户列表
- `POST /api/system/user` - 创建用户
- `PUT /api/system/user` - 更新用户
- `DELETE /api/system/user/:id` - 删除用户

### 角色模块

- `GET /api/system/role/list` - 获取角色列表
- `GET /api/system/role/permissions/:id` - 获取角色权限
- `POST /api/system/role` - 创建角色
- `PUT /api/system/role` - 更新角色
- `DELETE /api/system/role/:id` - 删除角色
- `PUT /api/system/role/permissions` - 更新角色权限

### 菜单模块

- `GET /api/system/menu/list` - 获取菜单列表
- `POST /api/system/menu` - 创建菜单
- `PUT /api/system/menu` - 更新菜单
- `DELETE /api/system/menu/:id` - 删除菜单

## 多企业支持

本系统支持多企业数据隔离，所有模型都必须包含企业ID字段。

### 企业ID字段要求

所有数据库表都必须包含企业ID字段，这是实现多企业数据隔离的关键字段。

```javascript
enterpriseId: {
  type: DataTypes.INTEGER,
  field: 'enterprise_id',
  allowNull: false,
  defaultValue: 1,
  comment: '企业ID'
}
```

### 查询过滤工具

系统提供了通用的查询过滤工具函数，用于在底层查询、修改和删除操作中自动添加企业ID条件：

```javascript
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

// 使用示例：查询时添加企业ID过滤
const users = await User.findAll(
  addEnterpriseFilter({
    where: { status: true }
  })
);

// 使用示例：创建时添加企业ID
const newUser = await User.create(
  addEnterpriseId({
    username: 'test',
    password: 'password'
  })
);
```

所有查询都会基于此ID进行过滤，确保不同企业的数据互相隔离。 