/**
 * 测试修复后的金牌毕业生成就功能
 * 验证ExamRecord字段名称问题的修复
 */
require('dotenv').config();
const { processGoldGraduateAchievement } = require('./server/src/utils/achievementUtils');

// 模拟用户信息 - 使用实际的用户ID
const userId = 77;  // 使用报错日志中的用户ID
const openId = 'oxiSG65bMvpNcF9TORr5mvW-HXo4';  // 使用报错日志中的openId
const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID || '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32';

async function testGoldGraduateAchievementFix() {
  console.log('开始测试修复后的金牌毕业生成就功能...');
  console.log('修复内容：解决ExamRecord表字段名称错误问题');
  console.log('='.repeat(60));
  
  try {
    // 调用修复后的金牌毕业生成就检测函数
    await processGoldGraduateAchievement(userId, openId, enterpriseId);
    
    console.log('='.repeat(60));
    console.log('测试完成！');
    console.log('\n修复验证要点：');
    console.log('1. 不应该再出现 "Unknown column ExamRecord.positionName" 错误');
    console.log('2. 应该能正常查询ExamRecord表，使用正确的字段名：');
    console.log('   - positionId (而不是positionName)');
    console.log('   - levelId (而不是positionLevel)');
    console.log('3. 应该能正常检测用户是否通过了所有必考科目');
    console.log('4. 如果通过所有必考科目，应该触发金牌毕业生成就');
    
  } catch (error) {
    console.error('测试失败:', error);
    console.error('\n错误分析：');
    if (error.message.includes('Unknown column')) {
      console.error('- 仍然存在字段名称错误，需要进一步检查ExamRecord模型');
    } else if (error.message.includes('positionName') || error.message.includes('positionLevel')) {
      console.error('- 可能还有其他地方使用了错误的字段名');
    } else {
      console.error('- 可能是其他类型的错误，需要进一步调试');
    }
  }
}

// 执行测试
testGoldGraduateAchievementFix();
