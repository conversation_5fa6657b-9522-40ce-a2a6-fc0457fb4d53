# 餐烤证书生成接口说明

## 接口信息

- **接口路径**: `POST /api/wechat/certificate/generate`
- **功能**: 根据提供的信息生成餐烤证书图片

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| name | string | 是 | 姓名 | "姜子介" |
| courseName | string | 是 | 课程名称 | "预定流程标准" |
| position | string | 是 | 职位信息 | "服务员（p4级）" |
| certificateNo | string | 是 | 证书编号 | "C2505493172" |
| issueDate | string | 是 | 颁发日期 | "2025-05-28" |
| validDate | string | 是 | 有效期至 | "2025-08-26" |

## 请求示例

```json
{
  "name": "姜子介",
  "courseName": "预定流程标准", 
  "position": "服务员（p4级）",
  "certificateNo": "C2505493172",
  "issueDate": "2025-05-28",
  "validDate": "2025-08-26"
}
```

## 响应示例

### 成功响应
```json
{
  "code": 200,
  "message": "证书生成成功",
  "data": {
    "certificateUrl": "/uploads/certificates/certificate_C2505493172_1733734800000.png",
    "fileName": "certificate_C2505493172_1733734800000.png",
    "certificateNo": "C2505493172"
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "姓名不能为空"
}
```

## 证书样式说明

根据效果图要求，证书包含以下元素：

1. **姓名**: 黑色字体，粗体，字体较大（48px）
2. **恭喜词**: 黑色字体，粗体，字体较小（24px），自动换行
3. **职位信息**: 白色字体，粗体，字体小（20px）
4. **证书编号**: 黑色字体，粗体，字体较小（24px）
5. **颁发日期**: #988A79颜色，粗体（18px）
6. **有效期至**: #988A79颜色，粗体（18px）

## 文件存储

- 背景图片路径: `server/uploads/餐烤证书背景图.jpg`
- 生成证书存储路径: `server/uploads/certificates/`
- 证书文件命名格式: `certificate_{证书编号}_{时间戳}.png`

## 使用前准备

1. 确保背景图片 `餐烤证书背景图.jpg` 存在于 `server/uploads/` 目录下
2. 确保 `server/uploads/certificates/` 目录存在（接口会自动创建）
3. 确保已安装 `canvas` 依赖包

## 测试方法

可以使用提供的测试文件进行测试：

```bash
node test_certificate.js
```

## 注意事项

1. 背景图片必须存在，否则接口返回404错误
2. 所有参数都是必填的，缺少任何参数都会返回400错误
3. 生成的证书图片为PNG格式
4. 证书文件名包含时间戳，确保唯一性 