# 无感更新脚本使用说明

本目录包含了用于实现无感更新（零停机部署）的脚本集合，适用于cankao-admin项目。

## 文件说明

- `graceful-update.sh`: 主要的无感更新脚本，实现零停机部署
- `update-version.sh`: 用于更新Docker镜像版本的脚本
- `monitor-services.sh`: 用于监控服务运行状态的脚本
- `docker-compose.yaml`: Docker Compose配置文件
- `cankao-demo-api.conf`: Nginx API服务配置文件
- `cankao-demo.conf`: Nginx Web服务配置文件
- `restart.sh`: 简单的重启脚本

## 使用方法

### 1. 更新Docker镜像版本

使用`update-version.sh`脚本更新Docker镜像版本：

```bash
# 更新后端API版本
./update-version.sh --api v1.2.3

# 更新前端Web版本
./update-version.sh --web v1.2.3

# 同时更新API和Web版本
./update-version.sh --both v1.2.3
```

### 2. 执行无感更新

使用`graceful-update.sh`脚本执行无感更新：

```bash
./graceful-update.sh
```

该脚本会执行以下操作：
1. 备份当前配置
2. 拉取最新镜像
3. 逐个更新服务（先前端，后后端）
4. 进行健康检查
5. 清理旧镜像

### 3. 监控服务状态

使用`monitor-services.sh`脚本监控服务状态：

```bash
./monitor-services.sh
```

该脚本会显示以下信息：
- Docker镜像版本
- 服务运行状态
- 健康检查状态
- 资源使用情况（CPU、内存等）
- 最近的日志

## 部署流程

完整的部署流程如下：

1. 更新环境变量中的版本号：
   ```bash
   ./update-version.sh --both v1.2.3
   ```

2. 监控服务状态（在另一个终端窗口）：
   ```bash
   ./monitor-services.sh
   ```

3. 执行无感更新：
   ```bash
   ./graceful-update.sh
   ```

4. 验证服务是否正常运行

## 注意事项

- 所有脚本需要在服务器上赋予执行权限：
  ```bash
  chmod +x *.sh
  ```

- 确保服务器上已安装Docker和Docker Compose

- 确保环境变量文件(.env)已正确配置

- 如果更新过程中出现问题，脚本会自动回滚到上一个版本

## 环境变量配置

请确保在项目目录下存在`.env`文件，包含以下必要的环境变量：

- `ADMIN_DOCKER_VERSION`: 后端API的Docker镜像版本
- `ADMIN_WEB_DOCKER_VERSION`: 前端Web的Docker镜像版本
- `ENT_ADMIN_API_PORT`: 后端API的端口
- `ENT_ADMIN_WEB_PORT`: 前端Web的端口
- 其他必要的数据库、Redis、JWT等配置 