# WebSocket连接问题排查指南

## 问题描述
- 直接IP+端口可以连接：`ws://**************:31490` ✅
- 域名连接失败：`ws://cankao-admin.dev.lingmiaoai.com` ❌

## 根本原因分析

### 1. Nginx配置缺少WebSocket代理
原来的nginx配置只有HTTP API代理，缺少WebSocket专门的代理配置。

### 2. WebSocket协议升级问题
WebSocket需要特殊的HTTP头来升级连接：
- `Upgrade: websocket`
- `Connection: upgrade`

### 3. 端口配置问题
- 直接IP访问：明确指定了端口31490
- 域名访问：没有指定端口，默认使用80端口

## 解决方案

### 1. 更新Nginx配置（已完成）
在nginx.conf中添加了WebSocket代理配置：

```nginx
# WebSocket 代理配置
location /ws/ {
    proxy_pass ${VITE_APP_API_BASE_URL};
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_read_timeout 86400;
    proxy_send_timeout 86400;
}
```

### 2. 前端WebSocket连接方式调整
推荐使用以下连接方式：

```javascript
// 开发环境
const wsUrl = 'ws://localhost:3000';

// 生产环境 - 通过nginx代理
const wsUrl = 'ws://cankao-admin.dev.lingmiaoai.com/ws';

// 或者动态判断
const wsUrl = process.env.NODE_ENV === 'production' 
  ? 'ws://cankao-admin.dev.lingmiaoai.com/ws'
  : 'ws://localhost:3000';
```

### 3. 环境变量配置
建议在.env文件中配置WebSocket地址：

```bash
# .env.production
VITE_WS_URL=ws://cankao-admin.dev.lingmiaoai.com/ws

# .env.development  
VITE_WS_URL=ws://localhost:3000
```

### 4. 生产环境部署检查清单

#### 网络层检查
- [ ] 确认域名DNS解析正确
- [ ] 检查防火墙是否开放WebSocket端口
- [ ] 确认负载均衡器支持WebSocket

#### 服务器配置检查
- [ ] Nginx配置已更新并重启
- [ ] 后端WebSocket服务正常运行
- [ ] 检查SSL证书（如果使用wss://）

#### 应用层检查
- [ ] 前端WebSocket连接地址正确
- [ ] 环境变量配置正确
- [ ] 错误日志查看

## 调试建议

### 1. 浏览器开发者工具
- 检查Network标签页的WebSocket连接
- 查看Console中的错误信息
- 检查连接状态和消息流

### 2. 服务器日志
```bash
# 查看nginx错误日志
tail -f /var/log/nginx/error.log

# 查看应用日志
tail -f /path/to/app/logs/app.log
```

### 3. 网络连通性测试
```bash
# 测试端口连通性
telnet cankao-admin.dev.lingmiaoai.com 80

# 测试域名解析
nslookup cankao-admin.dev.lingmiaoai.com
```

### 4. WebSocket连接测试工具
可以使用在线WebSocket测试工具：
- websocket.org/echo.html
- 或者使用postman的WebSocket功能

## 常见错误码及解决方案

### 错误码: 1006 (Connection closed abnormally)
- **原因**: 网络中断或服务器主动关闭连接
- **解决**: 检查网络稳定性，增加重连机制

### 错误码: 1015 (TLS handshake failure)
- **原因**: SSL证书问题
- **解决**: 检查证书配置，确保证书有效

### 404 Not Found
- **原因**: WebSocket路径配置错误
- **解决**: 检查nginx配置和前端连接路径

## 最佳实践

### 1. 连接重试机制
```javascript
function connectWebSocket() {
  const ws = new WebSocket(wsUrl);
  
  ws.onopen = function() {
    console.log('WebSocket连接成功');
  };
  
  ws.onclose = function() {
    console.log('WebSocket连接关闭，3秒后重试');
    setTimeout(connectWebSocket, 3000);
  };
  
  ws.onerror = function(error) {
    console.error('WebSocket连接错误:', error);
  };
}
```

### 2. 心跳检测
```javascript
setInterval(() => {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({ type: 'ping' }));
  }
}, 30000);
```

### 3. 环境隔离
```javascript
const getWebSocketUrl = () => {
  const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = process.env.NODE_ENV === 'production' 
    ? 'cankao-admin.dev.lingmiaoai.com'
    : 'localhost:3000';
  return `${protocol}//${host}/ws`;
};
```

## 总结
WebSocket连接问题主要是由于缺少正确的代理配置导致的。通过添加nginx的WebSocket代理配置，应该可以解决域名连接的问题。如果问题仍然存在，请按照调试建议逐步排查。 