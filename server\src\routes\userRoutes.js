const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { requireLogin } = require('../middleware/auth');

// 公开路由 - 不需要身份验证
// 用户登录
router.post('/login', userController.login);

// 需要身份验证的路由
// 获取当前用户信息
router.get('/current', requireLogin, userController.getCurrentUser);

// 获取用户信息
router.get('/info', requireLogin, userController.getUserInfo);

// 获取用户列表
router.get('/list', requireLogin, userController.getUserList);

// 获取用户菜单权限
router.get('/menu-permissions', requireLogin, userController.getUserMenuPermissions);

// 更新用户个人资料
router.put('/profile', requireLogin, userController.updateUserProfile);

// 修改用户密码
router.put('/password', requireLogin, userController.updateUserPassword);

// 创建用户
router.post('/', requireLogin, userController.createUser);

// 更新用户
router.put('/', requireLogin, userController.updateUser);

// 删除用户
router.delete('/:id', requireLogin, userController.deleteUser);

module.exports = router; 