<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="searchFormItems"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <template #actions>
        <a-button @click="handleRefresh">
          <template #icon><reload-outlined /></template>
          刷新
        </a-button>
      </template>
    </page-header>



    <!-- 成就数据表格 -->
    <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
      <a-tab-pane key="achieved" tab="已获得成就">
        <base-table
          :columns="achievedColumns"
          :data-source="achievedList"
          :loading="achievedLoading"
          :pagination="achievedPagination"
          @change="handleAchievedTableChange"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <!-- 图标列 -->
            <template v-if="column.key === 'icon'">
              <div class="table-icon">
                <img 
                  v-if="record.achievementIcon" 
                  :src="getFullImageUrl(record.achievementIcon)" 
                  :alt="record.achievementName"
                  style="width: 32px; height: 32px; border-radius: 4px;"
                  @error="handleImageError"
                />
                <trophy-outlined v-else style="font-size: 32px; color: #faad14;" />
              </div>
            </template>
            
            <!-- 描述列 -->
            <template v-if="column.key === 'description'">
              <div class="achievement-description">
                {{ getAchievementDescription(record) }}
              </div>
            </template>
            
            <!-- 类别列 -->
            <template v-if="column.key === 'category'">
              <a-tag :color="getCategoryColor(record.category)">
                {{ getCategoryName(record.category) }}
              </a-tag>
            </template>
            
            <!-- 获得时间列 -->
            <template v-if="column.key === 'achievedAt'">
              {{ formatDate(record.achievedAt) }}
            </template>
          </template>
        </base-table>
      </a-tab-pane>
      
      <a-tab-pane key="progress" tab="进行中成就">
        <base-table
          :columns="progressColumns"
          :data-source="progressList"
          :loading="progressLoading"
          :pagination="progressPagination"
          @change="handleProgressTableChange"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <!-- 进度列 -->
            <template v-if="column.key === 'progress'">
              <div>
                <a-progress 
                  :percent="record.progressPercentage" 
                  :size="'small'"
                  :stroke-color="getProgressColor(record.progressPercentage)"
                />
                <span style="font-size: 12px; color: #666;">
                  {{ record.currentValue }} / {{ record.targetValue }}
                </span>
              </div>
            </template>
            
            <!-- 更新时间列 -->
            <template v-if="column.key === 'updateTime'">
              {{ formatDate(record.updateTime) }}
            </template>
          </template>
        </base-table>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  TrophyOutlined, 
  ReloadOutlined
} from '@ant-design/icons-vue'
import { useTablePagination } from '@/utils/common'
import { 
  getUserAchievements,
  getUserAchievementProgress
} from '@/api/achievement/userAchievement'

// 图片基础路径
const VITE_APP_API_BASE_IMG_URL = import.meta.env.VITE_APP_API_BASE_IMG_URL || '';

// 搜索表单
const searchForm = reactive({
  category: ''
})

// 搜索表单配置
const searchFormItems = [
  {
    label: '成就类别',
    field: 'category',
    type: 'select',
    placeholder: '选择类别',
    width: '120px',
    options: [
      { label: '全部', value: '' },
      { label: '学习', value: 'learning' },
      { label: '时间', value: 'time' },
      { label: '考试', value: 'exam' },
      { label: '练习', value: 'practice' }
    ]
  }
]

// 标签页
const activeTab = ref('achieved')

// 已获得成就数据
const achievedList = ref([])
const achievedLoading = ref(false)

// 进行中成就数据
const progressList = ref([])
const progressLoading = ref(false)

// 已获得成就表格配置
const achievedColumns = [
  { title: '图标', key: 'icon', width: 80, align: 'center' },
  { title: '成就名称', dataIndex: 'achievementName', key: 'name', width: 150 },
  { title: '成就描述', key: 'description', width: 200 },
  { title: '用户ID', dataIndex: 'userId', key: 'userId', width: 100 },
  { title: '类别', key: 'category', width: 100, align: 'center' },
  { title: '获得时间', key: 'achievedAt', width: 120, align: 'center' }
]

// 进行中成就表格配置
const progressColumns = [
  { title: '成就名称', dataIndex: 'achievementName', key: 'name', width: 150 },
  { title: '用户名', dataIndex: 'userName', key: 'userName', width: 100 },
  { title: '进度', key: 'progress', width: 250 },
  { title: '最后更新', key: 'updateTime', width: 120, align: 'center' }
]

// 获取完整图片URL
const getFullImageUrl = (imagePath) => {
  if (!imagePath) return ''
  if (imagePath.startsWith('http')) return imagePath
  return VITE_APP_API_BASE_IMG_URL + imagePath
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none'
}

// 获取类别颜色
const getCategoryColor = (category) => {
  const colors = {
    learning: 'blue',
    time: 'green',
    exam: 'orange',
    practice: 'purple'
  }
  return colors[category] || 'default'
}

// 获取类别名称
const getCategoryName = (category) => {
  const names = {
    learning: '学习',
    time: '时间',
    exam: '考试',
    practice: '练习'
  }
  return names[category] || category
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#52c41a'
  if (percentage >= 50) return '#faad14'
  return '#1890ff'
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 获取成就描述
const getAchievementDescription = (record) => {
  // 优先从achievementData中获取description
  if (record.achievementData && record.achievementData.description) {
    return record.achievementData.description
  }
  
  // 其次从triggerCondition中获取rule作为描述
  try {
    if (record.triggerCondition) {
      const condition = typeof record.triggerCondition === 'string' 
        ? JSON.parse(record.triggerCondition)
        : record.triggerCondition
        
      if (condition.rule) {
        return condition.rule
      }
    }
  } catch (e) {
    console.error('解析triggerCondition失败', e)
  }
  
  // 最后返回成就名称
  return `获得成就：${record.achievementName}`
}

// 获取已获得成就数据
const fetchAchievedList = async () => {
  achievedLoading.value = true
  
  try {
    const params = {
      page: achievedPagination.current,
      pageSize: achievedPagination.pageSize,
      category: searchForm.category || undefined
    }
    
    const response = await getUserAchievements(params)
    if (response ) {
      achievedList.value = response?.list || []
      updateAchievedPagination({
        total: response.total || 0,
        current: response.page || achievedPagination.current,
        pageSize: response.pageSize || achievedPagination.pageSize
      })
    }
  } catch (error) {
    console.error('获取已获得成就数据失败:', error)
    message.error('获取数据失败')
  } finally {
    achievedLoading.value = false
  }
}

// 获取进行中成就数据
const fetchProgressList = async () => {
  progressLoading.value = true
  
  try {
    const params = {
      status: 'in_progress',
      page: progressPagination.current,
      pageSize: progressPagination.pageSize,
      category: searchForm.category || undefined
    }
    
    const response = await getUserAchievementProgress(params)
    if (response ) {
      // 处理返回的数据，添加用户名字段
      const processedList = (response.list || []).map(item => ({
        ...item,
        userName: item.user?.nickname || item.user?.username || `用户${item.userId}` || '未知用户'
      }))
      progressList.value = processedList
      updateProgressPagination({
        total: response.total || 0,
        current: response.page || progressPagination.current,
        pageSize: response.pageSize || progressPagination.pageSize
      })
    }
  } catch (error) {
    console.error('获取进行中成就数据失败:', error)
    message.error('获取数据失败')
  } finally {
    progressLoading.value = false
  }
}

// 使用表格分页组合式函数

const { 
  pagination: achievedPagination, 
  handleTableChange: handleAchievedTableChange, 
  updatePagination: updateAchievedPagination, 
  resetPagination: resetAchievedPagination 
} = useTablePagination({
  fetchData: fetchAchievedList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
})

const { 
  pagination: progressPagination, 
  handleTableChange: handleProgressTableChange, 
  updatePagination: updateProgressPagination, 
  resetPagination: resetProgressPagination 
} = useTablePagination({
  fetchData: fetchProgressList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
})

// 事件处理
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values)
  }
  
  // 刷新所有数据
  resetAchievedPagination()
  resetProgressPagination()
  
  // 根据当前标签页加载对应数据
  if (activeTab.value === 'achieved') {
    fetchAchievedList()
  } else if (activeTab.value === 'progress') {
    fetchProgressList()
  }
}

const resetSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values)
  } else {
    Object.assign(searchForm, {
      category: ''
    })
  }
  
  // 重置并刷新数据
  handleSearch()
}

const handleRefresh = () => {
  handleSearch()
}

const handleTabChange = (key) => {
  activeTab.value = key
  if (key === 'achieved') {
    fetchAchievedList()
  } else if (key === 'progress') {
    fetchProgressList()
  }
}

// 初始化
onMounted(() => {
  fetchAchievedList()
})
</script>

<style scoped>
.table-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.achievement-description {
  color: #666;
  font-size: 13px;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.stats-cards .ant-card {
  text-align: center;
}

.stats-cards .ant-statistic-title {
  font-size: 14px;
}

.stats-cards .ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}
</style> 