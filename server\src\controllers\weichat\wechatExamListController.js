/**
 * 微信考试控制器
 * 处理微信小程序考试相关请求
 */
const Employee = require('../../models/Employee');
const Position = require('../../models/Position');
const Level = require('../../models/Level');
const ExamConfig = require('../../models/ExamConfigModel');
const User = require('../../models/user');
const ExamReviewApplication = require('../../models/ExamReviewApplication');
const KnowledgeBase = require('../../models/knowledge-base');
const ExamRecord = require('../../models/ExamRecord');
const {DictionaryData} = require('../../models/dictionary');
const {Op} = require('sequelize');
const PracticeRecord = require('../../models/practice-record');
const sequelize = require('../../config/database');
const PositionName = require('../../models/PositionName');
const CertificateRecord = require('../../models/CertificateRecord');
const PositionType = require('../../models/PositionType');
const EmployeePromotion = require('../../models/EmployeePromotion');
const EmployeePosition = require('../../models/EmployeePosition');
// 导入wechatPracticeController中的方法
const { getPracticeRecords, calculatePracticeQualification } = require('./wechatPracticeController');

/**
 * 查询员工信息
 * @param {string} openId - 微信openId
 * @param {string} enterpriseId - 企业ID
 * @returns {Object} 员工信息
 */
async function getEmployeeInfo(openId, enterpriseId) {
    const employee = await Employee.findOne({
        where: {
            openId,
            enterpriseId,
            status: '1', // 在职状态
        },
        include: [
            {model: Position, as: 'position'},
            {model: Level, as: 'level'}
        ]
    });

    if (!employee) {
        throw { status: 400, message: '未找到员工信息或员工未激活' };
    }

    if (!employee.positionId || !employee.levelId) {
        return {
            baseInfo: {
                id: employee.id,
                name: employee.name,
                departmentId: employee.departmentId,
                positionId: employee.positionId,
                levelId: employee.levelId,
            },
            hasPosition: false
        };
    }

    return { baseInfo: employee, hasPosition: true };
}

/**
 * 获取岗位和等级信息
 * @param {Object} employee - 员工信息
 * @param {string} enterpriseId - 企业ID
 * @returns {Object} 岗位和等级信息
 */
async function getPositionAndLevelInfo(positionId,levelId, enterpriseId) {
    // 查询当前岗位等级
    const currentLevel = await Level.findOne({
        where: {
            id: levelId,
            enterpriseId
        }
    });

    if (!currentLevel) {
        throw { status: 400, message: '未找到岗位等级信息' };
    }

    console.log('当前等级:', currentLevel.name, 'ID:', currentLevel.id, 'orderNum:', currentLevel.orderNum);

    // 先获取当前岗位的详细信息（包括typeId和nameId）
    const currentPosition = await Position.findOne({
        where: {
            enterpriseId,
            nameId: positionId,
            levelId: levelId,
            status: true
        }
    });

    if (!currentPosition) {
        console.log('未找到当前岗位在Position表中的配置');
        // 如果没有找到Position配置，则使用当前级别作为目标级别
        const targetLevelId = currentLevel.id;
        const targetLevelName = currentLevel.name;
        
        // 查询岗位信息
        let positionName = '';
        console.log('岗位ID:', positionId);
        try {
            // 从PositionName表中获取岗位信息
            const position = await PositionName.findOne({
                where: {
                    id: positionId,
                    enterpriseId,
                    status: true
                }
            });
            console.log('岗位信息:', position);
            positionName = position ? position.name : '';
            console.log('从PositionName获取岗位名称:', positionName);
        } catch (error) {
            console.error('获取岗位信息出错:', error);
        }

        return {
            currentLevel,
            targetLevelId,
            targetLevelName,
            positionName
        };
    }

    console.log('当前岗位Position配置:', `typeId: ${currentPosition.typeId}, nameId: ${currentPosition.nameId}`);

    // 查找所有可能的下一等级（orderNum > 当前等级的orderNum）
    const availableNextLevels = await Level.findAll({
        where: {
            enterpriseId,
            orderNum: {[Op.gt]: currentLevel.orderNum},
            status: '1'
        },
        order: [['orderNum', 'ASC']]
    });

    console.log('可能的下一等级数量:', availableNextLevels.length);

    // 查找在Position表中存在对应配置的下一等级
    let nextLevel = null;
    for (const level of availableNextLevels) {
        const positionExists = await Position.findOne({
            where: {
                enterpriseId,
                typeId: currentPosition.typeId,
                nameId: currentPosition.nameId,
                levelId: level.id,
                status: true
            }
        });

        if (positionExists) {
            nextLevel = level;
            console.log('找到在Position表中存在的下一级:', `${level.name} (ID: ${level.id}, orderNum: ${level.orderNum})`);
            break;
        }
    }

    // 如果没有找到有效的下一级，则使用当前级
    const targetLevelId = nextLevel ? nextLevel.id : currentLevel.id;
    const targetLevelName = nextLevel ? nextLevel.name : currentLevel.name;

    console.log('最终确定的目标等级:', targetLevelName, 'ID:', targetLevelId);

    // 查询岗位信息
    let positionName = '';
    console.log('岗位ID:', positionId);
    try {
        // 从PositionName表中获取岗位信息
        const position = await PositionName.findOne({
            where: {
                id: positionId,
                enterpriseId,
                status: true
            }
        });
        console.log('岗位信息:', position);
        positionName = position ? position.name : '';
        console.log('从PositionName获取岗位名称:', positionName);
    } catch (error) {
        console.error('获取岗位信息出错:', error);
    }

    return {
        currentLevel,
        targetLevelId,
        targetLevelName,
        positionName
    };
}

/**
 * 获取考试配置
 * @param {Object} employee - 员工信息
 * @param {string} targetLevelId - 目标等级ID
 * @param {string} enterpriseId - 企业ID
 * @returns {Array} 考试配置列表
 */
async function getExamConfigs(positionId, targetLevelId, enterpriseId) {
    // 首先获取目标等级的orderNum
    const targetLevel = await Level.findOne({
        where: {
            id: targetLevelId,
            enterpriseId
        },
        attributes: ['id', 'orderNum']
    });

    if (!targetLevel) {
        console.log('未找到目标等级信息');
        return [];
    }

    console.log('目标等级信息:', targetLevel.id, 'orderNum:', targetLevel.orderNum);

    // 查询从第1级到目标等级的所有等级ID
    const allLevels = await Level.findAll({
        where: {
            enterpriseId,
            orderNum: {
                [Op.lte]: targetLevel.orderNum // 小于等于目标等级的orderNum
            },
            status: '1'
        },
        attributes: ['id', 'orderNum', 'name'],
        order: [['orderNum', 'ASC']]
    });

    const levelIds = allLevels.map(level => level.id);
    const levelIdsAsString = allLevels.map(level => level.id.toString());
    
    console.log('查询等级范围:', allLevels.map(l => `${l.name}(${l.id})`));
    console.log('等级IDs:', levelIds);

    // 查询必考考试 - 使用IN操作符查询多个等级
    let examConfigs = await ExamConfig.findAll({
        where: {
            enterpriseId,
            positionName: positionId,
            positionLevel: {
                [Op.in]: levelIdsAsString // 优先使用字符串格式
            },
            status: '必考'
        }
    });

    console.log('字符串格式找到考试数量:', examConfigs ? examConfigs.length : 0);

    // 如果没找到，尝试使用数值格式查询
    if (!examConfigs || examConfigs.length === 0) {
        console.log('尝试使用数字格式查询');
        const examConfigsAlt = await ExamConfig.findAll({
            where: {
                enterpriseId,
                positionName: positionId,
                positionLevel: {
                    [Op.in]: levelIds // 使用数字格式
                },
                status: '必考'
            }
        });

        if (examConfigsAlt && examConfigsAlt.length > 0) {
            console.log('数字格式找到考试数量:', examConfigsAlt.length);
            examConfigs = examConfigsAlt;
        } else {
            // 最后尝试不限制positionLevel（保持向后兼容）
            console.log('尝试只匹配岗位名称');
            const allExamConfigs = await ExamConfig.findAll({
                where: {
                    enterpriseId,
                    positionName: positionId,
                    status: '必考'
                }
            });
            console.log('找到考试总数:', allExamConfigs.length);
            if (allExamConfigs.length > 0) {
                console.log('所有position_level值:', allExamConfigs.map(e => e.positionLevel));
                // 从所有考试配置中筛选出符合等级范围的
                examConfigs = allExamConfigs.filter(config => {
                    const configLevel = parseInt(config.positionLevel);
                    return levelIds.includes(configLevel) || levelIdsAsString.includes(config.positionLevel);
                });
                console.log('筛选后的考试数量:', examConfigs.length);
            }
        }
    }

    console.log('最终返回考试配置数量:', examConfigs ? examConfigs.length : 0);
    return examConfigs || [];
}

/**
 * 计算考试资格
 * @param {Object} examConfig - 考试配置
 * @param {Array} practiceRecords - 练习记录列表
 * @returns {Object} 考试资格信息
 */
async function calculateExamQualification(examConfig, practiceRecords) {
    let status = 0; // 默认状态为0
    let examQualification = 0; // 默认资格值为0

    // 查找对应科目的练习记录
    const practiceRecord = practiceRecords.find(record =>
        record.examSubject === examConfig.examSubject
    );


    if (practiceRecord) {
        examQualification = calculatePracticeQualification(
            examConfig,
            practiceRecord
        );

         // 如果学习时长不够，则设置状态为-1（不可申请考试）
        if (examQualification < 100) {
            status = -1;
        } 

    }

    return { status, examQualification };
}

/**
 * 获取考试科目名称和状态
 * @param {Array} examConfigs - 考试配置列表
 * @param {string} openId - 微信openId
 * @param {string} enterpriseId - 企业ID
 * @returns {Object} 考试科目信息和状态
 */
async function getExamSubjectsAndStatus(examConfigs, openId, enterpriseId,positionName,positionLevel) {
    if (!examConfigs.length) {
        return { examSubjectMap: {}, examStatusMap: {}, examResults: [] };
    }

    // 获取所有考试科目ID
    const examSubjectIds = examConfigs.map(config => config.examSubject);
    console.log('考试科目IDs:', examSubjectIds);

    // 查询知识库获取科目名称
    const knowledgeBases = await KnowledgeBase.findAll({
        where: {
            id: {
                [Op.in]: examSubjectIds
            },
            enterpriseId
        }
    });

    // 创建id到name的映射
    const examSubjectMap = {};
    knowledgeBases.forEach(kb => {
        examSubjectMap[kb.id] = kb.name;
    });

    // 获取所有岗位ID并查询岗位名称
    const positionIds = [...new Set(examConfigs.map(config => config.positionName))];
    console.log('岗位IDs:', positionIds);

    // 查询岗位名称
    const positionNames = await PositionName.findAll({
        where: {
            id: {
                [Op.in]: positionIds
            },
            enterpriseId
        },
        attributes: ['id', 'name']
    });

    // 创建岗位ID到名称的映射
    const positionNameMap = {};
    positionNames.forEach(position => {
        positionNameMap[position.id] = position.name;
    });

    // 获取所有等级ID并查询等级名称
    const levelIds = [...new Set(examConfigs.map(config => config.positionLevel))];
    console.log('等级IDs:', levelIds);

    // 查询等级名称
    const levels = await Level.findAll({
        where: {
            id: {
                [Op.in]: levelIds
            },
            enterpriseId
        },
        attributes: ['id', 'name']
    });

    // 创建等级ID到名称的映射
    const levelMap = {};
    levels.forEach(level => {
        levelMap[level.id] = level.name;
    });

    // 根据openId查找用户
    const user = await User.findOne({
        where: {
            openId, 
            status: true,
            enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
        }
    });

    // 根据用户id查询ExamReviewApplication，按kbId分组获取最新记录
    // 先查询每个kbId的最新创建时间
    const latestRecords = await ExamReviewApplication.findAll({
        where: {
            enterpriseId,
            positionName,
            createdBy: user.id // 根据用户ID关联审核记录
        },
        attributes: [
            'kbId',
            [sequelize.fn('MAX', sequelize.col('created_at')), 'max_created_at']
        ],
        group: ['kbId'],
        raw: true
    });

    // 根据最新时间查询完整记录
    const userExamReviews = [];
    for (const record of latestRecords) {
        const latestReview = await ExamReviewApplication.findOne({
            where: {
                enterpriseId,
                positionName,
                createdBy: user.id,
                kbId: record.kbId,
                created_at: record.max_created_at
            }
        });
        if (latestReview) {
            userExamReviews.push(latestReview);
        }
    }


    // 创建kbId到审核状态的映射
    const examStatusMap = {};
    //创建kbId到考试确认状态的映射
    const examScoreConfirmStatusMap = {};
    const reviewApplicationMap = {}
    userExamReviews.forEach(review => {
        // 检查该审核记录是否包含kbId字段
        if (review.kbId && examSubjectIds.includes(review.kbId)) {
            examStatusMap[review.kbId] = review.status;
            examScoreConfirmStatusMap[review.kbId] = review.scoreConfirmStatus;
            reviewApplicationMap[review.kbId] = review
        } else if (review.examTitle && examSubjectIds.includes(review.examTitle)) {
            // 兼容处理：如果examTitle与examSubject匹配
            examStatusMap[review.examTitle] = review.status;
            examScoreConfirmStatusMap[review.examTitle] = review.scoreConfirmStatus;
            reviewApplicationMap[review.examTitle] = review
        }
    });



    // 获取所有科目的已考试次数
    const examCountMap = {};

    // 查询所有科目的考试记录次数
    for (const examSubject of examSubjectIds) {
        const reviewApplication = reviewApplicationMap[examSubject];
        console.log(reviewApplication)
        if(reviewApplication){
            const examCount = await ExamRecord.count({
                where: {
                    openId: openId,
                    reviewApplicationId:reviewApplicationMap[examSubject].id,
                    enterpriseId,
                    examStatus: 'completed'  // 只计算已完成的考试
                }
            });
            examCountMap[examSubject] = examCount;
        }else{
            examCountMap[examSubject] = 0
        }
    }

    // 处理考试状态并返回结果
    const examResults = await Promise.all(examConfigs.map(async (examConfig) => {
        const practiceRecords = await getPracticeRecords(openId, examConfig.positionName, examConfig.positionLevel);
        // 计算考试资格和初始状态
        let { status, examQualification } = await calculateExamQualification(examConfig, practiceRecords);

        let examId = null; // 添加examId字段，默认为null
        let certificationId = null
        //进度不达标，没达到100%
        if(examQualification <100){
            status = -1; // disabled
        }else{
            if (examStatusMap[examConfig.examSubject]) {
                // 审核记录存在，根据状态设置值
                const reviewStatus = examStatusMap[examConfig.examSubject];

                if (reviewStatus === '1') {
                    // 待审核状态
                    status = 1; // 审核中
                } else if (reviewStatus === '2') {
                    // 审核通过，需要进一步判断考试状态

                    // 检查该科目的考试记录
                    const examRecords = await ExamRecord.findOne({
                        where: {
                            positionId: examConfig.positionName,
                            levelId:examConfig.positionLevel,
                            kbId:examConfig.examSubject,
                            enterpriseId,
                            openId: openId,
                            examStatus:'ongoing',
                            reviewApplicationId: reviewApplicationMap[examConfig.examSubject]?.id
                        }
                    });

                    if (examRecords) {
                        // 1. 首先检查是否有进行中的考试
                        status = 5; // 继续考试
                        examId = examRecords.id;
                    }else{
                        const examScoreConfirmStatus = examScoreConfirmStatusMap[examConfig.examSubject] ;
                        // 修复：当scoreConfirmStatus为空或0时，应该默认为待审核状态'1'
                        if (!examScoreConfirmStatus || examScoreConfirmStatus === '0') {
                            //开始考试
                            status = 2
                        }else{
                            if(examScoreConfirmStatus === '1'){
                                //查看报告
                                const latestFinishedExam = await ExamRecord.findOne({
                                    where: {
                                        positionId: examConfig.positionName,
                                        levelId: examConfig.positionLevel,
                                        kbId: examConfig.examSubject,
                                        enterpriseId,
                                        openId: openId,
                                        examStatus: 'completed',
                                        reviewApplicationId: reviewApplicationMap[examConfig.examSubject]?.id
                                    },
                                    order: [['created_at', 'DESC']],
                                    limit: 1
                                });
                                status = 4;
                                console.log('最新完成考试:', examConfig);
                                if(latestFinishedExam){
                                    examId = latestFinishedExam.id;
                                }
                            }else if(examScoreConfirmStatus === '2'){
                                //查看证书
                                status = 6;
                                certificationId = reviewApplicationMap[examConfig.examSubject].certificateRecordId
                            }
                            else if(examScoreConfirmStatus === '3'){
                                //重新申请
                                status = 0
                            }
                        }
                    }
                } else if (reviewStatus === '3') {
                    // 审核被驳回，可以重新申请
                    status = 0; // 申请
                } else {
                    // 其他状态，默认为不可用
                    status = -1; // disabled
                }
            }else{
                //进度达标，但是未申请
                status = 0; // 显示 【申请】
            }

        }


        // 获取考试科目名称，如果不存在则使用ID
        const examSubjectName = examSubjectMap[examConfig.examSubject] || examConfig.examSubject;

        // 获取岗位中文名称
        const positionNameText = positionNameMap[examConfig.positionName] || '未知岗位';

        // 获取等级中文名称
        const positionLevelText = levelMap[examConfig.positionLevel] || '未知等级';

        // 获取已考试次数
        const examinedCount = examCountMap[examConfig.examSubject] || 0;

        return {
            examSubject: examConfig.examSubject,
            examSubjectName,
            positionNameText, // 岗位中文名称字段
            positionLevelText, // 等级中文名称字段
            status,
            examId,
            certificationId,
            examQualification,

            examConfig: reviewApplicationMap[examConfig.examSubject] && reviewApplicationMap[examConfig.examSubject].examConfigInfo
            ? {...reviewApplicationMap[examConfig.examSubject].examConfigInfo, examinedCount}
            : {
                id: examConfig.id,
                positionBelong: examConfig.positionBelong,
                positionName: examConfig.positionName,
                positionLevel: examConfig.positionLevel,
                examDuration: examConfig.examDuration,
                passScore: examConfig.passScore,
                questionCount: examConfig.questionCount || 0,
                examCount: examConfig.examCount || 0,
                practiceDuration: examConfig.practiceDuration || 0,
                examinedCount, // 添加已考试次数
            }
        };
    }));

    return { examSubjectMap, examStatusMap, examResults };
}

/**
 * 根据员工数据查询晋升时间记录
 * @param {Object} employee - 员工数据对象
 * @param {string} enterpriseId - 企业ID
 * @returns {Promise<Object>} 晋升时间记录
 */
async function findEmployeePromotionTime(employee,positionId,levelId, enterpriseId) {
  if(!employee){
    throw new Error('员工信息不能为空');
  }

  if (!positionId) {
    throw new Error('岗位ID不能为空');
  }

  if (!levelId) {
    throw new Error('等级ID不能为空');
  }


  const whereCondition = {
    employeeId: employee.id,
    enterpriseId: enterpriseId || process.env.ENTERPRISE_ID,
    positionId: positionId,
    levelId: levelId,
    positionTypeId: employee.positionTypeId
  };

  console.log('查询晋升记录条件:', employee);
  const promotionRecord = await EmployeePromotion.findOne({
    where: whereCondition,
    attributes: ['id', 'promotionTime', 'positionId', 'levelId', 'positionTypeId', 'createTime'],
    order: [['promotionTime', 'DESC']]
  });

  return promotionRecord;
}

/**
 * 构建响应数据结构
 * @param {Object} employee - 员工基本信息
 * @param {string} positionName - 岗位名称
 * @param {Object} currentLevel - 当前等级
 * @param {string} targetLevelName - 目标等级名称
 * @param {Array} examResults - 考试结果列表
 * @param {Object} promotionRecord - 晋升时间记录
 * @param {number} certificateCount - 证书数量
 * @returns {Object} 响应数据结构
 */
function buildResponseData(employee, positionName, currentLevel, targetLevelName, examResults, promotionRecord, certificateCount = 0) {
    // 计算证书进度：已获得证书数量（status为4的考试）/ 总证书数量
    const totalExams = examResults.length;
    const completedExams = certificateCount;
    const certificationProgress = `${completedExams}/${totalExams}`;

    return {
        employee: {
            id: employee.id,
            name: employee.name,
            position: positionName,
            level: currentLevel.name,
            levelId: currentLevel.id,
            nextLevel: targetLevelName,
            certificationProgress: certificationProgress, // 添加证书进度参数
            promotionTime: promotionRecord ? promotionRecord.promotionTime : employee.entryTime, // 添加晋升时间
            certificateCount: certificateCount,// 添加证书数量
        },
        exams: examResults
    };
}

/**
 * 查询用户当前岗位认证信息
 * @param {Object} req - 请求对象，包含openId和positionId
 * @param {Object} res - 响应对象
 * @returns {Object} 用户信息、考试列表及状态
 */
exports.getUserPositionCertification = async (req, res) => {
    try {
        const openId = req.headers.openid; // 从请求头获取openId
        const { positionId } = req.query; // 从查询参数获取positionId
        // 获取环境变量的DEFAULT_ENTERPRISE_ID
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

        if (!openId) {
            return res.status(400).json({
                code: 400,
                message: '参数错误：缺少openId',
            });
        }

        if (!positionId) {
            return res.status(400).json({
                code: 400,
                message: '参数错误：缺少positionId',
            });
        }

        // 1. 查询员工信息
        const employee = await Employee.findOne({
            where: {
                openId,
                enterpriseId,
                status: '1', // 在职状态
            }
        });

        if (!employee) {
            return res.status(404).json({
                code: 404,
                message: '未找到员工信息或员工未激活'
            });
        }

        // 2. 验证positionId是否为员工的晋升岗位
        Employee.setupEmployeePositionAssociations();

        const employeePosition = await EmployeePosition.findOne({
            where: {
                employeeId: employee.id,
                enterpriseId,
                positionId:positionId
            },
            include: [
                {
                    model: PositionName,
                    as: 'positionName',
                    attributes: ['id', 'name', 'code', 'typeId'],
                    where: {
                        id: positionId
                    },
                    required: true
                },
                {
                    model: Level,
                    as: 'level',
                    attributes: ['id', 'name', 'code', 'orderNum'],
                    required: false
                }
            ]
        });

        if (!employeePosition) {
            return res.status(400).json({
                code: 400,
                message: '该岗位不是员工的晋升岗位'
            });
        }
        // 2. 获取岗位和等级信息
        const { currentLevel, targetLevelId, targetLevelName, positionName } =
            await getPositionAndLevelInfo(employeePosition.positionId,employeePosition.levelId, enterpriseId);
           

        // 3. 查询考试配置
        const examConfigs = await getExamConfigs( employeePosition.positionId,targetLevelId, enterpriseId);

        // 4. 查询晋升时间记录
        const promotionRecord = await findEmployeePromotionTime(employee,employeePosition.positionId,employeePosition.levelId,enterpriseId);

        // 5. 查询证书数量
        const certificateCount = await CertificateRecord.count({
            where: {

                enterpriseId,
                openId,
                positionName:employeePosition.positionId,
                positionLevel:targetLevelId,
                delFlag: 0
            }
        });

        if (!examConfigs.length) {
            return res.status(200).json({
                code: 200,
                message: '没有需要考核的科目',
                data: buildResponseData(
                    employee,
                    positionName,
                    currentLevel,
                    targetLevelName,
                    [],
                    promotionRecord,
                    certificateCount
                )
            });
        }

        // 6. 获取考试科目信息和状态
        const { examResults } = await getExamSubjectsAndStatus(examConfigs, openId, enterpriseId,employeePosition.positionId,targetLevelId);

        // 7. 构建并返回结果
        const responseData = buildResponseData(
            employee,
            positionName,
            currentLevel,
            targetLevelName,
            examResults,
            promotionRecord,
            certificateCount
        );

        return res.status(200).json({
            code: 200,
            message: '获取岗位认证信息成功',
            data: responseData
        });
    } catch (error) {
        console.error('获取岗位认证信息出错：', error);
        const status = error.status || 500;
        const message = error.message || '服务器内部错误';

        return res.status(status).json({
            code: status,
            message,
            error: error.message
        });
    }
};

/**
 * 获取证书详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCertificateDetail = async (req, res) => {
    try {
        const { id } = req.query;
        const openId = req.headers.openid;
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: '证书ID不能为空'
            });
        }

        // 查询证书记录
        const certificate = await CertificateRecord.findOne({
            where: {
                id,
                openId,
                enterpriseId,
                delFlag: 0
            },
            include: [
                {
                    model: PositionName,
                    as: 'positionNameInfo',
                    attributes: ['id', 'name', 'code'],
                    required: false
                },
                {
                    model: Level,
                    as: 'positionLevelInfo',
                    attributes: ['id', 'name', 'code'],
                    required: false
                },
                {
                    model: PositionType,
                    as: 'positionBelongInfo',
                    attributes: ['id', 'name', 'code'],
                    required: false
                }
            ]
        });

        if (!certificate) {
            return res.status(404).json({
                code: 404,
                message: '证书记录不存在'
            });
        }

        // 返回证书信息
        return res.json({
            code: 200,
            data: certificate,
            message: '获取证书详情成功'
        });

    } catch (error) {
        console.error('获取证书详情失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取证书详情失败',
            error: error.message
        });
    }
};

/**
 * 根据openId获取员工晋升岗位信息
 * @param {Object} req - 请求对象，包含openId
 * @param {Object} res - 响应对象
 * @returns {Object} 员工的所有岗位配置信息，包括默认岗位和晋升岗位
 */
exports.getEmployeePromotionPositions = async (req, res) => {
    try {
        const openId = req.headers.openid; // 从请求头获取openId
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

        if (!openId) {
            return res.status(400).json({
                code: 400,
                message: '参数错误：缺少openId',
            });
        }

        // 1. 根据openId查找员工信息
        const employee = await Employee.findOne({
            where: {
                openId,
                enterpriseId,
                status: '1', // 在职状态
            }
        });

        if (!employee) {
            return res.status(404).json({
                code: 404,
                message: '未找到员工信息或员工未激活'
            });
        }

        // 2. 设置员工岗位关联关系
        Employee.setupEmployeePositionAssociations();

        // 3. 查询员工的所有岗位配置
        const employeePositions = await EmployeePosition.findAll({
            where: {
                employeeId: employee.id,
                enterpriseId
            },
            include: [
                {
                    model: PositionName,
                    as: 'positionName',
                    attributes: ['id', 'name', 'code', 'typeId'],
                    required: false
                },
                {
                    model: PositionType,
                    as: 'positionType',
                    attributes: ['id', 'name', 'code'],
                    required: false
                },
                {
                    model: Level,
                    as: 'level',
                    attributes: ['id', 'name', 'code', 'orderNum'],
                    required: false
                }
            ],
            order: [
                ['isDefault', 'DESC'], // 默认岗位排在前面
                ['createTime', 'ASC']
            ]
        });

        if (!employeePositions || employeePositions.length === 0) {
            return res.status(200).json({
                code: 200,
                message: '员工暂无岗位配置',
                data: {
                    rows: [],
                    total: 0,
                    employee: {
                        id: employee.id,
                        name: employee.name,
                        openId: employee.openId,
                        departmentId: employee.departmentId,
                        entryTime: employee.entryTime,
                        status: employee.status
                    }
                }
            });
        }

        // 4. 构建响应数据，为每个岗位基于其自己的等级计算higherLevels字段
        const responseData = {
            rows: await Promise.all(employeePositions.map(async (position) => {
                // 为每个岗位计算独立的higherLevels
                let higherLevels = [];
                let nextLevel = null;
                
                if (position.levelId) {
                    // 先获取该岗位的当前等级信息，以便获取orderNum
                    const currentLevel = await Level.findOne({
                        where: {
                            id: position.levelId,
                            enterpriseId
                        }
                    });

                    if (currentLevel) {
                        // 查询当前等级及之前的所有等级（orderNum <= 当前等级的orderNum）
                        const currentAndPreviousLevels = await Level.findAll({
                            where: {
                                enterpriseId,
                                orderNum: { [Op.lte]: currentLevel.orderNum }
                            },
                            order: [['orderNum', 'ASC']],
                            attributes: ['id', 'name', 'code', 'orderNum']
                        });

                        // 查询当前等级之后的两个等级（orderNum > 当前等级的orderNum）
                        const nextTwoLevels = await Level.findAll({
                            where: {
                                enterpriseId,
                                orderNum: { [Op.gt]: currentLevel.orderNum }
                            },
                            order: [['orderNum', 'ASC']],
                            limit: 2,
                            attributes: ['id', 'name', 'code', 'orderNum']
                        });

                        // 合并当前及之前的等级和后面两个等级
                        higherLevels = [...currentAndPreviousLevels, ...nextTwoLevels];
                        
                        // 查找下一级（需要确保在Position表中存在对应的岗位配置）
                        if (position.positionName?.id && position.positionTypeId) {
                            // 获取所有可能的下一等级
                            const availableNextLevels = await Level.findAll({
                                where: {
                                    enterpriseId,
                                    orderNum: { [Op.gt]: currentLevel.orderNum },
                                    status: '1' // 确保是有效状态
                                },
                                order: [['orderNum', 'ASC']], // 按orderNum升序排列
                                attributes: ['id', 'name', 'code', 'orderNum']
                            });

                            // 检查每个可能的下一等级是否在Position表中有对应的岗位配置
                            let nextLevelData = null;
                            for (const level of availableNextLevels) {
                                const positionExists = await Position.findOne({
                                    where: {
                                        enterpriseId,
                                        typeId: position.positionTypeId,
                                        nameId: position.positionName.id,
                                        levelId: level.id,
                                        status: true // 确保岗位状态有效
                                    }
                                });

                                if (positionExists) {
                                    nextLevelData = level;
                                    break; // 找到第一个有效的下一等级就停止
                                }
                            }
                            
                            if (nextLevelData) {
                                // 找到有效的下一等级（在Position表中存在对应配置）
                                nextLevel = {
                                    id: nextLevelData.id,
                                    name: nextLevelData.name,
                                    code: nextLevelData.code,
                                    orderNum: nextLevelData.orderNum
                                };
                            } else {
                                // 如果没有找到有效的下一级，说明当前已经是最高等级，返回当前等级信息
                                nextLevel = {
                                    id: currentLevel.id,
                                    name: currentLevel.name,
                                    code: currentLevel.code,
                                    orderNum: currentLevel.orderNum
                                };
                            }
                        } else {
                            // 如果缺少必要的岗位信息，返回当前等级信息
                            nextLevel = {
                                id: currentLevel.id,
                                name: currentLevel.name,
                                code: currentLevel.code,
                                orderNum: currentLevel.orderNum
                            };
                        }
                        
                    }
                }

                const positionData = {
                    id: position.positionName?.id,
                    name: position.positionName?.name || '',
                    code: position.positionName?.code || '',
                    typeId: position.positionTypeId,
                    type: {
                        id: position.positionTypeId,
                        name: position.positionType?.name || '',
                        code: position.positionType?.code || ''
                    },
                    level: {
                        id: position.levelId,
                        name: position.level?.name || '',
                        code: position.level?.code || '',
                        orderNum: position.level?.orderNum || 0
                    },
                    isDefault: position.isDefault,
                    employeeId: position.employeeId,
                    enterpriseId: position.enterpriseId,
                    createTime: position.createTime,
                    updateTime: position.updateTime,
                    higherLevels: higherLevels // 基于该岗位自己的等级计算的higherLevels
                };

                // 如果是默认岗位且存在下一等级，添加nextLevel字段
                // if (position.isDefault ) {
                    positionData.nextLevel = nextLevel;
                // }

                return positionData;
            })),
            total: employeePositions.length,
            employee: {
                id: employee.id,
                name: employee.name,
                openId: employee.openId,
                departmentId: employee.departmentId,
                entryTime: employee.entryTime,
                status: employee.status
            }
        };

        return res.status(200).json({
            code: 200,
            message: '获取员工晋升岗位信息成功',
            data: responseData
        });

    } catch (error) {
        console.error('获取员工晋升岗位信息出错：', error);
        const status = error.status || 500;
        const message = error.message || '服务器内部错误';

        return res.status(status).json({
            code: status,
            message,
            error: error.message
        });
    }
};

/**
 * 获取员工可选择的剩余岗位名称
 * 从全部岗位中排除员工已配置的晋升岗位
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 可选择的岗位名称列表，返回值结构与getPositionNamesByUserType一致
 */
exports.getAvailablePositionNames = async (req, res) => {
    try {
        // 从请求头获取openid
        const openId = req.headers.openid;
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;

        // 验证必填参数
        if (!openId) {
            return res.status(400).json({
                code: 400,
                message: 'openId不能为空'
            });
        }

        // 1. 根据openId查找员工信息
        const employee = await Employee.findOne({
            where: {
                openId,
                enterpriseId,
                status: '1', // 在职状态
            },
            attributes: ['id', 'positionTypeId', 'levelId']
        });

        if (!employee) {
            return res.status(404).json({
                code: 404,
                message: '未找到员工信息或员工未激活'
            });
        }

        // 2. 获取全部岗位（调用getPositionNamesByUserType的逻辑）
        // 引入positionController
        const positionController = require('../organization/positionController');
        const { addEnterpriseFilter } = require('../../utils/responseHelper');

        // 创建mock请求和响应对象以获取positionController.getPositionNameOptions的返回值
        const mockReq = {
            query: {
                // typeId: employee.positionTypeId
            }
        };

        let allPositionsData = null;
        const mockRes = {
            json: (data) => {
                allPositionsData = data;
            },
            status: (code) => {
                return {
                    json: (data) => {
                        allPositionsData = data;
                    }
                };
            }
        };

        // 调用positionController的getPositionNameOptions方法，获取全部岗位
        await positionController.getPositionNameOptions(mockReq, mockRes);

        if (!allPositionsData || !allPositionsData.data || !allPositionsData.data.rows) {
            return res.status(500).json({
                code: 500,
                message: '获取全部岗位失败'
            });
        }

        // 3. 获取员工已配置的晋升岗位
        // 设置员工岗位关联关系
        Employee.setupEmployeePositionAssociations();

        const employeePositions = await EmployeePosition.findAll({
            where: {
                employeeId: employee.id,
                enterpriseId
            },
            include: [
                {
                    model: PositionName,
                    as: 'positionName',
                    attributes: ['id', 'name', 'code', 'typeId'],
                    required: false
                }
            ]
        });

        // 获取员工已配置的岗位ID列表
        const employeePositionIds = employeePositions
            .map(position => position.positionName?.id)
            .filter(id => id !== null && id !== undefined);

        console.log('员工已配置的岗位IDs:', employeePositionIds);
        console.log('全部岗位数量:', allPositionsData.data.rows.length);

        // 4. 从全部岗位中排除员工已配置的岗位
        const availablePositions = allPositionsData.data.rows.filter(position =>
            !employeePositionIds.includes(position.id)
        );

        console.log('可选择的岗位数量:', availablePositions.length);

        // 5. 获取等级信息（与getPositionNamesByUserType保持一致）
        let higherLevels = [];
        if (employee.levelId) {
            // 先获取当前等级信息，以便获取orderNum
            const currentLevel = await Level.findOne({
                where: {
                    id: employee.levelId,
                    enterpriseId
                }
            });

            if (currentLevel) {
                // 查询当前等级及之前的所有等级（orderNum <= 当前等级的orderNum）
                const currentAndPreviousLevels = await Level.findAll({
                    where: {
                        enterpriseId,
                        orderNum: { [Op.lte]: currentLevel.orderNum }
                    },
                    order: [['orderNum', 'ASC']],
                    attributes: ['id', 'name', 'code', 'orderNum']
                });

                // 查询当前等级之后的两个等级（orderNum > 当前等级的orderNum）
                const nextTwoLevels = await Level.findAll({
                    where: {
                        enterpriseId,
                        orderNum: { [Op.gt]: currentLevel.orderNum }
                    },
                    order: [['orderNum', 'ASC']],
                    limit: 2,
                    attributes: ['id', 'name', 'code', 'orderNum']
                });

                // 合并当前及之前的等级和后面两个等级
                higherLevels = [...currentAndPreviousLevels, ...nextTwoLevels];
            }
        }

        // 6. 构建与getPositionNamesByUserType一致的返回值结构
        const finalResponse = {
            code: 200,
            data: {
                rows: availablePositions?availablePositions:[],
                total: availablePositions.length,
                higherLevels
            },
            message: '获取可选择岗位名称成功'
        };

        return res.json(finalResponse);

    } catch (error) {
        console.error('获取可选择岗位名称失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取可选择岗位名称失败',
            error: error.message
        });
    }
};
