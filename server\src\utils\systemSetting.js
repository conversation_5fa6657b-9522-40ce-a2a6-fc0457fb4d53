const { SystemSetting } = require('../models');

/**
 * 获取系统设置值
 * @param {string} code - 系统设置代码
 * @param {*} defaultValue - 默认值，如果设置不存在则返回此值
 * @returns {Promise<*>} - 返回设置值或默认值
 */
async function getSettingValue(code, defaultValue = null) {
  try {
    if (!code) {
      return defaultValue;
    }

    const setting = await SystemSetting.findOne({
      where: { code },
      attributes: ['value']
    });

    if (!setting || setting.value === undefined || setting.value === null) {
      return defaultValue;
    }

    return setting.value;
  } catch (error) {
    console.error(`获取系统设置 [${code}] 失败:`, error);
    return defaultValue;
  }
}

/**
 * 批量获取系统设置值
 * @param {string[]} codes - 系统设置代码数组
 * @returns {Promise<Object>} - 返回代码和值的映射对象
 */
async function getMultipleSettingValues(codes) {
  try {
    if (!Array.isArray(codes) || codes.length === 0) {
      return {};
    }

    const settings = await SystemSetting.findAll({
      where: { code: codes },
      attributes: ['code', 'value']
    });

    const result = {};
    settings.forEach(setting => {
      result[setting.code] = setting.value;
    });

    // 填充未找到的设置为null
    codes.forEach(code => {
      if (result[code] === undefined) {
        result[code] = null;
      }
    });

    return result;
  } catch (error) {
    console.error('批量获取系统设置失败:', error);
    return {};
  }
}

/**
 * 设置系统设置值
 * @param {string} code - 系统设置代码
 * @param {string} value - 设置值
 * @param {Object} options - 可选参数
 * @param {string} options.name - 设置名称（仅在创建新设置时使用）
 * @param {string} options.description - 设置描述
 * @returns {Promise<boolean>} - 是否设置成功
 */
async function setSettingValue(code, value, options = {}) {
  try {
    if (!code) {
      return false;
    }

    // 查找现有设置
    let setting = await SystemSetting.findOne({
      where: { code }
    });

    if (setting) {
      // 更新现有设置
      await setting.update({
        value,
        ...(options.description ? { description: options.description } : {})
      });
    } else {
      // 创建新设置
      if (!options.name) {
        return false;
      }

      await SystemSetting.create({
        code,
        name: options.name,
        value,
        description: options.description
      });
    }

    return true;
  } catch (error) {
    console.error(`设置系统设置 [${code}] 失败:`, error);
    return false;
  }
}

module.exports = {
  getSettingValue,
  getMultipleSettingValues,
  setSettingValue
}; 