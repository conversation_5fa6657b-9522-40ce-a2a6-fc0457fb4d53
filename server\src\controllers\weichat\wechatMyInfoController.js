/**
 * 微信用户练习信息控制器
 * 处理微信小程序用户练习记录相关请求
 */
const Employee = require('../../models/Employee');
const Position = require('../../models/Position');
const Level = require('../../models/Level');
const KnowledgeBase = require('../../models/knowledge-base');
const PracticeRecord = require('../../models/practice-record');
const PracticeRecordDetail = require('../../models/practice-record-detail');
const PositionType = require('../../models/PositionType');
const PositionName = require('../../models/PositionName');
const ExamRecord = require('../../models/ExamRecord');
const CertificateRecord = require('../../models/CertificateRecord');
const Feedback = require('../../models/Feedback');
const sequelize = require('../../config/database');
const {Op} = require('sequelize');
const logger = require('../../utils/logger'); // 引入统一的日志模块
const { getFileType } = require('../../utils/fileUpload'); // 引入文件上传工具
const path = require('path'); // 引入path模块用于处理文件路径

/**
 * 获取用户练习记录信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 用户练习记录信息
 */
exports.getUserPracticeRecords = async (req, res) => {
    try {
        // 记录请求日志
        logger.request(req, '获取用户练习记录', { query: req.query });

        // 获取参数
        const openId = req.headers.openid; // 从请求头获取openId
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID
        const { positionid, levelid, timeRange, page = 1, pageSize = 10 } = req.query; // 获取请求参数，添加分页参数

        // 验证必要参数
        if (!validateRequiredParams(res, { openId, enterpriseId })) {
            return;
        }

        // 处理和验证筛选参数
        const validParams = validateAndProcessParams({ positionid, levelid, timeRange });

        // 根据参数确定查询条件
        const queryConfig = await determineQueryConfig(
            openId,
            enterpriseId,
            validParams.positionId,
            validParams.levelId,
            validParams.timeRange
        );

        if (!queryConfig) {
            return res.status(200).json({
                code: 200,
                message: '员工未分配岗位或级别',
                data: getEmptyResultData()
            });
        }

        // 执行分页查询
        const result = await executePaginatedQuery(
            queryConfig.whereCondition,
            parseInt(page),
            parseInt(pageSize)
        );

        return res.status(200).json({
            code: 200,
            message: '获取练习记录成功',
            data: {
                records: formatRecordsData(result.records),
                stats: result.stats,
                pagination: {
                    currentPage: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total: result.total
                }
            }
        });
    } catch (error) {
        logger.error('获取练习记录出错：', error);

        // 根据错误类型返回不同的错误信息
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({
                code: 400,
                message: '参数验证失败',
                error: error.message
            });
        } else if (error.name === 'SequelizeDatabaseError') {
            return res.status(500).json({
                code: 500,
                message: '数据库操作错误',
                error: error.message
            });
        } else if (error.name === 'TimeRangeParseError') {
            return res.status(400).json({
                code: 400,
                message: '时间范围格式错误',
                error: error.message
            });
        } else {
            return res.status(500).json({
                code: 500,
                message: '服务器内部错误',
                error: error.message
            });
        }
    }
};

/**
 * 获取练习记录详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 练习记录详情列表
 */
exports.getPracticeRecordDetails = async (req, res) => {
    try {
        // 记录请求日志
        logger.request(req, '获取练习记录详情', { params: req.params, query: req.query });

        // 获取参数
        const { recordId } = req.params;
        const openId = req.headers.openid; // 从请求头获取openId
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID
        const { page = 1, pageSize = 50 } = req.query; // 获取分页参数，默认每页50条

        // 验证必要参数
        if (!validateRequiredParams(res, { openId, enterpriseId })) {
            return;
        }

        if (!recordId) {
            return res.status(400).json({
                code: 400,
                message: '缺少必要参数：练习记录ID',
            });
        }

        // 首先验证该记录是否属于当前用户
        const practiceRecord = await PracticeRecord.findOne({
            where: {
                id: recordId,
                enterprise_id: enterpriseId,
                open_id: openId
            }
        });

        if (!practiceRecord) {
            return res.status(403).json({
                code: 403,
                message: '无权访问该练习记录',
            });
        }

        // 计算分页偏移量
        const offset = (parseInt(page) - 1) * parseInt(pageSize);

        // 查询总记录数
        const { count: total } = await PracticeRecordDetail.findAndCountAll({
            where: {
                practiceRecordId: recordId,
            },
            limit: 0
        });

        // 查询分页记录
        const details = await PracticeRecordDetail.findAll({
            where: {
                practiceRecordId: recordId
            },
            order: [ ['id', 'ASC']], // 优先按顺序号排序，然后按ID排序
            offset,
            limit: parseInt(pageSize)
        });

        return res.status(200).json({
            code: 200,
            message: '获取练习记录详情成功',
            data: {
                details,
                pagination: {
                    currentPage: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total
                }
            }
        });
    } catch (error) {
        logger.error('获取练习记录详情出错：', error);

        // 根据错误类型返回不同的错误信息
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({
                code: 400,
                message: '参数验证失败',
                error: error.message
            });
        } else if (error.name === 'SequelizeDatabaseError') {
            return res.status(500).json({
                code: 500,
                message: '数据库操作错误',
                error: error.message
            });
        } else {
            return res.status(500).json({
                code: 500,
                message: '服务器内部错误',
                error: error.message
            });
        }
    }
};

/**
 * 获取用户考试统计信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 用户考试统计信息
 */
exports.getUserExamStatistics = async (req, res) => {
    try {
        // 记录请求日志
        logger.request(req, '获取用户考试统计', { query: req.query });

        // 获取参数
        const openId = req.headers.openid; // 从请求头获取openId
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID

        // 验证必要参数
        if (!validateRequiredParams(res, { openId, enterpriseId })) {
            return;
        }

        // 获取当前月份的开始和结束日期
        const now = new Date();
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

        // 查询总考试次数
        const totalExamsCount = await ExamRecord.count({
            where: {
                openId: openId,
                enterpriseId: enterpriseId,
                delFlag: 0,
                examStatus: 'completed' // 仅计算已完成的考试
            }
        });

        // 查询通过的考试次数
        const passedExamsCount = await ExamRecord.count({
            where: {
                openId: openId,
                enterpriseId: enterpriseId,
                delFlag: 0,
                examStatus: 'completed',
                confirmStatus: 2, // 添加确认状态条件
                [Op.where]: sequelize.literal('score >= pass_score') // 分数大于等于通过分数
            }
        });

        // 查询本月考试次数
        const currentMonthExamsCount = await ExamRecord.count({
            where: {
                openId: openId,
                enterpriseId: enterpriseId,
                delFlag: 0,
                examStatus: 'completed',
                examTime: {
                    [Op.between]: [monthStart, monthEnd]
                }
            }
        });

        // 计算通过率
        let passRate = 0;
        if (totalExamsCount > 0) {
            passRate = (passedExamsCount / totalExamsCount) * 100;
        }



        return res.status(200).json({
            code: 200,
            message: '获取考试统计信息成功',
            data: {
                totalExams: totalExamsCount,
                passedExams: passedExamsCount,
                passRate: parseFloat(passRate.toFixed(2)), // 保留两位小数
                passRateFormatted: `${passRate.toFixed(2)}%`, // 格式化为带百分号的字符串
                currentMonthExams: currentMonthExamsCount,
            }
        });
    } catch (error) {
        logger.error('获取考试统计信息出错：', error);

        // 根据错误类型返回不同的错误信息
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({
                code: 400,
                message: '参数验证失败',
                error: error.message
            });
        } else if (error.name === 'SequelizeDatabaseError') {
            return res.status(500).json({
                code: 500,
                message: '数据库操作错误',
                error: error.message
            });
        } else {
            return res.status(500).json({
                code: 500,
                message: '服务器内部错误',
                error: error.message
            });
        }
    }
};

/**
 * 获取用户证书列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 用户证书列表信息
 */
exports.getUserCertificates = async (req, res) => {
    try {
        // 记录请求日志
        logger.request(req, '获取用户证书列表', { query: req.query });

        // 获取参数
        const openId = req.headers.openid; // 从请求头获取openId
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID
        const { page = 1, pageSize = 10, positionId, levelId, certificateName } = req.query; // 分页参数、岗位和等级筛选、证书名称搜索

        // 验证必要参数
        if (!validateRequiredParams(res, { openId, enterpriseId })) {
            return;
        }

        // 构建查询条件
        const whereCondition = {
            openId: openId,
            enterpriseId: enterpriseId,
            delFlag: 0
        };

        // 添加岗位筛选条件（如果有）
        if (positionId) {
            whereCondition.positionName = positionId;
        }

        // 添加等级筛选条件（如果有）
        if (levelId) {
            whereCondition.positionLevel = levelId;
        }

        // 添加证书名称搜索条件（如果有）
        if (certificateName && certificateName.trim() !== '') {
            whereCondition.certificateName = {
                [Op.like]: `%${certificateName.trim()}%`
            };
        }

        // 查询全部证书个数（不考虑筛选条件，只考虑用户和企业）
        const totalAllCertificates = await CertificateRecord.count({
            where: {
                openId: openId,
                enterpriseId: enterpriseId,
                delFlag: 0
            }
        });

        // 查询总记录数（基于筛选条件）
        const total = await CertificateRecord.count({
            where: whereCondition
        });

        // 计算分页偏移量
        const offset = (parseInt(page) - 1) * parseInt(pageSize);

        // 查询证书列表
        const certificates = await CertificateRecord.findAll({
            where: whereCondition,
            include: [
                {
                    model: PositionName,
                    as: 'positionNameInfo',
                    attributes: ['id', 'name'],
                    required: false
                },
                {
                    model: Level,
                    as: 'positionLevelInfo',
                    attributes: ['id', 'name'],
                    required: false
                },
                {
                    model: PositionType,
                    as: 'positionBelongInfo',
                    attributes: ['id', 'name'],
                    required: false
                }
            ],
            order: [['obtainTime', 'DESC']], // 按获取时间降序排序
            offset: offset,
            limit: parseInt(pageSize)
        });

        // 处理证书数据，添加状态标识
        const certificatesWithStatus = certificates.map(cert => {
            const certData = cert.get({ plain: true });

            // 添加格式化日期
            certData.obtainTimeFormatted = formatDate(certData.obtainTime);
            certData.validUntilFormatted = formatDate(certData.validUntil);


            return certData;
        });

        return res.status(200).json({
            code: 200,
            message: '获取证书列表成功',
            data: {
                list: certificatesWithStatus,
                pagination: {
                    currentPage: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total: total
                },
                totalCertificates: totalAllCertificates  // 添加全部证书个数
            }
        });
    } catch (error) {
        logger.error('获取证书列表出错：', error);

        return res.status(500).json({
            code: 500,
            message: '服务器内部错误',
            error: error.message
        });
    }
};

/**
 * 创建反馈
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 创建反馈后的反馈信息
 */
exports.createFeedback = async (req, res) => {
    try {
        // 记录请求日志
        logger.request(req, '创建用户反馈', { body: req.body });

        // 获取参数
        const openId = req.headers.openid; // 从请求头获取openId
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID
        const { contactPerson, contactInfo, feedbackContent, feedbackImgs } = req.body;

        // 验证必要参数
        if (!validateRequiredParams(res, { openId, enterpriseId })) {
            return;
        }

        if (!feedbackContent || feedbackContent.trim() === '') {
            return res.status(400).json({
                code: 400,
                message: '缺少必要参数：反馈内容',
            });
        }

        // 查询员工信息，获取用户ID
        const employee = await Employee.findOne({
            where: {
                openId,
                enterpriseId,
                status: '1', // 在职状态
            }
        });

        // 创建反馈记录
        const feedback = await Feedback.create({
            createdBy: employee ? employee.id : null,
            open_id: openId, // 使用openId作为反馈的openId
            contactPerson: contactPerson || '',
            contactInfo: contactInfo || '',
            feedbackContent,
            feedbackImgs: feedbackImgs ? JSON.stringify(feedbackImgs) : null,
            feedbackTime: new Date(),
            enterpriseId
        });

        return res.status(201).json({
            code: 200,
            message: '反馈提交成功',
            data: {
                id: feedback.id,
                feedbackTime: feedback.feedbackTime
            }
        });
    } catch (error) {
        logger.error('创建反馈出错：', error);

        // 根据错误类型返回不同的错误信息
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({
                code: 400,
                message: '参数验证失败',
                error: error.message
            });
        } else if (error.name === 'SequelizeDatabaseError') {
            return res.status(500).json({
                code: 500,
                message: '数据库操作错误',
                error: error.message
            });
        } else {
            return res.status(500).json({
                code: 500,
                message: '服务器内部错误',
                error: error.message
            });
        }
    }
};

/**
 * 获取用户反馈列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 用户反馈列表信息
 */
exports.getUserFeedbacks = async (req, res) => {
    try {
        // 记录请求日志
        logger.request(req, '获取用户反馈列表', { query: req.query });

        // 获取参数
        const openId = req.headers.openid; // 从请求头获取openId
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID
        const { page = 1, pageSize = 10 } = req.query; // 分页参数

        // 验证必要参数
        if (!validateRequiredParams(res, { openId, enterpriseId })) {
            return;
        }

        // 计算分页偏移量
        const offset = (parseInt(page) - 1) * parseInt(pageSize);

        // 查询总记录数
        const { count: total } = await Feedback.findAndCountAll({
            where: {
                open_id: openId,
                enterpriseId: enterpriseId
            }
        });

        // 查询分页反馈
        const feedbacks = await Feedback.findAll({
            where: {
                open_id: openId,
                enterpriseId: enterpriseId
            },
            order: [['feedbackTime', 'DESC']], // 按反馈时间降序排序
            offset,
            limit: parseInt(pageSize)
        });

        // 处理反馈数据，解析图片信息
        const formattedFeedbacks = feedbacks.map(feedback => {
            const plainFeedback = feedback.get({ plain: true });

            // 解析图片信息
            let images = [];
            try {
                if (plainFeedback.feedbackImgs) {
                    images = JSON.parse(plainFeedback.feedbackImgs);
                }
            } catch (e) {
                console.error('解析图片信息失败:', e);
            }

            return {
                id: plainFeedback.id,
                feedbackContent: plainFeedback.feedbackContent,
                feedbackTime: plainFeedback.feedbackTime,
                images: images,
                replyContent: plainFeedback.replyContent,
                replyTime: plainFeedback.replyTime,
                hasReply: !!plainFeedback.replyContent, // 是否有回复
                contactPerson: plainFeedback.contactPerson,
                contactInfo: plainFeedback.contactInfo
            };
        });

        return res.status(200).json({
            code: 200,
            message: '获取用户反馈列表成功',
            data: {
                list: formattedFeedbacks,
                pagination: {
                    currentPage: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total: total
                }
            }
        });
    } catch (error) {
        logger.error('获取用户反馈列表出错：', error);

        // 根据错误类型返回不同的错误信息
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({
                code: 400,
                message: '参数验证失败',
                error: error.message
            });
        } else if (error.name === 'SequelizeDatabaseError') {
            return res.status(500).json({
                code: 500,
                message: '数据库操作错误',
                error: error.message
            });
        } else {
            return res.status(500).json({
                code: 500,
                message: '服务器内部错误',
                error: error.message
            });
        }
    }
};

/**
 * 上传反馈图片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 上传图片后的结果
 */
exports.uploadFeedbackImage = async (req, res) => {
    try {
        // 记录请求日志
        logger.request(req, '上传用户反馈图片', { file: req.file ? req.file.originalname : '无文件' });

        // 获取参数
        const openId = req.headers.openid; // 从请求头获取openId
        const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID

        // 验证必要参数
        if (!validateRequiredParams(res, { openId, enterpriseId })) {
            return;
        }

        // 检查是否存在上传的文件
        if (!req.file) {
            return res.status(400).json({
                code: 400,
                message: '未找到上传的文件'
            });
        }

        const { originalname, filename, path: filePath, size } = req.file;

        // 解码文件名，确保中文文件名正确
        let decodedFileName = originalname;
        if (typeof decodedFileName === 'string') {
            try {
                // 测试解码是否需要解码
                const testDecode = decodeURIComponent(escape(decodedFileName));
                // 如果解码成功且不是纯数字，则使用解码后的文件名
                if (testDecode !== decodedFileName && /[\u4e00-\u9fa5]/.test(testDecode)) {
                    decodedFileName = testDecode;
                }
            } catch (e) {
                // 如果解码失败，尝试直接从buffer解码
                decodedFileName = Buffer.from(decodedFileName, 'latin1').toString('utf8');
            }
        }

        // 获取文件类型 - 从getFileType函数
        const fileType = getFileType(decodedFileName);
        
        // 直接从文件名获取扩展名
        const fileExt = path.extname(decodedFileName).toLowerCase().substring(1);
        console.log('文件类型：', fileType, '文件扩展名：', fileExt, '文件名：', decodedFileName);

        // 允许的图片扩展名
        const imgExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        
        // 检查是否为图片文件
        // 双重验证：检查getFileType返回的类型和文件扩展名
        if (fileType !== 'image' && !imgExtensions.includes(fileExt)) {
            return res.status(400).json({
                code: 400,
                message: '不允许上传非图片文件'
            });
        }

        // 构建文件访问路径 - 确保路径与createUpload('feedback')一致
        const fileUrl = `/uploads/feedback/${filename}`;

        // 返回上传结果
        return res.status(200).json({
            code: 200,
            message: '上传图片成功',
            data: {
                fileName: decodedFileName,
                fileType,
                fileSize: size,
                fileUrl
            }
        });
    } catch (error) {
        logger.error('上传图片失败:', error);

        return res.status(500).json({
            code: 500,
            message: '上传图片失败',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

/**
 * 验证必要参数
 * @param {Object} res - 响应对象
 * @param {Object} params - 要验证的参数对象
 * @returns {boolean} 验证是否通过
 */
function validateRequiredParams(res, { openId, enterpriseId }) {
    if (!openId) {
        res.status(401).json({
            code: 401,
            message: '未授权访问：缺少openId',
        });
        return false;
    }

    if (!enterpriseId) {
        res.status(500).json({
            code: 500,
            message: '服务器配置错误：缺少企业ID',
        });
        return false;
    }

    return true;
}

/**
 * 验证并处理请求参数
 * @param {Object} params - 请求参数
 * @returns {Object} 处理后的有效参数
 */
function validateAndProcessParams({ positionid, levelid, timeRange }) {
    // 检查positionid和levelid参数是否有效
    const validPositionId = positionid && positionid !== 'undefined' ? positionid : null;
    const validLevelId = levelid && levelid !== 'undefined' ? levelid : null;
    const validTimeRange = timeRange && timeRange !== 'undefined' ? timeRange : null;

    return {
        positionId: validPositionId,
        levelId: validLevelId,
        timeRange: validTimeRange,
        isAllPositions: positionid === '-1',
        isAllLevels: levelid === '-1'
    };
}

/**
 * 根据参数确定查询配置
 * @param {string} openId - 微信openId
 * @param {string} enterpriseId - 企业ID
 * @param {string} positionId - 职位ID
 * @param {string} levelId - 级别ID
 * @param {string} timeRange - 时间范围
 * @returns {Object|null} 查询配置对象或null
 */
async function determineQueryConfig(openId, enterpriseId, positionId, levelId, timeRange) {
    // 构建基础查询条件
    const whereCondition = {
        enterprise_id: enterpriseId,
        open_id: openId
    };


    // 情况3: 使用提供的有效参数
    if (positionId) {
        whereCondition.position_name = positionId;
    }

    if (levelId) {
        whereCondition.position_level = levelId;
    }

    addTimeRangeCondition(whereCondition, timeRange);
    return { whereCondition };
}

/**
 * 获取带有职位和级别信息的员工
 * @param {string} openId - 微信openId
 * @param {string} enterpriseId - 企业ID
 * @returns {Object|null} 员工信息对象或null
 */
async function getEmployeeWithPositionAndLevel(openId, enterpriseId) {
    return await Employee.findOne({
        where: {
            openId,
            enterpriseId,
            status: '1', // 在职状态
        },
        include: [
            {model: Position, as: 'position'},
            {model: Level, as: 'level'}
        ]
    });
}

/**
 * 向查询条件中添加时间范围筛选
 * @param {Object} whereCondition - 查询条件对象
 * @param {string} timeRange - 时间范围字符串
 */
function addTimeRangeCondition(whereCondition, timeRange) {
    if (!timeRange) return;

    const now = new Date();

    try {
        if (timeRange === '本周') {
            // 计算本周的开始日期（周一）和结束日期（周日）
            const currentDay = now.getDay() || 7; // 获取当前是周几，周日为0转为7
            const mondayOffset = currentDay - 1; // 计算到周一的偏移天数

            const weekStart = new Date(now);
            weekStart.setDate(now.getDate() - mondayOffset); // 设置为本周一
            weekStart.setHours(0, 0, 0, 0); // 设置为当天开始时间

            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6); // 设置为本周日
            weekEnd.setHours(23, 59, 59, 999); // 设置为当天结束时间

            whereCondition.create_time = {
                [Op.between]: [weekStart, weekEnd]
            };
        } else if (timeRange === '本月') {
            // 计算本月的开始日期和结束日期
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            monthStart.setHours(0, 0, 0, 0);

            const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            monthEnd.setHours(23, 59, 59, 999);

            whereCondition.create_time = {
                [Op.between]: [monthStart, monthEnd]
            };
        } else if (timeRange === '本年') {
            // 计算本年的开始日期和结束日期
            const yearStart = new Date(now.getFullYear(), 0, 1);
            yearStart.setHours(0, 0, 0, 0);

            const yearEnd = new Date(now.getFullYear(), 11, 31);
            yearEnd.setHours(23, 59, 59, 999);

            whereCondition.create_time = {
                [Op.between]: [yearStart, yearEnd]
            };
        } else if (timeRange.includes(' - ')) {
            // 处理自定义时间范围，格式为"开始日期 - 结束日期"
            const [startDate, endDate] = timeRange.split(' - ');

            // 转换为Date对象并验证
            const start = new Date(startDate);
            const end = new Date(endDate);

            if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                const error = new Error('无效的日期格式');
                error.name = 'TimeRangeParseError';
                throw error;
            }

            start.setHours(0, 0, 0, 0);
            end.setHours(23, 59, 59, 999);

            whereCondition.create_time = {
                [Op.between]: [start, end]
            };
        }
    } catch (error) {
        if (error.name !== 'TimeRangeParseError') {
            error.name = 'TimeRangeParseError';
        }
        throw error;
    }
}

/**
 * 执行分页查询
 * @param {Object} whereCondition - 查询条件
 * @param {number} page - 当前页码
 * @param {number} pageSize - 每页数量
 * @returns {Object} 查询结果，包含记录和统计信息
 */
async function executePaginatedQuery(whereCondition, page, pageSize) {
    // 计算分页偏移量
    const offset = (page - 1) * pageSize;

    // 查询总记录数
    const { count: total } = await PracticeRecord.findAndCountAll({
        where: whereCondition,
        limit: 0
    });

    // 查询分页记录
    const practiceRecords = await PracticeRecord.findAll({
        where: whereCondition,
        include: [
            {
                model: KnowledgeBase,
                as: 'knowledge',
                attributes: ['id', 'name'],
                required: false
            },
            {
                model: PositionType,
                as: 'positionTypeData',
                attributes: ['id', 'name'],
                required: false
            },
            {
                model: PositionName,
                as: 'positionNameData',
                attributes: ['id', 'name'],
                required: false
            },
            {
                model: Level,
                as: 'level',
                attributes: ['id', 'name'],
                required: false
            }
        ],
        order: [['create_time', 'DESC']],
        offset,
        limit: pageSize
    });

    // 计算练习统计信息
    const totalPracticeStats = await PracticeRecord.findOne({
        where: whereCondition,
        attributes: [
            [sequelize.fn('COUNT', sequelize.col('id')), 'total_practice_count'],
            [
                sequelize.literal(`
                    SUM(
                        CASE 
                            WHEN total_duration REGEXP '^[0-9]+:[0-9]+:[0-9]+$' 
                            THEN TIME_TO_SEC(total_duration)
                            WHEN total_duration REGEXP '^[0-9]+:[0-9]+$' 
                            THEN TIME_TO_SEC(CONCAT('00:', total_duration))
                            ELSE 0 
                        END
                    )
                `),
                'total_duration_seconds'
            ],
            [
                sequelize.literal(`
                    SEC_TO_TIME(
                        SUM(
                            CASE 
                                WHEN total_duration REGEXP '^[0-9]+:[0-9]+:[0-9]+$' 
                                THEN TIME_TO_SEC(total_duration)
                                WHEN total_duration REGEXP '^[0-9]+:[0-9]+$' 
                                THEN TIME_TO_SEC(CONCAT('00:', total_duration))
                                ELSE 0 
                            END
                        )
                    )
                `),
                'total_duration_formatted'
            ]
        ],
        raw: true
    });

    // 获取总题目数量
    const totalQuestionsCount = await PracticeRecordDetail.count({
        include: [{
        model: PracticeRecord,
        as: 'practiceRecord',  // 根据实际定义的关联名称调整
        where: {
            open_id: whereCondition.open_id,
            enterprise_id: whereCondition.enterprise_id
        },
        required: true
        }],
        where: {
            role: '用户',
            type: 'answer'
        }
    });

    // 格式化并返回结果
    return {
        records: practiceRecords,
        stats: {
            totalPracticeCount: parseInt(totalPracticeStats?.total_practice_count || 0),
            totalDurationSeconds: parseInt(totalPracticeStats?.total_duration_seconds || 0),
            totalDurationFormatted: totalPracticeStats?.total_duration_formatted || '00:00:00',
            totalDurationMinutes: Math.floor(parseInt(totalPracticeStats?.total_duration_seconds || 0) / 60),
            totalQuestions: totalQuestionsCount 
        },
        total
    };
}

/**
 * 格式化练习记录数据
 * @param {Array} records - 原始练习记录数据
 * @returns {Array} 格式化后的记录数据
 */
function formatRecordsData(records) {
    return records.map(record => {
        const plainRecord = record.get({ plain: true });
        return {
            ...plainRecord,
            knowledgeName: plainRecord.knowledge?.name || '',
            typeName: plainRecord.positionTypeData?.name || '',
            positionNameRel: plainRecord.positionNameData?.name || '',
            levelName: plainRecord.level?.name || ''
        };
    });
}

/**
 * 获取空结果数据结构
 * @returns {Object} 空结果对象
 */
function getEmptyResultData() {
    return {
        records: [],
        stats: {
            totalPracticeCount: 0,
            totalDurationSeconds: 0,
            totalDurationFormatted: '00:00:00',
            totalDurationMinutes: 0
        },
        pagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
        }
    };
}

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.getFullYear() + '-' +
        String(d.getMonth() + 1).padStart(2, '0') + '-' +
        String(d.getDate()).padStart(2, '0');
}
