const { Agent, Enterprise } = require('../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');

// 获取智能体列表
exports.getAgentList = async (req, res) => {
  try {
    const { name, code, type, enterpriseId, status, pageNum = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }
    if (code) {
      where.code = { [Op.like]: `%${code}%` };
    }
    if (type) {
      where.type = type;
    }
    if (enterpriseId) {
      where.enterpriseId = parseInt(enterpriseId);
    }
    if (status !== undefined) {
      where.status = status === 'true' || status === '1' || status === 1;
    }
    
    // 分页查询，添加企业ID过滤
    const { count, rows } = await Agent.findAndCountAll(
      addEnterpriseFilter({
        where,
        include: [
          {
            model: Enterprise,
            attributes: ['name'],
            as: 'enterprise'
          }
        ],
        order: [['id', 'ASC']],
        limit: parseInt(pageSize),
        offset: (parseInt(pageNum) - 1) * parseInt(pageSize)
      })
    );
    
    // 处理返回数据，添加企业名称
    const list = rows.map(agent => {
      const plainAgent = agent.get({ plain: true });
      return {
        ...plainAgent,
        enterpriseName: plainAgent.enterprise ? plainAgent.enterprise.name : '未知企业'
      };
    });
    
    res.json({
      code: 200,
      message: '获取智能体列表成功',
      data: {
        list,
        total: count
      }
    });
  } catch (error) {
    console.error('获取智能体列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取智能体列表失败',
      error: error.message
    });
  }
};

// 获取智能体详情
exports.getAgentDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 添加企业ID过滤
    const agent = await Agent.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [
          {
            model: Enterprise,
            attributes: ['name'],
            as: 'enterprise'
          }
        ]
      })
    );
    
    if (!agent) {
      return res.status(404).json({
        code: 404,
        message: '智能体不存在'
      });
    }
    
    // 处理返回数据
    const plainAgent = agent.get({ plain: true });
    const result = {
      ...plainAgent,
      enterpriseName: plainAgent.enterprise ? plainAgent.enterprise.name : '未知企业'
    };
    
    res.json({
      code: 200,
      message: '获取智能体详情成功',
      data: result
    });
  } catch (error) {
    console.error('获取智能体详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取智能体详情失败',
      error: error.message
    });
  }
};

// 创建智能体
exports.createAgent = async (req, res) => {
  try {
    const { name, code, type, description, enterpriseId, status } = req.body;
    
    // 检查编码是否已存在，添加企业ID过滤
    const existAgent = await Agent.findOne(
      addEnterpriseFilter({
        where: { code }
      })
    );
    
    if (existAgent) {
      return res.status(400).json({
        code: 400,
        message: '智能体编码已存在'
      });
    }
    
    // 创建智能体，添加企业ID
    const agent = await Agent.create(
      addEnterpriseId({
        name,
        code,
        type,
        description,
        enterpriseId,
        status
      })
    );
    
    res.json({
      code: 200,
      message: '创建智能体成功',
      data: agent
    });
  } catch (error) {
    console.error('创建智能体失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建智能体失败',
      error: error.message
    });
  }
};

// 更新智能体
exports.updateAgent = async (req, res) => {
  try {
    const { id, name, code, type, description, enterpriseId, status } = req.body;
    
    // 检查智能体是否存在，添加企业ID过滤
    const agent = await Agent.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!agent) {
      return res.status(404).json({
        code: 404,
        message: '智能体不存在'
      });
    }
    
    // 检查编码是否已被其他智能体使用，添加企业ID过滤
    if (code !== agent.code) {
      const existAgent = await Agent.findOne(
        addEnterpriseFilter({
          where: { 
            code,
            id: { [Op.ne]: id }
          }
        })
      );
      
      if (existAgent) {
        return res.status(400).json({
          code: 400,
          message: '智能体编码已存在'
        });
      }
    }
    
    // 更新智能体
    await agent.update({
      name,
      code,
      type,
      description,
      enterpriseId,
      status
    });
    
    res.json({
      code: 200,
      message: '更新智能体成功',
      data: agent
    });
  } catch (error) {
    console.error('更新智能体失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新智能体失败',
      error: error.message
    });
  }
};

// 删除智能体
exports.deleteAgent = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查智能体是否存在
    const agent = await Agent.findByPk(id);
    if (!agent) {
      return res.status(404).json({
        code: 404,
        message: '智能体不存在'
      });
    }
    
    // 删除智能体
    await agent.destroy();
    
    res.json({
      code: 200,
      message: '删除智能体成功'
    });
  } catch (error) {
    console.error('删除智能体失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除智能体失败',
      error: error.message
    });
  }
}; 