const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const PositionName = require('./PositionName');

/**
 * 岗位模型
 */
const Position = sequelize.define('Position', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '岗位ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '岗位编码'
  },
  typeId: {
    type: DataTypes.INTEGER,
    field: 'type_id',
    allowNull: true,
    comment: '岗位类型ID，关联岗位类型表'
  },
  nameId: {
    type: DataTypes.INTEGER,
    field: 'name_id',
    allowNull: true,
    comment: '岗位名称ID，关联岗位名称表'
  },
  levelId: {
    type: DataTypes.INTEGER,
    field: 'level_id',
    allowNull: true,
    comment: '岗位等级ID，关联岗位等级表'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态（1正常 0停用）'
  },
  remark: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '备注'
  },
  createBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'create_by',
    comment: '创建者'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'update_by',
    comment: '更新者'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'org_position',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

// 岗位和员工关联关系将在Employee模型中定义

module.exports = Position;