const Department = require('../../models/Department');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../../utils/enterpriseFilter');

/**
 * 递归构建部门树
 * @param {Array} departments 部门列表
 * @param {Number} parentId 父部门ID
 * @returns {Array} 树形结构的部门列表
 */
const buildDepartmentTree = (departments, parentId = null) => {
  const result = [];
  
  departments.filter(dept => dept.parentId === parentId).forEach(dept => {
    const children = buildDepartmentTree(departments, dept.id);
    if (children.length > 0) {
      dept.dataValues.children = children;
    }
    result.push(dept);
  });
  
  return result;
};

/**
 * 获取部门树
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getDepartmentTree = async (req, res) => {
  try {
    // 添加企业ID过滤
    const departments = await Department.findAll(
      addEnterpriseFilter({
        where: {
          status: true // 只查询启用状态的部门
        },
        order: [
          ['orderNum', 'ASC'] // 按排序号升序排列
        ]
      })
    );
    
    const tree = buildDepartmentTree(departments);
    
    res.json({
      code: 200,
      data: tree,
      message: '获取部门树成功'
    });
  } catch (error) {
    console.error('获取部门树失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取部门树失败',
      error: error.message
    });
  }
};

/**
 * 获取部门列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getDepartmentList = async (req, res) => {
  try {
    const { name, status } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) {
      where.name = {
        [Op.like]: `%${name}%`
      };
    }
    if (status !== undefined) {
      where.status = status === 'true';
    }
    
    // 添加企业ID过滤
    const departments = await Department.findAll(
      addEnterpriseFilter({
        where,
        order: [
          ['orderNum', 'ASC'] // 按排序号升序排列
        ]
      })
    );
    
    res.json({
      code: 200,
      data: departments,
      message: '获取部门列表成功'
    });
  } catch (error) {
    console.error('获取部门列表失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取部门列表失败',
      error: error.message
    });
  }
};

/**
 * 获取部门详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getDepartmentDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 添加企业ID过滤
    const department = await Department.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!department) {
      return res.status(404).json({
        code: 404,
        message: '部门不存在'
      });
    }
    
    res.json({
      code: 200,
      data: department,
      message: '获取部门详情成功'
    });
  } catch (error) {
    console.error('获取部门详情失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取部门详情失败',
      error: error.message
    });
  }
};

/**
 * 新增部门
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.addDepartment = async (req, res) => {
  try {
    const {
      name,
      parentId,
      orderNum,
      status,
      remark
    } = req.body;
    
    // 如果是一级部门（没有parentId），检查该企业是否已有一级部门
    if (!parentId) {
      const topLevelDepartment = await Department.findOne(
        addEnterpriseFilter({
          where: {
            parentId: null
          }
        })
      );
      
      if (topLevelDepartment) {
        return res.status(400).json({
          code: 400,
          message: '企业下已存在一级部门，不能创建多个一级部门'
        });
      }
    }
    
    // 校验部门名称是否已存在，添加企业ID过滤
    const existDepartment = await Department.findOne(
      addEnterpriseFilter({
        where: {
          name,
          parentId: parentId || null
        }
      })
    );
    
    if (existDepartment) {
      return res.status(400).json({
        code: 400,
        message: '同级部门下已存在相同名称的部门'
      });
    }
    
    // 创建部门，添加企业ID
    const department = await Department.create(
      addEnterpriseId({
        name,
        parentId: parentId || null,
        orderNum: orderNum || 0,
        status: status === undefined ? true : status,
        remark,
        createBy: req.user ? req.user.username : 'admin', // 假设请求中有用户信息
        createTime: new Date()
      })
    );
    
    res.json({
      code: 200,
      data: department,
      message: '新增部门成功'
    });
  } catch (error) {
    console.error('新增部门失败：', error);
    res.status(500).json({
      code: 500,
      message: '新增部门失败',
      error: error.message
    });
  }
};

/**
 * 更新部门
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateDepartment = async (req, res) => {
  try {
    const {
      id,
      name,
      parentId,
      orderNum,
      status,
      remark
    } = req.body;
    
    // 查询部门是否存在，添加企业ID过滤
    const department = await Department.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!department) {
      return res.status(404).json({
        code: 404,
        message: '部门不存在'
      });
    }
    
    // 父部门不能是自己
    if (parentId && parseInt(parentId) === parseInt(id)) {
      return res.status(400).json({
        code: 400,
        message: '父部门不能是自己'
      });
    }
    
    // 检查是否有循环引用（即父部门不能是自己的子部门）
    if (parentId) {
      const isCircular = await checkCircularReference(id, parentId);
      if (isCircular) {
        return res.status(400).json({
          code: 400,
          message: '父部门不能是当前部门的子部门'
        });
      }
    }
    
    // 校验部门名称是否已存在（同级部门下不能有相同名称），添加企业ID过滤
    if (name !== department.name) {
      const existDepartment = await Department.findOne(
        addEnterpriseFilter({
          where: {
            name,
            parentId: parentId || null,
            id: { [Op.ne]: id }
          }
        })
      );
      
      if (existDepartment) {
        return res.status(400).json({
          code: 400,
          message: '同级部门下已存在相同名称的部门'
        });
      }
    }
    
    // 更新部门
    await department.update({
      name,
      parentId: parentId || null,
      orderNum,
      status,
      remark,
      updateBy: req.user ? req.user.username : 'admin', // 假设请求中有用户信息
      updateTime: new Date()
    });
    
    res.json({
      code: 200,
      data: department,
      message: '更新部门成功'
    });
  } catch (error) {
    console.error('更新部门失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新部门失败',
      error: error.message
    });
  }
};

/**
 * 检查是否有循环引用
 * @param {Number} deptId 当前部门ID
 * @param {Number} parentId 父部门ID
 * @returns {Boolean} 是否存在循环引用
 */
async function checkCircularReference(deptId, parentId) {
  // 查询所有子部门，添加企业ID过滤
  const childDepts = await Department.findAll(
    addEnterpriseFilter({
      where: { parentId: deptId }
    })
  );
  
  // 如果子部门中有与目标父部门ID相同的，则存在循环引用
  for (const child of childDepts) {
    if (child.id === parseInt(parentId)) {
      return true;
    }
    
    // 递归检查子部门的子部门
    const isCircular = await checkCircularReference(child.id, parentId);
    if (isCircular) {
      return true;
    }
  }
  
  return false;
}

/**
 * 删除部门
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询部门是否存在，添加企业ID过滤
    const department = await Department.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!department) {
      return res.status(404).json({
        code: 404,
        message: '部门不存在'
      });
    }
    
    // 查询是否有子部门，添加企业ID过滤
    const childCount = await Department.count(
      addEnterpriseFilter({
        where: { parentId: id }
      })
    );
    
    if (childCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '该部门下存在子部门，无法删除'
      });
    }
    
    // TODO: 检查是否有关联的员工，如果有则不能删除
    
    // 删除部门
    await department.destroy();
    
    res.json({
      code: 200,
      message: '删除部门成功'
    });
  } catch (error) {
    console.error('删除部门失败：', error);
    res.status(500).json({
      code: 500,
      message: '删除部门失败',
      error: error.message
    });
  }
};

/**
 * 更新部门状态
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateDepartmentStatus = async (req, res) => {
  try {
    const { id, status } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '部门ID不能为空'
      });
    }
    
    if (status === undefined) {
      return res.status(400).json({
        code: 400,
        message: '状态不能为空'
      });
    }
    
    // 查询部门是否存在，添加企业ID过滤
    const department = await Department.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );
    
    if (!department) {
      return res.status(404).json({
        code: 404,
        message: '部门不存在'
      });
    }
    
    // 更新状态
    await department.update({
      status,
      updateBy: req.user ? req.user.username : 'admin', // 假设请求中有用户信息
      updateTime: new Date()
    });
    
    res.json({
      code: 200,
      data: department,
      message: '更新部门状态成功'
    });
  } catch (error) {
    console.error('更新部门状态失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新部门状态失败',
      error: error.message
    });
  }
};

/**
 * 获取顶级部门列表（parent字段为null的部门）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getTopLevelDepartments = async (req, res) => {
  try {
    // 查询parent字段为null的部门，添加企业ID过滤
    const departments = await Department.findAll(
      addEnterpriseFilter({
        where: {
          status: true // 只查询启用状态的部门
        },
        attributes: ['id', 'name', 'orderNum', 'status', 'remark', 'createTime'],
        order: [
          ['orderNum', 'ASC'], // 按排序号升序排列
          ['createTime', 'ASC'] // 按创建时间升序排列
        ]
      })
    );

    res.json({
      code: 200,
      data: departments,
      message: '获取顶级部门成功'
    });
  } catch (error) {
    console.error('获取顶级部门失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取顶级部门失败',
      error: error.message
    });
  }
}; 
