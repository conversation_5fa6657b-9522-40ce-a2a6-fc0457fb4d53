# getEmployeePromotionPositions 方法更新说明

## 修改内容
为 `getEmployeePromotionPositions` 方法的每个岗位添加 `higherLevels` 字段，**每个岗位的 `higherLevels` 基于该岗位自己的等级ID独立计算**。

## 修改位置
- **文件**: `server/src/controllers/weichat/wechatExamListController.js`
- **方法**: `getEmployeePromotionPositions`

## 核心逻辑变化

### ⚠️ 重要：每个岗位独立计算等级信息
- **不是**基于员工的 `employee.levelId` 计算
- **而是**基于每个岗位自己的 `position.levelId` 计算
- 每个岗位都有自己独立的 `higherLevels`

## 新增功能

### 1. 为每个岗位独立计算等级信息
```javascript
// 构建响应数据，为每个岗位基于其自己的等级计算higherLevels字段
const responseData = {
    rows: await Promise.all(employeePositions.map(async (position) => {
        // 为每个岗位计算独立的higherLevels
        let higherLevels = [];
        if (position.levelId) {
            // 先获取该岗位的当前等级信息，以便获取orderNum
            const currentLevel = await Level.findOne({
                where: {
                    id: position.levelId, // 使用岗位自己的等级ID
                    enterpriseId
                }
            });

            if (currentLevel) {
                // 查询当前等级及之前的所有等级（orderNum <= 当前等级的orderNum）
                const currentAndPreviousLevels = await Level.findAll({
                    where: {
                        enterpriseId,
                        orderNum: { [Op.lte]: currentLevel.orderNum }
                    },
                    order: [['orderNum', 'ASC']],
                    attributes: ['id', 'name', 'code', 'orderNum']
                });

                // 查询当前等级之后的两个等级（orderNum > 当前等级的orderNum）
                const nextTwoLevels = await Level.findAll({
                    where: {
                        enterpriseId,
                        orderNum: { [Op.gt]: currentLevel.orderNum }
                    },
                    order: [['orderNum', 'ASC']],
                    limit: 2,
                    attributes: ['id', 'name', 'code', 'orderNum']
                });

                // 合并当前及之前的等级和后面两个等级
                higherLevels = [...currentAndPreviousLevels, ...nextTwoLevels];
            }
        }

        return {
            // ... 其他字段
            higherLevels: higherLevels // 基于该岗位自己的等级计算的higherLevels
        };
    }))
};
```

### 2. 使用 Promise.all 处理异步操作
由于每个岗位都需要独立查询数据库获取等级信息，使用 `Promise.all` 来并行处理所有异步操作，提高性能。

## higherLevels 逻辑说明

### 计算基准
- **基于每个岗位的 `position.levelId`**
- **不是基于员工的 `employee.levelId`**

### 包含的等级（针对每个岗位）
1. **当前岗位等级及之前的所有等级**：`orderNum <= 该岗位等级的orderNum`
2. **当前岗位等级之后的两个等级**：`orderNum > 该岗位等级的orderNum`，限制2个

### 排序规则
- 按 `orderNum` 升序排列

### 数据结构
```json
{
  "higherLevels": [
    {
      "id": "等级ID",
      "name": "等级名称", 
      "code": "等级编码",
      "orderNum": "等级排序号"
    }
  ]
}
```

## 示例场景

假设员工有3个岗位配置：
- 岗位A：等级为"初级"（orderNum: 1）
- 岗位B：等级为"中级"（orderNum: 3）  
- 岗位C：等级为"高级"（orderNum: 5）

那么：
- 岗位A的 `higherLevels` 基于"初级"等级计算
- 岗位B的 `higherLevels` 基于"中级"等级计算
- 岗位C的 `higherLevels` 基于"高级"等级计算

**每个岗位的等级选项都不相同！**

## 返回值变化

### 修改前
```json
{
  "data": {
    "rows": [
      {
        "id": "岗位ID",
        "name": "岗位名称",
        "level": {...}
      }
    ]
  }
}
```

### 修改后
```json
{
  "data": {
    "rows": [
      {
        "id": "岗位ID",
        "name": "岗位名称", 
        "level": {...},
        "higherLevels": [...] // 基于该岗位自己的等级计算
      }
    ]
  }
}
```

## 使用场景
前端可以直接从每个岗位对象中获取**该岗位对应的**等级选项，用于岗位等级的选择和配置。每个岗位的等级选项都是独立计算的，更加精确和实用。 