const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 系统信息配置模型
 * 用于管理系统的基础信息配置，如Banner标题、Logo、对话头像等
 */
const InfoConfig = sequelize.define('InfoConfig', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '配置ID'
  },

  mainTitle: {
    type: DataTypes.STRING(30),
    field: 'main_title',
    allowNull: true,
    comment: 'Banner主标题'
  },

  subTitle: {
    type: DataTypes.STRING(50),
    field: 'sub_title',
    allowNull: true,
    comment: 'Banner副标题'
  },

  logo: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Logo图片路径或Base64'
  },

  enterpriseLogo: {
    type: DataTypes.TEXT,
    field: 'enterprise_logo',
    allowNull: true,
    comment: '企业Logo图片路径或Base64'
  },

  enterpriseId: {
    type: DataTypes.STRING(255),
    field: 'enterprise_id',
    allowNull: true,
    comment: '企业ID'
  },

  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    allowNull: false,
    comment: '创建时间'
  },

  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    allowNull: false,
    comment: '更新时间'
  },

  isDefault: {
    type: DataTypes.TINYINT(1),
    field: 'is_default',
    allowNull: false,
    defaultValue: 0,
    comment: '是否为默认配置: 0-否, 1-是'
  }
}, {
  tableName: 'info_config',
  timestamps: false,
  indexes: [
    {
      name: 'idx_enterprise_id',
      fields: ['enterprise_id']
    },
    {
      name: 'idx_is_default',
      fields: ['is_default']
    }
  ]
});

module.exports = InfoConfig;
