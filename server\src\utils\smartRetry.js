const axios = require('axios');

/**
 * 智能重试配置
 */
const RETRY_CONFIG = {
  // 基础重试配置
  maxRetries: 3,
  baseDelay: 1000, // 基础延迟1秒
  maxDelay: 30000, // 最大延迟30秒
  
  // 不同错误类型的重试策略
  errorStrategies: {
    'ETIMEDOUT': { retries: 5, multiplier: 2, baseDelay: 2000 },
    'ECONNRESET': { retries: 4, multiplier: 1.5, baseDelay: 1500 },
    'ENOTFOUND': { retries: 2, multiplier: 1, baseDelay: 5000 },
    'ECONNREFUSED': { retries: 3, multiplier: 2, baseDelay: 3000 },
    'NETWORK_ERROR': { retries: 4, multiplier: 1.8, baseDelay: 2000 },
    'TIMEOUT': { retries: 5, multiplier: 2, baseDelay: 1000 }
  },
  
  // HTTP状态码重试策略
  statusCodeStrategies: {
    502: { retries: 4, multiplier: 1.5, baseDelay: 2000 }, // Bad Gateway
    503: { retries: 5, multiplier: 2, baseDelay: 3000 },   // Service Unavailable
    504: { retries: 3, multiplier: 2, baseDelay: 5000 },   // Gateway Timeout
    429: { retries: 6, multiplier: 3, baseDelay: 1000 },   // Too Many Requests
    408: { retries: 4, multiplier: 1.5, baseDelay: 2000 }  // Request Timeout
  }
};

/**
 * 智能重试类
 */
class SmartRetry {
  constructor(options = {}) {
    this.config = { ...RETRY_CONFIG, ...options };
    this.retryStats = new Map();
  }

  /**
   * 获取重试策略
   * @param {Error} error - 错误对象
   * @returns {Object} 重试策略
   */
  getRetryStrategy(error) {
    // 检查错误代码
    if (error.code && this.config.errorStrategies[error.code]) {
      return this.config.errorStrategies[error.code];
    }
    
    // 检查HTTP状态码
    if (error.response?.status && this.config.statusCodeStrategies[error.response.status]) {
      return this.config.statusCodeStrategies[error.response.status];
    }
    
    // 检查网络错误
    if (error.message && error.message.includes('Network Error')) {
      return this.config.errorStrategies['NETWORK_ERROR'];
    }
    
    // 检查超时错误
    if (error.message && (error.message.includes('timeout') || error.message.includes('TIMEOUT'))) {
      return this.config.errorStrategies['TIMEOUT'];
    }
    
    // 默认策略
    return {
      retries: this.config.maxRetries,
      multiplier: 1.5,
      baseDelay: this.config.baseDelay
    };
  }

  /**
   * 计算延迟时间
   * @param {number} attempt - 当前重试次数
   * @param {Object} strategy - 重试策略
   * @returns {number} 延迟毫秒数
   */
  calculateDelay(attempt, strategy) {
    const delay = strategy.baseDelay * Math.pow(strategy.multiplier, attempt);
    const jitter = Math.random() * 0.1 * delay; // 添加10%的随机抖动
    return Math.min(delay + jitter, this.config.maxDelay);
  }

  /**
   * 执行带重试的请求
   * @param {Function} requestFn - 请求函数
   * @param {Object} options - 选项
   * @returns {Promise} 请求结果
   */
  async executeWithRetry(requestFn, options = {}) {
    const { identifier = 'unknown', logPrefix = '[Smart Retry]' } = options;
    
    let lastError;
    let attempt = 0;
    
    // 初始化统计信息
    if (!this.retryStats.has(identifier)) {
      this.retryStats.set(identifier, {
        totalRequests: 0,
        successRequests: 0,
        totalRetries: 0,
        errorTypes: {}
      });
    }
    
    const stats = this.retryStats.get(identifier);
    stats.totalRequests++;

    while (attempt <= this.config.maxRetries) {
      try {
        console.log(`${logPrefix} 执行请求 (尝试 ${attempt + 1}/${this.config.maxRetries + 1}): ${identifier}`);
        
        const result = await requestFn();
        
        // 成功记录
        stats.successRequests++;
        if (attempt > 0) {
          console.log(`${logPrefix} ✅ 重试成功: ${identifier} (第${attempt}次重试)`);
        }
        
        return result;
        
      } catch (error) {
        lastError = error;
        attempt++;
        
        // 记录错误统计
        const errorKey = error.code || error.response?.status || 'UNKNOWN';
        stats.errorTypes[errorKey] = (stats.errorTypes[errorKey] || 0) + 1;
        
        console.error(`${logPrefix} ❌ 请求失败 (尝试 ${attempt}): ${identifier}`, {
          error: error.message,
          code: error.code,
          status: error.response?.status
        });
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt > this.config.maxRetries) {
          console.error(`${logPrefix} 🚫 所有重试都失败了: ${identifier}`);
          break;
        }
        
        // 获取重试策略
        const strategy = this.getRetryStrategy(error);
        
        // 检查是否应该重试
        if (attempt > strategy.retries) {
          console.error(`${logPrefix} 🚫 超过该错误类型的最大重试次数: ${identifier}`);
          break;
        }
        
        // 计算延迟时间
        const delay = this.calculateDelay(attempt - 1, strategy);
        
        console.log(`${logPrefix} ⏳ ${delay}ms后进行第${attempt}次重试: ${identifier}`);
        stats.totalRetries++;
        
        // 等待延迟
        await this.sleep(delay);
      }
    }
    
    // 所有重试都失败了
    throw lastError;
  }

  /**
   * 睡眠函数
   * @param {number} ms - 毫秒数
   * @returns {Promise}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取重试统计信息
   * @param {string} identifier - 标识符
   * @returns {Object} 统计信息
   */
  getStats(identifier) {
    const stats = this.retryStats.get(identifier);
    if (!stats) return null;
    
    return {
      ...stats,
      successRate: stats.totalRequests > 0 ? 
        (stats.successRequests / stats.totalRequests * 100).toFixed(2) + '%' : '0%',
      averageRetries: stats.totalRequests > 0 ? 
        (stats.totalRetries / stats.totalRequests).toFixed(2) : '0'
    };
  }

  /**
   * 获取所有统计信息
   * @returns {Object} 所有统计信息
   */
  getAllStats() {
    const allStats = {};
    this.retryStats.forEach((stats, identifier) => {
      allStats[identifier] = this.getStats(identifier);
    });
    return allStats;
  }

  /**
   * 重置统计信息
   * @param {string} identifier - 可选的标识符，不提供则重置所有
   */
  resetStats(identifier) {
    if (identifier) {
      this.retryStats.delete(identifier);
    } else {
      this.retryStats.clear();
    }
  }
}

/**
 * 创建带重试的axios实例
 * @param {Object} axiosConfig - axios配置
 * @param {Object} retryConfig - 重试配置
 * @returns {Object} 带重试功能的axios实例
 */
function createRetryAxios(axiosConfig = {}, retryConfig = {}) {
  const smartRetry = new SmartRetry(retryConfig);
  const axiosInstance = axios.create(axiosConfig);
  
  // 包装请求方法
  const originalRequest = axiosInstance.request;
  axiosInstance.request = async function(config) {
    const identifier = `${config.method?.toUpperCase()} ${config.url}`;
    
    return smartRetry.executeWithRetry(
      () => originalRequest.call(this, config),
      { identifier, logPrefix: '[Axios Retry]' }
    );
  };
  
  return {
    axios: axiosInstance,
    smartRetry,
    getStats: () => smartRetry.getAllStats()
  };
}

// 创建全局智能重试实例
const globalSmartRetry = new SmartRetry();

module.exports = {
  SmartRetry,
  createRetryAxios,
  globalSmartRetry,
  RETRY_CONFIG
}; 