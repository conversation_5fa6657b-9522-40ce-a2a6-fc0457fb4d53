const express = require('express');
const router = express.Router();
const testController = require('../../controllers/exam-manage/testController');
const { requireLogin } = require('../../middleware/auth');

/**
 * 测试路由
 * 仅用于开发和测试环境
 */

// 创建测试考试申请
router.post('/application', requireLogin, testController.createTestApplication);
// 也支持GET方法（仅测试环境使用），不要求登录
router.get('/application', testController.createTestApplication);

// 创建测试考试记录
router.post('/exam-records', requireLogin, testController.createTestExamRecords);
// 也支持GET方法（仅测试环境使用），不要求登录
router.get('/exam-records', testController.createTestExamRecords);

module.exports = router; 