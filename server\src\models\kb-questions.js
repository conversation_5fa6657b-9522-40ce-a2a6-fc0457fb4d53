// 知识库题目表模型
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const KBQuestions = sequelize.define('kb_questions', {
  id: {
    type: DataTypes.STRING(36),
    primaryKey: true,
    comment: '题目ID'
  },
  knowledge_base_id: {
    type: DataTypes.STRING(36),
    allowNull: false,
    comment: '关联的知识库ID'
  },
  segment_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '关联的分段ID，人工出题可为空'
  },
  question: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '题目内容'
  },
  answer: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '答案内容'
  },
  type: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: '人工出题',
    comment: '题目类型：智能出题、人工出题'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  },
  enterprise_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '企业ID'
  }
}, {
  tableName: 'kb_questions',
  timestamps: false
});

// 定义关联关系
const KnowledgeBase = require('./knowledge-base');

// 题目与知识库的关联
KBQuestions.belongsTo(KnowledgeBase, {
  foreignKey: 'knowledge_base_id',
  targetKey: 'id',
  as: 'knowledgeBase',
  // 解决字符集/排序规则不匹配问题
  scope: {
    // 空scope防止Sequelize自动添加额外的查询条件
  },
  hooks: true
});

module.exports = KBQuestions;
