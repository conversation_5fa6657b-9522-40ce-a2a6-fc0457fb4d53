const express = require('express');
const router = express.Router();
const wechatController = require('../controllers/weichat/wechatController');
const wechatPracticeController = require('../controllers/weichat/wechatPracticeController');
const wechatExamListController = require('../controllers/weichat/wechatExamListController');
const wechatMyInfoController = require('../controllers/weichat/wechatMyInfoController');
const announcementController = require('../controllers/announcementController');
const { createUpload } = require('../utils/fileUpload');
const fileUpload = createUpload('user');
const feedbackUpload = createUpload('feedback');
const { requireLogin } = require('../middleware/auth');
const positionController = require('../controllers/organization/positionController');
const departmentController = require('../controllers/organization/departmentController');


// 获取微信小程序OpenID
router.get('/openid', wechatController.getOpenId);

// 微信小程序用户认证
router.post('/authenticate', wechatController.authenticate);

// 检查用户授权状态
router.get('/check-authorization', wechatController.checkAuthorization);

// 微信小程序用户身份认证
router.post('/verify-identity', wechatController.verifyIdentity);

router.get('/getRestaurantConfig', wechatController.getRestaurantConfigByOpenId);

// 根据openId获取用户信息
router.get('/user', wechatController.getUserByOpenId);

// 检查用户是否已完成身份认证
router.get('/check-identity', wechatController.checkIdentityVerification);

// 检查用户是否绑定企业
router.get('/check-enterprise-binding', wechatController.checkEnterpriseBinding);

// 员工企业申请
router.post('/apply-to-enterprise', wechatController.applyToEnterprise);

// 获取手机号
router.post('/phone-number', wechatController.getPhoneNumber);

// 切换岗位
router.post('/user/update-position', wechatPracticeController.updatePosition);

// 切换级别
router.post('/user/update-level', wechatPracticeController.updateLevel);

// 获取练习列表
router.get('/practice/list', wechatPracticeController.getPracticeList);

// 获取练习统计
router.get('/practice/statistics', wechatPracticeController.getPracticeStatistics);

// 获取练习出题
router.post('/practice/question', wechatPracticeController.getQuestion);

// 获取练习解析
router.post('/practice/analysis', wechatPracticeController.parseAnswer);

// 获取用户当前岗位认证信息
router.get('/exam/position-certification', wechatExamListController.getUserPositionCertification);

// 根据openId获取员工晋升岗位信息
router.get('/employee/promotion-positions', wechatExamListController.getEmployeePromotionPositions);

// 获取员工可选择的剩余岗位名称
router.get('/employee/available-position-names', wechatExamListController.getAvailablePositionNames);

// 上传用户头像图片
router.post('/user/upload', fileUpload.single('file'), wechatController.uploadConfigImage);

// 查看证书详情
router.get('/getCertificateDetail', wechatExamListController.getCertificateDetail);

// 获取练习出题
router.get('/practice/getPositionName', wechatPracticeController.getPositionNamesByUserType);

// 根据openId获取员工所属企业的信息配置
router.get('/getInfoConfig', wechatController.getInfoConfigByOpenId);

// 获取用户练习记录信息
router.get('/myinfo/practice', wechatMyInfoController.getUserPracticeRecords);

// 获取练习记录详情
router.get('/myinfo/practice/:recordId/details', wechatMyInfoController.getPracticeRecordDetails);

// 获取用户考试统计信息
router.get('/myinfo/exam/statistics', wechatMyInfoController.getUserExamStatistics);

// 获取用户证书列表
router.get('/myinfo/certificates', wechatMyInfoController.getUserCertificates);

// 创建意见反馈
router.post('/myinfo/createFeedback', wechatMyInfoController.createFeedback);

// 获取用户历史反馈记录
router.get('/myinfo/feedbacks', wechatMyInfoController.getUserFeedbacks);

// 上传用户反馈图片
router.post('/myinfo/upload-feedback-image', feedbackUpload.single('file'), wechatMyInfoController.uploadFeedbackImage);

// 上传用户反馈图片
router.post('/home/<USER>',  wechatController.callCankaoshi);

// 获取最近练习的三个科目
router.get('/home/<USER>',  wechatPracticeController.getRecentPracticeSubjects);

// 获取当前时间段有效的公告列表
router.get('/announcements/current', wechatController.getCurrentAnnouncements);

// 获取公告详情
router.get('/announcements/:id', announcementController.getAnnouncement);

router.get('/position/:positionId/levels', positionController.getLevelsByPosition);

router.get('/position/getPositionOptions', positionController.getPositionOptions);

// 获取岗位信息并包含对应等级
router.get('/position/getPositionWithLevels', positionController.getPositionWithLevels);

// 获取岗位类型及相关信息（positionType -> positionName -> level）
router.get('/position/getPositionTypesWithNamesAndLevels', positionController.getPositionTypesWithNamesAndLevels);

// 获取顶级部门列表
router.get('/department/getTopLevelDepartments', departmentController.getTopLevelDepartments);
// 获取树机构部门列表
router.get('/department/tree', departmentController.getDepartmentTree);

// 根据企业邀请码获取企业信息
router.get('/enterprise/info/:inviteCode', wechatController.getEnterpriseByInviteCode);

// 生成餐烤证书
router.post('/certificate/generate', wechatController.generateCertificate);

// 获取用户未弹出的成就
router.get('/achievements/unpopped', wechatController.getUnpoppedAchievements);

// 获取所有徽章并标记用户获得状态
router.get('/achievements/all', wechatController.getAllAchievementsWithUserStatus);


module.exports = router;
