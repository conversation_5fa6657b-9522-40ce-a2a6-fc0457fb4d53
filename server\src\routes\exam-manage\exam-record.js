const express = require('express');
const router = express.Router();
const examRecordController = require('../../controllers/exam-manage/examRecordController');
const { requireLogin } = require('../../middleware/auth');

// 获取考试记录列表
router.get('/records', requireLogin, examRecordController.getExamRecords);

// 获取考试记录详情
router.get('/record/:id', requireLogin, examRecordController.getExamRecordDetail);

// 创建考试记录
router.post('/record', requireLogin, examRecordController.createExamRecord);

// 更新考试记录
router.put('/record/:id', requireLogin, examRecordController.updateExamRecord);

// 更新考试记录确认状态
router.post('/record/:id/confirm', requireLogin, examRecordController.updateConfirmStatus);

// 删除考试记录
router.delete('/record/:id', requireLogin, examRecordController.deleteExamRecord);

// 小程序接口
// 小程序创建考试记录
router.post('/miniapp/record', examRecordController.createExamRecordFromMiniapp);

module.exports = router; 