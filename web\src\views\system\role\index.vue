<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
        :model-value="searchForm"
        :items="searchFormItems"
        @search="handleSearch"
        @reset="handleReset"
      />
      </template>
      <template #actions>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增角色
        </a-button>
      </template>
    </page-header>

    <!-- 表格区域 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      @edit="handleEdit"
      @delete="handleDelete"
      :action-config="actionConfig"
      :delete-title="deleteTitle"
      
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'success' : 'error'">
            {{ record.status ? '正常' : '禁用' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-button 
              type="link" 
              size="small" 
              @click="handlePermission(record)"
              class="custom-button"
            >
            <key-outlined />权限
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="handleUsers(record)"
              class="custom-button"
            >
            <user-outlined />用户
            </a-button>
        </template>
      </template>
    </base-table>

    <!-- 角色表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-item label="角色名称" name="roleName">
          <a-input v-model:value="formData.roleName" placeholder="请输入角色名称" />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formData.sort" :min="0" style="width: 100%" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formData.status" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限分配弹窗 -->
    <a-modal
      v-model:visible="permissionModalVisible"
      title="分配权限"
      @ok="handlePermissionOk"
      @cancel="handlePermissionCancel"
      width="600px"
    >
      <a-spin :spinning="menuLoading">
        <div class="permission-tree-container">
          <div class="permission-tree-header">
            <a-checkbox 
              :checked="isAllChecked"
              :indeterminate="isIndeterminate"
              @change="handleCheckAll"
            >
              全选/取消全选
            </a-checkbox>
          </div>
          <a-divider style="margin: 8px 0" />
          <div v-if="menuTreeData.length > 0" class="permission-tree-content">
            <a-tree
              :default-expand-all="true"
              v-model:checkedKeys="checkedKeys"
              :tree-data="menuTreeData"
              :checkable="true"
              :selectable="false"
              :autoExpandParent="true"
            />
          </div>
          <a-empty v-else description="暂无菜单数据" />
        </div>
      </a-spin>
    </a-modal>

    <!-- 角色用户列表弹窗 -->
    <a-modal
      v-model:visible="usersModalVisible"
      :title="`角色用户列表 - ${currentRole?.roleName || ''}`"
      width="700px"
      @cancel="handleUsersCancel"
      :footer="null"
    >
      <a-table
        :columns="userColumns"
        :data-source="roleUsers"
        :loading="usersLoading"
        :pagination="{ 
          showSizeChanger: true, 
          showTotal: total => `共 ${total} 条`,
          defaultPageSize: 5
        }"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status ? 'success' : 'error'">
              {{ record.status ? '正常' : '禁用' }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  getRoleList, 
  createRole, 
  updateRole, 
  deleteRole, 
  getRolePermissions, 
  updateRolePermissions,
  getRoleUsers
} from '@/api/system/role'
import { getMenuList } from '@/api/system/menu'
import { useMenuStore } from '@/store/modules/menu'
import { SearchFormCard } from '@/components/SearchForm'
import { useTablePagination } from '@/utils/common'

// 加载状态
const loading = ref(false)

// 获取菜单store
const menuStore = useMenuStore()

// 搜索表单
const searchForm = reactive({
  roleName: ''
})

// 表格操作配置
const actionConfig = {
  edit: true,
  delete: true
};

// 删除确认标题
const deleteTitle = '确定删除该角色吗?';


// 搜索表单配置
const searchFormItems = [
  {
    label: '角色名称',
    field: 'roleName',
    type: 'input',
    placeholder: '请输入角色名称'
  }
]

// 表格列定义
const columns = [
  {
    title: '角色名称',
    dataIndex: 'roleName',
    key: 'roleName',
    width: 200  // 添加固定宽度
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 280
  }
]

// 表格数据
const tableData = ref([])



// 弹窗控制
const modalVisible = ref(false)
const modalTitle = ref('新增角色')
const formRef = ref()

// 表单数据
const formData = reactive({
  id: undefined,
  roleName: '',
  sort: 0,
  status: true,
  remark: ''
})

// 表单校验规则
const rules = {
  roleName: [
    { required: true, message: '请输入角色名称' },
    { max: 50, message: '角色名称最多50个字符' }
  ],
  sort: [{ required: true, message: '请输入排序号' }]
}

// 权限弹窗控制
const permissionModalVisible = ref(false)
const checkedKeys = ref({ checked: [], halfChecked: [] }) // checkStrictly=true模式下为对象 {checked: [], halfChecked: []}
const currentRole = ref(null)
const menuTreeData = ref([]) // 存储菜单树数据
const menuLoading = ref(false) // 添加菜单加载状态

// 用户列表控制
const usersModalVisible = ref(false)
const roleUsers = ref([])
const usersLoading = ref(false)
const userColumns = [
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  }
]

// 树形结构的展开节点控制
const expandedKeys = ref([])
const selectedKeys = ref([])
const expandedAll = ref(true) // 默认设置为展开状态

// 计算属性：全选状态
const isAllChecked = computed(() => {
  if (!menuTreeData.value || menuTreeData.value.length === 0) return false
  const allIds = getAllTreeIds(menuTreeData.value)
  const checkedCount = checkedKeys.value ? checkedKeys.value.length : 0
  return allIds.length > 0 && checkedCount === allIds.length
})

// 计算属性：半选状态
const isIndeterminate = computed(() => {
  if (!menuTreeData.value || menuTreeData.value.length === 0) return false
  const allIds = getAllTreeIds(menuTreeData.value)
  const checkedCount = checkedKeys.value ? checkedKeys.value.length : 0
  return checkedCount > 0 && checkedCount < allIds.length
})



// 全选/取消全选
const handleCheckAll = (e) => {
  if (e.target.checked) {
    // 全选
    // checkedKeys.value = {
    //   checked: getAllTreeIds(menuTreeData.value),
    //   halfChecked: []
    // };
    checkedKeys.value=getAllTreeIds(menuTreeData.value)
  } else {
    // 取消全选
    // checkedKeys.value = {
    //   checked: [],
    //   halfChecked: []
    // };
    checkedKeys.value=[]
  }
}

// 获取角色列表
const fetchRoleList = async () => {
  try {
    loading.value = true
    const params = {
      roleName: searchForm.roleName,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    
    const res = await getRoleList(params)
    tableData.value = res.list || []
    // 更新分页信息
    updatePagination({
      total: res.total || 0,
      pageNum: res.pageNum || pagination.current,
      pageSize: res.pageSize || pagination.pageSize
    })
  } catch (error) {
    message.error('获取角色列表失败: ' + error.message)
    tableData.value = []
    // 发生错误时重置分页
    updatePagination({ total: 0, pageNum: 1 })
  } finally {
    loading.value = false
  }
}

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchRoleList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
})

// 方法定义
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values)
  }
  // 重置到第一页
  resetPagination()
  fetchRoleList()
}

const handleReset = () => {
  searchForm.roleName = ''
  // 重置到第一页
  resetPagination()
  fetchRoleList()
}

const handleAdd = () => {
  modalTitle.value = '新增角色'
  formData.id = undefined
  formData.roleName = ''
  formData.sort = 0
  formData.status = true
  formData.remark = ''
  modalVisible.value = true
}

const handleEdit = (record) => {
  modalTitle.value = '编辑角色'
  Object.assign(formData, record)
  modalVisible.value = true
}

const handleDelete = async (record) => {
  try {
    loading.value = true
    await deleteRole(record.id)
    message.success('删除成功')
    fetchRoleList()
  } catch (error) {
    message.error('删除失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (formData.id) {
      // 编辑
      await updateRole(formData)
      message.success('更新成功')
    } else {
      // 新增
      await createRole(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    fetchRoleList()
  } catch (error) {
    message.error('操作失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const handlePermission = async (record) => {
  try {
    currentRole.value = record
    permissionModalVisible.value = true
    menuLoading.value = true
    // 获取菜单树
    await fetchMenuTree()
    // 获取角色的权限
    const rolePermResponse = await getRolePermissions(record.id)
    // 解析角色权限数据
    let roleMenuIds = []
    if (rolePermResponse && rolePermResponse.data && rolePermResponse.data.menuIds) {
      roleMenuIds = rolePermResponse.data.menuIds
    } else if (rolePermResponse && rolePermResponse.menuIds) {
      roleMenuIds = rolePermResponse.menuIds
    }
    
    // 处理权限回显：checkStrictly=true模式下直接设置checked数组
    // checkedKeys.value = {
    //   checked: Array.isArray(roleMenuIds) ? roleMenuIds : [],
    //   halfChecked: []
    // }
    checkedKeys.value=Array.isArray(roleMenuIds) ? roleMenuIds : []

    // 初始化展开状态，只展开第一级
    expandedKeys.value = []
  } catch (error) {
    console.error('获取权限数据失败:', error)
    message.error('获取权限数据失败: ' + error.message)
  } finally {
    menuLoading.value = false
  }
}

const handlePermissionOk = async () => {
  try {
    loading.value = true;
    
    // 获取当前选中的菜单ID数组（checkStrictly=true模式下从checked属性获取）
    const checked = [...(checkedKeys.value || [])];
    
    // checkStrictly=true模式下，直接使用用户选中的权限ID
    const finalMenuIds = checked;
    
    // 如果没有选中任何菜单，确认是否继续
    if (!finalMenuIds || finalMenuIds.length === 0) {
      const confirm = window.confirm('您没有选择任何菜单，确定要清空该角色的所有权限吗？');
      if (!confirm) {
        loading.value = false;
        return;
      }
    }
    
    // 提交选中的菜单ID到后端
    await updateRolePermissions({
      roleId: currentRole.value.id,
      permissions: finalMenuIds
    });
    
    message.success('权限更新成功');
    
    // 关闭弹窗
    permissionModalVisible.value = false;
    
    // 可选：重新获取数据以验证保存结果（用于调试）
    // await handlePermission(currentRole.value);
  } catch (error) {
    console.error('权限更新失败:', error);
    message.error('权限更新失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

const handlePermissionCancel = () => {
  permissionModalVisible.value = false
  checkedKeys.value = { checked: [], halfChecked: [] }
  currentRole.value = null
}

const handleUsers = async (record) => {
  try {
    loading.value = true
    currentRole.value = record
    
    // 获取角色用户列表
    await fetchRoleUsers()
    
    usersModalVisible.value = true
  } catch (error) {
    message.error('获取用户列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleUsersCancel = () => {
  usersModalVisible.value = false
}

// 获取菜单树数据
const fetchMenuTree = async () => {
  try {
    menuLoading.value = true
    
    // 直接调用接口获取菜单列表，不依赖store中的数据
    const response = await getMenuList()
    
    // 检查响应结构，兼容多种可能的返回格式
    let menuList = []
    
    if (response) {
      if (response.data) {
        // 标准返回结构 { data: [...] }
        menuList = response.data
      } else if (response.code === 200 && response.data) {
        // 业务封装返回结构 { code: 200, data: [...] }
        menuList = response.data
      } else if (Array.isArray(response)) {
        // 直接返回数组
        menuList = response
      }
    }
    
    // 检查菜单列表是否为数组
    if (!Array.isArray(menuList)) {
      console.error('菜单列表不是数组:', menuList)
      menuList = []
    }

    // 如果菜单列表为空，使用测试数据
    if (menuList.length === 0) {
      console.warn('菜单列表为空，使用测试数据')
      menuList = getTestMenuData()
    }

    // 将菜单转换为树形结构
    menuTreeData.value =  formatMenuTree(menuList)


    // 如果菜单树还是空的，尝试使用硬编码的基础菜单数据
    if (menuTreeData.value.length === 0) {
      console.warn('格式化后菜单树为空，使用基础菜单数据')
      menuTreeData.value = getBasicMenuTree();
    }
  } catch (error) {
    menuTreeData.value = getBasicMenuTree(); // 发生错误时使用基础菜单
  } finally {
    menuLoading.value = false
  }
}

// 获取测试菜单数据（用于调试）
const getTestMenuData = () => {
  return [
    {
      id: 1,
      name: '控制台',
      path: '/dashboard',
      icon: 'dashboard',
      parentId: 0,
      type: 1,
      perms: 'dashboard',
      children: []
    },
    {
      id: 2,
      name: '系统管理',
      path: '/system',
      icon: 'setting',
      parentId: 0,
      type: 0,
      perms: 'system',
      children: [
        {
          id: 21,
          name: '用户管理',
          path: '/system/user',
          icon: 'user',
          parentId: 2,
          type: 1,
          perms: 'system:user:list',
          children: []
        },
        {
          id: 22,
          name: '角色管理',
          path: '/system/role',
          icon: 'team',
          parentId: 2,
          type: 1,
          perms: 'system:role:list',
          children: []
        },
        {
          id: 23,
          name: '菜单管理',
          path: '/system/menu',
          icon: 'menu',
          parentId: 2,
          type: 1,
          perms: 'system:menu:list',
          children: []
        }
      ]
    },
    {
      id: 3,
      name: '企业管理',
      path: '/enterprise',
      icon: 'team',
      parentId: 0,
      type: 0,
      perms: 'enterprise',
      children: [
        {
          id: 31,
          name: '企业列表',
          path: '/enterprise/list',
          icon: 'list',
          parentId: 3,
          type: 1,
          perms: 'enterprise:list',
          children: []
        }
      ]
    }
  ]
}

// 获取基础菜单树（当API失败时使用）
const getBasicMenuTree = () => {
  return [
    {
      title: '控制台',
      key: 1,
      perms: 'dashboard'
    },
    {
      title: '系统管理',
      key: 2,
      children: [
        {
          title: '用户管理',
          key: 21,
          perms: 'system:user:list'
        },
        {
          title: '角色管理',
          key: 22,
          perms: 'system:role:list'
        },
        {
          title: '菜单管理',
          key: 23,
          perms: 'system:menu:list'
        }
      ]
    }
  ]
}

// 格式化菜单数据为树形结构
const formatMenuTree = (menus) => {
  if (!Array.isArray(menus)) {
    console.error('formatMenuTree: 输入不是数组', menus)
    return []
  }

  console.log('formatMenuTree: 开始格式化菜单', menus)

  return menus.map(menu => {
    if (!menu) {
      console.warn('formatMenuTree: 菜单项为空')
      return null
    }

    console.log(`formatMenuTree: 处理菜单项 ${menu.name}`, menu)

    const node = {
      title: menu.name || menu.title || '未命名',
      key: menu.id,
      perms: menu.perms || menu.permission
    }

    // 检查是否有子菜单
    if (menu.children && Array.isArray(menu.children) && menu.children.length > 0) {
      console.log(`formatMenuTree: ${menu.name} 有 ${menu.children.length} 个子菜单`, menu.children)
      node.children = formatMenuTree(menu.children)
      console.log(`formatMenuTree: ${menu.name} 格式化后的子菜单`, node.children)
    } else {
      console.log(`formatMenuTree: ${menu.name} 没有子菜单`)
    }

    return node
  }).filter(Boolean) // 过滤掉空值
}

// 递归获取树的所有节点ID
const getAllTreeIds = (treeData) => {
  const ids = []
  
  const traverse = (nodes) => {
    if (!nodes) return
    
    nodes.forEach(node => {
      ids.push(node.key)
      
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(treeData)
  return ids
}

// 获取角色用户列表
const fetchRoleUsers = async () => {
  try {
    loading.value = true
    const res = await getRoleUsers(currentRole.value.id)
    roleUsers.value = res.list
  } catch (error) {
    message.error('获取用户列表失败: ' + error.message)
    roleUsers.value = []
  } finally {
    loading.value = false
  }
}



// 初始化获取角色数据
onMounted(() => {
  fetchRoleList()
  // 预加载菜单数据
  fetchMenuTree()
})
</script>

<style scoped>
.table-toolbar {
  margin-bottom: 16px;
}

/* 权限树相关样式 */
.permission-tree-container {
  max-height: 450px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.permission-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
  margin-bottom: 8px;
}

.permission-tree-content {
  padding: 8px;
}

.tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 28px;
}

.node-title {
  margin-right: 5px;
  font-weight: 500;
}

.node-perms {
  font-size: 12px;
  color: #8c8c8c;
}

/* a-tree 组件样式 */
:deep(.ant-tree .ant-tree-treenode) {
  padding: 4px 0 !important;
}

:deep(.ant-tree .ant-tree-node-content-wrapper) {
  display: flex !important;
  flex: 1;
  align-items: center;
}

:deep(.ant-tree .ant-tree-switcher) {
  align-self: center;
}

:deep(.ant-tree .ant-tree-checkbox) {
  margin-right: 8px;
}
:deep(.ant-tree .ant-tree-checkbox:not(.ant-tree-checkbox-disabled):hover .ant-tree-checkbox-inner){
 border-color:var(--primary-color)
}
:deep(.ant-tree .ant-tree-checkbox-checked .ant-tree-checkbox-inner),:deep(.ant-tree .ant-tree-checkbox-checked:not(.ant-tree-checkbox-disabled):hover .ant-tree-checkbox-inner) {
 background-color:var(--primary-color);
 border-color:var(--primary-color)
}
:deep(.ant-checkbox-indeterminate .ant-checkbox-inner:after),
:deep(.ant-tree .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner:after){
  background-color:var(--primary-color);

}
</style>