const EmployeePosition = require('../../models/EmployeePosition');
const Employee = require('../../models/Employee');
const Position = require('../../models/Position');
const Level = require('../../models/Level');
const PositionName = require('../../models/PositionName');
const PositionType = require('../../models/PositionType');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../../utils/enterpriseFilter');

// 设置模型关联
Employee.setupEmployeePositionAssociations();

/**
 * 获取员工的岗位配置列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getEmployeePositions = async (req, res) => {
  try {
    const { employeeId } = req.params;

    // 验证员工是否存在
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { id: employeeId }
      })
    );

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    // 查询员工的所有岗位配置
    const employeePositions = await EmployeePosition.findAll(
      addEnterpriseFilter({
        where: { employeeId },
        include: [
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name', 'typeId']
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name']
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [
          // ['isDefault', 'DESC'], // 默认岗位排在前面
          ['createTime', 'ASC']
        ]
      })
    );

    // 格式化返回数据
    const formattedData = employeePositions.map(ep => {
      const data = ep.toJSON();
      return {
        id: data.id,
        employeeId: data.employeeId,
        positionId: data.positionId,
        positionTypeId: data.positionTypeId,
        levelId: data.levelId,
        isDefault: data.isDefault,
        positionName: data.positionName?.name || '',
        positionTypeName: data.positionType?.name || '',
        levelName: data.level?.name || '',
        createTime: data.createTime,
        updateTime: data.updateTime
      };
    });

    res.json({
      code: 200,
      data: formattedData,
      message: '获取员工岗位配置成功'
    });
  } catch (error) {
    console.error('获取员工岗位配置失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取员工岗位配置失败',
      error: error.message
    });
  }
};

/**
 * 更新员工的岗位配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateEmployeePositions = async (req, res) => {
  try {
    const { employeeId } = req.params;
    const { positions } = req.body; // positions: [{ positionId, positionTypeId, levelId, isDefault }]

    // 验证员工是否存在
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { id: employeeId }
      })
    );

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    // 验证数据
    if (!positions || !Array.isArray(positions) || positions.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '岗位配置不能为空'
      });
    }

    // 验证是否有且仅有一个默认岗位
    const defaultPositions = positions.filter(p => p.isDefault);
    if (defaultPositions.length !== 1) {
      return res.status(400).json({
        code: 400,
        message: '必须设置一个且仅有一个默认岗位'
      });
    }

    // 验证岗位、岗位类型和等级是否存在
    for (const pos of positions) {
      if (!pos.positionId || !pos.positionTypeId || !pos.levelId) {
        return res.status(400).json({
          code: 400,
          message: '岗位ID、岗位类型ID、等级ID不能为空'
        });
      }

      // 验证岗位类型是否存在（从PositionType模型验证）
      const positionType = await PositionType.findOne(
        addEnterpriseFilter({
          where: { id: pos.positionTypeId }
        })
      );
      if (!positionType) {
        return res.status(400).json({
          code: 400,
          message: `岗位类型ID ${pos.positionTypeId} 不存在`
        });
      }

      // 验证岗位是否存在（从PositionName模型验证）
      const positionName = await PositionName.findOne(
        addEnterpriseFilter({
          where: { id: pos.positionId }
        })
      );
      if (!positionName) {
        return res.status(400).json({
          code: 400,
          message: `岗位ID ${pos.positionId} 不存在`
        });
      }

      // 验证岗位是否属于指定的岗位类型
      if (positionName.typeId !== pos.positionTypeId) {
        return res.status(400).json({
          code: 400,
          message: `岗位ID ${pos.positionId} 不属于岗位类型ID ${pos.positionTypeId}`
        });
      }

      const level = await Level.findOne(
        addEnterpriseFilter({
          where: { id: pos.levelId }
        })
      );
      if (!level) {
        return res.status(400).json({
          code: 400,
          message: `等级ID ${pos.levelId} 不存在`
        });
      }
    }

    // 开始事务处理
    const transaction = await EmployeePosition.sequelize.transaction();

    try {
      console.log(`准备删除员工ID ${employeeId} 的岗位配置...`);
      
      // 删除员工现有的岗位配置
      const deleteCondition = addEnterpriseFilter({ where: { employeeId } }).where;
      console.log('删除条件:', JSON.stringify(deleteCondition, null, 2));
      
      const deletedCount = await EmployeePosition.destroy({
        where: deleteCondition,
        transaction
      });
      
      console.log(`成功删除 ${deletedCount} 条岗位配置记录`);

      // 创建新的岗位配置
      const newPositions = positions.map(pos => 
        addEnterpriseId({
          employeeId: parseInt(employeeId),
          positionId: pos.positionId,
          positionTypeId: pos.positionTypeId,
          levelId: pos.levelId,
          isDefault: pos.isDefault || false,
          createBy: req.user ? req.user.username : 'system',
          updateBy: req.user ? req.user.username : 'system'
        })
      );

      await EmployeePosition.bulkCreate(newPositions, { transaction });

      // 更新员工表的默认岗位信息（保持向后兼容）
      const defaultPosition = positions.find(p => p.isDefault);
      if (defaultPosition) {
        await Employee.update({
          positionId: defaultPosition.positionId,
          positionTypeId: defaultPosition.positionTypeId,
          levelId: defaultPosition.levelId,
          updateBy: req.user ? req.user.username : 'system',
          updateTime: new Date()
        }, {
          where: { id: employeeId },
          transaction
        });
      }

      await transaction.commit();

      res.json({
        code: 200,
        message: '更新员工岗位配置成功'
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('更新员工岗位配置失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新员工岗位配置失败',
      error: error.message
    });
  }
};

/**
 * 添加员工岗位配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.addEmployeePosition = async (req, res) => {
  try {
    const { employeeId, positionId, positionTypeId, levelId, isDefault } = req.body;

    // 验证必填字段
    if (!employeeId || !positionId || !positionTypeId || !levelId) {
      return res.status(400).json({
        code: 400,
        message: '员工ID、岗位ID、岗位类型ID、等级ID不能为空'
      });
    }

    // 验证员工是否存在
    const employee = await Employee.findOne(
      addEnterpriseFilter({
        where: { id: employeeId }
      })
    );

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    // 验证岗位类型是否存在（从PositionType模型验证）
    const positionType = await PositionType.findOne(
      addEnterpriseFilter({
        where: { id: positionTypeId }
      })
    );
    if (!positionType) {
      return res.status(400).json({
        code: 400,
        message: `岗位类型ID ${positionTypeId} 不存在`
      });
    }

    // 验证岗位是否存在（从PositionName模型验证）
    const positionName = await PositionName.findOne(
      addEnterpriseFilter({
        where: { id: positionId }
      })
    );
    if (!positionName) {
      return res.status(400).json({
        code: 400,
        message: `岗位ID ${positionId} 不存在`
      });
    }

    // 验证岗位是否属于指定的岗位类型
    if (positionName.typeId !== positionTypeId) {
      return res.status(400).json({
        code: 400,
        message: `岗位ID ${positionId} 不属于岗位类型ID ${positionTypeId}`
      });
    }

    // 验证等级是否存在
    const level = await Level.findOne(
      addEnterpriseFilter({
        where: { id: levelId }
      })
    );
    if (!level) {
      return res.status(400).json({
        code: 400,
        message: `等级ID ${levelId} 不存在`
      });
    }

    // 检查是否已存在相同的岗位配置
    const existingPosition = await EmployeePosition.findOne(
      addEnterpriseFilter({
        where: {
          employeeId,
          positionId,
          positionTypeId,
          levelId
        }
      })
    );

    if (existingPosition) {
      return res.status(400).json({
        code: 400,
        message: '该岗位配置已存在'
      });
    }

    // 如果设置为默认岗位，需要取消其他默认岗位
    if (isDefault) {
      await EmployeePosition.update(
        { isDefault: false },
        {
          where: addEnterpriseFilter({
            employeeId,
            isDefault: true
          }).where
        }
      );
    }

    // 创建新的岗位配置
    const newPosition = await EmployeePosition.create(
      addEnterpriseId({
        employeeId,
        positionId,
        positionTypeId,
        levelId,
        isDefault: isDefault || false,
        createBy: req.user ? req.user.username : 'system',
        updateBy: req.user ? req.user.username : 'system'
      })
    );

    res.json({
      code: 200,
      data: newPosition,
      message: '添加员工岗位配置成功'
    });
  } catch (error) {
    console.error('添加员工岗位配置失败：', error);
    res.status(500).json({
      code: 500,
      message: '添加员工岗位配置失败',
      error: error.message
    });
  }
};

/**
 * 删除员工岗位配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deleteEmployeePosition = async (req, res) => {
  try {
    const { id } = req.params;

    // 查找岗位配置
    const employeePosition = await EmployeePosition.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!employeePosition) {
      return res.status(404).json({
        code: 404,
        message: '岗位配置不存在'
      });
    }

    // 检查是否为默认岗位
    if (employeePosition.isDefault) {
      // 检查该员工是否还有其他岗位
      const otherPositions = await EmployeePosition.count(
        addEnterpriseFilter({
          where: {
            employeeId: employeePosition.employeeId,
            id: { [Op.ne]: id }
          }
        })
      );

      if (otherPositions === 0) {
        return res.status(400).json({
          code: 400,
          message: '不能删除唯一的岗位配置'
        });
      }

      // 如果删除的是默认岗位，需要设置另一个岗位为默认
      const firstOtherPosition = await EmployeePosition.findOne(
        addEnterpriseFilter({
          where: {
            employeeId: employeePosition.employeeId,
            id: { [Op.ne]: id }
          },
          order: [['createTime', 'ASC']]
        })
      );

      if (firstOtherPosition) {
        await firstOtherPosition.update({ isDefault: true });
      }
    }

    // 删除岗位配置
    await employeePosition.destroy();

    res.json({
      code: 200,
      message: '删除员工岗位配置成功'
    });
  } catch (error) {
    console.error('删除员工岗位配置失败：', error);
    res.status(500).json({
      code: 500,
      message: '删除员工岗位配置失败',
      error: error.message
    });
  }
}; 