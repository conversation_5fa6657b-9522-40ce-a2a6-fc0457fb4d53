const Position = require('../../models/Position');
const Level = require('../../models/Level');
const { DictionaryData } = require('../../models/dictionary');
const { Op } = require('sequelize');
const sequelize = require('../../config/database');
const { addEnterpriseFilter, addEnterpriseId } = require('../../utils/enterpriseFilter');
const PositionType = require('../../models/PositionType');
const PositionName = require('../../models/PositionName');

/**
 * 获取岗位列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionList = async (req, res) => {
  try {
    const { name, typeId, levelId, status, pageNum = 1, pageSize = 10 } = req.query;
    const where = {};

    // 岗位名称搜索改为使用关联表
    const include = [
      {
        model: Level,
        as: 'level',
        attributes: ['id', 'name'],
        required: false
      },
      {
        model: PositionType,
        as: 'positionType',
        attributes: ['id', 'name', 'code'],
        required: false
      },
      {
        model: PositionName,
        as: 'positionName',
        attributes: ['id', 'name', 'code', 'typeId'],
        required: false
      }
    ];

    // 如果按名称搜索，添加关联条件
    if (name) {
      include.find(item => item.as === 'positionName').where = {
        name: { [Op.like]: `%${name}%` }
      };
      include.find(item => item.as === 'positionName').required = true;
    }

    if (typeId) {
      where.typeId = typeId;
    }
    if (levelId) {
      where.levelId = levelId;
    }
    if (status !== undefined) {
      where.status = status === 'true' || status === true;
    }

    console.log('查询条件:', JSON.stringify(where));

    // 强制转换企业ID为整数类型，确保过滤有效
    const queryOptions = addEnterpriseFilter({
        where,
      offset: (pageNum - 1) * pageSize,
      limit: parseInt(pageSize),
        order: [
          ['id', 'ASC']
        ],
      include
    });

    console.log('完整查询条件:', JSON.stringify(queryOptions));

    const { count, rows } = await Position.findAndCountAll(queryOptions);

    // 检查关联数据是否正确
    console.log('查询结果样例:', rows.length > 0 ? JSON.stringify({
      id: rows[0].id,
      typeId: rows[0].typeId,
      nameId: rows[0].nameId,
      positionType: rows[0].positionType ? { id: rows[0].positionType.id, name: rows[0].positionType.name } : null,
      positionName: rows[0].positionName ? { id: rows[0].positionName.id, name: rows[0].positionName.name } : null
    }) : '无数据');

    res.json({
      code: 200,
      data: {
        total: count,
        rows,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      },
      message: '获取岗位列表成功'
    });
  } catch (error) {
    console.error('获取岗位列表失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位列表失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位名称选项（从PositionName表获取）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionNameOptions = async (req, res) => {
  try {
    const { typeId } = req.query;
    let where = { status: true };

    // 如果提供了typeId，则按类型ID筛选
    if (typeId) {
      where.typeId = typeId;
    }

    // 从PositionName表获取岗位名称数据
    const positionNames = await PositionName.findAll(
      addEnterpriseFilter({
        where,
        attributes: ['id', 'name', 'code', 'typeId'],
        order: [
          ['sort', 'ASC'],
          ['id', 'ASC']
        ],
        include: [
          {
            model: PositionType,
            as: 'type',
            attributes: ['id', 'name', 'code'],
            required: false
          }
        ]
      })
    );

    res.json({
      code: 200,
      data: {
        rows: positionNames,
        total: positionNames.length
      },
      message: '获取岗位名称选项成功'
    });
  } catch (error) {
    console.error('获取岗位名称选项失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位名称选项失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位类型选项（从PositionType表获取）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionTypeOptions = async (req, res) => {
  try {
    // 从PositionType表获取岗位类型数据
    const positionTypes = await PositionType.findAll(
      addEnterpriseFilter({
        where: { status: true },
        attributes: ['id', 'name', 'code', 'sort', 'remark'],
        order: [
          ['sort', 'ASC'],
          ['id', 'ASC']
        ]
      })
    );

    res.json({
      code: 200,
      data: positionTypes,
      message: '获取岗位类型选项成功'
    });
  } catch (error) {
    console.error('获取岗位类型选项失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位类型选项失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位选项（下拉列表用）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionOptions = async (req, res) => {
  try {
    // 查询启用状态的岗位，添加企业ID过滤
    const positions = await Position.findAll(
      addEnterpriseFilter({
        where: {
          status: true
        },
        attributes: ['id', 'typeId', 'nameId', 'levelId'],
        order: [
          ['id', 'ASC']
        ],
        include: [
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name'],
            required: false
          }
        ]
      })
    );

    // 转换数据结构，添加显示字段
    const formattedPositions = positions.map(position => {
      const data = position.toJSON();
      return {
        ...data,
        // 添加展示名称字段，优先使用关联表
        positionTypeName: data.positionType ? data.positionType.name : '',
        positionNameValue: data.positionName ? data.positionName.name : '',
        // 构建完整岗位名称 (类型-名称-等级)
        fullName: `${data.positionType ? data.positionType.name : ''}-${data.positionName ? data.positionName.name : ''}-${data.level ? data.level.name : ''}`
      };
    });

    res.json({
      code: 200,
      data: formattedPositions,
      message: '获取岗位选项成功'
    });
  } catch (error) {
    console.error('获取岗位选项失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位选项失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionDetail = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询岗位详情，添加企业ID过滤
    const position = await Position.findOne(
      addEnterpriseFilter({
        where: { id },
        include: [
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name', 'code', 'typeId'],
            required: false
          }
        ]
      })
    );

    if (!position) {
      return res.status(404).json({
        code: 404,
        message: '岗位不存在'
      });
    }

    // 转换数据并添加额外显示字段
    const positionData = position.toJSON();
    positionData.positionTypeName = positionData.positionType ? positionData.positionType.name : '';
    positionData.positionNameValue = positionData.positionName ? positionData.positionName.name : '';
    positionData.fullName = `${positionData.positionTypeName}-${positionData.positionNameValue}-${positionData.level ? positionData.level.name : ''}`;

    res.json({
      code: 200,
      data: positionData,
      message: '获取岗位详情成功'
    });
  } catch (error) {
    console.error('获取岗位详情失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位详情失败',
      error: error.message
    });
  }
};

/**
 * 新增岗位
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.addPosition = async (req, res) => {
  try {
    const { code, typeId, nameId, levelId, status, remark } = req.body;

    // 校验必填字段
    if (!nameId || !typeId || !levelId) {
      return res.status(400).json({
        code: 400,
        message: '岗位名称、类型和等级不能为空'
      });
    }

    // 校验编码是否已存在
    if (code) {
    const existCode = await Position.findOne(
      addEnterpriseFilter({
          where: { code }
      })
    );

    if (existCode) {
      return res.status(400).json({
        code: 400,
        message: '岗位编码已存在'
      });
    }
    }

    // 校验是否已存在相同岗位类型、岗位名称和岗位等级的数据
    const existPosition = await Position.findOne(
      addEnterpriseFilter({
        where: {
          typeId,
          nameId,
          levelId
        }
      })
    );

    if (existPosition) {
      return res.status(400).json({
        code: 400,
        message: '已存在相同岗位类型、岗位名称和岗位等级的岗位'
      });
    }

    // 使用当前用户名作为创建者
    const createBy = req.user?.username || 'admin';

    // 创建岗位，添加企业ID
    const position = await Position.create(
      addEnterpriseId({
        code,
        typeId,
        nameId,
        levelId,
        status: status === undefined ? true : status,
        remark,
        createBy
      })
    );

    res.json({
      code: 200,
      data: position,
      message: '新增岗位成功'
    });
  } catch (error) {
    console.error('已存在相同岗位类型、岗位名称和岗位等级的岗位：', error);
    res.status(500).json({
      code: 500,
      message: '已存在相同岗位类型、岗位名称和岗位等级的岗位',
      error: error.message
    });
  }
};

/**
 * 更新岗位
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updatePosition = async (req, res) => {
  try {
    const { id, code, typeId, nameId, levelId, status, remark } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '岗位ID不能为空'
      });
    }

    // 查询岗位是否存在
    const position = await Position.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!position) {
      return res.status(404).json({
        code: 404,
        message: '岗位不存在'
      });
    }

    // 校验编码是否已存在
    if (code && code !== position.code) {
      const existCode = await Position.findOne(
        addEnterpriseFilter({
          where: {
            code,
            id: { [Op.ne]: id }
          }
        })
      );

      if (existCode) {
        return res.status(400).json({
          code: 400,
          message: '岗位编码已存在'
        });
      }
    }

    // 校验是否已存在相同岗位类型、岗位名称和岗位等级的数据（排除当前记录）
    if (typeId && nameId && levelId) {
      const existPosition = await Position.findOne(
        addEnterpriseFilter({
          where: {
            typeId,
            nameId,
            levelId,
            id: { [Op.ne]: id }
          }
        })
      );

      if (existPosition) {
        return res.status(400).json({
          code: 400,
          message: '已存在相同岗位类型、岗位名称和岗位等级的岗位'
        });
      }
    }

    // 使用当前用户名作为更新者
    const updateBy = req.user?.username || 'admin';

    // 更新岗位信息
    await position.update({
      code,
      typeId,
      nameId,
      levelId,
      status,
      remark,
      updateBy
    });

    res.json({
      code: 200,
      data: position,
      message: '更新岗位成功'
    });
  } catch (error) {
    console.error('更新岗位失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新岗位失败',
      error: error.message
    });
  }
};

/**
 * 删除岗位
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deletePosition = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询岗位是否存在，添加企业ID过滤
    const position = await Position.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!position) {
      return res.status(404).json({
        code: 404,
        message: '岗位不存在'
      });
    }

    // 查询岗位下是否有员工，添加企业ID过滤
    const Employee = require('../../models/Employee');
    const employeeCount = await Employee.count(
      addEnterpriseFilter({
        where: {
          positionId: id
        }
      })
    );

    if (employeeCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '岗位下存在员工，无法删除'
      });
    }

    // 删除岗位
    await position.destroy();

    res.json({
      code: 200,
      message: '删除岗位成功'
    });
  } catch (error) {
    console.error('删除岗位失败：', error);
    res.status(500).json({
      code: 500,
      message: '删除岗位失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位结构树
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionStructureTree = async (req, res) => {
  try {
    // 查询所有岗位，并关联岗位类别和岗位等级
    const positions = await Position.findAll(
      addEnterpriseFilter({
        where: {
          status: true
        },
        include: [
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name', 'code', 'orderNum'],
            required: false
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name', 'code', 'sort'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name', 'code', 'typeId', 'sort'],
            required: false
          }
        ],
        order: [
          [{ model: PositionType, as: 'positionType' }, 'sort', 'ASC'],
          [{ model: PositionName, as: 'positionName' }, 'sort', 'ASC'],
          [{ model: Level, as: 'level' }, 'orderNum', 'ASC']
        ]
      })
    );
    // 构建树结构
    const tree = buildPositionStructureTree(positions);

    res.json({
      code: 200,
      data: tree,
      message: '获取岗位结构树成功'
    });
  } catch (error) {
    console.error('获取岗位结构树失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位结构树失败',
      error: error.message
    });
  }
};

/**
 * 构建岗位结构树
 * @param {Array} positions 岗位列表
 * @returns {Array} 树结构
 */
const buildPositionStructureTree = (positions) => {
  // 初始化树，包含"所有配置"节点
  const tree = [
    {
      title: '所有配置',
      key: 'all',
      type: 'all'
    }
  ];

  // 按岗位类型（第一级）分组
  const typeMap = {};

  positions.forEach(position => {
    const positionData = position.toJSON();
    const typeLabel = positionData.positionType ? positionData.positionType.name : '未分类';
    const typeId = positionData.typeId || 0;
    const typeKey = typeId;

    // 创建类型节点（如果不存在）
    if (!typeMap[typeKey]) {
      typeMap[typeKey] = {
        title: typeLabel,
        key: typeKey,
        type: 'category',
        typeId: typeId,
        children: {}
      };
    }

    // 岗位名称（第二级）- 使用positionName.name
    const positionNameLabel = positionData.positionName ? positionData.positionName.name : '未知岗位';
    const positionNameKey = positionData.nameId || 0;

    if (!typeMap[typeKey].children[positionNameKey]) {
      typeMap[typeKey].children[positionNameKey] = {
        title: positionNameLabel,
        key: typeId+'-'+positionNameKey,
        type: 'position',
        positionName: positionNameLabel,
        positionId: positionNameKey,
        typeId: typeId,
        children: []
      };
    }

    // 岗位等级（第三级）
    if (positionData.level) {
      const levelKey = `${positionData.level.id}`;

      // 检查是否已经添加了相同等级
      const existingLevel = typeMap[typeKey].children[positionNameKey].children.find(
        level => level.levelId === positionData.level.id
      );

      if (!existingLevel) {
        typeMap[typeKey].children[positionNameKey].children.push({
          title: positionData.level.name,
          key: typeId+'-'+positionNameKey+'-'+levelKey,
          type: 'level',
          levelId: positionData.level.id,
          positionId: positionNameKey,
          positionName: positionNameLabel,
          typeId: typeId,
          isLeaf: true
        });
      }
    }
  });

  // 转换为树结构
  Object.values(typeMap).forEach(typeNode => {
    // 转换岗位对象为数组
    typeNode.children = Object.values(typeNode.children).map(posNode => {
      // 如果没有岗位等级，标记为叶节点
      if (posNode.children.length === 0) {
        posNode.isLeaf = true;
      }
      return posNode;
    });

    // 添加到主树
    tree.push(typeNode);
  });

  return tree;
};

/**
 * 根据岗位ID获取对应的等级选项
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getLevelsByPosition = async (req, res) => {
  try {
    const { positionId } = req.params;

    if (!positionId) {
      return res.status(400).json({
        code: 400,
        message: '岗位ID不能为空'
      });
    }

    // 根据岗位ID查询Position表，获取对应的levelId，然后查询Level表
    const positions = await Position.findAll(
      addEnterpriseFilter({
        where: {
          nameId: positionId,  // 这里的positionId实际上是PositionName的ID
          status: true
        },
        attributes: ['id', 'levelId'],
        include: [
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name', 'orderNum'],
            required: true
          }
        ]
      })
    );

    // 提取唯一的等级信息
    const levelMap = new Map();
    positions.forEach(position => {
      if (position.level) {
        levelMap.set(position.level.id, {
          id: position.level.id,
          name: position.level.name,
          orderNum: position.level.orderNum
        });
      }
    });

    // 转换为数组并排序
    const levels = Array.from(levelMap.values()).sort((a, b) => {
      // 按orderNum降序排序，如果orderNum相同则按id升序
      if (a.orderNum !== b.orderNum) {
        return b.orderNum - a.orderNum;
      }
      return a.id - b.id;
    });

    // 转换为前端需要的格式
    const levelOptions = levels.map(level => ({
      value: level.id,
      label: level.name
    }));

    res.json({
      code: 200,
      data: levelOptions,
      message: '获取岗位等级选项成功'
    });
  } catch (error) {
    console.error('获取岗位等级选项失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位等级选项失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位信息，按岗位名称分组并包含对应等级
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionWithLevels = async (req, res) => {
  try {
    // 查询所有启用的岗位，包含关联信息
    const positions = await Position.findAll(
      addEnterpriseFilter({
        where: {
          status: true
        },
        attributes: ['id', 'typeId', 'nameId', 'levelId'],
        include: [
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name', 'orderNum'],
            required: false
          }
        ],
        order: [
          [{ model: PositionName, as: 'positionName' }, 'id', 'ASC'],
          [{ model: Level, as: 'level' }, 'orderNum', 'DESC']
        ]
      })
    );

    // 按岗位名称分组处理数据
    const positionMap = new Map();

    positions.forEach(position => {
      const positionData = position.toJSON();

      // 获取岗位名称ID作为分组键
      const positionNameId = positionData.nameId;
      const positionName = positionData.positionName ? positionData.positionName.name : '未知岗位';
      const positionTypeId = positionData.typeId;
      const positionTypeName = positionData.positionType ? positionData.positionType.name : '未知类型';

      // 如果该岗位名称还未添加到Map中
      if (!positionMap.has(positionNameId)) {
        positionMap.set(positionNameId, {
          positionName: positionName,
          positionId: positionNameId,
          positionTypeId: positionTypeId,
          positionTypeName: positionTypeName,
          levels: []
        });
      }

      // 添加等级信息（避免重复）
      const currentPosition = positionMap.get(positionNameId);
      if (positionData.level) {
        const existingLevel = currentPosition.levels.find(
          level => level.levelId === positionData.level.id
        );

        if (!existingLevel) {
          currentPosition.levels.push({
            levelId: positionData.level.id,
            levelName: positionData.level.name
          });
        }
      }
    });

    // 转换Map为数组
    const result = Array.from(positionMap.values());

    // 对每个岗位的等级进行排序（按等级orderNum降序）
    result.forEach(position => {
      position.levels.sort((a, b) => {
        // 需要重新查询orderNum来排序，这里先按levelId排序
        return a.orderNum - b.orderNum;
      });
    });

    res.json({
      code: 200,
      data: result,
      message: '获取岗位信息成功'
    });
  } catch (error) {
    console.error('获取岗位信息失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位信息失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位信息，按岗位类型分组，包含岗位名称和对应等级
 * 返回结构：positionType数组 -> positionName数组 -> level数组
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionTypesWithNamesAndLevels = async (req, res) => {
  try {
    // 查询所有启用的岗位，包含关联信息
    const positions = await Position.findAll(
      addEnterpriseFilter({
        where: {
          status: true
        },
        attributes: ['id', 'typeId', 'nameId', 'levelId'],
        include: [
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name', 'code', 'orderNum'],
            required: false
          }
        ],
        order: [
          [{ model: PositionType, as: 'positionType' }, 'id', 'ASC'],
          [{ model: PositionName, as: 'positionName' }, 'id', 'ASC'],
          [{ model: Level, as: 'level' }, 'orderNum', 'ASC']
        ]
      })
    );

    // 按岗位类型分组处理数据
    const positionTypeMap = new Map();

    positions.forEach(position => {
      const positionData = position.toJSON();

      // 获取岗位类型信息
      const typeId = positionData.typeId;
      const typeName = positionData.positionType ? positionData.positionType.name : '未知类型';
      const typeCode = positionData.positionType ? positionData.positionType.code : '';

      // 获取岗位名称信息
      const nameId = positionData.nameId;
      const positionName = positionData.positionName ? positionData.positionName.name : '未知岗位';
      const positionCode = positionData.positionName ? positionData.positionName.code : '';

      // 获取等级信息
      const levelData = positionData.level;

      // 如果该岗位类型还未添加到Map中
      if (!positionTypeMap.has(typeId)) {
        positionTypeMap.set(typeId, {
          id: typeId,
          name: typeName,
          code: typeCode,
          positionNames: new Map()
        });
      }

      const currentType = positionTypeMap.get(typeId);

      // 如果该岗位名称还未添加到该类型中
      if (!currentType.positionNames.has(nameId)) {
        currentType.positionNames.set(nameId, {
          id: nameId,
          name: positionName,
          code: positionCode,
          levels: []
        });
      }

      const currentPosition = currentType.positionNames.get(nameId);

      // 添加等级信息（避免重复）
      if (levelData) {
        const existingLevel = currentPosition.levels.find(
          level => level.id === levelData.id
        );

        if (!existingLevel) {
          currentPosition.levels.push({
            id: levelData.id,
            name: levelData.name,
            code: levelData.code,
            orderNum: levelData.orderNum
          });
        }
      }
    });

    // 转换Map为数组并进行排序
    const result = Array.from(positionTypeMap.values()).map(positionType => {
      // 转换positionNames Map为数组
      const positionNames = Array.from(positionType.positionNames.values()).map(positionName => {
        // 对每个岗位的等级进行排序（按等级orderNum升序）
        positionName.levels.sort((a, b) => a.orderNum - b.orderNum);
        return positionName;
      });

      return {
        id: positionType.id,
        name: positionType.name,
        code: positionType.code,
        positionNames: positionNames
      };
    });

    // 对岗位类型进行排序（按ID升序）
    result.sort((a, b) => a.id - b.id);

    res.json({
      code: 200,
      data: result,
      message: '获取岗位类型及相关信息成功'
    });
  } catch (error) {
    console.error('获取岗位类型信息失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位类型信息失败',
      error: error.message
    });
  }
};
