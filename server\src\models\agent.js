const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Agent = sequelize.define('agents', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '智能体名称'
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '智能体编码'
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '类型'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '描述'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'enterprise_id',
    comment: '所属企业ID'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态：0禁用，1正常'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'agents'
});

module.exports = Agent; 