const { setupAchievementEventListeners, emitAchievementEvent, ACHIEVEMENT_EVENTS } = require('./server/src/utils/achievementEventListener');
const { processPracticeProgressAchievements } = require('./server/src/utils/achievementUtils');

/**
 * 快速测试初入宝殿成就
 */
async function quickTestFirstEntry() {
  console.log('🚀 快速测试初入宝殿成就...\n');
  
  try {
    // 1. 启动事件监听器
    console.log('1️⃣ 启动成就事件监听器...');
    setupAchievementEventListeners();
    await delay(1000);
    
    // 2. 使用实际的用户数据进行测试（请替换为你的实际数据）
    const testData = {
      userId: 1,  // 替换为实际用户ID
      openId: 'your_actual_openid',  // 替换为实际openId
      enterpriseId: '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32',
      subjectId: '1',  // 替换为实际科目ID
      positionName: '1',  // 替换为实际岗位
      positionLevel: '1'   // 替换为实际等级
    };
    
    console.log('2️⃣ 使用测试数据:', testData);
    
    // 3. 直接调用首次学习检测函数
    console.log('3️⃣ 直接调用首次学习检测函数...');
    await processPracticeProgressAchievements(
      testData.userId,
      testData.openId,
      testData.enterpriseId,
      testData.subjectId,
      testData.positionName,
      testData.positionLevel
    );
    
    // 4. 也可以直接发射事件
    console.log('4️⃣ 直接发射FIRST_COMPLETE事件...');
    emitAchievementEvent(ACHIEVEMENT_EVENTS.FIRST_COMPLETE, {
      ...testData,
      subjectName: '测试科目',
      progress: 5, // 模拟5%的进度，超过1%的要求
      isFirstTime: true
    });
    
    // 等待处理完成
    await delay(3000);
    console.log('✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
  
  setTimeout(() => {
    console.log('测试结束，退出程序...');
    process.exit(0);
  }, 2000);
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 执行测试
quickTestFirstEntry().catch(console.error);

console.log(`
📝 使用说明：
1. 请替换 testData 中的数据为你的实际数据
2. 确保数据库中有对应的成就模板
3. 确保用户有相应的练习记录
4. 运行命令: node 快速测试初入宝殿.js
`); 