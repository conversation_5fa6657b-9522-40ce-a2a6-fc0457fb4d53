const express = require('express');
const router = express.Router();
const agentController = require('../controllers/agentController');

// 获取智能体列表
router.get('/list', agentController.getAgentList);

// 获取智能体详情
router.get('/detail/:id', agentController.getAgentDetail);

// 创建智能体
router.post('/', agentController.createAgent);

// 更新智能体
router.put('/', agentController.updateAgent);

// 删除智能体
router.delete('/:id', agentController.deleteAgent);

module.exports = router; 