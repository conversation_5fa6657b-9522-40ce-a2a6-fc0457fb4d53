const InfoConfig = require('../models/InfoConfig');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');
const { getFileType } = require('../utils/fileUpload');


// 获取当前企业的信息配置
exports.getInfoConfig = async (req, res) => {
  try {
    // 使用addEnterpriseFilter添加企业ID过滤
    const infoConfig = await InfoConfig.findOne(
      addEnterpriseFilter({
        where: { }
      })
    );

    // 如果没有找到企业特定配置，则返回默认配置
    if (!infoConfig) {
      const defaultConfig = await InfoConfig.findOne({
        where: { isDefault: 1 }
      });

      // 如果默认配置也不存在，返回404
      if (!defaultConfig) {
        return res.status(200).json({
          code: 200,
          message: '未找到信息配置'
        });
      }

      return res.status(200).json({
        code: 200,
        message: '获取信息配置成功',
        data: defaultConfig
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取信息配置成功',
      data: infoConfig
    });

  } catch (error) {
    console.error('获取信息配置出错:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 更新企业的信息配置
exports.updateInfoConfig = async (req, res) => {
  try {
    const configData = req.body;

    // 验证必填字段
    const requiredFields = [
      'mainTitle', 'subTitle', 'enterpriseLogo'
    ];

    for (const field of requiredFields) {
      if (!configData[field]) {
        return res.status(400).json({
          code: 400,
          message: `${field} 字段不能为空`
        });
      }
    }

    // 验证字段长度
    const fieldLengthLimits = {
      mainTitle: 30,
      subTitle: 50,
      frontDeskName: 10,
      kitchenName: 10
    };

    for (const [field, maxLength] of Object.entries(fieldLengthLimits)) {
      if (configData[field] && configData[field].length > maxLength) {
        // 自动截断超长字段，保留最大长度
        configData[field] = configData[field].substring(0, maxLength);
      }
    }

    // 查找是否已存在该企业的配置
    const infoConfig = await InfoConfig.findOne(
      addEnterpriseFilter({
        where: { }
      })
    );

    // 设置更新参数
    const updateParams = {
      mainTitle: configData.mainTitle,
      subTitle: configData.subTitle,
      logo: configData.logo,
      enterpriseLogo: configData.enterpriseLogo,
      updateTime: new Date()
    };

    let result;

    if (infoConfig) {
      // 更新现有配置
      result = await InfoConfig.update(
        updateParams,
        addEnterpriseFilter({
          where: { }
        })
      );

      // 获取更新后的数据
      const updatedConfig = await InfoConfig.findOne(
        addEnterpriseFilter({
          where: { }
        })
      );

      return res.status(200).json({
        code: 200,
        message: '更新信息配置成功',
        data: updatedConfig
      });

    } else {
      // 创建新配置，添加企业ID
      const newConfig = await InfoConfig.create(
        addEnterpriseId({
          ...updateParams,
          createTime: new Date(),
          isDefault: 0 // 非默认配置
        })
      );

      if (!newConfig) {
        return res.status(400).json({
          code: 400,
          message: '创建信息配置失败'
        });
      }

      return res.status(200).json({
        code: 200,
        message: '创建信息配置成功',
        data: newConfig
      });
    }

  } catch (error) {
    console.error('更新信息配置出错:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 上传信息配置图片
 * 用于上传Logo、前厅头像、后厨头像等图片
 */
exports.uploadConfigImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传的文件'
      });
    }

    const { originalname, filename, path: filePath, size } = req.file;

    // 确保文件名是正确解码的
    let decodedFileName = originalname;
    if (typeof decodedFileName === 'string') {
      try {
        // 尝试检测是否需要转换编码
        const testDecode = decodeURIComponent(escape(decodedFileName));
        // 如果解码成功但与原字符串不同，说明需要转换
        if (testDecode !== decodedFileName && /[\u4e00-\u9fa5]/.test(testDecode)) {
          decodedFileName = testDecode;
        }
      } catch (e) {
        // 如果解码失败，尝试直接从latin1转换
        decodedFileName = Buffer.from(decodedFileName, 'latin1').toString('utf8');
      }
    }

    // 获取文件类型
    const fileType = getFileType(decodedFileName);


    // 构建文件访问路径，添加时间戳
    const fileUrl = `/uploads/info-config/${filename}`;

    res.json({
      code: 200,
      message: '图片上传成功',
      data: {
        fileName: decodedFileName,
        fileType,
        fileSize: size,
        fileUrl
      }
    });
  } catch (error) {
    console.error('图片上传失败:', error);
    res.status(500).json({
      code: 500,
      message: '图片上传失败',
      error: error.message
    });
  }
};
