# 员工多岗位配置需求分析

## 需求概述

### 功能描述
实现员工多岗位配置功能，支持一个员工配置多个岗位，每个岗位有对应的等级，并设置一个默认岗位用于小程序端展示。

### 业务场景
- **管理端**：在"组织架构"菜单中新增员工时可选择一个或多个岗位并配置岗位等级，需设置一个默认岗位
- **小程序端**：
  - 练习列表：展示默认岗位及当前等级，支持切换晋升岗位和其他岗位
  - 考试列表：展示默认岗位的晋升路径，支持切换其他岗位查看或参加晋升考试

## 技术实现方案

### 1. 数据库设计

#### 新建表：员工岗位关联表 (`org_employee_position`)
```sql
CREATE TABLE `org_employee_position` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `employee_id` int NOT NULL COMMENT '员工ID',
  `position_id` int NOT NULL COMMENT '岗位ID',
  `level_id` int NOT NULL COMMENT '岗位等级ID',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认岗位(1是 0否)',
  `enterprise_id` varchar(255) NOT NULL COMMENT '企业ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_position_level` (`employee_id`, `position_id`, `level_id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_position_id` (`position_id`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_is_default` (`is_default`),
  CONSTRAINT `fk_emp_pos_employee` FOREIGN KEY (`employee_id`) REFERENCES `org_employee` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_emp_pos_position` FOREIGN KEY (`position_id`) REFERENCES `org_position` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_emp_pos_level` FOREIGN KEY (`level_id`) REFERENCES `org_level` (`id`) ON DELETE CASCADE
) COMMENT='员工岗位关联表';
```

#### 数据迁移
- 将现有员工表中的单岗位数据迁移到新的关联表
- 设置现有岗位为默认岗位

### 2. 后端实现

#### 新增文件
1. **模型文件**：`server/src/models/EmployeePosition.js`
   - 定义员工岗位关联模型
   - 建立与员工、岗位、等级的关联关系

2. **控制器文件**：`server/src/controllers/organization/employeePositionController.js`
   - `getEmployeePositions` - 获取员工的岗位配置列表
   - `updateEmployeePositions` - 更新员工的岗位配置
   - `addEmployeePosition` - 添加员工岗位配置
   - `deleteEmployeePosition` - 删除员工岗位配置

#### 修改文件
1. **员工模型**：`server/src/models/Employee.js`
   - 添加与EmployeePosition的关联关系
   - 设置延迟加载避免循环依赖

2. **员工控制器**：`server/src/controllers/organization/employeeController.js`
   - 修改`addEmployee`方法支持多岗位配置
   - 保持向后兼容，支持传统单岗位配置

3. **路由文件**：`server/src/routes/organization.js`
   - 添加员工岗位配置相关路由

#### 新增接口
```javascript
// 获取员工岗位配置
GET /api/organization/employee/:employeeId/positions

// 更新员工岗位配置
PUT /api/organization/employee/:employeeId/positions

// 添加员工岗位配置
POST /api/organization/employee/position

// 删除员工岗位配置
DELETE /api/organization/employee/position/:id
```

### 3. 前端实现

#### 修改文件
1. **组织架构页面**：`web/src/views/organization/structure/index.vue`
   - 将单岗位配置改为多岗位配置界面
   - 支持添加/删除岗位配置
   - 支持设置默认岗位
   - 添加表单验证逻辑

#### 界面设计
- 岗位配置区域采用卡片式布局
- 每个岗位配置包含：岗位选择、等级选择、默认岗位标识、删除按钮
- 支持动态添加和删除岗位配置
- 确保至少有一个岗位配置且有一个默认岗位

### 4. 业务逻辑

#### 数据验证规则
1. **员工必须至少有一个岗位配置**
2. **必须设置一个且仅有一个默认岗位**
3. **同一员工不能有重复的岗位-等级组合**
4. **删除默认岗位时自动设置其他岗位为默认**

#### 事务处理
- 员工创建和岗位配置创建使用事务确保数据一致性
- 岗位配置更新时先删除再创建，保证数据准确性

## 影响范围分析

### 管理端
- ✅ **组织架构页面** - 员工表单支持多岗位配置
- ✅ **员工管理接口** - 支持多岗位数据处理
- ✅ **数据库结构** - 新增员工岗位关联表

### 小程序端（后续实现）
- 🔄 **练习列表接口** - 支持根据岗位获取练习数据
- 🔄 **考试列表接口** - 支持根据岗位获取考试数据
- 🔄 **员工信息接口** - 返回员工的多岗位信息

### 报表统计（后续扩展）
- 🔄 **岗位人员统计** - 支持多岗位统计分析
- 🔄 **培训进度统计** - 按岗位统计培训完成情况
- 🔄 **考试通过率统计** - 按岗位统计考试通过情况

## 实施计划

### 第一阶段：管理端改造 ✅
1. ✅ 创建数据库表和模型
2. ✅ 实现后端接口
3. ✅ 修改前端页面
4. ✅ 数据迁移和测试

### 第二阶段：小程序端改造 🔄
1. 修改练习列表接口和页面
2. 修改考试列表接口和页面
3. 添加岗位切换功能
4. 测试多岗位场景

### 第三阶段：功能完善 🔄
1. 添加岗位配置的批量操作
2. 完善权限控制
3. 添加操作日志
4. 性能优化

## 测试用例

### 管理端测试
1. **新增员工**
   - 配置单个岗位
   - 配置多个岗位
   - 验证默认岗位设置
   - 验证表单验证规则

2. **编辑员工**
   - 修改岗位配置
   - 添加新岗位
   - 删除岗位
   - 更改默认岗位

3. **数据验证**
   - 至少一个岗位配置
   - 唯一默认岗位
   - 岗位-等级组合唯一性

### 数据一致性测试
1. **事务回滚测试**
2. **并发操作测试**
3. **数据迁移验证**

## 风险评估

### 技术风险
- **数据迁移风险**：现有数据迁移可能出现异常
- **性能风险**：多表关联查询可能影响性能
- **兼容性风险**：需要保持向后兼容

### 业务风险
- **用户体验**：界面复杂度增加可能影响操作便利性
- **数据准确性**：多岗位配置可能导致数据错误

### 缓解措施
1. **充分测试**：在测试环境进行全面测试
2. **分步实施**：分阶段上线，降低风险
3. **数据备份**：实施前进行完整数据备份
4. **回滚方案**：准备快速回滚机制

## 总结

员工多岗位配置功能的实现采用了关联表的设计方案，既保证了数据的规范性和扩展性，又维持了良好的查询性能。通过合理的事务处理和数据验证，确保了数据的一致性和准确性。

管理端的改造已经完成，为后续小程序端的功能扩展奠定了坚实的基础。整个方案具有良好的可维护性和扩展性，能够满足企业多样化的岗位管理需求。 