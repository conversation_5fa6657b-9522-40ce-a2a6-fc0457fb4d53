/**
 * 微信小程序考试控制器
 */
const { ExamConfig, ExamRecord,SystemSetting } = require('../../models');
const KBQuestions = require('../../models/kb-questions');
const redis = require('../../utils/redisClient');
const { v4: uuidv4 } = require('uuid');
const { Op, Sequelize } = require('sequelize');
const sequelize = require('../../config/database');
const axios = require('axios');
const dotenv = require('dotenv');
const moment = require('moment');
const { addEnterpriseFilter } = require('../../utils/enterpriseFilter');
// 添加需要的模型导入
const { User, Employee, Position, Level, KnowledgeBase, ExamReviewApplication,PositionName } = require('../../models');
// 如果需要logger，确保导入
const logger = console;

// 加载环境变量
dotenv.config();

// 题目解析API配置
const DIFY_URL = process.env.DIFY_URL;
const DEFAULT_ENTERPRISE_ID = process.env.DEFAULT_ENTERPRISE_ID;
const ANALYSIS_API = `${DIFY_URL}/api/agent/workflow/run/${DEFAULT_ENTERPRISE_ID}/AGENT-PARSE`;
const REPORT_API = `${DIFY_URL}/api/agent/workflow/run/${DEFAULT_ENTERPRISE_ID}/AGENT-REPORT`;

console.log('题目解析API地址:', ANALYSIS_API);

/**
 * 定时检查过期考试
 * 每5秒运行一次，将所有已到截止时间但状态未完成的考试更新为已完成，并生成考试报告
 */
const checkExpiredExams = async () => {
  try {
    const now = moment().format('YYYY-MM-DD HH:mm:ss');
    const enterpriseId = DEFAULT_ENTERPRISE_ID;

    // 记录开始检查的日志
    console.log(`开始检查企业(ID: ${enterpriseId})的过期考试...`);

    // 查找所有未完成但已过期的考试记录
    const expiredExams = await ExamRecord.findAll({
      where: {
        examStatus: 'ongoing',
        endTime: {
          [Op.lt]: now // 截止时间小于当前时间
        },
        enterpriseId: enterpriseId, // 添加企业ID条件
        delFlag: 0
      }
    });

    if (expiredExams.length > 0) {
      console.log(`发现企业(ID: ${enterpriseId})的 ${expiredExams.length} 条已过期但未完成的考试记录，正在更新状态...`);

      // 逐个处理考试记录，直接使用examDuration
      for (const exam of expiredExams) {
        // 更新考试状态为已完成，使用examDuration作为usedDuration
        await ExamRecord.update(
          {
            examStatus: 'completed',
            usedDuration: exam.examDuration // 时间到期意味着用完了所有考试时间
          },
          {
            where: {
              id: exam.id,
              enterpriseId: enterpriseId
            }
          }
        );

        console.log(`已更新考试记录(ID: ${exam.id})的状态为已完成，考试用时: ${exam.examDuration}分钟`);

        // 为过期考试生成报告，通过构造一个模拟的请求对象和响应对象调用getExamReport
        try {
          console.log(`开始为考试记录(ID: ${exam.id})生成报告...`);

          // 构造模拟请求对象
          const mockReq = {
            body: {
              exam_id: exam.id,
              isEarlySubmission: false // 这不是提前交卷，是时间到期
            }
          };

          // 构造模拟响应对象
          const mockRes = {
            status: function(statusCode) {
              console.log(`响应状态码: ${statusCode}`);
              return this;
            },
            json: function(data) {
              if (data.code === 200) {
                console.log(`成功为考试记录(ID: ${exam.id})生成报告，分数: ${data.data.score}`);
              } else {
                console.error(`为考试记录(ID: ${exam.id})生成报告失败: ${data.message}`);
              }
              return data;
            }
          };

          // 调用getExamReport方法
          await getExamReport(mockReq, mockRes);
        } catch (reportError) {
          console.error(`为考试记录(ID: ${exam.id})生成报告失败:`, reportError);
        }
      }

      console.log(`成功更新企业(ID: ${enterpriseId})的 ${expiredExams.length} 条考试记录的状态为已完成`);
    } else {
      console.log(`企业(ID: ${enterpriseId})没有需要更新的过期考试记录`);
    }
  } catch (error) {
    console.error('定时检查过期考试失败:', error);
  }
};

// 启动定时任务 - 每5秒运行一次
setInterval(checkExpiredExams, 5000);

// 初始运行一次检查过期考试
checkExpiredExams().catch(err => console.error('初始检查过期考试失败:', err));

/**
 * 格式化日期为 "yyyy-MM-dd HH:mm:ss" 格式
 * @param {Date} date - 日期对象
 * @returns {string} - 格式化后的日期字符串
 */
const formatDateTime = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 调用题目解析API获取解析
 * @param {string} question - 题目内容
 * @param {string} answer - 标准答案
 * @param {string} tip - 提示信息 (可选)
 * @returns {Promise<string>} - 解析内容
 */
const getQuestionAnalysis = async (question, answer, tip) => {
  try {
    const response = await axios.post(ANALYSIS_API, {
      inputs: {
        question,
        answer_yh: answer,
        tip
      }
    });
    console.log("response", response)
    // 检查API响应
    if (response.data && response.data.success && response.data.data && response.data.data.outputs) {
      return response.data.data.outputs;
    } else {
      console.error('解析API返回格式不符合预期:', response.data);
      return '解析服务异常，请稍后再试';
    }
  } catch (error) {
    console.error('调用解析API失败:', error.message);
    return '解析服务异常，请稍后再试';
  }
};

/**
 * 从题库中抽取题目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getRandomQuestion = async (req, res) => {
  try {
    // 从请求体中获取知识库ID和考试ID
    const { knowledgeBaseId, examId } = req.body;

    // 验证必要参数
    if (!knowledgeBaseId) {
      return res.status(400).json({
        code: 400,
        message: '缺少知识库ID参数',
        data: null
      });
    }

    if (!examId) {
      return res.status(400).json({
        code: 400,
        message: '缺少考试ID参数',
        data: null
      });
    }

    // 查找考试记录
    const examRecord = await ExamRecord.findOne({
      where: {
        id: examId
      }
    });

    if (!examRecord) {
      console.log(`未找到ID为${examId}的考试记录`);
      return res.status(404).json({
        code: 404,
        message: `未找到ID为${examId}的考试记录`,
        data: null
      });
    }

    // 检查考试是否已完成
    if(examRecord.examStatus === 'completed') {
      return res.status(400).json({
        code: 400,
        message: '考试已完成',
        data: {
          examFinished: true
        }
      });
    }

    // 获取练考配置信息
    let examConfigInfo = null;
    const configCacheKey = `exam_config:${examId}`;

    // 首先尝试从Redis缓存中获取
    try {
      const cachedConfig = await redis.get(configCacheKey);
      if (cachedConfig) {
        examConfigInfo = cachedConfig;
        console.log(`从缓存获取考试配置信息: ${examId}`);
      }
    } catch (cacheError) {
      console.log('从缓存获取配置信息失败:', cacheError.message);
    }

    // 如果缓存中没有，从数据库获取
    if (!examConfigInfo && examRecord.reviewApplicationId) {
      try {
        const reviewApplication = await ExamReviewApplication.findOne({
          where: {
            id: examRecord.reviewApplicationId
          },
          attributes: ['examConfigInfo']
        });
        console.log("reviewApplication", reviewApplication)

        if (reviewApplication && reviewApplication.examConfigInfo) {
          examConfigInfo = reviewApplication.examConfigInfo;
          console.log("examConfigInfo", examConfigInfo)

          // 将配置信息存储到Redis缓存中，设置1小时过期
          try {
            await redis.set(configCacheKey, examConfigInfo, 3600);
            console.log(`缓存考试配置信息: ${examId}`);
          } catch (cacheError) {
            console.log('缓存配置信息失败:', cacheError.message);
          }
        }
      } catch (dbError) {
        console.log('从数据库获取配置信息失败:', dbError.message);
      }
    }

    // 从配置信息中获取人工出题和智能出题占比，如果没有配置则使用默认值50
    let manualQuestionRatio = 50;
    let intelligentQuestionRatio = 50;

    if (examConfigInfo) {
      manualQuestionRatio = examConfigInfo.manualQuestionRatio ;
      intelligentQuestionRatio = examConfigInfo.intelligentQuestionRatio ;
    }

    console.log(`出题占比 - 人工出题: ${manualQuestionRatio}, 智能出题: ${intelligentQuestionRatio}`);

    // 获取此次考试已经抽过的题目ID和占比统计
    const usedQuestionsKey = `exam:${examId}:used_questions`;
    const manualCountKey = `exam:${examId}:manual_count`;
    const intelligentCountKey = `exam:${examId}:intelligent_count`;

    let usedQuestionIds = await redis.smembers(usedQuestionsKey);
    let manualCount = parseInt(await redis.get(manualCountKey) || '0');
    let intelligentCount = parseInt(await redis.get(intelligentCountKey) || '0');

    // 获取考试所需题目数量
    const questionNumber = examRecord.questionNumber || 0;
    const currentQuestionCount = (examRecord.examContent || []).length;

    // 计算当前应该出哪种类型的题目
    let questionType = null;
    const totalQuestionsUsed = manualCount + intelligentCount;

    // 根据占比决定出题类型
    console.log(`当前出题统计 - 人工: ${manualCount}, 智能: ${intelligentCount}, 总计: ${totalQuestionsUsed}`);
    console.log(`目标占比 - 人工: ${manualQuestionRatio}%, 智能: ${intelligentQuestionRatio}%`);

    // 检查占比配置的有效性
    if (manualQuestionRatio === 0 && intelligentQuestionRatio === 0) {
      console.log('警告: 人工出题和智能出题占比都为0，默认使用人工出题');
      questionType = '人工出题';
    } 
    // 如果只有人工出题启用（智能出题占比为0）
    else if (manualQuestionRatio > 0 && intelligentQuestionRatio === 0) {
      questionType = '人工出题';
      console.log('只启用人工出题，选择人工出题');
    } 
    // 如果只有智能出题启用（人工出题占比为0）
    else if (manualQuestionRatio === 0 && intelligentQuestionRatio > 0) {
      questionType = '智能出题';
      console.log('只启用智能出题，选择智能出题');
    }
    // 如果两种类型都启用，按比例出题
    else {
      // 计算目标题目数量（基于当前已出题目数+1）
      const nextQuestionIndex = totalQuestionsUsed + 1;
      
      // 计算到目前为止应该出的人工题目数量
      const targetManualCount = Math.round((nextQuestionIndex * manualQuestionRatio) / 100);
      
      // 如果当前人工题目数量少于目标数量，出人工题
      if (manualCount < targetManualCount) {
        questionType = '人工出题';
        console.log(`按比例出题：当前人工题${manualCount}，目标${targetManualCount}，选择人工出题`);
      } else {
        questionType = '智能出题';
        console.log(`按比例出题：当前人工题${manualCount}，目标${targetManualCount}，选择智能出题`);
      }
    }

    console.log(`决定出题类型: ${questionType} (当前统计 - 人工: ${manualCount}, 智能: ${intelligentCount})`);

    // 查询条件：属于指定知识库、指定类型且不在已使用题目ID列表中
    const where = {
      knowledge_base_id: knowledgeBaseId,
      type: questionType
    };

    // 如果已有使用过的题目，则排除这些题目
    if (usedQuestionIds && usedQuestionIds.length > 0) {
      where.id = {
        [Op.notIn]: usedQuestionIds
      };
    }

    // 随机获取一个题目 - 使用Sequelize.fn('RAND')
    let question = await KBQuestions.findOne({
      where,
      order: [[Sequelize.fn('RAND')]],
      attributes: ['id', 'question', 'answer', 'type']
    });

    // 如果指定类型没有可用的题目了，尝试获取另一种类型的题目
    if (!question) {
      console.log(`${questionType}类型题目已抽完，尝试获取另一种类型的题目`);

      const alternativeType = questionType === '人工出题' ? '智能出题' : '人工出题';
      
      // 检查备选类型是否启用（占比大于0）
      const alternativeEnabled = (alternativeType === '人工出题' && manualQuestionRatio > 0) ||
                                 (alternativeType === '智能出题' && intelligentQuestionRatio > 0);

      if (alternativeEnabled) {
        console.log(`尝试获取备选类型：${alternativeType}`);
        const alternativeWhere = {
          knowledge_base_id: knowledgeBaseId,
          type: alternativeType
        };

        if (usedQuestionIds && usedQuestionIds.length > 0) {
          alternativeWhere.id = {
            [Op.notIn]: usedQuestionIds
          };
        }

        question = await KBQuestions.findOne({
          where: alternativeWhere,
          order: [[Sequelize.fn('RAND')]],
          attributes: ['id', 'question', 'answer', 'type']
        });

        if (question) {
          console.log(`成功获取备选类型题目：${alternativeType}`);
          // 更新questionType为实际获取的题目类型
          questionType = alternativeType;
        }
      } else {
        console.log(`备选类型${alternativeType}的占比为0，不获取该类型题目`);
      }
    }

    // 如果两种类型都没有可用的题目了，重置已使用题目记录并再次抽取
    if (!question) {
      console.log('所有题目已抽完，重置已使用题目记录并重新抽取');

      // 删除Redis中的已使用题目记录和统计
      await redis.del(usedQuestionsKey);
      await redis.del(manualCountKey);
      await redis.del(intelligentCountKey);

      // 重新获取一个随机题目（只从占比大于0的类型中选择）
      const enabledTypes = [];
      if (manualQuestionRatio > 0) enabledTypes.push('人工出题');
      if (intelligentQuestionRatio > 0) enabledTypes.push('智能出题');

      console.log('重置题目统计，重新开始出题');
      console.log(`启用的题目类型：${enabledTypes.join(', ')}`);

      if (enabledTypes.length > 0) {
        // 重新计算应该出什么类型的题目（基于重置后的第一题）
        let newQuestionType;
        if (enabledTypes.length === 1) {
          // 只有一种类型启用
          newQuestionType = enabledTypes[0];
        } else {
          // 两种类型都启用，按比例决定第一题
          newQuestionType = manualQuestionRatio >= intelligentQuestionRatio ? '人工出题' : '智能出题';
        }

        console.log(`重置后第一题选择类型：${newQuestionType}`);

        question = await KBQuestions.findOne({
          where: {
            knowledge_base_id: knowledgeBaseId,
            type: newQuestionType
          },
          order: [[Sequelize.fn('RAND')]],
          attributes: ['id', 'question', 'answer', 'type']
        });

        // 如果指定类型没有题目，从其他启用类型中获取
        if (!question && enabledTypes.length > 1) {
          const otherTypes = enabledTypes.filter(type => type !== newQuestionType);
          question = await KBQuestions.findOne({
            where: {
              knowledge_base_id: knowledgeBaseId,
              type: {
                [Op.in]: otherTypes
              }
            },
            order: [[Sequelize.fn('RAND')]],
            attributes: ['id', 'question', 'answer', 'type']
          });
          
          if (question) {
            console.log(`从其他启用类型获取题目：${question.type}`);
          }
        }
      } else {
        // 如果所有类型占比都为0，获取任意类型作为兜底
        console.log('所有类型占比都为0，获取任意类型题目作为兜底');
        question = await KBQuestions.findOne({
          where: {
            knowledge_base_id: knowledgeBaseId
          },
          order: [[Sequelize.fn('RAND')]],
          attributes: ['id', 'question', 'answer', 'type']
        });
      }

      // 如果仍然没有题目（比如知识库中根本没有题目）
      if (!question) {
        return res.status(404).json({
          code: 404,
          message: '知识库中没有可用题目',
          data: null
        });
      }

      // 重置统计
      manualCount = 0;
      intelligentCount = 0;
    }

    // 将此题目ID添加到Redis集合中，标记为已使用
    await redis.sadd(usedQuestionsKey, question.id);

    // 更新对应类型的统计计数
    if (question.type === '人工出题') {
      await redis.incr(manualCountKey);
      manualCount++;
      console.log(`更新人工出题计数：${manualCount}`);
    } else if (question.type === '智能出题') {
      await redis.incr(intelligentCountKey);
      intelligentCount++;
      console.log(`更新智能出题计数：${intelligentCount}`);
    }

    // 设置Redis key的过期时间（24小时）
    const redisKey = await redis.exists(usedQuestionsKey);
    if (redisKey === 1) {
      await redis.expire(usedQuestionsKey, 24 * 60 * 60); // 24小时过期
      await redis.expire(manualCountKey, 24 * 60 * 60);
      await redis.expire(intelligentCountKey, 24 * 60 * 60);
    }

    // 获取该知识库的题目总数，用于返回循环状态信息
    const totalQuestionsInKB = await KBQuestions.count({
      where: {
        knowledge_base_id: knowledgeBaseId
      }
    });

    // 获取当前已使用的题目数量
    const currentUsedCount = await redis.smembers(usedQuestionsKey);
    const usedCount = currentUsedCount.length;

    // 生成UUID作为问题ID
    const uniqueQuestionId = uuidv4();

    // 准备要添加的题目数据
    const questionData = {
      questionId: uniqueQuestionId,  // 使用UUID作为题目ID
      originalQuestionId: question.id, // 保存原始题目ID
      question: question.question,
      answer: question.answer,
      type: question.type,
      timestamp: formatDateTime(new Date())  // 格式化为 "yyyy-MM-dd HH:mm:ss"
    };

    // 获取现有的题目列表或初始化为空数组
    let examContent = examRecord.examContent || [];

    // 添加新题目到数组中（不覆盖）
    examContent.push(questionData);

    // 更新考试记录
    try {
      // 开启事务
      const transaction = await sequelize.transaction();

      try {
        // 使用JSON.stringify显式将对象转为字符串
        const examContentString = JSON.stringify(examContent);

        // 直接使用SQL更新而不是ORM模型更新
        await sequelize.query(
            `UPDATE exam_records SET exam_content = :examContent, kb_id = :kbId WHERE id = :id`,
            {
              replacements: {
                examContent: examContentString,
                kbId: knowledgeBaseId,
                id: examId
              },
              transaction
            }
        );

        // 提交事务
        await transaction.commit();

        // 验证更新是否成功
        const verifyRecord = await ExamRecord.findByPk(examId);

        if (!verifyRecord || !verifyRecord.examContent) {
          console.error(`题目保存验证失败(第一层检查)，examId: ${examId}, questionId: ${uniqueQuestionId}`);
        } else {
          // 尝试找出新添加的题目
          const foundQuestion = verifyRecord.examContent.find(q => q.questionId === uniqueQuestionId);
          console.log('查找题目:', foundQuestion ? '找到题目' : '未找到题目');

          if (!foundQuestion) {
            console.error(`题目保存验证失败(第二层检查)，examId: ${examId}, questionId: ${uniqueQuestionId}`);
          } else {
            console.log(`成功更新考试记录(ID:${examId})的题目列表，当前题目数: ${verifyRecord.examContent.length}, 题目类型: ${question.type}`);
          }
        }
      } catch (innerError) {
        // 回滚事务
        await transaction.rollback();
        console.error(`事务执行失败: ${innerError.message}`, innerError);
        throw innerError; // 重新抛出错误
      }
    } catch (error) {
      console.error(`更新考试记录失败: ${error.message}`, error);
      // 尝试紧急备份方案
      try {
        console.log('尝试备用更新方法...');
        // 尝试使用原生SQL方法直接更新
        await sequelize.query(
            `UPDATE exam_records SET exam_content = :examContent WHERE id = :id`,
            {
              replacements: {
                examContent: JSON.stringify(examContent),
                id: examId
              }
            }
        );
        console.log('备用更新方法执行完成');
      } catch (backupError) {
        console.error(`备用更新方法也失败: ${backupError.message}`, backupError);
      }
    }

    // 检查更新后的题目数量是否已达到考试题目要求
    if (questionNumber > 0 && examContent.length >= questionNumber) {
      // 如果这是最后一题，则更新考试状态为已完成
      // await ExamRecord.update({
      //   examStatus: 'completed'
      // }, {
      //   where: { id: examId }
      // });

      // 返回数据并标记这是最后一题
      return res.json({
        code: 200,
        message: '题目抽取成功',
        data: {
          questionId: uniqueQuestionId,
          question: question.question,
          answer: question.answer,
          type: question.type,
          isLastQuestion: true, // 标记这是最后一题
          examFinished: true, // 标记考试已完成
          questionRatioStats: {
            manualCount: question.type === '人工出题' ? manualCount : manualCount - 1,
            intelligentCount: question.type === '智能出题' ? intelligentCount : intelligentCount - 1,
            manualRatio: manualQuestionRatio,
            intelligentRatio: intelligentQuestionRatio
          }
        }
      });
    }

    // 返回常规数据
    return res.json({
      code: 200,
      message: '题目抽取成功',
      data: {
        questionId: uniqueQuestionId, // 使用与questionData相同的UUID
        question: question.question,
        answer: question.answer,
        type: question.type,
        examProgress: {
          currentQuestion: examContent.length,
          totalQuestions: questionNumber
        },
        questionRatioStats: {
          manualCount: manualCount,
          intelligentCount: intelligentCount,
          manualRatio: manualQuestionRatio,
          intelligentRatio: intelligentQuestionRatio
        }
      }
    });
  } catch (error) {
    console.error('抽取题目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
};

/**
 * 提交考试题目的回答
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const submitAnswer = async (req, res) => {
  try {
    // 从请求体中获取考试ID、题目ID和回答内容
    const { examId, questionId, userAnswer } = req.body;

    // 验证必要参数
    if (!examId) {
      return res.status(400).json({
        code: 400,
        message: '缺少考试ID参数',
        data: null
      });
    }

    if (!questionId) {
      return res.status(400).json({
        code: 400,
        message: '缺少题目ID参数',
        data: null
      });
    }

    if (userAnswer === undefined || userAnswer === null) {
      return res.status(400).json({
        code: 400,
        message: '缺少回答内容',
        data: null
      });
    }

    // 查找考试记录
    const examRecord = await ExamRecord.findOne({
      where: {
        id: examId
      }
    });

    if (!examRecord) {
      return res.status(404).json({
        code: 404,
        message: `未找到ID为${examId}的考试记录`,
        data: null
      });
    }

    // 检查考试是否已完成
    if(examRecord.examStatus === 'completed') {
      return res.status(400).json({
        code: 400,
        message: '考试已完成',
        data: {
          examFinished: true
        }
      });
    }

    // 检查考试时间是否已结束
    const now = moment();
    const endTimeObj = moment(examRecord.endTime, 'YYYY-MM-DD HH:mm:ss');

    if (now.isAfter(endTimeObj)) {
      // 考试时间已结束，更新考试状态为已完成
      await ExamRecord.update({
        examStatus: 'completed'
      }, {
        where: {
          id: examId,
          enterpriseId: examRecord.enterpriseId
        }
      });

      return res.status(400).json({
        code: 400,
        message: '考试时间已结束',
        data: {
          examFinished: true,
          timeoutExam: true
        }
      });
    }

    // 获取题目列表
    let examContent = examRecord.examContent || [];

    // 找到对应questionId的题目
    const questionIndex = examContent.findIndex(item => item.questionId === questionId);

    if (questionIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: `在考试记录中未找到ID为${questionId}的题目`,
        data: null
      });
    }

    // 给题目添加用户回答字段和回答时间
    examContent[questionIndex].userAnswer = userAnswer;
    examContent[questionIndex].answerTime = formatDateTime(new Date());

    // 调用题目解析API获取解析
    const questionText = examContent[questionIndex].question;
    const correctAnswer = examContent[questionIndex].answer;

    // 调用解析API获取题目解析，将用户答案作为tip参数传入
    const analysis = await getQuestionAnalysis(questionText, userAnswer, correctAnswer);
    console.log("原始analysis:", JSON.stringify(analysis, null, 2));

    // 直接使用analysis作为分析内容，不再解析JSON
    try {
        // 获取分析内容文本
        let analysisText = '';
        if (typeof analysis === 'string') {
            analysisText = analysis;
        } else if (analysis && typeof analysis === 'object' && analysis.text) {
            analysisText = analysis.text;
        } else {
            analysisText = analysis ? String(analysis) : '暂无解析';
        }

        // 直接使用分析文本
        examContent[questionIndex].analysis = analysisText || '暂无解析';
        examContent[questionIndex].correctAnswer = '';
        
        // 根据文本内容判断是否回答正确
        examContent[questionIndex].result = analysisText.includes('回答正确');

        console.log('最终解析结果:', {
            content: examContent[questionIndex].analysis,
            answer: examContent[questionIndex].correctAnswer,
            result: examContent[questionIndex].result
        });
    } catch (error) {
        console.error('解析题目分析结果失败:', error);
        console.error('错误详情:', {
            errorName: error.name,
            errorMessage: error.message,
            errorStack: error.stack
        });
        examContent[questionIndex].analysis = '解析服务异常，请稍后再试';
        examContent[questionIndex].correctAnswer = '';
        examContent[questionIndex].result = false;
    }

    // 更新考试记录
    try {
      // 开启事务
      const transaction = await sequelize.transaction();

      try {
        // 使用JSON.stringify显式将对象转为字符串
        const examContentString = JSON.stringify(examContent);

        // 直接使用SQL更新而不是ORM模型更新
        await sequelize.query(
            `UPDATE exam_records SET exam_content = :examContent WHERE id = :id`,
            {
              replacements: {
                examContent: examContentString,
                id: examId
              },
              transaction
            }
        );

        // 提交事务
        await transaction.commit();

        // 验证更新是否成功
        const verifyRecord = await ExamRecord.findByPk(examId);

        if (!verifyRecord || !verifyRecord.examContent) {
          console.error(`答案和解析保存验证失败(基础检查)，examId: ${examId}, questionId: ${questionId}`);
        } else {
          // 查找这题是否存在
          const foundQuestion = verifyRecord.examContent.find(q => q.questionId === questionId);
          if (!foundQuestion || !foundQuestion.analysis) {
            console.error(`答案和解析保存验证失败(内容检查)，examId: ${examId}, questionId: ${questionId}`);
          } else {
            console.log(`成功更新考试记录答案和解析，examId: ${examId}, questionId: ${questionId}`);
          }
        }
      } catch (innerError) {
        // 回滚事务
        await transaction.rollback();
        console.error(`事务执行失败: ${innerError.message}`, innerError);
        throw innerError; // 重新抛出错误
      }
    } catch (error) {
      console.error(`更新考试答案和解析失败: ${error.message}`, error);
      // 尝试紧急备份方案
      try {
        console.log('尝试备用更新方法...');
        // 尝试使用原生SQL方法直接更新
        await sequelize.query(
            `UPDATE exam_records SET exam_content = :examContent WHERE id = :id`,
            {
              replacements: {
                examContent: JSON.stringify(examContent),
                id: examId
              }
            }
        );
        console.log('备用更新方法执行完成');
      } catch (backupError) {
        console.error(`备用更新方法也失败: ${backupError.message}`, backupError);
      }
    }

    // 检查考试是否应该结束
    const currentTime = moment();
    const examEndTime = moment(examRecord.endTime, 'YYYY-MM-DD HH:mm:ss');
    const questionNumber = examRecord.questionNumber || 0;
    let examShouldEnd = false;
    let endReason = '';

    // 判断1: 检查考试时间是否已到
    if (currentTime.isAfter(examEndTime)) {
      examShouldEnd = true;
      endReason = '考试时间已结束';
      console.log(`考试时间已到: examId=${examId}, 当前时间=${currentTime.format('YYYY-MM-DD HH:mm:ss')}, 结束时间=${examEndTime.format('YYYY-MM-DD HH:mm:ss')}`);
    }

    // 判断2: 检查答题数量是否足够（所有题目都已回答）
    if (!examShouldEnd && questionNumber > 0) {
      // 统计已回答的题目数量（有userAnswer且不为空的题目）
      const answeredQuestions = examContent.filter(item =>
        item.userAnswer !== undefined &&
        item.userAnswer !== null &&
        item.userAnswer !== ''
      );

      console.log(`答题进度检查: examId=${examId}, 已答题数=${answeredQuestions.length}, 总题数=${questionNumber}`);

      if (answeredQuestions.length >= questionNumber) {
        examShouldEnd = true;
        endReason = '所有题目已完成';
        console.log(`所有题目已完成: examId=${examId}, 已答=${answeredQuestions.length}/${questionNumber}`);
      }
    }

    // 如果考试应该结束，更新考试状态
    if (examShouldEnd && examRecord.examStatus !== 'completed') {
      try {
        // 计算考试用时
        const startTime = moment(examRecord.examTime, 'YYYY-MM-DD HH:mm:ss');
        const usedDuration = Math.max(1, Math.ceil(currentTime.diff(startTime, 'minutes', true)));
        const finalUsedDuration = Math.min(usedDuration, examRecord.examDuration || 9999);

        // 更新考试状态为已完成
        await ExamRecord.update({
          examStatus: 'completed',
          usedDuration: finalUsedDuration
        }, {
          where: {
            id: examId,
            enterpriseId: examRecord.enterpriseId
          }
        });

        console.log(`考试已自动结束: examId=${examId}, 原因=${endReason}, 用时=${finalUsedDuration}分钟`);

        // 如果考试结束，返回特定格式
        return res.status(400).json({
          code: 400,
          message: endReason,
          data: {
            examFinished: true,
            examEndReason: endReason,
            timeoutExam: endReason.includes('时间'),
            allQuestionsCompleted: endReason.includes('题目')
          }
        });
      } catch (updateError) {
        console.error(`更新考试结束状态失败: examId=${examId}`, updateError);
      }
    }

    // 考试未结束，返回正常响应
    return res.json({
      code: 200,
      message: '回答提交成功',
      data: {
        questionId,
        correctAnswer: examContent[questionIndex].correctAnswer,
        analysis: examContent[questionIndex].analysis
      }
    });
  } catch (error) {
    console.error('提交回答失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
};

/**
 * 重置考试已使用题目列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const resetExamQuestions = async (req, res) => {
  try {
    const { examId } = req.params;

    if (!examId) {
      return res.status(400).json({
        code: 400,
        message: '缺少考试ID参数',
        data: null
      });
    }

    // 删除Redis中存储的已使用题目记录
    const usedQuestionsKey = `exam:${examId}:used_questions`;
    await redis.del(usedQuestionsKey);

    return res.json({
      code: 200,
      message: '考试题目状态重置成功',
      data: null
    });
  } catch (error) {
    console.error('重置考试题目状态失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
};

/**
 * 从小程序创建考试记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const createExamRecordFromMiniapp = async (req, res) => {
  try {
    const { knowledgeBaseId,positionName,positionLevel } = req.body;
    const openId = req.headers.openid; // 从请求头获取openId

    if (!openId || !knowledgeBaseId) {
      return res.status(400).json({
        code: 400,
        message: '参数不完整'
      });
    }

    // 1. 根据openId查找用户
    const user = await User.findOne({
      where: { 
        openId, 
        status: true,
        enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
      }
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    const enterpriseId = DEFAULT_ENTERPRISE_ID;

    // 2. 查找员工信息
    const employee = await Employee.findOne({
      where: {
        openId,
        status: '1', // 在职状态
        enterpriseId
      },
      include: [
        {
          model: Position,
          as: 'position',
          attributes: ['id', 'nameId']
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工信息不存在'
      });
    }

    // 3. 查询知识库数据
    const knowledgeBase = await KnowledgeBase.findOne({
      where: {
        id: knowledgeBaseId,
        deleted: false,
        enterpriseId: String(enterpriseId)
      }
    });

    if (!knowledgeBase) {
      return res.status(404).json({
        code: 404,
        message: '知识库数据不存在'
      });
    }

    // 4. 检查考试审核申请状态
    const reviewApplication = await ExamReviewApplication.findOne({
      where: {
        enterpriseId,
        delFlag: 0,
        kbId: knowledgeBaseId,
        positionName:positionName,
        positionLevel:positionLevel,
        createdBy: user.id,
      },
      order: [['created_at', 'DESC']], // 根据创建时间降序排序，获取最新的一条记录
    });

    // 如果没有申请记录，返回错误
    if (!reviewApplication) {
      return res.status(403).json({
        code: 403,
        message: '请先申请考试资格'
      });
    }

    // 如果申请状态不是2(已通过)，返回错误
    if (reviewApplication.status !== '2') {
      return res.status(403).json({
        code: 403,
        message: '考试资格尚未审核，请等待或联系管理人员'
      });
    }

    // 检查考试时间限制
    if (reviewApplication.examDate) {
      const currentTime = moment();
      const examDate = moment(reviewApplication.examDate);

      // 如果当前时间小于指定的考试时间，不允许开始考试
      if (currentTime.isBefore(examDate)) {
        return res.status(403).json({
          code: 403,
          message: `考试尚未开始，请在${examDate.format('YYYY-MM-DD HH:mm')}后参加考试`,
          data: {
            examDate: examDate.format('YYYY-MM-DD HH:mm:ss')
          }
        });
      }
    }

    // 4.5 查询练考配置，查找examSubject与knowledgeBaseId匹配的配置
    let examConfig;
    // 优先使用申请中保存的配置信息
    if (reviewApplication.examConfigInfo) {
      examConfig = reviewApplication.examConfigInfo;
    } else {
      // 如果申请中没有保存配置信息，则从数据库查询
      examConfig = await ExamConfig.findOne({
        where: {
          positionName: reviewApplication.positionName,
          positionLevel: reviewApplication.positionLevel,
          examSubject: reviewApplication.kbId,
          enterpriseId: reviewApplication.enterpriseId,
          status: "必考"
        }
      });
    }

    // 获取考试时长、合格分等信息
    const examDuration = examConfig ? examConfig.examDuration : 60; // 默认60分钟
    const passScore = examConfig ? examConfig.passScore : 80; // 默认80分
    const examCount = examConfig ? examConfig.examCount : 3; // 默认最多考3次，如果没有配置

    // 4.6 检查考试次数是否已达上限
    // 查询当前申请下已有的考试记录数量
    const examRecordsCount = await ExamRecord.count({
      where: {
        reviewApplicationId: reviewApplication.id,
        enterpriseId,
        delFlag: 0,
        openId: openId // 添加openId条件
      }
    });

    // 如果考试次数已达上限，返回错误
    if (examRecordsCount >= examCount) {
      return res.status(403).json({
        code: 403,
        message: `可考试次数已达上限`
      });
    }

    // 4.7 检查是否有正在进行中的考试
    const ongoingExam = await ExamRecord.findOne({
      where: {
        reviewApplicationId: reviewApplication.id,
        examStatus: 'ongoing',
        enterpriseId,
        delFlag: 0,
        openId: openId // 添加openId条件
      }
    });

    if (ongoingExam) {
      return res.status(403).json({
        code: 403,
        message: '您有正在进行中的考试，请先完成当前考试',
        data: {
          examId: ongoingExam.id,
          endTime: ongoingExam.endTime
        }
      });
    }

    // 计算考试截止时间 (当前时间 + 考试时长分钟)
    const now = moment();
    const endTime = now.clone().add(examDuration, 'minutes').format('YYYY-MM-DD HH:mm:ss');

    // 5. 创建考试记录
    const examRecord = await ExamRecord.create({
      examSubject: knowledgeBase.name, // 证书名称作为考试科目
      examTime: now.format('YYYY-MM-DD HH:mm:ss'), // 当前时间作为考试时间
      positionBelongId: employee.position ? employee.levelId : 0, // 非空默认值
      positionId: positionName,
      levelId: positionLevel,
      categoryId: knowledgeBase.fileCategory || 0,
      category: knowledgeBase.fileCategory || '未分类',
      examineeId: user.id,
      examinee: user.realName || user.nickname || user.username,
      openId: req.headers.openid || user.openId, // 添加openId字段
      score: 0, // 非空默认值
      usedDuration: 0, // 非空默认值
      confirmStatus: '1', // 补充缺少的confirmStatus字段
      reviewApplicationId: reviewApplication.id, // 关联审核申请ID
      examContent: [], // 题目列表为空数组
      enterpriseId: enterpriseId,
      kbId: knowledgeBaseId, // 关联知识库ID
      createdBy: user.id,
      updatedBy: user.id,
      delFlag: 0,
      questionNumber: examConfig.questionCount,
      // 新增4个字段
      passScore: passScore, // 合格分
      examDuration: examDuration, // 考试分钟数
      endTime: endTime, // 截止时间
      examStatus: 'ongoing' // 考试状态：ongoing-考试中
    });

    // 6. 返回结果
    return res.status(201).json({
      code: 200,
      message: '创建考试记录成功',
      data: {
        examId: examRecord.id,
        endTime: endTime,
        examNumber: examConfig.questionCount,
        passScore: passScore,
        examDuration: examDuration,
        remainingTimes: examCount - examRecordsCount - 1 // 剩余考试次数
      }
    });

  } catch (error) {
    logger.error('创建考试记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建考试记录失败',
      error: error.message
    });
  }
};

/**
 * 计算考试已用时间
 * @param {Object} examRecord - 考试记录
 * @returns {number} 已用时间(分钟)
 */
const calculateUsedDuration = (examRecord) => {
  // 如果考试已完成且已用时间大于0，则直接返回记录中的已用时间
  if (examRecord.examStatus === 'completed' && examRecord.usedDuration > 0) {
    return examRecord.usedDuration;
  }

  try {
    // 计算从考试开始到现在的时间差
    const startTime = moment(examRecord.examTime, 'YYYY-MM-DD HH:mm:ss');
    const now = moment();

    // 如果考试已结束，则使用结束时间计算
    if (examRecord.examStatus === 'completed' && examRecord.updatedAt) {
      const endTime = moment(examRecord.updatedAt);
      return Math.max(1, Math.ceil(endTime.diff(startTime, 'minutes', true)));
    }

    // 如果当前时间早于考试开始时间（异常情况），返回1分钟
    if (now.isBefore(startTime)) {
      console.warn(`警告: 当前时间(${now.format('YYYY-MM-DD HH:mm:ss')})早于考试开始时间(${startTime.format('YYYY-MM-DD HH:mm:ss')})`);
      return 1;
    }

    // 如果超过了考试时长上限，则返回考试时长
    const usedMinutes = Math.ceil(now.diff(startTime, 'minutes', true));
    if (examRecord.examDuration && usedMinutes > examRecord.examDuration) {
      return examRecord.examDuration;
    }

    // 返回计算的分钟数，至少为1分钟
    return Math.max(1, usedMinutes);
  } catch (error) {
    console.error('计算考试用时出错:', error);
    // 发生错误时返回默认值1分钟
    return 1;
  }
};

/**
 * 更新考试状态
 * @param {Object} examRecord - 考试记录
 * @param {boolean} isEarlySubmission - 是否提前交卷
 * @returns {Object} 状态更新结果
 */
const updateExamStatus = async (examRecord, isEarlySubmission) => {
  // 判断考试是否需要更新状态
  if (examRecord.examStatus === 'completed') {
    // 确保已完成考试的usedDuration不为0
    if (examRecord.usedDuration === 0) {
      const realUsedDuration = calculateUsedDuration(examRecord);
      await ExamRecord.update(
        { usedDuration: realUsedDuration },
        { where: { id: examRecord.id } }
      );
      examRecord.usedDuration = realUsedDuration;
      console.log(`已修复考试ID:${examRecord.id}的用时为${realUsedDuration}分钟`);
    }
    return { statusUpdated: false, examRecord };
  }

  let statusUpdated = false;
  const now = moment();

  // 如果是提前交卷
  if (isEarlySubmission === true) {
    // 计算已用时间（分钟）
    const usedDuration = calculateUsedDuration(examRecord);

    // 更新考试状态为已完成，并记录用时
    await ExamRecord.update({
      examStatus: 'completed',
      usedDuration: usedDuration
    }, {
      where: {
        id: examRecord.id,
        enterpriseId: examRecord.enterpriseId
      }
    });

    console.log(`用户提前交卷，自动更新考试状态为已完成，考试ID: ${examRecord.id}，考试用时: ${usedDuration}分钟`);

    // 更新本地examRecord对象的状态
    examRecord.examStatus = 'completed';
    examRecord.usedDuration = usedDuration;
    statusUpdated = true;
  } else {
    // 检查考试时间是否已结束
    const endTimeObj = moment(examRecord.endTime, 'YYYY-MM-DD HH:mm:ss');

    if (now.isAfter(endTimeObj)) {
      // 考试时间已结束，更新考试状态为已完成
      // 时间到期，使用截止时间计算用时
      const startTime = moment(examRecord.examTime, 'YYYY-MM-DD HH:mm:ss');
      const usedDuration = Math.ceil(endTimeObj.diff(startTime, 'minutes', true));

      // 确保用时至少为1分钟且不超过考试时长
      const finalUsedDuration = Math.min(
        Math.max(1, usedDuration),
        examRecord.examDuration || 9999
      );

      await ExamRecord.update({
        examStatus: 'completed',
        usedDuration: finalUsedDuration
      }, {
        where: {
          id: examRecord.id,
          enterpriseId: examRecord.enterpriseId
        }
      });

      console.log(`考试时间已结束，自动更新考试状态为已完成，考试ID: ${examRecord.id}，考试用时: ${finalUsedDuration}分钟`);

      // 更新本地examRecord对象的状态
      examRecord.examStatus = 'completed';
      examRecord.usedDuration = finalUsedDuration;
      statusUpdated = true;
    }
  }

  return { statusUpdated, examRecord };
};

/**
 * 获取成绩审核状态
 * @param {Object} examRecord - 考试记录
 * @returns {Object} 包含审核状态和题目数量的对象
 */
const getScoreApprovalStatus = async (examRecord) => {
  let scoreApprovalStatus = null;
  let questionCount = 0;

  if (examRecord.reviewApplicationId) {
    // 查询考试审核申请记录
    const reviewApplication = await ExamReviewApplication.findOne({
      where: {
        id: examRecord.reviewApplicationId,
        enterpriseId: examRecord.enterpriseId
      }
    });

    if (reviewApplication) {
      // 根据scoreConfirmStatus判断审核状态
      switch (reviewApplication.scoreConfirmStatus) {
        case '2':
          scoreApprovalStatus = '已通过';
          break;
        case '3':
          scoreApprovalStatus = '已驳回';
          break;
        default:
          scoreApprovalStatus = '审核中';
          break;
      }

      if (reviewApplication.examConfigInfo && reviewApplication.examConfigInfo.questionCount) {
        questionCount = reviewApplication.examConfigInfo.questionCount;
      }
    }
  }

  return { scoreApprovalStatus, questionCount };
};

/**
 * 计算考试得分
 * @param {Array} examContent - 考试内容
 * @param {number} totalQuestions - 题目总数
 * @returns {number} 考试得分
 */
const calculateExamScore = (examContent, totalQuestions) => {
  // 统计答对的题目数量
  let correctCount = 0;
  examContent.forEach(item => {
    if (item.result === true) {
      correctCount++;
    }
  });

  // 计算正确率并取整
  return correctCount;
};

/**
 * 格式化考试内容用于报告生成
 * @param {Array} examContent - 考试内容
 * @returns {Object} 格式化后的考试内容
 */
const formatExamContentForReport = (examContent) => {
  // 处理examContent数组，移除不需要的字段，设置默认值
  const processedItems = examContent.map(item => {
    // 创建一个新对象，只包含需要的字段
    return {
      question: item.question,
      analysis: item.analysis,
      userAnswer: item.userAnswer || '',
      answer: item.answer || ''
    };
  });

  // 将题目列表转换为更直白的字符串格式
  let examContentText = '';
  processedItems.forEach((item) => {
    const userAnswerText = item.userAnswer ? item.userAnswer : '无';
    examContentText += `问题:"${item.question}"。问题解析:"${item.analysis}"。用户答案:"${userAnswerText}"\n`;
  });

  return { processedItems, examContentText };
};

/**
 * 调用外部API生成考试报告
 * @param {string} examContentText - 格式化后的考试内容文本
 * @returns {Object} API返回的报告数据
 */
const generateExamReportFromAPI = async (examContentText) => {
  // 构造请求数据
  const requestData = {
    inputs: {
      all: examContentText
    }
  };

  console.log('requestData', requestData);
  console.log('REPORT_API', REPORT_API);

  // 调用报告API
  const response = await axios.post(REPORT_API, requestData);
  console.log('报告API返回结果:', JSON.stringify(response.data.data.outputs.text));

  // 检查API响应
  if (response.data && response.data.success && response.data.data && response.data.data.outputs) {
    // 处理返回的text，移除可能存在的Markdown代码块标记
    let jsonText = response.data.data.outputs.text;
    // 去除开头的```json和结尾的```标记
    jsonText = jsonText.replace(/^```json\n/, '').replace(/\n```$/, '').replaceAll("*","");

    // 尝试将返回的text解析为JSON对象
    try {
      return JSON.parse(jsonText);
    } catch (parseError) {
      console.error('解析API返回的JSON失败:', parseError);
      return response.data.data.outputs.text;
    }
  }

  throw new Error('报告API返回格式不符合预期');
};

/**
 * 更新考试记录中的报告数据
 * @param {string} examId - 考试ID
 * @param {Object} reportData - 报告数据
 * @param {number} score - 考试得分
 */
const updateExamReportData = async (examId, reportData, score) => {
  let advantage = '';
  let improve = '';
  let reportDataStr = '';

  // 如果成功解析为JSON对象
  if (typeof reportData === 'object' && reportData !== null) {
    // 提取优点和改进建议 - 适配不同的字段名称
    advantage = reportData.strengths || '暂无优点分析';
    improve = reportData.improvement_areas || '暂无改进建议';
    // 将完整的报告数据保存为JSON字符串
    reportDataStr = JSON.stringify(reportData);
  } else {
    // 如果返回的不是有效JSON，使用默认处理逻辑
    advantage = '暂无优点分析';
    improve = '暂无改进建议';
    reportDataStr = JSON.stringify({ summary: reportData });
  }

  // 更新考试记录中的报告数据
  await ExamRecord.update({
    advantage,
    improve,
    score, // 使用计算出的正确率作为分数
    reportData: reportDataStr // 确保reportData是字符串类型
  }, {
    where: { id: examId }
  });

  return { advantage, improve, reportData };
};

/**
 * 生成返回给客户端的报告响应
 * @param {Object} data - 报告数据
 * @param {boolean} statusUpdated - 状态是否已更新
 * @returns {Object} 格式化后的响应对象
 */
const generateExamReportResponse = (data, statusUpdated) => {
  const message = statusUpdated
    ? '考试时间已结束，自动完成考试并生成报告成功'
    : '获取考试报告成功';

  // 确保usedDuration不为0
  if (!data.usedDuration || data.usedDuration <= 0) {
    console.log('警告: usedDuration为0或无效值，已设置为默认值1分钟');
    data.usedDuration = 1;
  }

  return {
    code: 200,
    message,
    data
  };
};

/**
 * 获取考试报告
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getExamReport = async (req, res) => {
  try {
    // 从请求体中获取考试ID和提前交卷标志
    const { exam_id, isEarlySubmission } = req.body;

    // 验证必要参数
    if (!exam_id) {
      return res.status(400).json({
        code: 400,
        message: '缺少考试ID参数',
        data: null
      });
    }

    // 查找考试记录
    const examRecord = await ExamRecord.findOne({
      where: {
        id: exam_id,
        enterpriseId: DEFAULT_ENTERPRISE_ID
      }
    });

    if (!examRecord) {
      return res.status(404).json({
        code: 404,
        message: `未找到ID为${exam_id}的考试记录`,
        data: null
      });
    }

    // 1. 更新考试状态
    const { statusUpdated, examRecord: updatedExamRecord } = await updateExamStatus(examRecord, isEarlySubmission);

    // 如果考试未完成且不是提前交卷且时间未到，返回错误
    if (updatedExamRecord.examStatus !== 'completed') {
      return res.status(400).json({
        code: 400,
        message: '考试尚未完成，无法生成报告',
        data: null
      });
    }

    // 2. 获取成绩审核状态
    const { scoreApprovalStatus, questionCount } = await getScoreApprovalStatus(updatedExamRecord);

    // 3. 判断score字段是否已有值
    if (updatedExamRecord.score && updatedExamRecord.reportData) {
      // 比较得分与通过分数，如果低于通过分数，更新confirmStatus为3(不通过)
      if (updatedExamRecord.score < updatedExamRecord.passScore && updatedExamRecord.confirmStatus !== '3') {
        await ExamRecord.update(
          { confirmStatus: '3' },
          { where: { id: exam_id } }
        );
        updatedExamRecord.confirmStatus = '3';
        console.log(`考试得分${updatedExamRecord.score}低于通过分数${updatedExamRecord.passScore}，已更新考试ID:${exam_id}的确认状态为不通过(3)`);
      }

      // 直接返回已有的报告数据
      let reportData = {};
      try {
        reportData = JSON.parse(updatedExamRecord.reportData);
      } catch (e) {
        console.error('解析reportData失败:', e);
      }

      // 确保usedDuration不为0
      const usedDuration = updatedExamRecord.usedDuration > 0
        ? updatedExamRecord.usedDuration
        : calculateUsedDuration(updatedExamRecord);

      if (updatedExamRecord.usedDuration <= 0) {
        // 更新数据库中的usedDuration
        await ExamRecord.update(
          { usedDuration },
          { where: { id: exam_id } }
        );
        console.log(`已修复考试ID:${exam_id}的用时为${usedDuration}分钟`);
      }

      const responseData = {
        advantage: updatedExamRecord.advantage,
        improve: updatedExamRecord.improve,
        score: updatedExamRecord.score,
        exam: updatedExamRecord.examContent,
        pass_score: updatedExamRecord.passScore,
        reportData,
        questionCount,
        scoreApprovalStatus,
        examSubject: updatedExamRecord.examSubject,
        examTime: updatedExamRecord.examTime,
        usedDuration,
        confirmStatus: updatedExamRecord.confirmStatus // 添加confirmStatus到响应中
      };

      // 4. 生成返回响应
      const response = generateExamReportResponse(responseData, statusUpdated);
      return res.json(response);
    }

    // 5. 如果没有报告数据，开始生成报告流程
    const examContent = updatedExamRecord.examContent || [];
    const totalQuestions = updatedExamRecord.questionNumber;

    // 6. 计算得分
    const score = calculateExamScore(examContent, totalQuestions);

    // 7. 格式化考试内容
    const { examContentText } = formatExamContentForReport(examContent);

    // 8. 调用API生成报告
    try {
      const outputData = await generateExamReportFromAPI(examContentText);

      // 9. 更新考试记录中的报告数据
      const { advantage, improve, reportData } = await updateExamReportData(exam_id, outputData, score);

      // 确保usedDuration不为0
      const usedDuration = updatedExamRecord.usedDuration > 0
        ? updatedExamRecord.usedDuration
        : calculateUsedDuration(updatedExamRecord);

      if (updatedExamRecord.usedDuration <= 0) {
        // 更新数据库中的usedDuration
        await ExamRecord.update(
          { usedDuration },
          { where: { id: exam_id } }
        );
        console.log(`已修复考试ID:${exam_id}的用时为${usedDuration}分钟`);
      }

      // 比较得分与通过分数，如果低于通过分数，更新confirmStatus为3(不通过)
      let confirmStatus = updatedExamRecord.confirmStatus;
      if (score < updatedExamRecord.passScore && confirmStatus !== '3') {
        await ExamRecord.update(
          { confirmStatus: '3' },
          { where: { id: exam_id } }
        );
        confirmStatus = '3';
        console.log(`考试得分${score}低于通过分数${updatedExamRecord.passScore}，已更新考试ID:${exam_id}的确认状态为不通过(3)`);
      }

      // 10. 检查是否需要更新考试申请状态
      await checkAndUpdateExamApplication(updatedExamRecord);

      // 11. 触发考试成就检测 - 异步执行，不阻塞主流程
      try {
        const { handleExamAchievementTrigger } = require('../../utils/achievementUtils');
        const userId = updatedExamRecord.examineeId;
        const openId = updatedExamRecord.openId;
        
        // 使用setImmediate确保异步执行且优先级更高，不阻塞主流程
        setImmediate(async () => {
          try {
            // 检查成就系统是否启用
            
            const achieveSetting = await SystemSetting.findOne(
              addEnterpriseFilter({
                where: { code: 'achieve' },
                attributes: ['value']
              })
            );

            const isAchieveEnabled = achieveSetting && achieveSetting.value === 'true';

            if (!isAchieveEnabled) {
              console.log('[考试成就] 成就系统已禁用，跳过成就检测');
            }else{
              console.log('[考试成就] 成就系统已启用，开始成就检测');
              await handleExamAchievementTrigger(
                userId,
                openId,
                updatedExamRecord.enterpriseId,
                updatedExamRecord.id,
                score,
                questionCount
              );
              console.log(`[考试成就] 成就检测已触发，用户ID: ${userId}, 考试ID: ${updatedExamRecord.id}`);
            }

          } catch (achievementError) {
            console.error('[考试成就] 成就检测失败:', achievementError);
          }
        }, 0);
      } catch (achievementError) {
        console.error('触发考试成就检测失败:', achievementError);
        // 不影响主流程，继续执行
      }

      // 12. 准备返回数据
      const responseData = {
        advantage,
        improve,
        pass_score: updatedExamRecord.passScore,
        score,
        reportData: outputData,
        exam: examContent,
        scoreApprovalStatus,
        examSubject: updatedExamRecord.examSubject,
        examTime: updatedExamRecord.examTime,
        usedDuration,
        questionCount,
        confirmStatus // 添加confirmStatus到响应中
      };

      // 13. 生成返回响应
      const response = generateExamReportResponse(responseData, statusUpdated);
      return res.json(response);
    } catch (apiError) {
      console.error('调用报告API失败:', apiError.message);
      return res.status(500).json({
        code: 500,
        message: '报告生成服务异常，请稍后再试',
        data: null
      });
    }
  } catch (error) {
    console.error('获取考试报告失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
};

/**
 * 检查并更新考试申请状态
 * @param {Object} examRecord - 考试记录对象
 */
const checkAndUpdateExamApplication = async (examRecord) => {
  try {
    console.log("检查并更新考试申请状态")
    if (!examRecord.reviewApplicationId) {
      console.log("未找到申请id，无法更新考试申请状态")
      return;
    }

    // 获取考试申请信息
    const examApplication = await ExamReviewApplication.findOne({
      where: {
        id: examRecord.reviewApplicationId,
        delFlag: false
      }
    });

    if (!examApplication) {
      console.log('未找到对应的考试申请记录');
      return;
    }

    // 获取练考配置信息
    let examConfig;
    // 优先使用申请中保存的配置信息
    if (examApplication.examConfigInfo) {
      examConfig = examApplication.examConfigInfo;
    } else {
      // 如果申请中没有保存配置信息，则从数据库查询
      examConfig = await ExamConfig.findOne({
        where: {
          positionName: examApplication.positionName,
          positionLevel: examApplication.positionLevel,
          examSubject: examApplication.kbId,
          enterpriseId: examRecord.enterpriseId,
          status: "必考"
        }
      });
    }

    if (!examConfig) {
      console.log('examConfig',JSON.stringify(examApplication));
      console.log('未找到对应的练考配置');
      return;
    }

    // 获取该申请的所有考试记录
    const examRecords = await ExamRecord.findAll({
      where: {
        reviewApplicationId: examRecord.reviewApplicationId,
        examStatus: 'completed',
        delFlag: false
      }
    });

    // 检查是否有合格的考试记录
    let hasPassedExam = false;
    for (const record of examRecords) {

      const score = (record.score || 0);

      if (score >= examConfig.passScore) {
        hasPassedExam = true;
        break;
      }
    }

    // 如果有合格的考试记录，更新考试申请状态为待审核(1)
    if (hasPassedExam) {
      await examApplication.update({
        scoreConfirmStatus: '1', // 1表示待审核
        updatedAt: new Date()
      });

      console.log(`已自动更新考试申请(ID: ${examApplication.id})的状态为待审核，因为有考试已达到合格分数`);
      return;
    }

    // 检查是否达到考试次数上限
    if (examRecords.length >= examConfig.examCount) {
      // 检查是否所有考试都不合格
      const allFailed = examRecords.every(record => {

        const score =  record.score || 0;
        console.log('score',JSON.stringify(record));
        console.log('examConfig.passScore',examConfig.passScore);
        return score < examConfig.passScore;
      });

      if (allFailed) {
        const now = new Date();
        const confirmedAt = now.getFullYear() + '-' +
                        String(now.getMonth() + 1).padStart(2, '0') + '-' +
                        String(now.getDate()).padStart(2, '0') + ' ' +
                        String(now.getHours()).padStart(2, '0') + ':' +
                        String(now.getMinutes()).padStart(2, '0') + ':' +
                        String(now.getSeconds()).padStart(2, '0');
        // 更新考试申请状态为不通过
        await examApplication.update({
          scoreConfirmStatus: '3', // 3表示不通过
          updatedAt: new Date(),
          confirmInfo: {
            confirmComments: '全部考试全部不合格，系统自动驳回',
            confirmedAt: confirmedAt,
            confirmedBy: 'system',
            confirmedByName: '系统'
          }
        });

        console.log(`已自动更新考试申请(ID: ${examApplication.id})的状态为不通过`);

        // 同时更新所有考试记录的confirmStatus为3
        for (const record of examRecords) {
          await record.update({
            confirmStatus: 3
          });
          console.log(`已更新考试记录(ID: ${record.id})的confirmStatus为3（不通过）`);
        }
      }
    }
  } catch (error) {
    console.error('检查并更新考试申请状态时出错:', error);
  }
};

/**
 * 获取用户的考试记录
 * 按照通过和未通过分类（通过判断分数是否大于等于通过分数）
 * 支持分页和按状态过滤
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getUserExamRecords = async (req, res) => {
  try {
    const openId = req.headers.openid; // 从请求头获取openId

    // 获取分页参数
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const category = req.query.category || 'all'; // all, passed, failed, pending
    const positionId = req.query.positionId; // 岗位id查询条件

    // 验证分页参数
    if (page < 1) {
      return res.status(400).json({
        code: 400,
        message: '页码必须大于0',
        data: null
      });
    }

    if (pageSize < 1 || pageSize > 100) {
      return res.status(400).json({
        code: 400,
        message: '每页记录数必须在1-100之间',
        data: null
      });
    }

    if (!openId) {
      return res.status(400).json({
        code: 400,
        message: '参数不完整',
        data: null
      });
    }

    // 计算偏移量
    const offset = (page - 1) * pageSize;

    // 1. 根据openId查找用户
    const user = await User.findOne({
      where: { 
        openId, 
        status: true,
        enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
      }
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    const enterpriseId = DEFAULT_ENTERPRISE_ID;

    // 2. 查找用户在指定知识库下的所有考试审核申请
    const reviewApplicationWhere = {
      createdBy: user.id,
      enterpriseId,
      delFlag: false
    };

    // 如果传入了岗位id，添加查询条件
    if (positionId) {
      reviewApplicationWhere.positionName = positionId;
    }

    const reviewApplications = await ExamReviewApplication.findAll({
      where: reviewApplicationWhere,
      attributes: ['id']
    });

    if (reviewApplications.length === 0) {
      return res.json({
        code: 200,
        message: '暂无考试记录',
        data: {
          records: [],
          pagination: {
            page: page,
            pageSize: pageSize,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false
          },
          statistics: {
            totalExamCount: 0,
            passedCount: 0,
            failedCount: 0,
            pendingCount: 0
          },
          category: category,
          // 为了向后兼容，保留原有字段
          passedExams: [],
          failedExams: [],
          pendingExams: [],
          totalExamCount: 0,
          allExams: []
        }
      });
    }

    // 获取所有审核申请的ID
    const applicationIds = reviewApplications.map(app => app.id);

    // 构建查询条件
    const whereCondition = {
      reviewApplicationId: {
        [Op.in]: applicationIds
      },
      examineeId: user.id,
      enterpriseId,
      delFlag: 0,
      examStatus: 'completed' // 只获取已完成的考试
    };

    // 如果传入了岗位id，添加ExamRecord的positionId查询条件
    if (positionId) {
      whereCondition.positionId = positionId;
    }

    // 根据category添加过滤条件
    if (category !== 'all') {
      switch (category) {
        case 'passed':
          whereCondition.confirmStatus = { [Op.ne]: '3' };
          break;
        case 'failed':
          whereCondition.confirmStatus = '3';
          break;
        case 'pending':
          whereCondition.confirmStatus = '1';
          break;
      }
    }

    // 3. 查找考试记录（带分页）
    const { count, rows: examRecords } = await ExamRecord.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Level,
          as: 'level',
          attributes: ['name'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name'],
          required: false
        }
      ],
      order: [['examTime', 'DESC']], // 按考试时间降序排列
      limit: pageSize,
      offset: offset
    });

    // 4. 处理考试记录数据
    const processExamRecords = (records) => {
      return records.map(record => {
        // 格式化日期
        const examDate = moment(record.examTime).format('YYYY-MM-DD HH:mm:ss');

        // 获取考试内容，计算题目总数和回答总数
        let questionTotal = 0;
        let answerTotal = 0;
        let correctAnswerCount = 0;

        if (record.examContent) {
          // 如果examContent存在且是一个数组，直接获取其长度作为题目和回答的数量
          if (Array.isArray(record.examContent)) {
            answerTotal = record.examContent.length;
             // 统计result为true的数量
             correctAnswerCount = record.examContent.filter(item => item.result === true).length;
          }
          // 如果examContent是对象且有questions属性
          else if (record.examContent.questions && Array.isArray(record.examContent.questions)) {
            answerTotal = record.examContent.questions.length;
            // 统计result为true的数量
            correctAnswerCount = record.examContent.questions.filter(item => item.result === true).length;
          }
        }

        // 修改questionTotal的赋值逻辑
        questionTotal = record.questionNumber ? parseInt(record.questionNumber) : 0;

        return {
          id: record.id,
          examSubject: record.examSubject,
          examTime: examDate,
          score: record.score,
          passScore: record.passScore,
          examStatus: record.examStatus,
          usedDuration: record.usedDuration,
          examDuration: record.examDuration,
          confirmStatus: record.confirmStatus,
          // 新增考试级别字段
          examLevel: record.level ? record.level.name : (record.levelId || '未知'),
          // 新增岗位信息字段
          positionId: record.positionName ? record.positionName.id : (record.positionId || ''),
          positionName: record.positionName ? record.positionName.name : (record.positionId || ''),
          // 修改题目总数和回答总数的赋值
          questionTotal: questionTotal,
          answerTotal: answerTotal,
          correctAnswerCount: correctAnswerCount
        };
      });
    };

    // 5. 处理当前页的考试记录
    const processedRecords = processExamRecords(examRecords);

    // 6. 获取统计数据（查询全部记录的统计信息，不受分页影响）
    const allExamRecordsWhere = {
      reviewApplicationId: {
        [Op.in]: applicationIds
      },
      examineeId: user.id,
      enterpriseId,
      delFlag: 0,
      examStatus: 'completed'
    };

    // 如果传入了岗位id，添加positionId查询条件
    if (positionId) {
      allExamRecordsWhere.positionId = positionId;
    }

    const allExamRecords = await ExamRecord.findAll({
      where: allExamRecordsWhere,
      attributes: ['id', 'confirmStatus', 'positionId', 'levelId'],
      include: [
        {
          model: Level,
          as: 'level',
          attributes: ['name'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name'],
          required: false
        }
      ],
      order: [['examTime', 'DESC']]
    });

    // 处理全部考试记录用于统计和向后兼容
    const allProcessedExams = processExamRecords(allExamRecords);

    const statistics = {
      totalExamCount: allExamRecords.length,
      passedCount: allExamRecords.filter(exam => exam.confirmStatus === '2').length,
      failedCount: allExamRecords.filter(exam => exam.confirmStatus === '3').length,
      pendingCount: allExamRecords.filter(exam => exam.confirmStatus === '1').length
    };

    // 7. 计算分页信息
    const totalPages = Math.ceil(count / pageSize);

    // 8. 为了向后兼容，提供按状态分类的完整数据
    const passedExams = allProcessedExams.filter(exam => exam.confirmStatus == '2');
    const failedExams = allProcessedExams.filter(exam => exam.confirmStatus == '3');
    const pendingExams = allProcessedExams.filter(exam => exam.confirmStatus == '1');

    return res.json({
      code: 200,
      message: '获取考试记录成功',
      data: {
        // 新的分页数据结构
        records: processedRecords,
        pagination: {
          page: page,
          pageSize: pageSize,
          total: count,
          totalPages: totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        statistics: statistics,
        category: category,

        // 为了向后兼容，保留原有字段结构
        passedExams: passedExams,
        failedExams: failedExams,
        pendingExams: pendingExams,
        totalExamCount: statistics.totalExamCount,
        allExams: allProcessedExams
      }
    });
  } catch (error) {
    console.error('获取用户考试记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
};

/**
 * 继续未完成的考试
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const continueExam = async (req, res) => {
  try {
    const { examId } = req.query;
    const openId = req.headers.openid; // 从请求头获取openId

    // 验证必要参数
    if (!examId) {
      return res.status(400).json({
        code: 400,
        message: '缺少考试ID参数',
        data: null
      });
    }

    // 查找用户
    const user = await User.findOne({
      where: { 
        openId, 
        status: true,
        enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
      }
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    const enterpriseId = DEFAULT_ENTERPRISE_ID;

    // 查找考试记录
    const examRecord = await ExamRecord.findOne({
      where: {
        id: examId,
        enterpriseId,
        delFlag: 0
      }
    });

    if (!examRecord) {
      return res.status(404).json({
        code: 404,
        message: '考试记录不存在',
        data: null
      });
    }

    // 检查考试状态
    if (examRecord.examStatus !== 'ongoing') {
      return res.status(400).json({
        code: 400,
        message: '该考试已完成，无法继续',
        data: {
          examFinished: true,
          examStatus: examRecord.examStatus
        }
      });
    }

    // 检查考试时间是否已结束
    const now = moment();
    const endTimeObj = moment(examRecord.endTime, 'YYYY-MM-DD HH:mm:ss');

    if (now.isAfter(endTimeObj)) {
      // 考试时间已结束，更新考试状态为已完成
      await ExamRecord.update({
        examStatus: 'completed'
      }, {
        where: {
          id: examId,
          enterpriseId: examRecord.enterpriseId
        }
      });

      return res.status(400).json({
        code: 400,
        message: '考试时间已结束',
        data: {
          examFinished: true,
          timeoutExam: true
        }
      });
    }

    // 计算剩余时间（分钟）
    const remainingMinutes = Math.ceil(endTimeObj.diff(now, 'minutes', true));

    // 准备返回数据
    // 从examContent中去除答案字段，避免前端获取到正确答案
    const processedExamContent = examRecord.examContent ? examRecord.examContent.map(item => {
      // 创建一个新对象，只包含需要的字段
      const processedItem = {
        questionId: item.questionId,
        originalQuestionId: item.originalQuestionId,
        question: item.question,
        type: item.type,
        userAnswer: item.userAnswer || '',
        timestamp: item.timestamp
      };
      // 不包含answer字段
      return processedItem;
    }) : [];

    // 返回考试信息
    return res.json({
      code: 200,
      message: '获取考试信息成功',
      data: {
        examId: examRecord.id,
        examSubject: examRecord.examSubject,
        examStatus: examRecord.examStatus,
        remainingMinutes: remainingMinutes,
        endTime: examRecord.endTime,
        examDuration: examRecord.examDuration,
        questionNumber: examRecord.questionNumber,
        completedQuestions: processedExamContent.filter(q => q.userAnswer).length,
        totalQuestions: examRecord.questionNumber || processedExamContent.length,
        passScore: examRecord.passScore,
        examContent: processedExamContent
      }
    });
  } catch (error) {
    console.error('继续考试失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
};

module.exports = {
  getRandomQuestion,
  submitAnswer,
  resetExamQuestions,
  createExamRecordFromMiniapp,
  getExamReport,
  getUserExamRecords,
  continueExam,
  updateExamStatus,
  getScoreApprovalStatus,
  calculateExamScore,
  formatExamContentForReport,
  generateExamReportFromAPI,
  updateExamReportData,
  generateExamReportResponse,
  calculateUsedDuration
};
