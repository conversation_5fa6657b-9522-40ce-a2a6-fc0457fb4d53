// 测试日期处理修复
const { handlePracticeAchievementTrigger } = require('./src/utils/achievementUtils');

// 模拟请求和响应
const mockReq = {
  body: {
    time: '45:30',
    positionName: 1,
    positionLevel: 1,
    file_id: 1
  },
  headers: {
    openid: 'test_open_id_001'
  }
};

const mockResponseBody = {
  code: 200,
  message: '成功',
  data: {}
};

console.log('开始测试日期处理修复...');

// 测试练习成就触发
handlePracticeAchievementTrigger(mockReq, mockResponseBody)
  .then(() => {
    console.log('✅ 日期处理修复测试完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ 日期处理修复测试失败:', error);
    process.exit(1);
  }); 