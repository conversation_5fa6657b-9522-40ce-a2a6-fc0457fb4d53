// 菜单数据模型
const menuData = [
  {
    id: 1,
    name: '系统管理',
    icon: 'SettingOutlined',
    sort: 1,
    path: '/system',
    type: 'M',
    status: true,
    children: [
      {
        id: 2,
        parentId: 1,
        name: '用户管理',
        icon: 'UserOutlined',
        sort: 1,
        path: '/system/user',
        component: '/system/user/index',
        type: 'C',
        permission: 'system:user:list',
        status: true
      },
      {
        id: 3,
        parentId: 1,
        name: '角色管理',
        icon: 'TeamOutlined',
        sort: 2,
        path: '/system/role',
        component: '/system/role/index',
        type: 'C',
        permission: 'system:role:list',
        status: true
      },
      {
        id: 4,
        parentId: 1,
        name: '菜单管理',
        icon: 'MenuOutlined',
        sort: 3,
        path: '/system/menu',
        component: '/system/menu/index',
        type: 'C',
        permission: 'system:menu:list',
        status: true
      }
    ]
  }
];

// 获取菜单列表
exports.getMenuList = () => {
  return menuData;
};

// 创建菜单
exports.createMenu = (menu) => {
  const newMenu = {
    ...menu,
    id: Date.now()
  };
  
  if (menu.parentId) {
    const addToParent = (array, parentId) => {
      for (const item of array) {
        if (item.id === parentId) {
          if (!item.children) {
            item.children = [];
          }
          item.children.push(newMenu);
          return true;
        }
        if (item.children && addToParent(item.children, parentId)) {
          return true;
        }
      }
      return false;
    };
    
    addToParent(menuData, menu.parentId);
  } else {
    menuData.push(newMenu);
  }
  
  return newMenu;
};

// 更新菜单
exports.updateMenu = (menu) => {
  const updateInArray = (array) => {
    for (let i = 0; i < array.length; i++) {
      if (array[i].id === menu.id) {
        array[i] = { ...array[i], ...menu };
        return array[i];
      }
      if (array[i].children) {
        const result = updateInArray(array[i].children);
        if (result) return result;
      }
    }
    return null;
  };
  
  return updateInArray(menuData);
};

// 删除菜单
exports.deleteMenu = (id) => {
  const removeFromArray = (array, targetId) => {
    const index = array.findIndex(item => item.id === targetId);
    if (index > -1) {
      const removed = array.splice(index, 1)[0];
      return removed;
    }
    for (const item of array) {
      if (item.children) {
        const removed = removeFromArray(item.children, targetId);
        if (removed) return removed;
      }
    }
    return null;
  };
  
  return removeFromArray(menuData, id);
}; 