const PositionType = require('../../models/PositionType');
const PositionName = require('../../models/PositionName');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../../utils/enterpriseFilter');

/**
 * 获取岗位类型列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionTypeList = async (req, res) => {
  try {
    const { name, status, pageNum = 1, pageSize = 10 } = req.query;
    const where = {};
    
    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }
    if (status !== undefined) {
      where.status = status === 'true' || status === '1';
    }

    const { count, rows } = await PositionType.findAndCountAll(
      addEnterpriseFilter({
        where,
        // offset: (pageNum - 1) * pageSize,
        // limit: parseInt(pageSize),
        order: [
          ['sort', 'ASC'],
          ['id', 'ASC']
        ]
      })
    );

    res.json({
      code: 200,
      data: {
        total: count,
        rows,
        // pageNum: parseInt(pageNum),
        // pageSize: parseInt(pageSize)
      },
      message: '获取岗位类型列表成功'
    });
  } catch (error) {
    console.error('获取岗位类型列表失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位类型列表失败',
      error: error.message
    });
  }
};

/**
 * 新增岗位类型
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.addPositionType = async (req, res) => {
  try {
    const { name, code, status, sort, remark } = req.body;

    // 校验必填字段
    if (!name || !code) {
      return res.status(400).json({
        code: 400,
        message: '类型名称和编码不能为空'
      });
    }

    // 校验编码是否已存在
    const existCode = await PositionType.findOne(
      addEnterpriseFilter({
        where: { code }
      })
    );

    if (existCode) {
      return res.status(400).json({
        code: 400,
        message: '类型编码已存在'
      });
    }

    const positionType = await PositionType.create(
      addEnterpriseId({
        name,
        code,
        status: status === undefined ? true : status,
        sort: sort || 0,
        remark,
        createBy: req.user?.username || 'admin'
      })
    );

    res.json({
      code: 200,
      data: positionType,
      message: '新增岗位类型成功'
    });
  } catch (error) {
    console.error('新增岗位类型失败：', error);
    res.status(500).json({
      code: 500,
      message: '新增岗位类型失败',
      error: error.message
    });
  }
};

/**
 * 更新岗位类型
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updatePositionType = async (req, res) => {
  try {
    const { id, name, code, status, sort, remark } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '类型ID不能为空'
      });
    }

    const positionType = await PositionType.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!positionType) {
      return res.status(404).json({
        code: 404,
        message: '岗位类型不存在'
      });
    }

    // 校验编码是否已存在
    if (code && code !== positionType.code) {
      const existCode = await PositionType.findOne(
        addEnterpriseFilter({
          where: {
            code,
            id: { [Op.ne]: id }
          }
        })
      );

      if (existCode) {
        return res.status(400).json({
          code: 400,
          message: '类型编码已存在'
        });
      }
    }

    await positionType.update({
      name,
      code,
      status,
      sort,
      remark,
      updateBy: req.user?.username || 'admin'
    });

    res.json({
      code: 200,
      data: positionType,
      message: '更新岗位类型成功'
    });
  } catch (error) {
    console.error('更新岗位类型失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新岗位类型失败',
      error: error.message
    });
  }
};

/**
 * 删除岗位类型
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deletePositionType = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查是否有关联的岗位名称
    const hasNames = await PositionName.findOne({
      where: { typeId: id }
    });

    if (hasNames) {
      return res.status(400).json({
        code: 400,
        message: '该类型下存在岗位名称，无法删除'
      });
    }

    await PositionType.destroy(
      addEnterpriseFilter({
        where: { id }
      })
    );

    res.json({
      code: 200,
      message: '删除岗位类型成功'
    });
  } catch (error) {
    console.error('删除岗位类型失败：', error);
    res.status(500).json({
      code: 500,
      message: '删除岗位类型失败',
      error: error.message
    });
  }
};

/**
 * 获取岗位类型选项
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionTypeOptions = async (req, res) => {
  try {
    const types = await PositionType.findAll(
      addEnterpriseFilter({
        where: { status: true },
        attributes: ['id', 'name', 'code'],
        order: [
          ['sort', 'ASC'],
          ['id', 'ASC']
        ]
      })
    );

    res.json({
      code: 200,
      data: types,
      message: '获取岗位类型选项成功'
    });
  } catch (error) {
    console.error('获取岗位类型选项失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位类型选项失败',
      error: error.message
    });
  }
}; 