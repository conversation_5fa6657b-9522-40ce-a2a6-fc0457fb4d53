const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

// 字典类型表
const DictionaryType = sequelize.define('dictionary_type', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  typeCode: {
    type: DataTypes.STRING(100),
    field: 'type_code',
    allowNull: false,
    unique: true,
    comment: '字典类型编码'
  },
  typeName: {
    type: DataTypes.STRING(100),
    field: 'type_name',
    allowNull: false,
    comment: '字典类型名称'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态（true-正常，false-停用）'
  },
  remark: {
    type: DataTypes.STRING(500),
    comment: '备注'
  },
  createBy: {
    type: DataTypes.STRING(64),
    field: 'create_by',
    comment: '创建者'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    field: 'update_by',
    comment: '更新者'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'dictionary_type',
  timestamps: false
});

// 字典数据表
const DictionaryData = sequelize.define('dictionary_data', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  typeId: {
    type: DataTypes.INTEGER,
    field: 'type_id',
    allowNull: false,
    comment: '字典类型ID'
  },
  dictLabel: {
    type: DataTypes.STRING(100),
    field: 'dict_label',
    allowNull: false,
    comment: '字典标签'
  },
  dictValue: {
    type: DataTypes.STRING(100),
    field: 'dict_value',
    allowNull: false,
    comment: '字典键值'
  },
  dictSort: {
    type: DataTypes.INTEGER,
    field: 'dict_sort',
    defaultValue: 0,
    comment: '显示顺序'
  },
  cssClass: {
    type: DataTypes.STRING(100),
    field: 'css_class',
    comment: 'CSS样式'
  },
  listClass: {
    type: DataTypes.STRING(100),
    field: 'list_class',
    comment: '表格回显样式'
  },
  isDefault: {
    type: DataTypes.BOOLEAN,
    field: 'is_default',
    defaultValue: false,
    comment: '是否默认（true-是，false-否）'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态（true-正常，false-停用）'
  },
  remark: {
    type: DataTypes.STRING(500),
    comment: '备注'
  },
  createBy: {
    type: DataTypes.STRING(64),
    field: 'create_by',
    comment: '创建者'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    field: 'update_by',
    comment: '更新者'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'dictionary_data',
  timestamps: false
});

// 设置关联关系
DictionaryType.hasMany(DictionaryData, { foreignKey: 'typeId' });
DictionaryData.belongsTo(DictionaryType, { foreignKey: 'typeId' });

module.exports = {
  DictionaryType,
  DictionaryData
}; 