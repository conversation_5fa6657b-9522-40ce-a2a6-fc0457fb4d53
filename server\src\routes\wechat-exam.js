/**
 * 微信小程序考试相关路由
 */
const express = require('express');
const router = express.Router();
const wechatExamController = require('../controllers/weichat/wechatExamStartController');

// 创建考试记录
router.post('/create-exam', wechatExamController.createExamRecordFromMiniapp);

// 从题库中抽取随机题目
router.post('/questions/random', wechatExamController.getRandomQuestion);

// 提交题目回答
router.post('/questions/answer', wechatExamController.submitAnswer);

// 重置考试已使用题目记录
router.delete('/questions/reset/:examId', wechatExamController.resetExamQuestions);

// 获取考试报告
router.post('/report', wechatExamController.getExamReport);

// 获取用户的考试记录（通过和未通过分类）
router.get('/user-exams', wechatExamController.getUserExamRecords);

// 继续未完成的考试
router.get('/continue-exam', wechatExamController.continueExam);

module.exports = router; 