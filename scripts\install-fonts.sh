#!/bin/bash

# 中文字体安装脚本
# 用于解决 Linux 系统证书生成中文乱码问题

echo "开始安装中文字体..."

# 检测操作系统类型
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
elif type lsb_release >/dev/null 2>&1; then
    OS=$(lsb_release -si)
    VER=$(lsb_release -sr)
elif [ -f /etc/lsb-release ]; then
    . /etc/lsb-release
    OS=$DISTRIB_ID
    VER=$DISTRIB_RELEASE
elif [ -f /etc/debian_version ]; then
    OS=Debian
    VER=$(cat /etc/debian_version)
elif [ -f /etc/SuSe-release ]; then
    OS=openSUSE
elif [ -f /etc/redhat-release ]; then
    OS=RedHat
else
    OS=$(uname -s)
    VER=$(uname -r)
fi

echo "检测到操作系统: $OS $VER"

# 根据不同的操作系统安装字体
case $OS in
    "Ubuntu"*|"Debian"*)
        echo "在 Ubuntu/Debian 系统上安装字体..."
        sudo apt-get update
        sudo apt-get install -y fonts-noto-cjk fonts-wqy-microhei fonts-wqy-zenhei
        ;;
    "CentOS"*|"Red Hat"*|"RedHat"*|"Fedora"*)
        echo "在 CentOS/RHEL/Fedora 系统上安装字体..."
        if command -v dnf &> /dev/null; then
            sudo dnf install -y google-noto-sans-cjk-fonts wqy-microhei-fonts
        else
            sudo yum install -y google-noto-sans-cjk-fonts wqy-microhei-fonts
        fi
        ;;
    "openSUSE"*)
        echo "在 openSUSE 系统上安装字体..."
        sudo zypper install -y google-noto-sans-cjk-fonts wqy-microhei-fonts
        ;;
    "Arch"*)
        echo "在 Arch Linux 系统上安装字体..."
        sudo pacman -S --noconfirm noto-fonts-cjk wqy-microhei
        ;;
    "Alpine"*)
        echo "在 Alpine Linux 系统上安装字体..."
        sudo apk add --no-cache font-noto-cjk font-wqy-microhei
        ;;
    *)
        echo "未识别的操作系统: $OS"
        echo "请手动安装中文字体包"
        echo "常用的中文字体包名称:"
        echo "- fonts-noto-cjk"
        echo "- fonts-wqy-microhei"
        echo "- google-noto-sans-cjk-fonts"
        exit 1
        ;;
esac

# 检查字体是否安装成功
echo "检查字体安装状态..."

# 刷新字体缓存
if command -v fc-cache &> /dev/null; then
    sudo fc-cache -fv
fi

# 检查是否有中文字体
if command -v fc-list &> /dev/null; then
    echo "已安装的中文字体:"
    fc-list :lang=zh | head -5
    
    if fc-list :lang=zh | grep -q .; then
        echo "✅ 中文字体安装成功!"
    else
        echo "❌ 未检测到中文字体，可能安装失败"
        exit 1
    fi
else
    echo "⚠️  无法检测字体状态（缺少 fontconfig 工具）"
fi

echo "字体安装完成！"
echo "现在可以重启应用程序以使用新安装的字体。" 