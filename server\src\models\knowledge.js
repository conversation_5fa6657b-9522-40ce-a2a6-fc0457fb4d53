const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Knowledge = sequelize.define('knowledge', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '知识库名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '描述'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'enterprise_id',
    comment: '所属企业ID'
  },
  documentCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'document_count',
    comment: '文档数量'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态：0禁用，1正常'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'knowledge_bases'
});

module.exports = Knowledge; 