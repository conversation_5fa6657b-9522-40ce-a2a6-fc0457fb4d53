const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 岗位等级模型
 */
const Level = sequelize.define('Level', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '等级ID'
  },
  enterpriseId: {
    type: DataTypes.STRING(36),
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: '1',
    comment: '企业ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '等级名称'
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '等级编码'
  },
  orderNum: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'order_num',
    comment: '等级顺序，越小越靠前'
  },
  remark: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '备注'
  },
  operatorId: {
    type: DataTypes.INTEGER,
    field: 'operator_id',
    allowNull: true,
    comment: '操作人ID，关联用户表的id'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'org_level',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

module.exports = Level; 
