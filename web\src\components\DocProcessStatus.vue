<template>
  <div class="doc-process-status">
    <div class="progress-container">
      <div class="progress-track">
        <div class="progress-line">
          <div class="line-completed" :style="{ width: `${progressBarWidth}%` }"></div>
        </div>
        <div class="progress-nodes">
          <div 
            v-for="(node, index) in progressNodes" 
            :key="index"
            class="progress-node"
            :class="{ 
              'active': status.status === 'completed' ? true : index <= currentNodeIndex,
              'failed': status.status === 'failed' && index === currentNodeIndex
            }"
          >
            <a-tooltip v-if="status.status === 'failed' && index === currentNodeIndex">
              <template #title>处理失败，请重新出题</template>
                <div class="node-dot">
                  <close-outlined />
                </div>
            </a-tooltip>
            <div class="node-dot" v-else>
              <check-outlined v-if="status.status === 'completed' ? true : index <= currentNodeIndex" />
            </div>
            <div class="node-label">
              <template v-if="status.status === 'failed' && index === currentNodeIndex">
                未完成
              </template>
              <template v-else>
                {{ node.label }}
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { getDocumentProcessStatus } from '@/api/knowledge-base';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  docId: {
    type: String,
    required: true
  },
  autoRefresh: {
    type: Boolean,
    default: true
  },
  refreshInterval: {
    type: Number,
    default: 5000 // 默认5秒刷新一次
  },
  // 新增属性：初始状态，用于传入列表已知的状态，避免不必要的请求
  initialStatus: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['status-change']);

const status = ref({
  id: props.docId,
  status: 'waiting',
  progress: 0,
  message: '等待处理'
});
const timerInterval = ref(null);
const isLoading = ref(false);
// 标记是否已停止查询
const hasStopped = ref(false);

// 跟踪请求错误次数
const errorCount = ref(0);
const MAX_ERROR_COUNT = 3; // 最大错误次数

// 当接收到初始状态时，立即设置状态
onMounted(() => {
  if (props.initialStatus) {
    status.value = props.initialStatus;
    
    // 如果初始状态为已完成或失败，则不启动自动刷新
    if (isFinished.value) {
      hasStopped.value = true;
      emit('status-change', status.value);
    } else if (props.autoRefresh) {
      // 否则启动自动刷新
      startAutoRefresh();
    }
  } else {
    // 没有初始状态时，正常获取状态
    fetchStatus();
    if (props.autoRefresh) {
      startAutoRefresh();
    }
  }
});

// 进度节点定义
const progressNodes = [
  { key: 'preparing', label: '开始', failedLabel: '未完成' },
  { key: 'embedding', label: '上传中', failedLabel: '未完成' },
  { key: 'indexing', label: '索引中', failedLabel: '未完成' },
  { key: 'completed', label: '已完成', failedLabel: '未完成' }
];

// 当前节点索引
const currentNodeIndex = computed(() => {
  const statusIndex = {
    'waiting': 0,
    'preparing': 0,
    'embedding': 1,
    'indexing': 2,
    'generating': 2, // 将"出题中"状态映射到"索引中"的索引位置
    'completed': 3,
    'failed': 3 // 失败时也显示在相应节点，但会标红
  };
  return statusIndex[status.value.status] || 0;
});

// 进度条宽度计算
const progressBarWidth = computed(() => {
  if (status.value.status === 'completed') {
    return 100; // 完成状态返回100%
  }
  
  if (status.value.status === 'failed') {
    return (currentNodeIndex.value + 1) / progressNodes.length * 100; // 失败时进度停在当前节点
  }
  
  // 根据当前节点计算进度条宽度
  const maxIndex = progressNodes.length - 1;
  const baseWidth = (currentNodeIndex.value / maxIndex) * 100;
  
  // 如果有进度信息，计算当前段内的进度
  if (status.value.progress) {
    // 每个段的宽度
    const segmentWidth = 100 / maxIndex;
    // 当前段内的进度宽度
    const progressInSegment = (status.value.progress / 100) * segmentWidth;
    
    return Math.min(baseWidth + progressInSegment, 100);
  }
  
  return baseWidth;
});

// 判断是否完成或失败，用于停止自动刷新
const isFinished = computed(() => {
  return status.value.status === 'completed' || status.value.status === 'failed';
});

// 获取状态
const fetchStatus = async () => {
  // 如果已标记停止或正在加载，则不执行
  if (hasStopped.value || isLoading.value) return;
  
  isLoading.value = true;
  try {
    const response = await getDocumentProcessStatus(props.docId);
    
    // 处理各种状态返回情况
    if (response === null || 
        (response && response.code === 404) || 
        (response && response.message && response.message.includes('未找到文档处理状态')) ||
        (response && response.message && response.message.includes('未找到文档'))) {
      // 设置为完成状态
      status.value = {
        id: props.docId,
        status: 'completed',
        progress: 100,
        message: '处理完成',
        updatedAt: new Date().toISOString()
      };
      
      // 标记已停止
      hasStopped.value = true;
      
      // 通知父组件状态变化
      emit('status-change', status.value);
      
      // 停止自动刷新
      if (props.autoRefresh) {
        stopAutoRefresh();
      }
    } else if (response) {
      // 正常响应处理
      status.value = response;
      
      // 如果状态是完成或失败，停止自动刷新并标记已停止
      if (isFinished.value) {
        hasStopped.value = true;
        if (props.autoRefresh) {
          stopAutoRefresh();
        }
        emit('status-change', status.value); // 通知父组件状态变化
      }
      
      // 重置错误计数
      errorCount.value = 0;
    }
  } catch (error) {
    console.error('获取文档状态失败:', error);
    
    // 增加错误计数
    errorCount.value++;
    
    // 如果错误次数达到阈值，停止请求
    if (errorCount.value >= MAX_ERROR_COUNT) {
      console.log(`已达到最大错误次数(${MAX_ERROR_COUNT})，停止状态查询`);
      hasStopped.value = true;
      stopAutoRefresh();
      
      // 设置一个默认值作为最终状态
      status.value = {
        id: props.docId,
        status: 'failed',
        progress: 0,
        message: '状态查询失败',
        updatedAt: new Date().toISOString()
      };
      
      emit('status-change', status.value);
    }
    
    // 如果是404错误（未找到文档处理状态），则默认为上传成功
    if (error.response && error.response.status === 404 || 
        (error.message && error.message.includes('404')) ||
        (error.response && error.response.data && error.response.data.message && 
         error.response.data.message.includes('未找到文档处理状态'))) {
      // 设置为完成状态
      status.value = {
        id: props.docId,
        status: 'completed',
        progress: 100,
        message: '处理完成',
        updatedAt: new Date().toISOString()
      };
      
      // 标记已停止
      hasStopped.value = true;
      
      // 通知父组件状态变化
      emit('status-change', status.value);
      
      // 停止自动刷新
      if (props.autoRefresh) {
        stopAutoRefresh();
      }
    }
  } finally {
    isLoading.value = false;
  }
};

// 开始自动刷新
const startAutoRefresh = () => {
  // 如果已标记停止，则不启动刷新
  if (hasStopped.value) return;
  
  stopAutoRefresh(); // 确保之前的计时器被清理
  timerInterval.value = setInterval(() => {
    if (!isFinished.value && !hasStopped.value) {
      fetchStatus();
    } else {
      stopAutoRefresh();
    }
  }, props.refreshInterval);
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
};

// 手动刷新状态
const refresh = () => {
  // 如果已标记停止，则不刷新
  if (hasStopped.value) return;
  fetchStatus();
};

// 监听docId变化，如果发生变化则重新获取状态
watch(() => props.docId, (newDocId) => {
  if (newDocId) {
    // 重置状态
    errorCount.value = 0;
    hasStopped.value = false;
    
    // 如果有新的初始状态，使用它
    if (props.initialStatus) {
      status.value = props.initialStatus;
      
      // 如果初始状态为已完成或失败，则不启动自动刷新
      if (isFinished.value) {
        hasStopped.value = true;
        emit('status-change', status.value);
      } else if (props.autoRefresh) {
        // 否则启动自动刷新
        startAutoRefresh();
      }
    } else {
      // 没有初始状态，正常获取
      fetchStatus();
      if (props.autoRefresh) {
        startAutoRefresh();
      }
    }
  }
});

// 监听初始状态变化
watch(() => props.initialStatus, (newStatus) => {
  if (newStatus) {
    status.value = newStatus;
    
    // 重置错误计数和停止标志
    errorCount.value = 0;
    hasStopped.value = false;
    
    // 如果新状态为已完成或失败，则停止自动刷新
    if (isFinished.value) {
      hasStopped.value = true;
      stopAutoRefresh();
      emit('status-change', status.value);
    } else if (props.autoRefresh) {
      // 如果新状态不是完成状态，重新启动自动刷新
      startAutoRefresh();
    }
  }
});

// 组件销毁前停止自动刷新
onBeforeUnmount(() => {
  stopAutoRefresh();
});
</script>

<style scoped>
.doc-process-status {
  margin: 12px 0;
  width: 100%;
}

.progress-container {
  padding: 0 12px;
}

.progress-track {
  position: relative;
  height: 60px; /* 增加高度以容纳标签 */
  display: flex;
  align-items: flex-start; /* 改为顶部对齐 */
  margin-bottom: 5px;
}

.progress-line {
  position: absolute;
  height: 2px;
  background-color: #e8e8e8;
  top: 16px;
  left: 10px;
  right: 10px;
  z-index: 1;
}

.line-completed {
  position: absolute;
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.progress-nodes {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  z-index: 2;
  padding-top: 6px; /* 确保节点位于线上的中心 */
}

.progress-node {
  display: flex;
  flex-direction: column; /* 改为纵向排列 */
  align-items: center; /* 水平居中 */
  position: relative;
}

.node-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
}

.progress-node.active .node-dot {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
}

.node-label {
  font-size: 11px;
  color: rgba(0, 0, 0, 0.65);
  text-align: center;
  margin-top: 5px;
  white-space: nowrap;
}



.line-failed {
  background-color: #ff4d4f; /* 使用红色表示失败 */
}

.progress-node.failed .node-dot {
  background-color: #ff4d4f; /* 红色背景 */
  border-color: #ff4d4f; /* 红色边框 */
  color: #fff;
}

.progress-node.failed .node-label {
  color: #ff4d4f; /* 红色文字 */
}

.node-dot {
  width: 24px; /* 增大节点大小 */
  height: 24px; /* 增大节点大小 */
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #e8e8e8; /* 增加边框粗细 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px; /* 增大图标大小 */
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}

.progress-line {
  height: 3px; /* 增加线条粗细 */
  border-radius: 2px; /* 添加圆角 */
}

.progress-node.failed .node-dot {
  animation: pulse 1.5s infinite; /* 添加脉冲动画效果 */
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 5px rgba(255, 77, 79, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

</style> 