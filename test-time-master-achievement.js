const { setupAchievementEventListeners } = require('./server/src/utils/achievementEventListener');
const { processTimeMasterAchievement } = require('./server/src/utils/achievementUtils');
const PracticeRecord = require('./server/src/models/practice-record');
const { addEnterpriseId } = require('./server/src/utils/enterpriseFilter');
const { sequelize } = require('./server/src/config/database');

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.DEFAULT_ENTERPRISE_ID = '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32';

/**
 * 创建模拟练习记录
 * @param {string} openId - 用户openId
 * @param {Array<number>} hours - 要创建记录的小时数组
 * @param {string} enterpriseId - 企业ID
 */
async function createMockPracticeRecords(openId, hours, enterpriseId) {
  console.log(`开始创建${hours.length}个时间段的练习记录...`);
  
  const today = new Date();
  const positionName = '测试岗位';
  const positionLevel = '初级';
  
  // 删除之前的测试记录
  await PracticeRecord.destroy(
    addEnterpriseId({
      where: { openId }
    }, enterpriseId)
  );
  
  // 创建练习记录
  for (const hour of hours) {
    const recordDate = new Date(today);
    recordDate.setHours(hour, 0, 0);
    
    await PracticeRecord.create(
      addEnterpriseId({
        openId,
        questionNum: 10,
        totalDuration: '00:30:00',
        positionBelong: '1',
        positionName,
        positionLevel,
        examSubject: '1',
        status: '必练',
        createTime: recordDate,
        updateTime: recordDate
      }, enterpriseId)
    );
    
    console.log(`创建练习记录: 时间=${recordDate.toLocaleString()}`);
  }
  
  console.log(`成功创建${hours.length}个时间段的练习记录`);
}

/**
 * 测试碎片时间大师成就
 */
async function testTimeMasterAchievement() {
  try {
    console.log('⏰ 开始测试碎片时间大师成就...\n');

    // 1. 初始化成就事件监听器
    console.log('1️⃣ 初始化成就事件监听器...');
    setupAchievementEventListeners();

    // 2. 设置测试数据
    const testData = {
      userId: 1,
      openId: 'test_openid_time_master',
      enterpriseId: process.env.DEFAULT_ENTERPRISE_ID,
      positionName: '测试岗位',
      positionLevel: '初级'
    };

    console.log('2️⃣ 测试数据准备完成:');
    console.log(`   - 用户ID: ${testData.userId}`);
    console.log(`   - OpenID: ${testData.openId}`);
    console.log(`   - 企业ID: ${testData.enterpriseId}`);
    console.log(`   - 岗位名称: ${testData.positionName}`);
    console.log(`   - 岗位级别: ${testData.positionLevel}`);
    console.log('');

    // 3. 显示当前时间
    const now = new Date();
    console.log(`3️⃣ 当前时间: ${now.toLocaleString()}`);
    console.log(`   - 当前小时: ${now.getHours()}`);
    console.log('');

    // 4. 说明碎片时间大师成就的要求
    console.log('4️⃣ 碎片时间大师成就要求:');
    console.log('   - 一天内在三个不同时间范围都有练习记录');
    console.log('   - 默认时间范围:');
    console.log('     • 时间段1: 0:00 - 12:00 (上午)');
    console.log('     • 时间段2: 12:00 - 18:00 (下午)');
    console.log('     • 时间段3: 18:00 - 23:00 (晚上)');
    console.log('   - 需要在每个时间段至少有一条练习记录');
    console.log('');

    // 5. 创建模拟练习记录
    console.log('5️⃣ 创建模拟练习记录...');
    
    // 测试场景1: 只有一个时间段有记录
    console.log('\n测试场景1: 只有一个时间段有记录');
    await createMockPracticeRecords(testData.openId, [10], testData.enterpriseId);
    await processTimeMasterAchievement(
      testData.userId,
      testData.openId,
      testData.enterpriseId,
      testData.positionName,
      testData.positionLevel
    );
    
    // 测试场景2: 有两个时间段有记录
    console.log('\n测试场景2: 有两个时间段有记录');
    await createMockPracticeRecords(testData.openId, [10, 15], testData.enterpriseId);
    await processTimeMasterAchievement(
      testData.userId,
      testData.openId,
      testData.enterpriseId,
      testData.positionName,
      testData.positionLevel
    );
    
    // 测试场景3: 三个时间段都有记录
    console.log('\n测试场景3: 三个时间段都有记录');
    await createMockPracticeRecords(testData.openId, [10, 15, 20], testData.enterpriseId);
    await processTimeMasterAchievement(
      testData.userId,
      testData.openId,
      testData.enterpriseId,
      testData.positionName,
      testData.positionLevel
    );

    console.log('\n✅ 碎片时间大师成就测试完成！');
    
    // 关闭数据库连接
    await sequelize.close();

  } catch (error) {
    console.error('❌ 测试失败:', error);
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  testTimeMasterAchievement().catch(console.error);
}

module.exports = { testTimeMasterAchievement }; 