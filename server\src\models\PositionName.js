const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 岗位名称模型
 */
const PositionName = sequelize.define('PositionName', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  typeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'type_id',
    comment: '关联岗位类型ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '岗位名称'
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '岗位编码'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态（1正常 0停用）'
  },
  sort: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '排序'
  },
  remark: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '备注'
  },
  createBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'create_by',
    comment: '创建者'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'update_by',
    comment: '更新者'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'org_position_name',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

module.exports = PositionName; 