const { Op, Sequelize } = require('sequelize');
const Employee = require('../../models/Employee');
const Department = require('../../models/Department');
const PositionName = require('../../models/PositionName');
const PositionType = require('../../models/PositionType');
const Level = require('../../models/Level');
const Position = require('../../models/Position');
const PracticeRecord = require('../../models/practice-record');
const ExamRecord = require('../../models/ExamRecord');
const CertificateRecord = require('../../models/CertificateRecord');
const ExamConfig = require('../../models/ExamConfigModel');
const EmployeePosition = require('../../models/EmployeePosition');
const { addEnterpriseFilter } = require('../../utils/enterpriseFilter');
const XLSX = require('xlsx');

// 默认企业ID
const DEFAULT_ENTERPRISE_ID = process.env.DEFAULT_ENTERPRISE_ID || 1;

/**
 * 时长转换为分钟
 */
const convertDurationToMinutes = (duration) => {
  if (!duration) return 0;
  
  const parts = duration.split(':');
  
  // 处理 "HH:MM:SS" 格式
  if (parts.length === 3) {
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseInt(parts[2]) || 0;
    return hours * 60 + minutes + seconds / 60;
  }
  
  // 处理 "MM:SS" 格式（实际存储格式）
  if (parts.length === 2) {
    const minutes = parseInt(parts[0]) || 0;
    const seconds = parseInt(parts[1]) || 0;
    return minutes + seconds / 60;
  }
  
  // 处理纯数字格式（假设为分钟）
  const numericValue = parseFloat(duration);
  return isNaN(numericValue) ? 0 : numericValue;
};

/**
 * 获取部门路径（前两级）
 */
const getDepartmentPath = (departmentId, allDepartments) => {
  if (!departmentId || !allDepartments) return '';
  
  const currentDept = allDepartments.find(dept => dept.id === departmentId);
  if (!currentDept) return '';
  
  // 如果有父部门，返回 "父部门/当前部门"
  if (currentDept.parentId) {
    const parentDept = allDepartments.find(dept => dept.id === currentDept.parentId);
    if (parentDept) {
      return `${parentDept.name}/${currentDept.name}`;
    }
  }
  
  // 只返回当前部门名称
  return currentDept.name;
};

/**
 * 获取员工统计数据
 */
const getEmployeeStatistics = async (req, res) => {
  try {
    const enterpriseId = DEFAULT_ENTERPRISE_ID;
    console.log('使用企业ID:', enterpriseId);

    // 获取查询参数
    const {
      name = '',
      position,
      level,
      page = 1,
      pageSize = 10
    } = req.query;

    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 先获取所有部门数据用于构建部门路径
    const allDepartments = await Department.findAll(addEnterpriseFilter({
      attributes: ['id', 'name', 'parentId']
    }));

    // 构建员工查询条件
    let employeeWhere = {
      status: '1' // 在职员工
    };
    
    // 使用企业过滤工具添加企业ID过滤
    employeeWhere = addEnterpriseFilter({ where: employeeWhere }).where;

    if (name) {
      employeeWhere.name = {
        [Op.like]: `%${name}%`
      };
    }

    // 添加岗位和等级过滤
    if (position) {
      employeeWhere.positionId = position;
    }
    if (level) {
      employeeWhere.levelId = level;
    }

    // 查询员工基本信息（直接关联岗位信息）
    const { count: totalEmployees, rows: employees } = await Employee.findAndCountAll({
      where: employeeWhere,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name', 'parentId'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: PositionType,
          as: 'positionType',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name'],
          required: false
        }
      ],
      attributes: ['id', 'name', 'departmentId', 'openId', 'positionId', 'levelId', 'positionTypeId'],
      offset,
      limit,
      order: [['id', 'ASC']]
    });

    // 获取所有员工的openId数组
    const employeeOpenIds = employees.map(emp => emp.openId).filter(Boolean);
    const employeeIds = employees.map(emp => emp.id);

    // 批量查询练习记录统计
    const practiceStats = await PracticeRecord.findAll(addEnterpriseFilter({
      attributes: [
        'openId',
        [Sequelize.fn('SUM', Sequelize.col('question_num')), 'totalQuestions'],
        [Sequelize.fn('GROUP_CONCAT', Sequelize.col('total_duration')), 'allDurations']
      ],
      where: {
        openId: {
          [Op.in]: employeeOpenIds
        }
      },
      group: ['openId'],
      raw: true
    }));

    // 批量查询考试记录统计
    const examStats = await ExamRecord.findAll(addEnterpriseFilter({
      attributes: [
        'openId',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalExams']
      ],
      where: {
        openId: {
          [Op.in]: employeeOpenIds
        },
        delFlag: 0
      },
      group: ['openId'],
      raw: true
    }));

    // 批量查询证书记录统计
    const certificateStats = await CertificateRecord.findAll({
      attributes: [
        'employeeId',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalCertificates']
      ],
      where: {
        enterpriseId: enterpriseId.toString(),
        employeeId: {
          [Op.in]: employeeIds
        },
        delFlag: 0
      },
      group: ['employeeId'],
      raw: true
    });

    // 组装数据
    const statisticsData = employees.map((employee) => {
      // 获取部门路径（前两级）
      const departmentPath = getDepartmentPath(employee.departmentId, allDepartments);
      
      // 查找该员工的练习记录统计
      const employeePracticeStats = practiceStats.find(stat => 
        stat.openId === employee.openId
      );

      // 查找该员工的考试记录统计
      const employeeExamStats = examStats.find(stat => 
        stat.openId === employee.openId
      );

      // 查找该员工的证书统计
      const employeeCertificateStats = certificateStats.find(stat => 
        stat.employeeId === employee.id
      );

      // 计算总练习时长和答题数
      let totalTime = 0;
      let totalQuestions = parseInt(employeePracticeStats?.totalQuestions) || 0;

      if (employeePracticeStats && employeePracticeStats.allDurations) {
        // 处理时长字符串，可能是逗号分隔的多个时长
        const durations = employeePracticeStats.allDurations.split(',').filter(Boolean);
        totalTime = durations.reduce((sum, duration) => {
          const minutes = convertDurationToMinutes(duration.trim());
          return sum + minutes;
        }, 0);
      }

      // 获取统计数据
      const totalExams = parseInt(employeeExamStats?.totalExams) || 0;
      const totalCertificates = parseInt(employeeCertificateStats?.totalCertificates) || 0;
      const totalBadges = 0; // 徽章数，暂时设为0，后续可根据实际徽章系统调整

      return {
        id: employee.id,
        name: employee.name,
        department: departmentPath,
        position: employee.positionName?.name || '未分配',
        level: employee.level?.name || '未分配',
        positionId: employee.positionId || null,
        levelId: employee.levelId || null,
        positionTypeId: employee.positionTypeId || null,
        totalTime: Math.round(totalTime * 100) / 100,
        totalQuestions,
        totalExams,
        totalCertificates,
        totalBadges
      };
    });

    // 计算总体统计（基于所有员工，不受分页影响）
    // 先查询所有符合条件的员工（不分页）
    const allEmployeesForStats = await Employee.findAll({
      where: employeeWhere,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name', 'parentId'],
          required: false
        }
      ],
      attributes: ['id', 'openId']
    });

    const allEmployeeOpenIds = allEmployeesForStats.map(emp => emp.openId).filter(Boolean);
    const allEmployeeIds = allEmployeesForStats.map(emp => emp.id);

    // 查询所有员工的练习统计（不分页）
    const allPracticeStats = await PracticeRecord.findAll(addEnterpriseFilter({
      attributes: [
        [Sequelize.fn('SUM', Sequelize.col('question_num')), 'totalQuestions'],
        [Sequelize.fn('GROUP_CONCAT', Sequelize.col('total_duration')), 'allDurations']
      ],
      where: {
        openId: {
          [Op.in]: allEmployeeOpenIds
        }
      },
      raw: true
    }));

    // 查询所有员工的考试统计（不分页）
    const allExamStats = await ExamRecord.findAll(addEnterpriseFilter({
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalExams']
      ],
      where: {
        openId: {
          [Op.in]: allEmployeeOpenIds
        },
        delFlag: 0
      },
      raw: true
    }));

    // 查询所有员工的证书统计（不分页）
    const allCertificateStats = await CertificateRecord.findAll({
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalCertificates']
      ],
      where: {
        enterpriseId: enterpriseId.toString(),
        employeeId: {
          [Op.in]: allEmployeeIds
        },
        delFlag: 0
      },
      raw: true
    });

    // 计算全员总练习时长
    let allTotalTime = 0;
    if (allPracticeStats[0] && allPracticeStats[0].allDurations) {
      const allDurations = allPracticeStats[0].allDurations.split(',').filter(Boolean);
      allTotalTime = allDurations.reduce((sum, duration) => {
        const minutes = convertDurationToMinutes(duration.trim());
        return sum + minutes;
      }, 0);
    }

    const totalStatistics = {
      totalTime: Math.round(allTotalTime * 100) / 100,
      totalQuestions: parseInt(allPracticeStats[0]?.totalQuestions) || 0,
      totalExams: parseInt(allExamStats[0]?.totalExams) || 0,
      totalCertificates: parseInt(allCertificateStats[0]?.totalCertificates) || 0,
      totalBadges: 0, // 徽章数，暂时设为0
      totalEmployees: totalEmployees // 这里用查询得到的总数
    };

    res.json({
      code: 200,
      data: {
        list: statisticsData,
        pagination: {
          current: parseInt(page),
          pageSize: limit,
          total: totalEmployees
        },
        totalStatistics
      },
      message: '获取员工统计数据成功'
    });

  } catch (error) {
    console.error('获取员工统计数据失败:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({
      code: 500,
      message: '获取员工统计数据失败',
      error: error.message
    });
  }
};

/**
 * 获取员工岗位统计数据（支持多岗位）
 */
const getEmployeePositionStatistics = async (req, res) => {
  try {
    const { employeeId } = req.params;
    const enterpriseId = DEFAULT_ENTERPRISE_ID;

    // 查询员工基本信息
    const employee = await Employee.findOne({
      where: { 
        id: employeeId, 
        ...addEnterpriseFilter({ where: {} }).where 
      },
      attributes: ['id', 'name', 'openId']
    });

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    // 查询该员工的所有岗位（使用原生SQL或手动关联）
    const employeePositions = await EmployeePosition.findAll({
      where: {
        employeeId: employeeId,
        enterpriseId: enterpriseId.toString()
      }
    });

    // 如果没有找到岗位分配，使用员工主岗位作为后备
    let positionsToProcess = [];

    if (employeePositions.length > 0) {
      // 有岗位分配，获取岗位详细信息
      for (const empPos of employeePositions) {
        const positionName = await PositionName.findByPk(empPos.positionId);
        const level = await Level.findByPk(empPos.levelId);
        const positionType = await PositionType.findByPk(empPos.positionTypeId);
        
        positionsToProcess.push({
          positionId: empPos.positionId,
          levelId: empPos.levelId,
          positionTypeId: empPos.positionTypeId,
          isDefault: empPos.isDefault,
          positionName: positionName,
          level: level,
          positionType: positionType
        });
      }
    } else {
      // 没有岗位分配，使用员工表中的主岗位
      const employeeWithPosition = await Employee.findOne({
        where: { 
          id: employeeId, 
          ...addEnterpriseFilter({ where: {} }).where 
        },
        include: [
          {
            model: PositionName,
            as: 'positionName',
            attributes: ['id', 'name', 'typeId']
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name', 'orderNum']
          },
          {
            model: PositionType,
            as: 'positionType',
            attributes: ['id', 'name']
          }
        ]
      });
      
      if (employeeWithPosition && employeeWithPosition.positionId) {
        positionsToProcess.push({
          positionId: employeeWithPosition.positionId,
          levelId: employeeWithPosition.levelId,
          positionTypeId: employeeWithPosition.positionTypeId,
          isDefault: true,
          positionName: employeeWithPosition.positionName,
          level: employeeWithPosition.level,
          positionType: employeeWithPosition.positionType
        });
      }
    }

    if (positionsToProcess.length === 0) {
      return res.json({
        code: 200,
        data: {
          employee: {
            id: employee.id,
            name: employee.name
          },
          positionStats: []
        },
        message: '该员工暂无岗位分配'
      });
    }

    // 获取所有岗位类型，用于查找名称
    const allPositionTypes = await PositionType.findAll(addEnterpriseFilter({
      attributes: ['id', 'name'],
      raw: true
    }));
    
    const positionTypeMap = {};
    allPositionTypes.forEach(type => {
      positionTypeMap[type.id] = type.name;
    });

    // 为每个岗位统计数据
    let positionStatsData = await Promise.all(
      positionsToProcess.map(async (empPos) => {
        // 查询该岗位名称下所有配置的等级
        const positionLevels = await Position.findAll(addEnterpriseFilter({
          where: {
            nameId: empPos.positionId
          },
          attributes: ['levelId'],
          raw: true
        }));
        
        // 提取该岗位配置的所有等级ID
        const configuredLevelIds = positionLevels.map(pos => pos.levelId);
        console.log(`岗位 ${empPos.positionName?.name} 配置的等级IDs:`, configuredLevelIds);
        
        // 先查询下一级等级信息 - 需要同时满足：1.顺序在当前等级之后 2.在岗位配置的等级中
        let nextLevel = null;
        if (configuredLevelIds.length > 0) {
          // 获取所有高于当前等级的等级列表
          const higherLevels = await Level.findAll(addEnterpriseFilter({
            where: {
              id: {
                [Op.in]: configuredLevelIds // 必须是岗位配置的等级
              },
              orderNum: {
                [Op.gt]: empPos.level?.orderNum || 0 // orderNum更大的是更高等级
              }
            },
            order: [['orderNum', 'ASC']], // 升序，取最接近的下一级
            attributes: ['id', 'name', 'orderNum']
          }));
          
          // 取第一个作为下一级
          if (higherLevels.length > 0) {
            nextLevel = higherLevels[0];
          }
        }

        console.log(`当前等级: ${empPos.level?.name}(${empPos.levelId}), 下一级: ${nextLevel?.name}(${nextLevel?.id})`);

        // 查询练习记录 - 如果有下一级则查询下一级的，否则查询当前等级的
        const practiceStats = await PracticeRecord.findAll(addEnterpriseFilter({
          attributes: [
            [Sequelize.fn('SUM', Sequelize.col('question_num')), 'totalQuestions'],
            [Sequelize.fn('GROUP_CONCAT', Sequelize.col('total_duration')), 'allDurations']
          ],
          where: {
            openId: employee.openId,
            positionName: empPos.positionId.toString(),
            positionLevel: empPos.levelId.toString() // 始终使用当前等级ID
          },
          raw: true
        }));

        // 查询考试记录 - 如果有下一级则查询下一级的，否则查询当前等级的
        const examStats = await ExamRecord.findAll(addEnterpriseFilter({
          attributes: [
            [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalExams']
          ],
          where: {
            openId: employee.openId,
            positionId: empPos.positionId,
            levelId: empPos.levelId, // 始终使用当前等级ID
            delFlag: 0
          },
          raw: true
        }));

        console.log(`当前等级练习统计:`, practiceStats[0]);
        console.log(`当前等级考试统计:`, examStats[0]);

        // 获取员工已获得的证书信息
        const employeeCertificates = await CertificateRecord.findAll({
          where: {
            enterpriseId: enterpriseId.toString(),
            employeeId: employeeId,
            positionName: empPos.positionId,
            delFlag: 0
          },
          attributes: ['positionLevel'],
          raw: true
        });

        console.log(`员工在当前岗位的证书:`, employeeCertificates);

        // 计算证书进度：员工已获得证书在其对应等级中的完成度
        let certificateProgress = 0;
        let obtainedCertificates = employeeCertificates.length;
        let requiredCertificates = 0;

        if (employeeCertificates.length > 0) {
          // 找到员工证书对应的等级（取第一个证书的等级）
          const certificateLevel = employeeCertificates[0].positionLevel;
          // 查询该等级的必考证书总数
          requiredCertificates = await ExamConfig.count(addEnterpriseFilter({
            where: {
              positionName: empPos.positionId,
              positionLevel:  certificateLevel,
              status: '必考'
            }
          }));

          certificateProgress = requiredCertificates > 0 ? Math.round((obtainedCertificates / requiredCertificates) * 100) : 0;
          
          console.log(`证书等级: ${certificateLevel}, 该等级必考数: ${requiredCertificates}, 已得: ${obtainedCertificates}, 进度: ${certificateProgress}%`);
        } else {
          // 员工暂无证书，显示下一级等级的必考证书数量
          if (nextLevel) {
            requiredCertificates = await ExamConfig.count(addEnterpriseFilter({
              where: {
                positionName: empPos.positionId,
                positionLevel: nextLevel.id,
                status: '必考'
              }
            }));
          } else {
            // 如果没有下一级，显示当前等级的必考证书数量
            requiredCertificates = await ExamConfig.count(addEnterpriseFilter({
              where: {
                positionName: empPos.positionId,
                positionLevel: empPos.levelId,
                status: '必考'
              }
            }));
          }
          
          certificateProgress = 0; // 没有证书，进度为0
          console.log(`员工暂无证书，下一级/当前等级必考证书数: ${requiredCertificates}, 已得: ${obtainedCertificates}, 进度: ${certificateProgress}%`);
        }

        // 计算练习时长
        let currentPositionTime = 0;
        if (practiceStats[0] && practiceStats[0].allDurations) {
          const durations = practiceStats[0].allDurations.split(',').filter(Boolean);
          currentPositionTime = durations.reduce((sum, duration) => {
            const minutes = convertDurationToMinutes(duration.trim());
            return sum + minutes;
          }, 0);
        }

        // 查询下一级等级的必考证书数量和员工在下一级的证书数量（使用前面已获取的nextLevel）
        let nextLevelRequiredCertificates = 0;
        let nextLevelObtainedCertificates = 0;
        if (nextLevel) {
          nextLevelRequiredCertificates = await ExamConfig.count(addEnterpriseFilter({
            where: {
              positionName: empPos.positionId,
              positionLevel: nextLevel.id,
              status: '必考'
            }
          }));

          // 查询员工在下一级已获得的证书数量
          nextLevelObtainedCertificates = await CertificateRecord.count({
            where: {
              enterpriseId: enterpriseId.toString(),
              employeeId: employeeId,
              positionName: empPos.positionId,
              positionLevel: nextLevel.id.toString(),
              delFlag: 0
            }
          });
          
          console.log(`下一级: ${nextLevel.name}(${nextLevel.id}), 必考证书数: ${nextLevelRequiredCertificates}, 已获得: ${nextLevelObtainedCertificates}`);
          
        }
          return {
            positionId: empPos.positionId,
            positionName: empPos.positionName?.name || '未分配',
            levelId: empPos.levelId,
            levelName: empPos.level?.name || '未分配',
            positionTypeId: empPos.positionTypeId,
            positionTypeName: empPos.positionType ? empPos.positionType.name : positionTypeMap[empPos.positionTypeId] || '未知类型',
            isDefault: empPos.isDefault || false,
            currentPositionTime: Math.round(currentPositionTime * 100) / 100,
            currentPositionQuestions: parseInt(practiceStats[0]?.totalQuestions) || 0,
            currentPositionExams: parseInt(examStats[0]?.totalExams) || 0,
            requiredCertificates,
            obtainedCertificates,
            certificateProgress,
            nextLevelCertificates: nextLevel ? `还需${Math.max(0, nextLevelRequiredCertificates - nextLevelObtainedCertificates)}个证书升级到${nextLevel.name}` : '已处于最高级',
            nextLevelRequiredCertificates: nextLevel ? nextLevelRequiredCertificates : 0,
            badges: [] // 徽章数据，暂时为空数组
          };
      })
    );
    // 不再过滤，显示所有岗位数据
    // positionStatsData = positionStatsData.filter(item => 
    //   item.currentPositionTime > 0 || 
    //   item.currentPositionQuestions > 0 ||
    //   item.currentPositionExams > 0 ||
    //   item.obtainedCertificates > 0
    // );

    res.json({
      code: 200,
      data: {
        employee: {
          id: employee.id,
          name: employee.name
        },
        positionStats: positionStatsData
      },
      message: '获取员工岗位统计成功'
    });

  } catch (error) {
    console.error('获取员工岗位统计失败:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({
      code: 500,
      message: '获取员工岗位统计失败',
      error: error.message
    });
  }
};

/**
 * 获取员工其他岗位统计数据（非晋升岗位的所有练习记录）
 */
const getEmployeeOtherPositionStatistics = async (req, res) => {
  try {
    const { employeeId } = req.params;
    const enterpriseId = DEFAULT_ENTERPRISE_ID;

    // 查询员工基本信息
    const employee = await Employee.findOne({
      where: { 
        id: employeeId, 
        ...addEnterpriseFilter({ where: {} }).where 
      },
      attributes: ['id', 'name', 'openId']
    });

    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工不存在'
      });
    }

    // 获取员工的所有岗位记录
    const employeePositions = await EmployeePosition.findAll({
      where: {
        employeeId: employeeId,
        enterpriseId: enterpriseId.toString()
      },
      attributes: ['positionId', 'levelId'],
      raw: true
    });

    console.log(`找到员工岗位记录 ${employeePositions.length} 条`);

    // 获取所有等级信息，用于判断等级层级关系
    const allLevels = await Level.findAll(addEnterpriseFilter({
      attributes: ['id', 'name', 'orderNum'],
      raw: true
    }));

    // 创建等级ID到orderNum的映射
    const levelOrderMap = {};
    allLevels.forEach(level => {
      levelOrderMap[level.id] = level.orderNum;
    });

    console.log('等级顺序映射:', levelOrderMap);

    // 创建晋升岗位集合（岗位ID+等级ID的组合）
    const promotionPositionSet = new Set();
    
    // 处理所有岗位
    for (const pos of employeePositions) {
      if (pos.positionId && pos.levelId) {
        // 添加当前岗位和等级
        promotionPositionSet.add(`${pos.positionId}_${pos.levelId}`);
        
        // 查询该岗位名称下所有配置的等级
        const positionLevels = await Position.findAll(addEnterpriseFilter({
          where: {
            nameId: pos.positionId
          },
          attributes: ['levelId'],
          raw: true
        }));
        
        // 提取该岗位配置的所有等级ID
        const configuredLevelIds = positionLevels.map(p => p.levelId);
        console.log(`岗位ID=${pos.positionId} 配置的等级IDs:`, configuredLevelIds);
        
        if (configuredLevelIds.length > 0) {
          // 获取该岗位的等级顺序
          const levelOrder = levelOrderMap[pos.levelId];
          
          if (levelOrder !== undefined) {
            // 获取所有高于当前等级的等级列表（必须是岗位配置的等级）
            const higherLevels = allLevels.filter(level => 
              configuredLevelIds.includes(level.id) && 
              level.orderNum > levelOrder
            );
            
            // 按orderNum升序排序
            higherLevels.sort((a, b) => a.orderNum - b.orderNum);
            
            // 取第一个作为下一级
            const nextLevel = higherLevels.length > 0 ? higherLevels[0] : null;
            
            // 只添加下一级等级
            if (nextLevel) {
              promotionPositionSet.add(`${pos.positionId}_${nextLevel.id}`);
              console.log(`添加晋升等级: 岗位ID=${pos.positionId}, 等级ID=${nextLevel.id}, 等级名称=${nextLevel.name}, orderNum=${nextLevel.orderNum}`);
            }
          }
        }
      }
    }

    console.log('晋升岗位集合:', Array.from(promotionPositionSet));

    // 获取该员工的所有练习记录
    const allPracticeRecords = await PracticeRecord.findAll(addEnterpriseFilter({
      where: {
        openId: employee.openId
      },
      attributes: [
        'positionName',
        'positionLevel',
        'question_num',
        'total_duration'
      ],
      raw: true
    }));

    console.log(`找到员工练习记录 ${allPracticeRecords.length} 条`);

    // 筛选出非晋升岗位的练习记录
    const otherPositionRecords = allPracticeRecords.filter(record => {
      // 检查该记录是否属于晋升岗位
      const positionKey = `${record.positionName}_${record.positionLevel}`;
      const isPromotionPosition = promotionPositionSet.has(positionKey);
      
      if (isPromotionPosition) {
        console.log(`排除晋升岗位记录: ${positionKey}`);
      }
      
      return !isPromotionPosition;
    });

    console.log(`筛选出非晋升岗位练习记录 ${otherPositionRecords.length} 条`);

    // 按照岗位+等级分组统计
    const groupedStats = {};
    
    for (const record of otherPositionRecords) {
      const key = `${record.positionName}_${record.positionLevel}`;
      
      if (!groupedStats[key]) {
        groupedStats[key] = {
          positionId: record.positionName,
          levelId: record.positionLevel,
          practiceQuestions: 0,
          practiceTime: 0,
          durations: []
        };
      }
      
      // 累加题目数量
      groupedStats[key].practiceQuestions += parseInt(record.question_num) || 0;
      
      // 收集时长数据，稍后转换
      if (record.total_duration) {
        groupedStats[key].durations.push(record.total_duration);
      }
    }

    // 获取岗位名称和等级名称
    const positionIds = Object.values(groupedStats).map(stat => stat.positionId);
    const levelIds = Object.values(groupedStats).map(stat => stat.levelId);
    
    const [positions, levels, positionTypes] = await Promise.all([
      PositionName.findAll({
        where: {
          id: {
            [Op.in]: positionIds
          }
        },
        attributes: ['id', 'name', 'typeId'],
        raw: true
      }),
      Level.findAll({
        where: {
          id: {
            [Op.in]: levelIds
          }
        },
        attributes: ['id', 'name'],
        raw: true
      }),
      PositionType.findAll(addEnterpriseFilter({
        attributes: ['id', 'name'],
        raw: true
      }))
    ]);
    
    // 创建ID到名称的映射
    const positionMap = {};
    const levelMap = {};
    const positionTypeMap = {};
    
    positions.forEach(pos => {
      positionMap[pos.id] = pos.name;
    });
    
    levels.forEach(level => {
      levelMap[level.id] = level.name;
    });
    
    positionTypes.forEach(type => {
      positionTypeMap[type.id] = type.name;
    });

    // 计算每个分组的总练习时长并转换为分钟
    Object.values(groupedStats).forEach(stat => {
      stat.practiceTime = stat.durations.reduce((total, duration) => {
        return total + convertDurationToMinutes(duration);
      }, 0);
      
      // 四舍五入到两位小数
      stat.practiceTime = Math.round(stat.practiceTime * 100) / 100;
      
      // 添加名称
      stat.positionName = positionMap[stat.positionId] || `岗位${stat.positionId}`;
      stat.levelName = levelMap[stat.levelId] || `等级${stat.levelId}`;
      
      // 删除不需要的字段
      delete stat.durations;
    });

    // 转换为数组
    const positionStatsData = Object.values(groupedStats);

    // 查询每个岗位等级组合的考试记录和岗位类型
    for (const stat of positionStatsData) {
      // 查询该岗位的类型信息
      const position = positions.find(p => p.id == stat.positionId);
      
      if (position && position.typeId) {
        stat.positionTypeId = position.typeId;
        stat.positionTypeName = positionTypeMap[position.typeId] || '未知类型';
      } else {
        stat.positionTypeId = null;
        stat.positionTypeName = '未知类型';
      }
      
      // 查询该岗位等级的考试记录
      const examStats = await ExamRecord.findAll(addEnterpriseFilter({
        attributes: [
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'examCount']
        ],
        where: {
          openId: employee.openId,
          positionId: stat.positionId,
          levelId: stat.levelId,
          delFlag: 0
        },
        raw: true
      }));
      
      stat.examCount = parseInt(examStats[0]?.examCount) || 0;
      
      // 查询该岗位等级的证书记录
      const certificateStats = await CertificateRecord.findAll({
        where: {
          enterpriseId: enterpriseId.toString(),
          employeeId: employeeId,
          positionName: stat.positionId,
          positionLevel: stat.levelId,
          delFlag: 0
        },
        attributes: ['id'],
        raw: true
      });
      
      stat.certificateCount = certificateStats.length;
    }

    console.log(`最终返回其他岗位统计数据 ${positionStatsData.length} 条`);

    res.json({
      code: 200,
      data: {
        employee: {
          id: employee.id,
          name: employee.name
        },
        positionStats: positionStatsData
      },
      message: '获取员工其他岗位统计成功'
    });

  } catch (error) {
    console.error('获取员工其他岗位统计失败:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({
      code: 500,
      message: '获取员工其他岗位统计失败',
      error: error.message
    });
  }
};

/**
 * 导出员工统计数据为Excel
 */
const exportEmployeeStatistics = async (req, res) => {
  try {
    const enterpriseId = DEFAULT_ENTERPRISE_ID;
    
    // 获取查询参数（与列表查询相同的过滤条件）
    const { name = '', position, level } = req.query;

    // 构建员工查询条件
    let employeeWhere = {
      status: '1'
    };
    employeeWhere = addEnterpriseFilter({ where: employeeWhere }).where;

    if (name) {
      employeeWhere.name = {
        [Op.like]: `%${name}%`
      };
    }
    if (position) {
      employeeWhere.positionId = position;
    }
    if (level) {
      employeeWhere.levelId = level;
    }

    // 获取所有部门数据
    const allDepartments = await Department.findAll(addEnterpriseFilter({
      attributes: ['id', 'name', 'parentId']
    }));

    // 查询所有符合条件的员工数据
    const employees = await Employee.findAll({
      where: employeeWhere,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name', 'parentId'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name'],
          required: false
        }
      ],
      attributes: ['id', 'name', 'departmentId', 'openId', 'positionId', 'levelId'],
      order: [['id', 'ASC']]
    });

    // 获取统计数据（复用之前的逻辑）
    const employeeOpenIds = employees.map(emp => emp.openId).filter(Boolean);
    const employeeIds = employees.map(emp => emp.id);

    const [practiceStats, examStats, certificateStats] = await Promise.all([
      PracticeRecord.findAll(addEnterpriseFilter({
        attributes: [
          'openId',
          [Sequelize.fn('COUNT', Sequelize.col('question_num')), 'totalQuestions'],
          [Sequelize.fn('GROUP_CONCAT', Sequelize.col('total_duration')), 'allDurations']
        ],
        where: { openId: { [Op.in]: employeeOpenIds } },
        group: ['openId'],
        raw: true
      })),
      ExamRecord.findAll(addEnterpriseFilter({
        attributes: [
          'openId',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalExams']
        ],
        where: { openId: { [Op.in]: employeeOpenIds }, delFlag: 0 },
        group: ['openId'],
        raw: true
      })),
      CertificateRecord.findAll({
        attributes: [
          'employeeId',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalCertificates']
        ],
        where: {
          enterpriseId: enterpriseId.toString(),
          employeeId: { [Op.in]: employeeIds },
          delFlag: 0
        },
        group: ['employeeId'],
        raw: true
      })
    ]);

    // 组装Excel数据
    const excelData = employees.map((employee) => {
      const departmentPath = getDepartmentPath(employee.departmentId, allDepartments);
      
      const employeePracticeStats = practiceStats.find(stat => stat.openId === employee.openId);
      const employeeExamStats = examStats.find(stat => stat.openId === employee.openId);
      const employeeCertificateStats = certificateStats.find(stat => stat.employeeId === employee.id);

      let totalTime = 0;
      let totalQuestions = 0;

      if (employeePracticeStats && employeePracticeStats.allDurations) {
        const durations = employeePracticeStats.allDurations.split(',');
        totalTime = durations.reduce((sum, duration) => {
          return sum + convertDurationToMinutes(duration);
        }, 0);
        totalQuestions = parseInt(employeePracticeStats.totalQuestions) || 0;
      }

      return {
        '姓名': employee.name,
        '所属门店': departmentPath,
        '岗位名称': employee.positionName?.name || '未分配',
        '岗位等级': employee.level?.name || '未分配',
        '总练习时长(分钟)': Math.round(totalTime * 100) / 100,
        '总答题数': totalQuestions,
        '总考试次数': parseInt(employeeExamStats?.totalExams) || 0,
        '总证书数': parseInt(employeeCertificateStats?.totalCertificates) || 0,
        '总徽章数': 0 // 暂时设为0
      };
    });

    // 创建工作簿和工作表
    const ws = XLSX.utils.json_to_sheet(excelData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '员工统计数据');

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="employee_statistics_${new Date().toISOString().slice(0,10)}.xlsx"`);

    // 生成Excel文件并发送
    const excelBuffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
    res.send(excelBuffer);

  } catch (error) {
    console.error('导出员工统计数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '导出员工统计数据失败',
      error: error.message
    });
  }
};

module.exports = {
  getEmployeeStatistics,
  getEmployeePositionStatistics,
  exportEmployeeStatistics,
  getEmployeeOtherPositionStatistics
}; 