const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const PracticeRecordDetail = sequelize.define('practice_record_detail', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id'
  },
  practiceRecordId: {
    type: DataTypes.INTEGER,
    field: 'practice_record_id',
    allowNull: true,
    comment: '练习记录id'
  },
  role: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '角色：系统，用户'
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '类型：question-题目，answer-答案，analysis-解析'
  },
  num: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '顺序'
  },
  content: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '内容'
  },
  result: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '回答判断结果：true-正确，false-错误，unknown-未知'
  },
  employeeId: {
    type: DataTypes.INTEGER,
    field: 'employee_id',
    allowNull: true,
    comment: '员工ID'
  },
  openId: {
    type: DataTypes.STRING(255),
    field: 'open_id',
    allowNull: true,
    comment: '微信openId'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    field: 'is_active',
    allowNull: true,
    defaultValue: true,
    comment: '是否有效(1有效 0无效)'
  },
  careerRecordId: {
    type: DataTypes.INTEGER,
    field: 'career_record_id',
    allowNull: true,
    comment: '员工履历记录ID'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    allowNull: true,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    allowNull: true,
    comment: '更新时间'
  }
}, {
  tableName: 'practice_record_detail',
  timestamps: false,
  comment: '练习记录详情'
});

// 建立关联关系
PracticeRecordDetail.belongsTo(require('./practice-record'), {
  foreignKey: 'practiceRecordId',
  as: 'practiceRecord'
});

module.exports = PracticeRecordDetail;