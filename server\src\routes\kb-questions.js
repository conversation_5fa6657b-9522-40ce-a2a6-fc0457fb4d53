const express = require('express');
const router = express.Router();
const kbQuestionsController = require('../controllers/kb-questions-controller');
const multer = require('multer');
const path = require('path');

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../uploads/'));
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueName = `import_${Date.now()}_${Math.round(Math.random() * 1E9)}.xlsx`;
    cb(null, uniqueName);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: function (req, file, cb) {
    // 只允许上传Excel文件
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传Excel文件'), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// 获取知识库列表选项（用于筛选）
router.get('/knowledge-options', kbQuestionsController.getKnowledgeBaseOptions);

// 下载题目导入模板
router.get('/download-template', kbQuestionsController.downloadTemplate);

// 导入题目Excel文件
router.post('/import', upload.single('file'), kbQuestionsController.importQuestions);

// 导出所有题目为Excel
router.get('/all/export', kbQuestionsController.exportQuestions);

// 导出特定知识库下的题目为Excel
router.get('/:id/export', kbQuestionsController.exportQuestions);

// 获取所有题目列表（所有知识库的题目）
router.get('/all/questions', kbQuestionsController.getAllQuestions);

// 获取特定知识库下的题目
router.get('/:id/questions', kbQuestionsController.getAllQuestions);

// 添加题目
router.post('/:id/questions', kbQuestionsController.addQuestion);

// 更新题目
router.put('/:id/questions', kbQuestionsController.updateQuestion);

// 删除题目
router.delete('/:id/questions/:questionId', kbQuestionsController.deleteQuestion);

module.exports = router; 
