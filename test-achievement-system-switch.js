/**
 * 测试成就系统开关功能
 * 验证通过 system_settings 表控制成就系统的启用/禁用
 */
require('dotenv').config();

console.log('成就系统开关功能说明');
console.log('='.repeat(60));

console.log('\n功能描述：');
console.log('在 setImmediate 中检查 system_settings 表中 code="achieve" 的 value 值');
console.log('如果 value="true"，则执行成就检测代码');
console.log('如果 value!="true"，则跳过成就检测代码');

console.log('\n修改位置：');
console.log('1. server/src/controllers/weichat/wechatPracticeController.js (练习成就检测)');
console.log('2. server/src/controllers/weichat/wechatExamStartController.js (考试成就检测)');

console.log('\n实现逻辑：');

console.log('\n1. 练习成就检测 (wechatPracticeController.js)：');
console.log(`
setImmediate(async () => {
  try {
    // 检查成就系统是否启用
    const SystemSetting = require('../../models/systemSetting');
    const { addEnterpriseFilter } = require('../../utils/enterpriseFilter');
    
    const achieveSetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: { code: 'achieve' },
        attributes: ['value']
      })
    );
    
    const isAchieveEnabled = achieveSetting && achieveSetting.value === 'true';
    
    if (!isAchieveEnabled) {
      console.log('[parseAnswerWebSocket] 成就系统已禁用，跳过成就检测');
      return;
    }
    
    console.log('[parseAnswerWebSocket] 成就系统已启用，异步触发完整的成就检测系统');
    
    // 原有的成就检测代码
    const { handlePracticeAchievementTrigger } = require('../../utils/achievementUtils');
    // ... 继续执行成就检测
  } catch (error) {
    console.error('成就检测失败:', error);
  }
});
`);

console.log('\n2. 考试成就检测 (wechatExamStartController.js)：');
console.log(`
setImmediate(async () => {
  try {
    // 检查成就系统是否启用
    const SystemSetting = require('../../models/systemSetting');
    const { addEnterpriseFilter } = require('../../utils/enterpriseFilter');
    
    const achieveSetting = await SystemSetting.findOne(
      addEnterpriseFilter({
        where: { code: 'achieve' },
        attributes: ['value']
      })
    );
    
    const isAchieveEnabled = achieveSetting && achieveSetting.value === 'true';
    
    if (!isAchieveEnabled) {
      console.log('[考试成就] 成就系统已禁用，跳过成就检测');
      return;
    }
    
    console.log('[考试成就] 成就系统已启用，开始成就检测');
    
    // 原有的成就检测代码
    await handleExamAchievementTrigger(userId, openId, enterpriseId, examId, score, questionCount);
  } catch (error) {
    console.error('成就检测失败:', error);
  }
});
`);

console.log('\n数据库配置：');
console.log('需要在 system_settings 表中添加以下记录：');
console.log(`
INSERT INTO system_settings (code, name, value, description, enterprise_id) 
VALUES (
  'achieve', 
  '成就系统开关', 
  'true', 
  '控制成就系统是否启用：true-启用，false-禁用', 
  1
);
`);

console.log('\n控制方式：');
console.log('1. 启用成就系统：');
console.log("   UPDATE system_settings SET value='true' WHERE code='achieve';");
console.log('\n2. 禁用成就系统：');
console.log("   UPDATE system_settings SET value='false' WHERE code='achieve';");

console.log('\n日志输出：');
console.log('✅ 成就系统启用时：');
console.log('   [parseAnswerWebSocket] 成就系统已启用，异步触发完整的成就检测系统');
console.log('   [考试成就] 成就系统已启用，开始成就检测');

console.log('\n❌ 成就系统禁用时：');
console.log('   [parseAnswerWebSocket] 成就系统已禁用，跳过成就检测');
console.log('   [考试成就] 成就系统已禁用，跳过成就检测');

console.log('\n关键特性：');
console.log('✅ 企业级隔离：使用 addEnterpriseFilter 确保企业数据隔离');
console.log('✅ 性能优化：在 setImmediate 开始就检查，避免不必要的计算');
console.log('✅ 安全检查：检查设置存在性和值的准确性');
console.log('✅ 详细日志：清晰的启用/禁用状态日志');
console.log('✅ 错误处理：完善的异常处理机制');

console.log('\n判断逻辑：');
console.log('- achieveSetting 存在 && achieveSetting.value === "true" → 启用');
console.log('- achieveSetting 不存在 || achieveSetting.value !== "true" → 禁用');

console.log('\n使用场景：');
console.log('1. 系统维护期间临时禁用成就系统');
console.log('2. 特定企业禁用成就功能');
console.log('3. 性能调优时关闭成就检测');
console.log('4. 测试环境控制成就系统行为');

console.log('\n测试步骤：');
console.log('1. 确保数据库中有 achieve 设置记录');
console.log('2. 设置 value="true"，进行练习或考试，观察成就检测日志');
console.log('3. 设置 value="false"，进行练习或考试，确认跳过成就检测');
console.log('4. 验证企业隔离功能正常工作');

console.log('\n注意事项：');
console.log('- 设置检查在每次 setImmediate 执行时进行，支持动态开关');
console.log('- 不影响主要业务流程，只控制成就检测部分');
console.log('- 使用字符串比较，确保 value 值为 "true" 才启用');
console.log('- 支持企业级别的独立控制');

console.log('\n SQL 查询示例：');
console.log('-- 查看当前设置');
console.log("SELECT * FROM system_settings WHERE code='achieve';");
console.log('\n-- 启用成就系统');
console.log("UPDATE system_settings SET value='true' WHERE code='achieve';");
console.log('\n-- 禁用成就系统');
console.log("UPDATE system_settings SET value='false' WHERE code='achieve';");

console.log('\n🎯 实现完成！');
console.log('成就系统现在可以通过 system_settings 表动态控制启用/禁用状态。');
