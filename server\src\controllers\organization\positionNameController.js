const Position = require('../../models/Position');
const PositionName = require('../../models/PositionName');
const PositionType = require('../../models/PositionType');
const Employee = require('../../models/Employee');
const KnowledgeBase = require('../../models/knowledge-base');
const ExamConfig = require('../../models/ExamConfigModel');
const ExamReviewApplication = require('../../models/ExamReviewApplication');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../../utils/enterpriseFilter');
const sequelize = require('../../config/database');
const Level = require('../../models/Level');

/**
 * 获取岗位名称列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPositionNameList = async (req, res) => {
  try {
    const { name, typeId, status, pageNum = 1, pageSize = 10 } = req.query;
    const where = {};
    
    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }
    if (typeId) {
      where.typeId = typeId;
    }
    if (status !== undefined) {
      where.status = status === 'true' || status === '1';
    }

    const { count, rows } = await PositionName.findAndCountAll(
      addEnterpriseFilter({
        where,
        offset: (pageNum - 1) * pageSize,
        limit: parseInt(pageSize),
        order: [
          ['sort', 'ASC'],
          ['id', 'ASC']
        ],
        include: [
          {
            model: PositionType,
            as: 'type',
            attributes: ['id', 'name', 'code']
          }
        ]
      })
    );

    // 为每个岗位名称查询关联的Position和Level信息
    const enhancedRows = await Promise.all(
      rows.map(async (positionName) => {
        const positions = await Position.findAll(
          addEnterpriseFilter({
            where: { nameId: positionName.id },
            include: [
              {
                model: Level,
                as: 'level',
                attributes: ['id', 'name', 'orderNum']
              }
            ]
          })
        );

        const positionData = positionName.toJSON();
        positionData.positionLevels = positions.map(pos => pos.level).filter(level => level);
        return positionData;
      })
    );

    res.json({
      code: 200,
      data: {
        total: count,
        rows: enhancedRows,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      },
      message: '获取岗位名称列表成功'
    });
  } catch (error) {
    console.error('获取岗位名称列表失败：', error);
    res.status(500).json({
      code: 500,
      message: '获取岗位名称列表失败',
      error: error.message
    });
  }
};

/**
 * 新增岗位名称
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.addPositionName = async (req, res) => {
  const transaction = await sequelize.transaction(); // 添加事务
  try {
    const { name, code, typeId, status, sort, remark, levelIds } = req.body;

    // 校验必填字段
    if (!name || !code || !typeId) {
      return res.status(400).json({
        code: 400,
        message: '岗位名称、编码和类型不能为空'
      });
    }

    // 校验等级ID数组
    if (!levelIds || !Array.isArray(levelIds) || levelIds.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请至少选择一个岗位等级'
      });
    }

    // 校验等级序号是否重复
    const selectedLevels = await Level.findAll(
      addEnterpriseFilter({
        where: { id: { [Op.in]: levelIds } },
        attributes: ['id', 'name', 'orderNum']
      })
    );

    // 检查是否有相同的orderNum
    const orderNumMap = new Map();
    const duplicateNames = [];
    
    for (const level of selectedLevels) {
      if (orderNumMap.has(level.orderNum)) {
        // 找到重复的orderNum
        const existingLevel = orderNumMap.get(level.orderNum);
        duplicateNames.push(`${existingLevel.name}与${level.name}`);
      } else {
        orderNumMap.set(level.orderNum, level);
      }
    }

    if (duplicateNames.length > 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: `${duplicateNames.join('、')}为相同序号，不能同时选择`
      });
    }

    // 校验类型是否存在
    const type = await PositionType.findOne(
      addEnterpriseFilter({
        where: { id: typeId }
      })
    );

    if (!type) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '岗位类型不存在'
      });
    }

    // 校验编码是否已存在
    const existCode = await PositionName.findOne(
      addEnterpriseFilter({
        where: { code }
      })
    );

    if (existCode) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '岗位编码已存在'
      });
    }

    // 创建岗位名称
    const positionName = await PositionName.create(
      addEnterpriseId({
        name,
        code,
        typeId,
        status: status === undefined ? true : status,
        sort: sort || 0,
        remark,
        createBy: req.user?.username || 'admin'
      }),
      { transaction }
    );

    // 为每个等级创建Position记录
    const positionPromises = levelIds.map(levelId => {
      return Position.create(
        addEnterpriseId({
          code: `${code}_${levelId}`, // 生成唯一编码
          typeId,
          nameId: positionName.id,
          levelId,
          status: true,
          remark: `${name} - 等级${levelId}`,
          createBy: req.user?.username || 'admin'
        }),
        { transaction }
      );
    });

    await Promise.all(positionPromises);
    
    await transaction.commit();

    res.json({
      code: 200,
      data: positionName,
      message: '新增岗位名称成功'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('新增岗位名称失败：', error);
    res.status(500).json({
      code: 500,
      message: '新增岗位名称失败',
      error: error.message
    });
  }
};

/**
 * 更新岗位名称
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updatePositionName = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id, name, code, typeId, status, sort, remark, levelIds } = req.body;

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '岗位名称ID不能为空'
      });
    }

    // 校验等级ID数组
    if (!levelIds || !Array.isArray(levelIds) || levelIds.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '请至少选择一个岗位等级'
      });
    }

    // 校验等级序号是否重复
    const selectedLevels = await Level.findAll(
      addEnterpriseFilter({
        where: { id: { [Op.in]: levelIds } },
        attributes: ['id', 'name', 'orderNum']
      })
    );

    // 检查是否有相同的orderNum
    const orderNumMap = new Map();
    const duplicateNames = [];
    
    for (const level of selectedLevels) {
      if (orderNumMap.has(level.orderNum)) {
        // 找到重复的orderNum
        const existingLevel = orderNumMap.get(level.orderNum);
        duplicateNames.push(`${existingLevel.name}与${level.name}`);
      } else {
        orderNumMap.set(level.orderNum, level);
      }
    }

    if (duplicateNames.length > 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: `${duplicateNames.join('、')}为相同序号，不能同时选择`
      });
    }

    const positionName = await PositionName.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!positionName) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '岗位名称不存在'
      });
    }

    // 校验类型是否存在
    if (typeId && typeId !== positionName.typeId) {
      const type = await PositionType.findOne(
        addEnterpriseFilter({
          where: { id: typeId }
        })
      );

      if (!type) {
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '岗位类型不存在'
        });
      }
    }

    // 校验编码是否已存在
    if (code && code !== positionName.code) {
      const existCode = await PositionName.findOne(
        addEnterpriseFilter({
          where: {
            code,
            id: { [Op.ne]: id }
          }
        })
      );

      if (existCode) {
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '岗位编码已存在'
        });
      }
    }

    // 更新岗位名称
    await positionName.update({
      name,
      code,
      typeId,
      status,
      sort,
      remark,
      updateBy: req.user?.username || 'admin'
    }, { transaction });

    // 删除现有的Position记录
    await Position.destroy(
      addEnterpriseFilter({
        where: { nameId: id },
        transaction
      })
    );

    // 为每个等级创建新的Position记录
    const positionPromises = levelIds.map(levelId => {
      return Position.create(
        addEnterpriseId({
          code: `${code || positionName.code}_${levelId}`,
          typeId: typeId || positionName.typeId,
          nameId: id,
          levelId,
          status: true,
          remark: `${name || positionName.name} - 等级${levelId}`,
          createBy: req.user?.username || 'admin'
        }),
        { transaction }
      );
    });

    await Promise.all(positionPromises);
    await transaction.commit();

    res.json({
      code: 200,
      data: positionName,
      message: '更新岗位名称成功'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新岗位名称失败：', error);
    res.status(500).json({
      code: 500,
      message: '更新岗位名称失败',
      error: error.message
    });
  }
};

/**
 * 删除岗位名称
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deletePositionName = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;

    // 检查是否有关联的岗位（这个检查可以保留，因为可能有其他系统直接创建的Position记录）
    const hasPositions = await Position.findOne(
      addEnterpriseFilter({
        where: { nameId: id }
      })
    );

    // 检查是否有员工使用该岗位
    const hasEmployees = await Employee.findOne(
      addEnterpriseFilter({
        where: { positionId: id }
      })
    );

    if (hasEmployees) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '该岗位名称已被员工使用，无法删除'
      });
    }

    // 检查是否有知识库关联该岗位
    const hasKnowledgeBase = await KnowledgeBase.findOne(
      addEnterpriseFilter({
        where: { position: id }
      })
    );

    if (hasKnowledgeBase) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '该岗位名称已被知识库关联，无法删除'
      });
    }

    // 检查是否有练考配置关联该岗位
    const hasExamConfig = await ExamConfig.findOne(
      addEnterpriseFilter({
        where: { positionName: id }
      })
    );

    if (hasExamConfig) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '该岗位名称已被练考配置关联，无法删除'
      });
    }

    // 检查是否有考试审核关联该岗位
    const hasExamReview = await ExamReviewApplication.findOne(
      addEnterpriseFilter({
        where: { positionName: id }
      })
    );

    if (hasExamReview) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '该岗位名称已被考试审核关联，无法删除'
      });
    }

    // 先删除关联的Position记录
    await Position.destroy(
      addEnterpriseFilter({
        where: { nameId: id },
        transaction
      })
    );

    // 再删除岗位名称记录
    await PositionName.destroy(
      addEnterpriseFilter({
        where: { id },
        transaction
      })
    );

    await transaction.commit();

    res.json({
      code: 200,
      message: '删除岗位名称成功'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('删除岗位名称失败：', error);
    res.status(500).json({
      code: 500,
      message: '删除岗位名称失败',
      error: error.message
    });
  }
};
