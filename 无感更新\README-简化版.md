# 无感更新脚本使用说明

## 简介

`seamless-update.sh` 是一个一键式无感更新脚本，用于实现零停机部署cankao-admin项目。

## 使用方法

```bash
# 赋予脚本执行权限
chmod +x seamless-update.sh

# 不更改版本，只重新部署当前版本
./seamless-update.sh

# 更新后端API版本并部署
./seamless-update.sh --api v1.2.3

# 更新前端Web版本并部署
./seamless-update.sh --web v1.2.3

# 同时更新API和Web版本并部署
./seamless-update.sh --both v1.2.3

# 查看帮助信息
./seamless-update.sh --help
```

## 脚本功能

该脚本会执行以下操作：

1. 更新环境变量中的版本号（如果指定了新版本）
2. 备份当前配置
3. 拉取最新镜像
4. 逐个更新服务（先前端，后后端）
5. 进行健康检查，确保服务正常运行
6. 如果更新失败，自动回滚到上一个版本
7. 清理旧镜像
8. 显示当前版本信息

## 注意事项

- 确保服务器上已安装Docker和Docker Compose
- 确保环境变量文件(.env)已正确配置
- 脚本需要sudo权限来操作Docker 