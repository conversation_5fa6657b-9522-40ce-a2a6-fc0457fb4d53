# 初入宝殿成就功能使用说明

## 功能概述

"初入宝殿"成就是成就系统中的一种学习进度类成就，当用户首次学习一门科目的进度达到配置的百分比时，系统会自动触发此成就。

## 核心特性

### 1. 首次学习检测
- 系统会自动检测用户是否为首次学习某个科目
- 通过查询练习记录数量判断（≤1条为首次）
- 确保每个科目的首次学习成就只能获得一次

### 2. 进度计算方式
- 使用 `wechatPracticeController.js` 中的 `calculateExamQualification` 方法
- 支持按题数和时长两种模式计算进度
- 与考试资格计算保持一致的算法

### 3. 自动触发机制
- 在用户完成练习时自动检测
- 通过中间件 `achievementMiddleware.js` 进行拦截
- 异步处理，不影响主业务流程

## 技术实现

### 文件结构
```
server/src/
├── utils/
│   └── achievementEventListener.js     # 成就事件监听器（核心逻辑）
├── middleware/
│   └── achievementMiddleware.js        # 成就中间件（触发检测）
└── controllers/weichat/
    └── wechatPracticeController.js     # 练习控制器（进度计算）
```

### 核心函数

#### 1. handleFirstSubjectProgressEvent
位置：`server/src/utils/achievementEventListener.js`
```javascript
const handleFirstSubjectProgressEvent = async (eventData) => {
  // 1. 检查是否为首次学习
  // 2. 获取考试配置和练习记录
  // 3. 使用 calculatePracticeQualification 计算进度
  // 4. 检查是否达到成就要求
  // 5. 颁发成就
}
```

#### 2. checkFirstSubjectProgressAchievement
位置：`server/src/middleware/achievementMiddleware.js`
```javascript
const checkFirstSubjectProgressAchievement = async (userId, openId, enterpriseId, subjectId, positionName, positionLevel) => {
  // 1. 验证是否首次学习
  // 2. 获取考试配置和练习统计
  // 3. 计算实际学习进度
  // 4. 发射成就事件
}
```

#### 3. handlePracticeAchievementTrigger
位置：`server/src/middleware/achievementMiddleware.js`
```javascript
const handlePracticeAchievementTrigger = async (req, responseBody) => {
  // 练习完成后自动调用首次学习进度检测
  if (file_id && positionName && positionLevel) {
    await checkFirstSubjectProgressAchievement(user.id, openId, enterpriseId, file_id, positionName, positionLevel);
  }
}
```

## 配置方法

### 1. 创建成就模板
在成就模板管理界面创建"初入宝殿"类型的成就：

```json
{
  "name": "初入宝殿",
  "description": "首次学习一门科目进度达到50%",
  "ruleType": "first_progress",
  "triggerCondition": {
    "type": "first_progress", 
    "progress": 50
  },
  "isActive": true
}
```

### 2. 参数说明
- `progress`: 要求的进度百分比（如 50 表示50%）
- `ruleType`: 必须设置为 "first_progress"
- `triggerCondition.type`: 必须设置为 "first_progress"

## 触发流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as 练习API
    participant M as 成就中间件
    participant L as 事件监听器
    participant DB as 数据库
    
    U->>API: 完成练习
    API->>M: 触发中间件
    M->>M: 检查是否首次学习
    M->>M: 计算学习进度
    M->>L: 发射首次进度事件
    L->>L: 检查成就条件
    L->>DB: 颁发成就
    L->>U: 成就通知
```

## 测试方法

### 1. 运行测试文件
```bash
node test-first-entry-achievement.js
```

### 2. 手动测试步骤
1. 确保数据库中有相应的成就模板
2. 用户首次开始学习某个科目
3. 完成足够的练习让进度达到要求百分比
4. 观察是否自动获得"初入宝殿"成就
5. 再次学习同一科目，确认不会重复获得

### 3. 调试日志
系统会输出详细的调试日志：
```
[成就检测] 处理首次科目学习进度事件: 用户1, 科目1(测试科目), 岗位1-1
[成就检测] 科目1练习记录数: 1, 是否首次学习: true
[成就检测] 科目1实际进度: 65.5%
[成就检测] 检查成就初入宝殿, 需要进度50%, 当前进度65.5%
[成就触发] 初入宝殿成就: 初入宝殿, 科目测试科目, 进度达到65.5%
```

## 注意事项

### 1. 数据依赖
- 需要有效的考试配置（ExamConfig）
- 需要练习记录（PracticeRecord）
- 需要成就模板（AchievementTemplate）

### 2. 权限检查
- 所有查询都会自动加上企业ID过滤
- 确保用户只能获得所属企业的成就

### 3. 性能考虑
- 首次学习检测使用count查询，性能较好
- 异步处理成就逻辑，不阻塞主流程
- 有防重复获得机制

## 扩展功能

### 1. 支持不同进度要求
可以创建多个"初入宝殿"成就模板，设置不同的进度要求：
- 初入宝殿（新手）: 30%
- 初入宝殿（入门）: 50%  
- 初入宝殿（熟练）: 80%

### 2. 支持不同科目类型
可以为不同类型的科目设置不同的首次学习成就要求。

### 3. 成就升级
可以基于此成就创建进阶成就，如"学习达人"（完成多个"初入宝殿"）。

---

*此文档介绍了初入宝殿成就的完整实现和使用方法。* 