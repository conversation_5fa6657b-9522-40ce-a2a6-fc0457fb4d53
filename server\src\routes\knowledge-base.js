const express = require('express');
const router = express.Router();
const { upload } = require('../utils/fileUpload');
const knowledgeBaseController = require('../controllers/knowledge-base');

// 获取知识库列表
router.get('/list', knowledgeBaseController.getKnowledgeBaseList);

// 上传单个文件
router.post('/upload', upload.single('file'), knowledgeBaseController.uploadKnowledgeFile);

// 批量上传文件
router.post('/batch-upload', upload.array('files', 10), knowledgeBaseController.batchUploadFiles);

// 批量保存知识库文档
router.post('/batch-save', knowledgeBaseController.batchSaveDocuments);

// 获取知识库文档详情
router.get('/detail/:id', knowledgeBaseController.getKnowledgeDetail);

// 更新知识库文档
router.put('/:id', knowledgeBaseController.updateKnowledgeDocument);

// 删除知识库文档
router.delete('/:id', knowledgeBaseController.deleteKnowledgeDocument);

// 下载知识库文档
router.get('/download/:id', knowledgeBaseController.downloadKnowledgeFile);


router.get('/:id/questions', knowledgeBaseController.getQuestionList);

// 添加固定题目
router.post('/:id/questions', knowledgeBaseController.addQuestion);

// 更新题目
router.put('/:id/questions', knowledgeBaseController.updateQuestion);

// 删除题目
router.delete('/:id/questions/:questionId', knowledgeBaseController.deleteQuestion);

// 批量保存题目
router.post('/:id/batch-save-questions', knowledgeBaseController.batchSaveQuestions);

// 段落管理接口
router.get('/document/:id/segments', knowledgeBaseController.getDocumentSegments);
router.post('/document/:id/segments', knowledgeBaseController.createDocumentSegments);
router.put('/document/:id/segments/:segmentId', knowledgeBaseController.updateDocumentSegment);
router.delete('/document/:id/segments/:segmentId', knowledgeBaseController.deleteDocumentSegment);

// 删除DIFY文档(彻底删除)
router.delete('/dify-document/:id', knowledgeBaseController.deleteDifyDocument);

// 切换文档状态(启用/禁用)
router.patch('/:id/status', knowledgeBaseController.toggleDocumentStatus);

// 获取文档处理状态
router.get('/:id/process-status', knowledgeBaseController.getDocumentProcessStatus);

// 出题策略管理
router.get('/:id/prompt-strategy', knowledgeBaseController.getPromptStrategy);
router.post('/:id/prompt-strategy', knowledgeBaseController.savePromptStrategy);

module.exports = router;