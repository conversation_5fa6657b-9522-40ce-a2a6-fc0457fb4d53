<!-- 练习记录组件 -->
<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="formItems"
          :key="formKey"
          @search="handleSearch"
          @reset="handleReset"
        />
      </template>
    </page-header>

    <!-- 表格 -->
    <base-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :show-default-action="false"
      :pagination="pagination"
      @change="handleTableChangeWrapper"
      rowKey="id"
    >
      <!-- 操作按钮 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
            <a-button type="link" class="custom-button" @click="viewPracticeRecord(record)">
              <file-text-outlined />查看记录
            </a-button>
        </template>
      </template>
    </base-table>

    <!-- 练习对话记录弹窗 -->
    <a-modal
      v-model:visible="recordModalVisible"
      title="练习对话记录"
      :footer="null"
      width="800px"
      class="practice-modal"
    >
      <div class="practice-record-modal">
        <div class="practice-info">
          <div class="practice-detail">
            <h3 class="detail-title">练习记录详情</h3>
            <div class="detail-content">
              <div class="detail-item">
                <span class="item-label">练习人：</span>
                <span class="item-value">{{ currentRecord?.userName }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">练习科目：</span>
                <span class="item-value">{{ currentRecord?.subjectName }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">岗位类型：</span>
                <span class="item-value">{{ currentRecord?.departmentName }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">岗位名称：</span>
                <span class="item-value">{{ currentRecord?.positionFullName }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">晋升岗位等级：</span>
                <span class="item-value">{{ currentRecord?.levelFullName }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">题目数量：</span>
                <span class="item-value">{{ currentRecord?.questionNum }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">练习时长：</span>
                <span class="item-value">{{ currentRecord?.totalDuration }}分钟</span>
              </div>
              <div class="detail-item">
                <span class="item-label">练习时间：</span>
                <span class="item-value">{{ currentRecord?.createTime }}</span>
              </div>
            </div>
          </div>
        </div>

        <a-divider />

        <div class="practice-chat">
          <div class="chat-timeline">
            <a-timeline>
              <a-timeline-item
                v-for="(item, index) in currentRecord?.details"
                :key="item.id"
                :color="item.role === '系统' ? '#fbc2eb' : '#a18cd1'"
              >
                <div class="chat-item" :class="[item.role=='系统'?'system':'user',item.type=='analysis'?'analysis':'']">
                  <div class="chat-content">
                    <div :class="['chat-title',item.role=='系统'?'system':'user']">{{ item.role=='系统'?'餐考':'' }}</div>
                    <div class="chat-text">{{ item.content }}</div>
                    <div class="chat-time">{{ formatTime(item.createTime) }}</div>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { FileTextOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { practiceRecordData } from '@/mock/exam-manage';
import { SearchFormCard } from '@/components/SearchForm';
import { useTablePagination } from '@/utils/common';
import { getPracticeRecords, getPracticeRecordDetail } from '@/api/exam/practice-record';

// 表格加载状态
const loading = ref(false);

// 表格列定义
const columns = [
{
    title: '练习人',
    dataIndex: 'userName',  // 从 user 关联表中获取的用户名
    key: 'userName',
    width: 120,
  },
  {
    title: '练习科目',
    dataIndex: 'subjectName',  // 从 subjectKnowledge 中获取的名称
    key: 'subjectName',
    width: 120,
  },
  {
    title: '岗位类型',
    dataIndex: 'departmentName',  // 从 positionBelongDict 中获取的名称
    key: 'departmentName',
    width: 100,
  },
  {
    title: '岗位名称',
    dataIndex: 'positionFullName',  // 从 positionDict 中获取的名称
    key: 'positionFullName',
    width: 120,
  },
  {
    title: '晋升岗位等级',
    dataIndex: 'levelFullName',  // 从 level 中获取的名称
    key: 'levelFullName',
    width: 100,
  },
  {
    title: '题目数量',
    dataIndex: 'questionNum',  // 练习题目数量
    key: 'questionNum',
    width: 100,
    sorter: true,  // 添加排序功能
  },
  {
    title: '练习时长',
    dataIndex: 'totalDuration',  // 总练习时长
    key: 'totalDuration',
    width: 120,
    sorter: true,  // 添加排序功能
  },
  {
    title: '练习时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    sorter: true,  // 添加排序功能
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 120,
  }
];
const formKey = ref(0);

// 搜索文本
const searchText = ref('');

// 筛选
const positionFilter = ref('');

// 表格数据源
const dataSource = ref([]);

// 查询参数
const searchForm = reactive({
  userName: '',
  examSubject:'',
  durationRange: [], // 练习时长范围 [最小时长, 最大时长]
  timeRange: [], // 练习时间范围 [开始时间, 结束时间]
  sortField: '', // 排序字段
  sortOrder: '', // 排序方向
});

const formItems = ref([
  {
    label: '练习人',
    field: 'userName',
    type: 'input',
    placeholder: '请输入练习人'
  },
  {
    label: '练习科目',
    field: 'examSubject',
    type: 'input',
    placeholder: '请输入练习科目'
  },
  {
    label: '练习时长',
    field: 'durationRange',
    type: 'range-number',
    placeholder: ['最小时长(分钟)', '最大时长(分钟)'],
    style: { width: '240px' }
  },
  {
    label: '练习时间',
    field: 'timeRange',
    type: 'range-picker',
    placeholder: ['开始时间', '结束时间'],
    showTime: false,
    style: { width: '240px' }
  }
]);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      userName: searchForm.userName || undefined,
      examSubject: searchForm.examSubject || undefined,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    };

    // 添加排序参数
    if (searchForm.sortField && searchForm.sortOrder) {
      params.sortField = searchForm.sortField;
      params.sortOrder = searchForm.sortOrder;
    }

    // 处理练习时长范围筛选
    if (searchForm.durationRange && searchForm.durationRange.length === 2) {
      const [minDuration, maxDuration] = searchForm.durationRange;
      if (minDuration !== null && minDuration !== undefined) {
        params.minDuration = minDuration;
      }
      if (maxDuration !== null && maxDuration !== undefined) {
        params.maxDuration = maxDuration;
      }
    }

    // 处理练习时间范围筛选
    if (searchForm.timeRange && searchForm.timeRange.length === 2) {
      const [startTime, endTime] = searchForm.timeRange;
      if (startTime) {
        params.startTime = dayjs(startTime).format('YYYY-MM-DD 00:00:00');
      }
      if (endTime) {
        params.endTime = dayjs(endTime).format('YYYY-MM-DD 23:59:59');
      }
    }

    // 移除所有undefined的参数
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key];
      }
    });

    console.log('API请求参数:', params);

    // 使用API调用
    const response = await getPracticeRecords(params);

    if (response) {
      dataSource.value = response.rows;
      // 更新分页信息
      updatePagination({
        total: response.total || 0,
        pageNum: response.pageNum,
        pageSize: response.pageSize
      });
    } else {
      message.error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取练习记录数据失败', error);
    message.error('获取练习记录数据失败');
  } finally {
    loading.value = false;
  }
};

// 使用表格分页组合式函数
const {
  pagination,
  handleTableChange,
  updatePagination,
  resetPagination
} = useTablePagination({
  fetchData,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
});

// 创建包装函数处理Ant Design Vue表格的change事件
const handleTableChangeWrapper = (event) => {
  // 构造common.js中handleTableChange函数期望的参数格式
  const wrappedPag = {
    pagination: event.pagination,
    filters: event.filters,
    sorter: event.sorter
  };
  // 调用原始的handleTableChange函数
  handleTableChange(event.pagination,event.filters,event.sorter);
};

// 查看练习记录详情
const viewPracticeRecord = async (record) => {
  try {
    const response = await getPracticeRecordDetail(record.id);

    if (response) {
      currentRecord.value = {
        ...record,
        details: response.details
      };
      recordModalVisible.value = true;
    } else {
      message.error(response.message || '获取记录详情失败');
    }
  } catch (error) {
    console.error('获取练习记录详情失败', error);
    message.error('获取练习记录详情失败');
  }
};

// 搜索和筛选处理函数
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values);
  }
  // 重置到第一页
  resetPagination();
  fetchData();
};

const handleFilterChange = () => {
  resetPagination();
  fetchData();
};
const handleReset = () => {
  searchForm.userName = '';
  searchForm.examSubject = '';
  searchForm.durationRange = [];
  searchForm.timeRange = [];
  searchForm.sortField = '';
  searchForm.sortOrder = '';
  resetPagination();
  fetchData();
};

// 生命周期钩子
onMounted(() => {
  fetchData();
  formKey.value+=1
});

// 弹窗可见性
const recordModalVisible = ref(false);

// 当前查看的记录
const currentRecord = ref(null);

// 格式化聊天时间
const formatChatTime = (startTimeStr, minutesLater) => {
  if (!startTimeStr) return '';

  try {
    const startTime = new Date(startTimeStr);
    startTime.setMinutes(startTime.getMinutes() + minutesLater);

    const hours = startTime.getHours().toString().padStart(2, '0');
    const minutes = startTime.getMinutes().toString().padStart(2, '0');

    return `${hours}:${minutes}`;
  } catch (error) {
    console.error('时间格式化错误', error);
    return '';
  }
};

// 在script setup部分添加formatTime方法
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  return dayjs(timeStr).format('HH:mm:ss');
};
</script>

<style scoped>
/* 样式部分 */

/* 移除重复的.action-buttons样式定义，使用全局样式 */

:deep(.ant-table-fixed-right) {
  background-color: #fff;
}


.practice-info {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.practice-detail h3 {
  font-size: 18px;
  color: #333333;
  margin-bottom: 16px;
  background-image: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.practice-detail p {
  margin-bottom: 8px;
  color: #666666;
}


.chat-item {
  margin-bottom: 16px;
}

.chat-content {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-radius: 8px;
  position: relative;
}

.chat-item.user .chat-content {
  background-color: #f0f2ff;
}
.chat-item.analysis .chat-content {
  border: 1px solid #a18cd1;
}

.chat-title {
  font-weight: 500;
  color: #fff;
  margin-bottom: 8px;
  font-size: 12px;
  width:30px;
  height:30px;
  border-radius: 50%;
  background: #a18cd1;
  display: flex;
  justify-content: center;
  align-items: center;
  /* background: url('@/assets/images/user.png') no-repeat center center; */

}
.chat-title.user{
  background: url('@/assets/images/user.png') no-repeat center center;
  background-size: 100% 100%;
}

.chat-text {
  color: #666666;
  margin-bottom: 8px;
}

.chat-options {
  margin-top: 8px;
  color: #666666;
}

.chat-options p {
  margin-bottom: 4px;
}

.chat-time {
  font-size: 12px;
  color: #999999;
  text-align: right;
}

/* 弹窗样式优化 */
.practice-modal {
  :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
    padding: 16px 24px;
    border-bottom: none;
  }

  :deep(.ant-modal-title) {
    color: #fff;
    font-size: 18px;
    font-weight: 500;
  }

  :deep(.ant-modal-close) {
    color: #fff;
  }
}

.practice-record-modal {
  background-color: #fff;
  border-radius: 8px;
}

.practice-info {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.detail-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 500;
  position: relative;
  padding-left: 12px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
    border-radius: 2px;
  }
}

.detail-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.item-label {
  color: #666;
  min-width: 80px;
  margin-right: 8px;
}

.item-value {
  color: #333;
  flex: 1;
}


.chat-timeline {
  max-height: 400px;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
  }
}

.chat-item {
  margin-bottom: 16px;

  &.system .chat-content {
    background: #f0f2ff;
    border-radius: 8px;
  }

  &.user .chat-content {
    background: #f8f9fa;
    border-radius: 8px 8px 0 8px;
  }
}

.chat-content {
  padding: 12px 16px;
  position: relative;
}



.chat-text {
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
  word-break: break-all;
}

.chat-time {
  font-size: 12px;
  color: #999;
  text-align: right;
}
</style>
