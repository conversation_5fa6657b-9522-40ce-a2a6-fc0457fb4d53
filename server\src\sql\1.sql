SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;




INSERT INTO `achievement_templates`( `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ( '72ed5378-4d6d-40cf-af83-8ab801d379c2','初入宝殿', '首次学习一门科目的进度达到1%', '/uploads/achievements/1.png', 'learning', 'progress', '{\"type\":\"first_complete\",\"rule\":\"初入宝殿\",\"progress\":1}', 10, 1, 1, NULL, 'system',NOW(), NOW());
INSERT INTO `achievement_templates`( `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('72ed5378-4d6d-40cf-af83-8ab801d379c2', '知识探索', '完成5门科目的进度达到100%', '/uploads/achievements/2.png', 'learning', 'progress', '{\"type\":\"multiple_complete\",\"rule\":\"知识探索\",\"progress\":100,\"subjectCount\":5}', 20, 1, 2, NULL, 'system', NOW(), NOW());
INSERT INTO `achievement_templates`(`enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ( '72ed5378-4d6d-40cf-af83-8ab801d379c2', '学霸模式', '连续学习超过10天', '/uploads/achievements/3.png', 'time', 'streak', '{\"type\":\"study_streak\",\"rule\":\"学霸模式\",\"days\":10}', 30, 1, 3, NULL, NULL, NOW(), NOW());
INSERT INTO `achievement_templates`( `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ( '72ed5378-4d6d-40cf-af83-8ab801d379c2', '学无止境', '累计学习时长超过50小时', '/uploads/achievements/4.png', 'time', 'time', '{\"type\":\"study_time\",\"rule\":\"学无止境\",\"hours\":50}', 40, 1, 4, NULL, NULL, NOW(),NOW());
INSERT INTO `achievement_templates`( `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ( '72ed5378-4d6d-40cf-af83-8ab801d379c2', '碎片时间大师', '一天内三个时间范围都有练习记录', '/uploads/achievement/13.png', 'learning', 'time_based', '{\"type\":\"time_master\",\"rule\":\"跨片时间大师\",\"start1\":1,\"end1\":12,\"start2\":12,\"end2\":18,\"start3\":18,\"end3\":24,\"startTime1\":0,\"endTime1\":12,\"startTime2\":12,\"endTime2\":18,\"startTime3\":18,\"endTime3\":23}', 50, 1, 5, NULL, 'system', NOW(), NOW());
INSERT INTO `achievement_templates`( `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ( '72ed5378-4d6d-40cf-af83-8ab801d379c2', '全能力者', '所有岗位都有练习记录', '/uploads/achievement/12.png', 'learning', 'progress', '{\"type\":\"time_master\",\"rule\":\"跨片时间大师\",\"start1\":1,\"end1\":12,\"start2\":12,\"end2\":18,\"start3\":18,\"end3\":24}', 60, 1, 6, NULL, 'system',NOW(), NOW());
INSERT INTO `achievement_templates`( `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ( '72ed5378-4d6d-40cf-af83-8ab801d379c2', '金牌毕业生', '所有考试都是通过率100%', '/uploads/achievement/11.png', 'exam', 'progress', '{\"type\":\"all_pass\",\"rule\":\"全能毕业生\",\"passRate\":100}', 70, 1, 7, NULL, 'system', NOW(),NOW());
INSERT INTO `achievement_templates`( `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ( '72ed5378-4d6d-40cf-af83-8ab801d379c2', '早起鸟', '4点～7点之间有练习记录，连续5天', '/uploads/achievements/8.png', 'learning', 'time_based', '{\"type\":\"early_bird\",\"rule\":\"早起鸟\",\"startHour\":4,\"endHour\":7,\"days\":5,\"startTime\":4,\"endTime\":7}', 25, 1, 8, NULL, 'system',NOW(),NOW());
INSERT INTO `achievement_templates`(`enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('72ed5378-4d6d-40cf-af83-8ab801d379c2', '夜猫子', '22点～2点之间有练习记录，连续7天', '/uploads/achievements/9.png', 'learning', 'time_based', '{\"type\":\"夜猫子\",\"rule\":\"夜猫子\",\"timeRange\":null,\"days\":7,\"startTime\":22,\"endTime\":2}', 25, 1, 9, NULL, 'system', NOW(), NOW());
INSERT INTO `achievement_templates`( `enterprise_id`, `name`, `description`, `icon`, `category`, `rule_type`, `trigger_condition`, `reward_points`, `is_active`, `sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ( '72ed5378-4d6d-40cf-af83-8ab801d379c2', '旗开得胜', '获得第一个考试满分', '/uploads/achievements/10.png', 'exam', 'count', '{\"type\":\"first_perfect_score\",\"rule\":\"旗开得胜\"}', 80, 1, 10, NULL, NULL,NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;
