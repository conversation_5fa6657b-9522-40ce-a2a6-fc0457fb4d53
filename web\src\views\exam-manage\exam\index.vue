<!-- 考试记录组件 -->
<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchParams"
          :items="formItems"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
    </page-header>
    <!-- 表格 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      :action-config="actionConfig"
      :delete-title="deleteTitle"
      @view="viewExamRecord"
      @confirm="confirmRecord"

    >
      <!-- 考试科目/考试时间/岗位类型/岗位名称/岗位等级/分类/餐考人/得分/用时 -->
      <template #bodyCell="{ column, record }">
        <!-- 岗位名称 -->
        <template v-if="column.dataIndex === 'positionName'">
          {{ (record.positionName?.name || '未知') + (record.level?.name ? ' ' + record.level.name : '') }}
        </template>

           <!-- 审核状态 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.score >= record.passScore)">
            {{ record.score >= record.passScore ? '合格' : '不合格' }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template v-if="column.key === 'action'">
          <a-button
            type="link"
            size="small"
            class="custom-button"
            @click="viewExamDialog(record)"
          >
            <MessageOutlined />查看对话
          </a-button>
        </template>
      </template>
    </base-table>

    <!-- 考试记录详情弹窗 -->
    <a-modal
      v-model:visible="recordModalVisible"
      :title="`考试记录详情 - ${currentRecord?.examSubject || ''}`"
      width="800px"
      :footer="null"
    >
      <div class="exam-record-modal">
        <div class="record-header">
          <div class="record-title">
            <h2>{{ currentRecord?.examSubject }}</h2>
            <div class="record-score">
              <div class="score-circle" :class="getScoreClass(currentRecord?.score)">
                <span class="score-value">{{ currentRecord?.score }}</span>
                <span class="score-unit">分</span>
              </div>
              <div class="score-status">
                <a-tag :color="currentRecord?.score >= 80 ? '#52c41a' : '#f5222d'">
                  {{ currentRecord?.score >= 80 ? '合格' : '不合格' }}
                </a-tag>
              </div>
            </div>
          </div>

          <div class="record-info">
            <div class="info-item">
              <div class="info-label">餐考人</div>
              <div class="info-value">{{ currentRecord?.examinee }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">考试时间</div>
              <div class="info-value">{{ formatDateTime(currentRecord?.examTime) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">截止时间</div>
              <div class="info-value">{{ formatDateTime(currentRecord?.endTime) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">合格分</div>
              <div class="info-value">{{ currentRecord?.passScore || 80 }}分</div>
            </div>
            <div class="info-item">
              <div class="info-label">考试分钟数</div>
              <div class="info-value">{{ currentRecord?.examDuration || '-' }} 分钟</div>
            </div>
            <div class="info-item">
              <div class="info-label">用时</div>
              <div class="info-value">{{ currentRecord?.usedDuration }} 分钟</div>
            </div>
            <div class="info-item">
              <div class="info-label">岗位</div>
              <div class="info-value">
                {{ currentRecord?.positionName?.name || '未知' }}
                ({{ currentRecord?.level?.name || '未知' }})
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">考试状态</div>
              <div class="info-value">
                <a-tag :color="getExamStatusColor(currentRecord?.examStatus)">
                  {{ getStatusText(currentRecord?.examStatus) }}
                </a-tag>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">确认状态</div>
              <div class="info-value">
                <a-tag :color="getConfirmStatusColor(currentRecord?.confirmStatus)">
                  {{ getConfirmStatusText(currentRecord?.confirmStatus) }}
                </a-tag>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">确认人</div>
              <div class="info-value">{{ currentRecord?.confirmPerson || '-' }}</div>
            </div>
            <div class="info-item full-width">
              <div class="info-label">优点</div>
              <div class="info-value">{{ currentRecord?.advantage || '-' }}</div>
            </div>
            <div class="info-item full-width">
              <div class="info-label">改进点</div>
              <div class="info-value">{{ currentRecord?.improve || '-' }}</div>
            </div>
          </div>
        </div>

        <a-divider />

        <!-- <div class="record-content">
          <h3>试卷内容</h3>

          <div class="question-list" v-if="currentRecord?.examContent">
            <div
              v-for="(question, index) in currentRecord.examContent.questions"
              :key="index"
              class="question-item"
            >
              <div class="question-header">
                <div class="question-title">{{ index + 1 }}. {{ question.title }}</div>
                <div class="question-score">{{ question.score }}分</div>
              </div>
              <div class="question-answer">
                <div class="answer-label">考生答案：</div>
                <div class="answer-content">{{ question.userAnswer }}</div>
              </div>
              <div class="question-correct">
                <div class="correct-label">餐考答案：</div>
                <div class="correct-content">{{ question.correctAnswer }}</div>
              </div>
              <div class="question-result">
                <a-tag :color="question.isCorrect ? '#52c41a' : '#f5222d'">
                  {{ question.isCorrect ? '正确' : '错误' }}
                </a-tag>
                <span class="result-score">得分：{{ question.userScore }}/{{ question.score }}</span>
              </div>
            </div>
          </div>
          <div class="empty-content" v-else>
            <a-empty description="暂无试卷内容数据" />
          </div>
        </div> -->
      </div>
    </a-modal>

    <!-- 考试对话记录弹窗 -->
    <a-modal
      v-model:visible="dialogModalVisible"
      title="考试对话记录"
      :footer="null"
      width="800px"
      class="exam-dialog-modal"
    >
      <div class="exam-dialog-modal">
        <div class="exam-info">
          <div class="exam-detail">
            <div class="detail-content">
              <div class="detail-item">
                <span class="item-label">考试人：</span>
                <span class="item-value">{{ currentRecord?.examinee }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">考试科目：</span>
                <span class="item-value">{{ currentRecord?.examSubject }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">岗位名称：</span>
                <span class="item-value">{{ currentRecord?.positionName?.name }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">岗位等级：</span>
                <span class="item-value">{{ currentRecord?.level?.name }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">题目数量：</span>
                <span class="item-value">{{ currentRecord?.examContent?.length || 0 }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">考试时长：</span>
                <span class="item-value">{{ currentRecord?.usedDuration }}分钟</span>
              </div>
              <div class="detail-item">
                <span class="item-label">考试时间：</span>
                <span class="item-value">{{ formatDateTime(currentRecord?.examTime) }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">正确题数：</span>
                <span class="item-value">{{ getCorrectCount(currentRecord?.examContent) }}题</span>
              </div>
            </div>
          </div>
        </div>

        <a-divider />

        <div class="exam-chat">
          <div class="chat-timeline">
            <a-timeline>
              <template v-for="(item, index) in currentRecord?.examContent" :key="index">
                <!-- 问题 -->
                <a-timeline-item color="#a18cd1">
                  <div class="chat-item system">
                    <div class="chat-content">
                      <div class="chat-title system">餐考</div>
                      <div class="chat-text">{{ item.question }}</div>
                    </div>
                  </div>
                </a-timeline-item>

                                 <!-- 用户回答 -->
                <a-timeline-item :color="item.result ? '#52c41a' : '#f5222d'">
                  <div class="chat-item user">
                    <div class="chat-content">
                      <div class="chat-title user"></div>
                      <div class="chat-text">{{ item.userAnswer }}</div>
                      <div class="chat-result">
                        <a-tag :color="item.result ? '#52c41a' : '#f5222d'">
                          {{ item.result ? '正确' : '错误' }}
                        </a-tag>
                      </div>
                    </div>
                  </div>
                </a-timeline-item>

                <!-- 解析 -->
                <a-timeline-item v-if="item.analysis" color="#fbc2eb">
                  <div class="chat-item analysis">
                    <div class="chat-content">
                      <div class="chat-title analysis">解析</div>
                      <div class="chat-text">{{ item.analysis }}</div>
                    </div>
                  </div>
                </a-timeline-item>
              </template>
            </a-timeline>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 确认弹窗 -->
    <a-modal
      v-model:visible="confirmModalVisible"
      title="确认考试记录"
      @ok="handleConfirm"
      @cancel="confirmModalVisible = false"
      :confirmLoading="confirmLoading"
    >
      <p>确认通过此考试记录吗？</p>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, h } from 'vue';
import { message, Tag } from 'ant-design-vue';
import { MessageOutlined } from '@ant-design/icons-vue';
import { getExamRecords, getExamRecordDetail, updateConfirmStatus } from '@/api/exam/exam-record';
import { getPositionNameOptions, getLevelOptions } from '@/api/organization/position';
import { getExamSubjectOptions } from '@/api/exam/exam-review';
import dayjs from 'dayjs';
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN';
import SearchFormCard from '@/components/SearchForm/index.vue';
// import { getExamList } from '@/api/exam';
import { useTablePagination } from '@/utils/common';

// 表格列定义
const columns = [
  {
    title: '姓名',
    dataIndex: 'examinee',
    key: 'examinee',
  },
  {
    title: '所属岗位',
    dataIndex: 'positionName',
    key: 'positionName',
  },
  {
    title: '考试科目',
    dataIndex: 'examSubject',
    key: 'examSubject',
  },
  // {
  //   title: '考试得分',
  //   dataIndex: 'score',
  //   key: 'score',
  // },

  // {
  //   title: '考试分钟数',
  //   dataIndex: 'examDuration',
  //   key: 'examDuration',
  //   width: 100,
  //   customRender: ({ text }) => `${text || '-'} 分钟`
  // },
  // {
  //   title: '用时',
  //   dataIndex: 'usedDuration',
  //   key: 'usedDuration',
  //   width: 80,
  //   customRender: ({ text }) => `${text} 分钟`
  // },

   {
    title: '考试用时',
    dataIndex: 'examTime',
    key: 'examTime',
    customRender: ({ text ,record}) => {
      return `${record.usedDuration}/${record.examDuration}分钟`;
    }
  },
  {
    title: '考试时间',
    dataIndex: 'examTime',
    key: 'examTime',
    customRender: ({ text }) => formatDateTime(text)
  },
  // {
  //   title: '截止时间',
  //   dataIndex: 'endTime',
  //   key: 'endTime',
  //   width: 180,
  //   customRender: ({ text }) => formatDateTime(text)
  // },
  {
    title: '正确题数',
    dataIndex: 'correctCount',
    key: 'correctCount',
    customRender: ({ record }) => {
      const examContent = record.examContent;
      if (!examContent || !Array.isArray(examContent)) return '0';
      const correctCount = examContent.filter(item => item.result === true).length;
      return correctCount;
    }
  },
  {
    title: '答题数',
    dataIndex: 'questionNumber',
    key: 'questionNumber',
    customRender: ({ record }) => {
      const questionNumber = record.questionNumber || 0;
      const totalQuestions = record.examContent?.length || 0;
      return `${totalQuestions}/${questionNumber}`;
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    // customRender: ({ record }) => `${record.score >= record.passScore ? '合格' : '不合格'}`
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 200
  }

];

// 获取状态对应的颜色
const getStatusColor = (status) => {
  const colorMap = {
    '1': '#faad14', // 审核中-橙色
    '2': '#52c41a', // 已通过-绿色
    '3': '#f5222d', // 已驳回-红色
    '4': '#1890ff'  // 已完成-蓝色
  };
  return status? '#52c41a' : '#f5222d';
};

// 表格数据和加载状态
const tableData = ref([]);
const loading = ref(false);
const positionNames = ref([
  { id: 1, name: '测试岗位1' },
  { id: 2, name: '测试岗位2' },
  { id: 3, name: '测试岗位3' }
]);
const levelOptions = ref([]); // 岗位等级选项
// 添加考试科目选项
const examSubjectOptions = ref([]);

// 表格操作配置
const actionConfig = {
  view: true,
  confirm: true,
  delete: false
};

// 删除确认标题
const deleteTitle = '确定删除此考试记录吗?';



// 搜索参数
const searchParams = reactive({
  examSubject: '',
  examinee: '',
  confirmStatus: undefined,
  positionId: undefined,
  levelId: undefined,
  startDate: undefined,
  endDate: undefined
});

// 日期范围选择
const dateRange = ref([]);

// 弹窗可见性
const recordModalVisible = ref(false);
const confirmModalVisible = ref(false);
const confirmLoading = ref(false);
const dialogModalVisible = ref(false);

// 当前查看的记录
const currentRecord = ref(null);
const currentRecordId = ref(null);

// 搜索表单
const formItems = [

  {
    label: '餐考人',
    field: 'examinee',
    type: 'input',
    placeholder: '请输入餐考人'
  },
  {
    label: '考试科目',
    field: 'examSubject',
    type: 'select',
    placeholder: '请选择考试科目',
    options: examSubjectOptions,
    selectLabel: 'label',
    selectValue: 'value',
    showSearch: true,
    optionFilterProp: 'children',
    allowClear: true,
    width: '250px'
  },
  {
    label: '岗位名称',
    field: 'positionId',
    type: 'select',
    placeholder: '请选择岗位名称',
    options: positionNames,
    selectLabel: 'name',
    selectValue: 'id'
  },
  {
    label: '考试时间',
    field: 'dateRange',
    type: 'dateRange',
    placeholder: ['开始日期', '结束日期'],
    width: '240px',
  },
  // {
  //   label: '确认状态',
  //   field: 'confirmStatus',
  //   type: 'select',
  //   placeholder: '请选择确认状态',
  //   options: [
  //     { label: '已确认', value: '已确认' },
  //     { label: '未确认', value: '未确认' }
  //   ]
  // },

];

// 日期选择器中文配置
const locale = zhCN;

// 生命周期钩子
onMounted(async () => {
  try {
    // 先加载选项数据，再加载表格数据
    await Promise.all([
      fetchPositionOptions(),
      fetchExamSubjectOptions()
    ]);
    // 选项加载完成后再加载表格数据
    await fetchExamList();
  } catch (error) {
    console.error('页面初始化失败:', error);
    message.error('页面初始化失败，请刷新重试');
  }
});

// 获取筛选选项数据
const fetchPositionOptions = async () => {
  try {
    // 获取岗位名称选项
    const nameRes = await getPositionNameOptions();
    console.log("nameRes原始数据:", nameRes);

    // 检查返回的数据结构
    if (nameRes && nameRes.data) {
      console.log("nameRes.data:", nameRes.data);
      positionNames.value = nameRes.data;
    } else if (nameRes && nameRes.rows) {
      console.log("nameRes.rows:", nameRes.rows);
      positionNames.value = nameRes.rows;
    } else if (Array.isArray(nameRes)) {
      console.log("nameRes是数组:", nameRes);
      positionNames.value = nameRes;
    } else {
      console.log("无法解析的数据结构，使用测试数据");
      // 保留测试数据
    }

    console.log("最终positionNames:", positionNames.value);

    // 获取岗位等级选项
    const levelRes = await getLevelOptions();
    levelOptions.value = levelRes || [];

    console.log("选项数据加载完成");
    return Promise.resolve(); // 确保返回Promise
  } catch (error) {
    console.error('获取岗位选项数据失败', error);
    message.error('获取岗位选项数据失败');
    return Promise.resolve(); // 即使出错也返回Promise
  }
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

// 处理日期范围变化
const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    searchParams.startDate = dates[0].format('YYYY-MM-DD');
    searchParams.endDate = dates[1].format('YYYY-MM-DD');
  } else {
    searchParams.startDate = undefined;
    searchParams.endDate = undefined;
  }
  handleSearch();
};
// 重置搜索
const resetSearch = (values) => {
  if (values) {
    Object.assign(searchParams, values);
  } else {
    // 清空所有搜索条件
    Object.keys(searchParams).forEach(key => {
      searchParams[key] = undefined;
    });
    // 确保日期区间也被重置
    searchParams.dateRange = undefined;
    searchParams.startDate = undefined;
    searchParams.endDate = undefined;
  }
  resetPagination();
  fetchExamList();
};

// 查看考试记录
const viewExamRecord = async (record) => {
  try {
    loading.value = true;
    const result = await getExamRecordDetail(record.id);
    currentRecord.value = result;
    recordModalVisible.value = true;
  } catch (error) {
    console.error('获取考试记录详情失败', error);
    message.error('获取考试记录详情失败');
  } finally {
    loading.value = false;
  }
};

// 确认考试记录
const confirmRecord = (record) => {
  currentRecordId.value = record.id;
  confirmModalVisible.value = true;
};

// 查看考试对话记录
const viewExamDialog = async (record) => {
  try {
    loading.value = true;
    const result = await getExamRecordDetail(record.id);
    currentRecord.value = result;
    dialogModalVisible.value = true;
  } catch (error) {
    console.error('获取考试对话记录失败', error);
    message.error('获取考试对话记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理确认
const handleConfirm = async () => {
  try {
    confirmLoading.value = true;
    await updateConfirmStatus(currentRecordId.value, { confirmStatus: '已确认' });
    message.success('考试记录确认成功');
    confirmModalVisible.value = false;
    fetchExamList(); // 刷新数据
  } catch (error) {
    console.error('确认考试记录失败', error);
    message.error('确认考试记录失败');
  } finally {
    confirmLoading.value = false;
  }
};

// 获取成绩对应的样式类
const getScoreClass = (score) => {
  if (!score) return 'fail';
  score = Number(score);
  if (score >= 90) return 'excellent';
  if (score >= 80) return 'good';
  if (score >= 60) return 'pass';
  return 'fail';
};

// 获取考试状态文本
const getStatusText = (status) => {
  const statusMap = {
    'ongoing': '考试中',
    'completed': '已完成',
    'timeout': '已超时'
  };
  return statusMap[status] || '未知';
};

// 获取考试状态颜色
const getExamStatusColor = (status) => {
  const colorMap = {
    'ongoing': '#1890ff',
    'completed': '#52c41a',
    'timeout': '#ff4d4f'
  };
  return colorMap[status] || '#d9d9d9';
};

// 查询参数
const searchForm = reactive({
  examSubject: '',        // 考试科目 - 修改字段名与searchParams保持一致
  examinee: '',     // 考生姓名
  confirmStatus: undefined, // 确认状态 - 修改字段名与searchParams保持一致
  positionId: undefined, // 岗位ID
  startDate: undefined, // 开始日期
  endDate: undefined,   // 结束日期
});

// 获取考试列表
const fetchExamList = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      examSubject: searchForm.examSubject,  // 修改为使用正确的字段名
      examinee: searchForm.examinee,
      confirmStatus: searchForm.confirmStatus,  // 修改为使用正确的字段名
      positionId: searchForm.positionId,
      startDate: searchForm.startDate,
      endDate: searchForm.endDate,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    };

    console.log("API查询参数:", params);
    const response = await getExamRecords(params);
    tableData.value = response.records || [];
    // 更新分页信息
    updatePagination({
      total: response.total || 0,
      pageNum: response.pageNum,
      pageSize: response.pageSize
    });
  } catch (error) {
    console.error('获取考试列表失败', error);
    message.error('获取考试列表失败');
    // 发生错误时重置分页
    updatePagination({ total: 0, page: 1 });
  } finally {
    loading.value = false;
  }
};

// 使用表格分页组合式函数
const {
  pagination,
  handleTableChange,
  updatePagination,
  resetPagination
} = useTablePagination({
  fetchData: fetchExamList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
});

// 搜索
const handleSearch = (values) => {
  if (values) {
    // 处理日期区间
    if (values.dateRange && values.dateRange.length === 2) {
      values.startDate = values.dateRange[0].format('YYYY-MM-DD');
      values.endDate = values.dateRange[1].format('YYYY-MM-DD');
      delete values.dateRange; // 删除辅助字段
    }

    Object.assign(searchForm, values);
  }
  // 重置到第一页
  resetPagination();
  fetchExamList();
};

// 获取确认状态文本
const getConfirmStatusText = (status) => {
  const statusMap = {
    '1': '待审核',
    '2': '已通过',
    '3': '未通过'
  };
  return statusMap[status] || '未知';
};

// 获取确认状态颜色
const getConfirmStatusColor = (status) => {
  const colorMap = {
    '1': '#faad14', // 待审核-橙色
    '2': '#52c41a', // 已通过-绿色
    '3': '#f5222d'  // 未通过-红色
  };
  return colorMap[status] || '#d9d9d9';
};

// 计算正确题数
const getCorrectCount = (examContent) => {
  if (!examContent || !Array.isArray(examContent)) return 0;
  return examContent.filter(item => item.result === true).length;
};

// 获取考试科目选项
const fetchExamSubjectOptions = async () => {
  try {
    console.log('开始调用 getExamSubjectOptions API...');
    // 调用获取考试科目列表的API
    const res = await getExamSubjectOptions();
    console.log('API 返回的原始数据:', res);
    console.log('原始数据类型:', typeof res);
    console.log('是否为数组:', Array.isArray(res));

    // 如果是数组，查看第一个元素的结构
    if (Array.isArray(res) && res.length > 0) {
      console.log('第一个元素的结构:', res[0]);
      console.log('第一个元素的所有键:', Object.keys(res[0]));
    }

    // 处理API响应数据
    if (res && res.code === 200 && res.data) {
      console.log('匹配到格式: { code: 200, data: [...] }');
      // 如果后端返回的数据格式是 { code: 200, data: [...] }
      const subjects = Array.isArray(res.data) ? res.data : [];
      console.log('处理后的subjects:', subjects);
      examSubjectOptions.value = subjects.map(item => ({
        label: item.name || item.title || item.label,
        value: item.name || item.title || item.label // 使用name作为value
      }));
    } else if (Array.isArray(res)) {
      console.log('匹配到格式: 直接返回数组');
      // 如果后端直接返回数组
      console.log('数组长度:', res.length);
      if (res.length > 0) {
        console.log('第一个选项原始数据:', res[0]);
      }

      examSubjectOptions.value = res.map((item, index) => {
        console.log(`处理第${index}个选项:`, item);
        const label = item.name || item.title || item.label || item.knowledgeBaseName;
        const value = item.name || item.title || item.label || item.knowledgeBaseName;
        console.log(`处理结果: label=${label}, value=${value}`);
        return {
          label: label,
          value: value
        };
      });
    } else {
      console.log('没有匹配的数据格式，使用空数组');
      // 如果没有数据，使用空数组
      examSubjectOptions.value = [];
    }

    console.log('最终的 examSubjectOptions.value:', examSubjectOptions.value);
    console.log('选项数量:', examSubjectOptions.value.length);
    if (examSubjectOptions.value.length > 0) {
      console.log('第一个选项:', examSubjectOptions.value[0]);
    }
    return Promise.resolve();
  } catch (error) {
    console.error('获取考试科目选项失败:', error);
    // 如果API调用失败，使用模拟数据作为备用
    const mockData = [
      { label: '安全生产基础知识', value: '安全生产基础知识' },
      { label: '消防安全管理', value: '消防安全管理' },
      { label: '职业健康安全', value: '职业健康安全' },
      { label: '环境保护法规', value: '环境保护法规' },
      { label: '特种设备操作', value: '特种设备操作' }
    ];
    examSubjectOptions.value = mockData;
    console.warn('使用模拟数据作为考试科目选项:', examSubjectOptions.value);
    return Promise.resolve();
  }
};
</script>

<style scoped>
/* 样式部分 */

/* 移除重复的.action-buttons样式定义，使用全局样式 */

:deep(.ant-table-fixed-right) {
  background-color: #fff;
}

.action-column {
  display: flex;
  gap: 8px;
}

/* 记录详情弹窗样式 */
.record-header {
  margin-bottom: 24px;
}

.record-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.record-title h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.record-score {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  position: relative;
  color: white;
}

.score-circle.excellent {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.score-circle.good {
  background: linear-gradient(135deg, #1890ff, #096dd9);
}

.score-circle.pass {
  background: linear-gradient(135deg, #faad14, #d48806);
}

.score-circle.fail {
  background: linear-gradient(135deg, #ff4d4f, #cf1322);
}

.score-value {
  font-size: 28px;
  font-weight: bold;
  line-height: 1;
}

.score-unit {
  font-size: 14px;
  margin-top: 4px;
}

.score-status {
  margin-top: 4px;
}

.record-info {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
}

.info-item {
  display: flex;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  min-width: 80px;
  color: #666;
  font-weight: 500;
}

.info-value {
  flex: 1;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.5;
}

.record-content {
  margin-top: 20px;
}

.record-content h3 {
  font-size: 18px;
  margin-bottom: 16px;
  color: #333;
}

.question-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.question-item {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
}

.question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.question-title {
  font-weight: 500;
  font-size: 16px;
  color: #333;
  flex: 1;
}

.question-score {
  color: #ff7a45;
  font-weight: 500;
}

.question-answer,
.question-correct {
  display: flex;
  margin-bottom: 12px;
}

.answer-label,
.correct-label {
  min-width: 80px;
  color: #666;
  font-weight: 500;
}

.answer-content,
.correct-content {
  flex: 1;
}

.question-result {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #e8e8e8;
}

.result-score {
  color: #666;
}

.empty-content {
  padding: 32px 0;
  text-align: center;
}

/* 分数颜色样式 */
.excellent {
  color: #52c41a;
  font-weight: bold;
}

.good {
  color: #1890ff;
  font-weight: bold;
}

.pass {
  color: #faad14;
}

.fail {
  color: #f5222d;
}

/* 表格操作按钮样式 */
.custom-button {
  padding: 0 8px;
}

/* 考试对话记录弹窗样式 */
.exam-dialog-modal {
  .exam-info {
    margin-bottom: 24px;
  }

  .exam-detail {
    .detail-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
    }

    .detail-content {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      background-color: #f9f9f9;
      padding: 16px;
      border-radius: 6px;
    }

    .detail-item {
      display: flex;
      align-items: center;

      .item-label {
        font-weight: 500;
        color: #666;
        min-width: 80px;
      }

      .item-value {
        color: #333;
        flex: 1;
      }
    }
  }

  .exam-chat {
    .chat-timeline {
      max-height: 400px;
      overflow-y: auto;

      .chat-item {
        margin-bottom: 8px;

        .chat-content {
          padding: 12px;
          border-radius: 8px;
          background-color: #f5f5f5;

                     .chat-title {
             font-weight: 600;
             margin-bottom: 8px;
             font-size: 12px;
             width: 30px;
             height: 30px;
             border-radius: 50%;
             display: flex;
             justify-content: center;
             align-items: center;

             &.system {
               background: #722ed1;
               color: #fff;
             }

             &.user {
               background: url('@/assets/images/user.png') no-repeat center center;
               background-size: 100% 100%;
             }

             &.analysis {
               background: #eb2f96;
               color: #fff;
             }
           }

          .chat-text {
            color: #333;
            line-height: 1.6;
            margin-bottom: 8px;
            white-space: pre-wrap;
          }

          .chat-result {
            margin-top: 8px;
          }
        }

        &.system .chat-content {
          background: linear-gradient(135deg, #f6f0ff 0%, #e8d5ff 100%);
        }

        &.user .chat-content {
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
        }

        &.analysis .chat-content {
          background: linear-gradient(135deg, #fff0f6 0%, #ffd6e7 100%);
        }
      }
    }
  }
}
</style>
