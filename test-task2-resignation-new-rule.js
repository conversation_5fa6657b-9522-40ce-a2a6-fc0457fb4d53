/**
 * 任务2：员工离职功能测试脚本（新增规则）
 * 
 * 新增规则：如果离职时，该员工一条都没有employee_career_record记录，则新建一条
 * 
 * 测试场景：
 * 1. 员工有在职履历记录 -> 更新为离职状态
 * 2. 员工有履历记录但无在职状态 -> 创建新的离职记录
 * 3. 员工完全没有履历记录 -> 创建新的离职记录（新增规则）
 */

const axios = require('axios');

// 配置
const config = {
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    // 这里需要添加实际的认证token
    'Authorization': 'Bearer your-token-here'
  }
};

// 测试数据
const testScenarios = [
  {
    name: '场景1：员工有在职履历记录',
    employeeId: 69,
    description: '员工已有在职状态的履历记录，应该更新为离职状态'
  },
  {
    name: '场景2：员工有履历记录但无在职状态',
    employeeId: 70,
    description: '员工有履历记录但都不是在职状态，应该创建新的离职记录'
  },
  {
    name: '场景3：员工完全没有履历记录（新增规则）',
    employeeId: 71,
    description: '员工完全没有履历记录，应该创建新的离职记录'
  }
];

/**
 * 测试员工离职功能
 */
async function testEmployeeResignation(scenario) {
  console.log(`\n🚀 开始测试${scenario.name}`);
  console.log('='.repeat(60));
  console.log(`📋 测试描述: ${scenario.description}`);
  
  try {
    console.log(`\n📋 测试参数:`);
    console.log(`员工ID: ${scenario.employeeId}`);
    console.log(`状态: 0 (离职)`);
    
    console.log('\n📡 发送离职请求...');
    const response = await axios.put(
      `${config.baseURL}/organization/employee`,
      {
        id: scenario.employeeId,
        status: "0"
      },
      {
        headers: config.headers,
        timeout: config.timeout
      }
    );
    
    if (response.status === 200) {
      console.log('\n✅ 离职请求成功!');
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
      console.log('\n📊 预期结果:');
      if (scenario.name.includes('场景1')) {
        console.log('1. ✅ 更新现有在职履历记录为离职状态');
        console.log('2. ✅ 设置 departure_time 为当前时间');
        console.log('3. ✅ 设置 departure_reason 为"离职"');
      } else if (scenario.name.includes('场景2')) {
        console.log('1. ✅ 创建新的离职履历记录');
        console.log('2. ✅ 设置 status 为 0 (离职)');
        console.log('3. ✅ 设置 entry_time 和 departure_time');
      } else if (scenario.name.includes('场景3')) {
        console.log('1. ✅ 创建新的离职履历记录（新增规则）');
        console.log('2. ✅ 设置 status 为 0 (离职)');
        console.log('3. ✅ 使用员工入职时间作为 entry_time');
        console.log('4. ✅ 设置当前时间为 departure_time');
      }
      
      console.log('\n📊 通用预期结果:');
      console.log('1. ✅ 员工状态更新为离职(0)');
      console.log('2. ✅ 相关表的 is_active 设置为 0');
      console.log('3. ✅ 履历ID为空的记录更新 career_record_id');
      console.log('4. ✅ 填充所有名称字段');
      
    } else {
      console.log('\n❌ 离职请求失败');
      console.log('状态码:', response.status);
      console.log('响应数据:', response.data);
    }
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', error.response.data);
    }
    
    console.log('\n🔧 可能的问题:');
    console.log('1. 员工ID不存在或已经是离职状态');
    console.log('2. 认证token无效或过期');
    console.log('3. 服务器连接问题');
    console.log('4. 数据库连接或事务问题');
  }
}

/**
 * 显示SQL验证查询
 */
function showVerificationQueries() {
  console.log('\n📝 数据库验证查询:');
  
  testScenarios.forEach((scenario, index) => {
    console.log(`\n-- ${scenario.name} 验证查询`);
    console.log(`
-- 检查员工状态
SELECT id, name, status, entry_time FROM org_employee WHERE id = ${scenario.employeeId};

-- 检查履历记录（按时间倒序）
SELECT 
  id,
  employee_id,
  status,
  entry_time,
  departure_time,
  departure_reason,
  create_time,
  create_by
FROM employee_career_record 
WHERE employee_id = ${scenario.employeeId} 
ORDER BY create_time DESC;

-- 检查履历记录数量
SELECT COUNT(*) as total_records FROM employee_career_record WHERE employee_id = ${scenario.employeeId};

-- 检查相关表的状态更新
SELECT COUNT(*) as inactive_records FROM practice_record WHERE employee_id = ${scenario.employeeId} AND is_active = 0;
SELECT COUNT(*) as records_with_career_id FROM practice_record WHERE employee_id = ${scenario.employeeId} AND career_record_id IS NOT NULL;
    `);
  });
}

/**
 * 显示测试前的数据准备建议
 */
function showDataPreparation() {
  console.log('\n📋 测试前数据准备建议:');
  console.log(`
-- 场景1：为员工69创建在职履历记录
INSERT INTO employee_career_record (employee_id, open_id, enterprise_id, status, entry_time, create_by, update_by)
SELECT id, open_id, enterprise_id, 1, entry_time, 'test', 'test'
FROM org_employee WHERE id = 69;

-- 场景2：为员工70创建非在职履历记录
INSERT INTO employee_career_record (employee_id, open_id, enterprise_id, status, entry_time, departure_time, create_by, update_by)
SELECT id, open_id, enterprise_id, 0, entry_time, NOW(), 'test', 'test'
FROM org_employee WHERE id = 70;

-- 场景3：确保员工71没有任何履历记录
DELETE FROM employee_career_record WHERE employee_id = 71;

-- 确保测试员工存在且状态为在职
UPDATE org_employee SET status = '1' WHERE id IN (69, 70, 71);
  `);
}

// 运行所有测试
async function runAllTests() {
  console.log('🎯 任务2员工离职功能测试（新增规则）');
  console.log('='.repeat(80));
  
  showDataPreparation();
  
  for (const scenario of testScenarios) {
    await testEmployeeResignation(scenario);
    
    // 在测试之间添加延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  showVerificationQueries();
  
  console.log('\n' + '='.repeat(80));
  console.log('📋 所有测试完成');
  console.log('请根据上述验证查询检查数据库中的实际结果');
  
  console.log('\n🔍 重点验证项目:');
  console.log('1. 场景3（新增规则）：员工71应该有新创建的离职履历记录');
  console.log('2. 所有场景：相关表的is_active字段应该为0');
  console.log('3. 所有场景：career_record_id字段应该被正确设置');
  console.log('4. 所有场景：名称字段应该被正确填充');
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testEmployeeResignation,
  showVerificationQueries,
  showDataPreparation,
  testScenarios
};
