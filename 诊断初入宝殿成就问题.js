const { setupAchievementEventListeners, emitAchievementEvent, ACHIEVEMENT_EVENTS } = require('./server/src/utils/achievementEventListener');

/**
 * 诊断初入宝殿成就问题
 */
async function diagnoseFirstEntryAchievement() {
  console.log('🔍 开始诊断初入宝殿成就问题...\n');
  
  try {
    // 1. 启动事件监听器
    console.log('1️⃣ 启动成就事件监听器...');
    setupAchievementEventListeners();
    await delay(1000);
    console.log('✅ 事件监听器启动完成\n');
    
    // 2. 检查数据库连接和模型
    console.log('2️⃣ 检查数据库模型...');
    await checkDatabaseModels();
    
    // 3. 检查成就模板配置
    console.log('3️⃣ 检查成就模板配置...');
    await checkAchievementTemplates();
    
    // 4. 模拟发射事件
    console.log('4️⃣ 模拟发射初入宝殿事件...');
    await simulateFirstCompleteEvent();
    
    console.log('\n🎯 诊断完成！请查看上述日志以定位问题。');
    
  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
  }
  
  // 保持进程运行一段时间以观察日志
  setTimeout(() => {
    console.log('诊断结束，退出程序...');
    process.exit(0);
  }, 5000);
}

/**
 * 检查数据库模型
 */
async function checkDatabaseModels() {
  try {
    const sequelize = require('./server/src/config/database');
    
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接正常');
    
    // 检查模型是否可以加载
    const AchievementTemplate = require('./server/src/models/AchievementTemplate');
    const UserAchievement = require('./server/src/models/UserAchievement');
    const PracticeRecord = require('./server/src/models/practice-record');
    const ExamConfig = require('./server/src/models/ExamConfigModel');
    
    console.log('✅ 所有必要模型加载成功');
    
  } catch (error) {
    console.error('❌ 数据库模型检查失败:', error.message);
  }
}

/**
 * 检查成就模板配置
 */
async function checkAchievementTemplates() {
  try {
    const AchievementTemplate = require('./server/src/models/AchievementTemplate');
    const { addEnterpriseFilter } = require('./server/src/utils/enterpriseFilter');
    
    const enterpriseId = '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32'; // 你的企业ID
    
    // 查找所有成就模板
    const allTemplates = await AchievementTemplate.findAll(
      addEnterpriseFilter({
        where: { isActive: true }
      }, enterpriseId)
    );
    
    console.log(`📊 找到 ${allTemplates.length} 个活跃的成就模板`);
    
    // 查找初入宝殿模板
    const firstCompleteTemplates = await AchievementTemplate.findAll(
      addEnterpriseFilter({
        where: {
          isActive: true,
          ruleType: 'progress'
        }
      }, enterpriseId)
    );
    
    console.log(`🏆 找到 ${firstCompleteTemplates.length} 个进度类成就模板:`);
    
    firstCompleteTemplates.forEach(template => {
      const condition = JSON.parse(template.triggerCondition || '{}');
      console.log(`  - ${template.name}: type="${condition.type}", progress=${condition.progress}%`);
    });
    
    // 检查是否有 first_complete 类型的模板
    const firstCompleteTemplate = firstCompleteTemplates.find(t => {
      const condition = JSON.parse(t.triggerCondition || '{}');
      return condition.type === 'first_complete';
    });
    
    if (firstCompleteTemplate) {
      console.log('✅ 找到初入宝殿成就模板');
    } else {
      console.log('❌ 未找到初入宝殿成就模板（type: first_complete）');
    }
    
  } catch (error) {
    console.error('❌ 检查成就模板失败:', error.message);
  }
}

/**
 * 模拟发射初入宝殿事件
 */
async function simulateFirstCompleteEvent() {
  try {
    const testEvent = {
      userId: 999, // 测试用户ID
      openId: 'test_diagnostic_openid',
      enterpriseId: '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32',
      subjectId: '999', // 测试科目ID
      subjectName: '诊断测试科目',
      positionName: '1',
      positionLevel: '1'
    };
    
    console.log('🚀 发射测试事件:', testEvent);
    emitAchievementEvent(ACHIEVEMENT_EVENTS.FIRST_COMPLETE, testEvent);
    
    // 等待事件处理
    await delay(3000);
    
  } catch (error) {
    console.error('❌ 模拟事件发射失败:', error.message);
  }
}

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行诊断
diagnoseFirstEntryAchievement().catch(console.error); 