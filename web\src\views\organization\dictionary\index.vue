<template>
  <div class="dictionary-container">
    <page-header>
      <template #search>
        <!-- 添加搜索表单 -->
        <search-form-card
          :model-value="searchForm"
          :items="searchFormItems"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <template #actions>
        <a-space>
          <a-button type="primary" class="primary-button" @click="handleAdd">
            新增字典
          </a-button>
          <a-button class="primary-button" @click="batchEnable(true)">
            批量启用
          </a-button>
          <a-button class="primary-button" @click="batchEnable(false)">
            批量禁用
          </a-button>
        </a-space>
      </template>
    </page-header>

      <!-- 表格 -->
      <base-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
        rowKey="id"
        @edit="handleEdit"
        @delete="handleDelete"
        :action-config="actionConfig"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-badge :status="record.status ? 'success' : 'error'" :text="record.status ? '启用' : '禁用'" />
          </template>
          <template v-if="column.key === 'action'">
              <a-button type="link" size="small"   class="custom-button" @click="handleDictionaryItems(record)">
                <unordered-list-outlined />字典项</a-button>
          </template>
        </template>
      </base-table>

    <!-- 新增/编辑字典弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form :model="formState" :rules="formRules" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="字典名称" name="typeName">
          <a-input v-model:value="formState.typeName" placeholder="请输入字典名称" />
        </a-form-item>
        <a-form-item label="字典编码" name="typeCode">
          <a-input v-model:value="formState.typeCode" placeholder="请输入字典编码" :disabled="modalType === 'edit'" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formState.status">
            <a-radio :value="true">启用</a-radio>
            <a-radio :value="false">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" placeholder="请输入备注" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 字典项管理弹窗 -->
    <a-modal
      v-model:visible="itemsModalVisible"
      :title="`字典项管理 - ${currentDictionary.typeName || ''}`"
      @cancel="handleItemsModalCancel"
      width="900px"
      :footer="null"
    >
      <div class="dict-items-header">
        <a-space>
          <a-button type="primary" class="primary-button" @click="handleAddItem">
            新增字典项
          </a-button>
          <a-input-search
            placeholder="搜索字典项"
            v-model:value="itemSearchText"
            style="width: 250px;"
            @search="handleItemSearch"
          />
        </a-space>
      </div>
      
      <a-table
        :columns="itemColumns"
        :data-source="itemDataSource"
        :loading="itemsLoading"
        :pagination="itemPagination"
        @change="handleItemTableChange"
        rowKey="id"
        bordered
        size="middle"
        :scroll="{ y: 400, x: 800 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-badge :status="record.status ? 'success' : 'error'" :text="record.status ? '启用' : '禁用'" />
          </template>
          <template v-if="column.key === 'action'">
            <a-space size="small">
              <a-button type="link" size="small" class="dict-action-btn" @click="handleEditItem(record)">编辑</a-button>
              <a-button type="link" size="small" class="dict-action-btn" @click="handleDeleteItem(record)">删除</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
      
      <div class="dict-items-footer">
        <a-button class="primary-button" @click="handleItemsModalCancel">关闭</a-button>
      </div>
    </a-modal>

    <!-- 新增/编辑字典项弹窗 -->
    <a-modal
      v-model:visible="itemModalVisible"
      :title="itemModalTitle"
      @ok="handleItemModalOk"
      @cancel="handleItemModalCancel"
      width="500px"
    >
      <a-form :model="itemFormState" :rules="itemFormRules" ref="itemFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="字典项名称" name="dictLabel">
          <a-input v-model:value="itemFormState.dictLabel" placeholder="请输入字典项名称" />
        </a-form-item>
        <a-form-item label="字典项值" name="dictValue">
          <a-input v-model:value="itemFormState.dictValue" placeholder="请输入字典项值" />
        </a-form-item>
        <a-form-item label="排序号" name="dictSort">
          <a-input-number v-model:value="itemFormState.dictSort" placeholder="请输入排序号" style="width: 100%" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="itemFormState.status">
            <a-radio :value="true">启用</a-radio>
            <a-radio :value="false">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="CSS样式" name="cssClass">
          <a-input v-model:value="itemFormState.cssClass" placeholder="请输入CSS样式" />
        </a-form-item>
        <a-form-item label="列表样式" name="listClass">
          <a-input v-model:value="itemFormState.listClass" placeholder="请输入列表样式" />
        </a-form-item>
        <a-form-item label="是否默认" name="isDefault">
          <a-radio-group v-model:value="itemFormState.isDefault">
            <a-radio :value="true">是</a-radio>
            <a-radio :value="false">否</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="itemFormState.remark" placeholder="请输入备注" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import { 
  getDictionaryTypeList, 
  getDictionaryTypeDetail, 
  addDictionaryType, 
  updateDictionaryType, 
  deleteDictionaryType,
  batchUpdateDictionaryStatus,
  getDictionaryDataList,
  getDictionaryDataByTypeCode,
  getDictionaryDataDetail,
  addDictionaryData,
  updateDictionaryData,
  deleteDictionaryData
} from '@/api/organization/dictionary';
import { useTablePagination } from '@/utils/common';
import { SearchFormCard } from '@/components/SearchForm';

const actionConfig = {
  edit: true,
  delete: true
};
// 表格列定义
const columns = [
  {
    title: '字典名称',
    dataIndex: 'typeName',
    key: 'typeName',
    width: '15%',
  },
  {
    title: '字典编码',
    dataIndex: 'typeCode',
    key: 'typeCode',
    width: '15%',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '8%',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true,
    width: '25%',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: '15%',
  },
  {
    title: '操作',
    key: 'action',
    width: '170px',
    fixed: 'right',
  },
];

// 表格数据
const dataSource = ref([]);
const loading = ref(false);

// 查询参数
const searchForm = reactive({
  typeName: '',
  typeCode: '',
  status: undefined,
  sortField: '',
  sortOrder: ''
});

// 搜索表单配置
const searchFormItems = computed(() => [
  {
    label: '字典名称',
    field: 'typeName',
    type: 'input',
    placeholder: '请输入字典名称',
    width: '200px'
  },
  {
    label: '字典编码',
    field: 'typeCode',
    type: 'input',
    placeholder: '请输入字典编码',
    width: '200px'
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    placeholder: '请选择状态',
    width: '150px',
    options: [
      { label: '全部', value: undefined },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ]
  }
]);

// 加载字典类型数据
const loadData = async () => {
  loading.value = true;
  try {
    // 构建API请求参数，注意参数名可能需要适配后端API
    const params = {
      typeName: searchForm.typeName,
      typeCode: searchForm.typeCode,
      status: searchForm.status,
      pageNum: pagination.current,  // 注意：这里使用pageNum而不是page，与原始代码保持一致
      pageSize: pagination.pageSize,
      sortField: searchForm.sortField,
      sortOrder: searchForm.sortOrder
    };
    
    // 使用setTimeout减少界面阻塞感
    setTimeout(async () => {
      try {
        const res = await getDictionaryTypeList(params);
        
        // 添加缓存优化 - 避免不必要的重新渲染
        if (JSON.stringify(dataSource.value) !== JSON.stringify(res.rows || [])) {
          dataSource.value = res.rows || [];
        }
        
        // 更新分页信息
        updatePagination({
          total: res.total || 0,
          current: pagination.current, // 保持当前页码
          pageSize: pagination.pageSize
        });
      } catch (error) {
        console.error('获取字典类型列表失败:', error);
        message.error('获取字典类型列表失败');
        // 发生错误时重置分页
        updatePagination({ total: 0, current: 1 });
      } finally {
        loading.value = false;
      }
    }, 50); 
  } catch (error) {
    console.error('参数处理失败:', error);
    message.error('获取字典类型列表失败');
    loading.value = false;
    // 发生错误时重置分页
    updatePagination({ total: 0, current: 1 });
  }
};

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: loadData,
  initialPagination: { 
    current: 1, 
    pageSize: 10, 
    total: 0,
    showTotal: (total) => `共 ${total} 条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100']
  },
  searchForm
});

// 表格选择
const selectedRowKeys = ref([]);
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 批量启用/禁用
const batchEnable = async (status) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }
  
  try {
    await batchUpdateDictionaryStatus(selectedRowKeys.value, status);
    const action = status ? '启用' : '禁用';
    message.success(`批量${action}成功`);
    
    // 重新加载数据
    loadData();
    // 清空选择
    selectedRowKeys.value = [];
  } catch (error) {
    console.error('批量更新状态失败:', error);
  }
};

// 新增/编辑表单相关
const formRef = ref(null);
const modalVisible = ref(false);
const modalTitle = ref('新增字典');
const modalType = ref('add'); // add 或 edit
const currentRecord = ref(null);

const formState = reactive({
  id: undefined,
  typeName: '',
  typeCode: '',
  status: true,
  remark: '',
});

const formRules = {
  typeName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
  typeCode: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
};

// 编辑字典
const handleEdit = async (record) => {
  modalType.value = 'edit';
  modalTitle.value = '编辑字典';
  currentRecord.value = record;
  
  try {
    const res = await getDictionaryTypeDetail(record.id);
    Object.assign(formState, res);
    modalVisible.value = true;
  } catch (error) {
    console.error('获取字典类型详情失败:', error);
    message.error('获取字典类型详情失败');
  }
};

// 删除字典
const handleDelete = async (record) => {
  try {
    await deleteDictionaryType(record.id);
    message.success('删除成功');
    loadData();
  } catch (error) {
    console.error('删除字典类型失败:', error);
  }
};

// 打开新增弹窗
const handleAdd = () => {
  modalType.value = 'add';
  modalTitle.value = '新增字典';
  currentRecord.value = null;
  
  // 重置表单数据
  Object.assign(formState, {
    id: undefined,
    typeName: '',
    typeCode: '',
    status: true,
    remark: ''
  });
  
  modalVisible.value = true;
};

// 确认弹窗
const handleModalOk = async () => {
  try {
    await formRef.value.validate();
    
    if (modalType.value === 'add') {
      await addDictionaryType(formState);
      message.success('新增成功');
    } else {
      await updateDictionaryType(formState);
      message.success('修改成功');
    }
    
    modalVisible.value = false;
    loadData();
  } catch (error) {
    console.error('提交表单失败:', error);
  }
};

// 取消弹窗
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 字典项管理相关
// 字典项表格列定义
const itemColumns = [
  {
    title: '字典项名称',
    dataIndex: 'dictLabel',
    key: 'dictLabel',
    width: '25%',
  },
  {
    title: '字典项值',
    dataIndex: 'dictValue',
    key: 'dictValue',
    width: '20%',
  },
  {
    title: '排序号',
    dataIndex: 'dictSort',
    key: 'dictSort',
    width: '10%',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '15%',
  },
  {
    title: '操作',
    key: 'action',
    width: '15%'
  },
];

// 字典项表格数据
const itemsModalVisible = ref(false);
const currentDictionary = ref({});
const itemDataSource = ref([]);
const itemsLoading = ref(false);

// 字典项查询参数
const itemSearchForm = reactive({
  searchText: '',
  sortField: '',
  sortOrder: ''
});

// 字典项搜索文本
const itemSearchText = ref('');

// 字典项搜索处理
const handleItemSearch = () => {
  itemSearchForm.searchText = itemSearchText.value;
  resetItemPagination();
  loadDictionaryItems(currentDictionary.value.id);
};

// 加载字典项数据
const loadDictionaryItems = async (typeId) => {
  if (!typeId) return;
  
  itemsLoading.value = true;
  try {
    // 构建API请求参数，注意参数名可能需要适配后端API
    const params = {
      typeId,
      searchText: itemSearchForm.searchText,
      pageNum: itemPagination.current,  // 注意：这里使用pageNum而不是page，与原始代码保持一致
      pageSize: itemPagination.pageSize,
      sortField: itemSearchForm.sortField,
      sortOrder: itemSearchForm.sortOrder
    };
    
    // 使用setTimeout减少界面阻塞感
    setTimeout(async () => {
      try {
        const res = await getDictionaryDataList(params);
        
        // 添加缓存优化 - 避免不必要的重新渲染
        if (JSON.stringify(itemDataSource.value) !== JSON.stringify(res.rows || [])) {
          itemDataSource.value = res.rows || [];
        }
        
        // 更新分页信息
        updateItemPagination({
          total: res.total || 0,
          current: itemPagination.current, // 保持当前页码
          pageSize: itemPagination.pageSize
        });
      } catch (error) {
        console.error('获取字典数据列表失败:', error);
        message.error('获取字典数据列表失败');
        // 发生错误时重置分页
        updateItemPagination({ total: 0, current: 1 });
      } finally {
        itemsLoading.value = false;
      }
    }, 50);
  } catch (error) {
    console.error('参数处理失败:', error);
    message.error('获取字典数据列表失败');
    itemsLoading.value = false;
    // 发生错误时重置分页
    updateItemPagination({ total: 0, current: 1 });
  }
};

// 使用表格分页组合式函数(字典项)
const { 
  pagination: itemPagination, 
  handleTableChange: handleItemTableChange, 
  updatePagination: updateItemPagination, 
  resetPagination: resetItemPagination 
} = useTablePagination({
  fetchData: () => loadDictionaryItems(currentDictionary.value.id),
  initialPagination: { 
    current: 1, 
    pageSize: 10, 
    total: 0,
    showTotal: (total) => `共 ${total} 条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100']
  },
  searchForm: itemSearchForm
});

// 打开字典项管理弹窗
const handleDictionaryItems = (record) => {
  currentDictionary.value = record;
  
  // 重置字典项分页和搜索
  resetItemPagination();
  itemSearchForm.searchText = '';
  itemSearchForm.sortField = '';
  itemSearchForm.sortOrder = '';
  itemSearchText.value = '';
  
  itemsModalVisible.value = true;
  loadDictionaryItems(record.id);
};

// 关闭字典项管理弹窗
const handleItemsModalCancel = () => {
  itemsModalVisible.value = false;
  itemSearchText.value = '';
  itemSearchForm.searchText = '';
};

// 字典项表单相关
const itemFormRef = ref(null);
const itemModalVisible = ref(false);
const itemModalTitle = ref('新增字典项');
const itemModalType = ref('add'); // add 或 edit
const currentItemRecord = ref(null);

const itemFormState = reactive({
  id: undefined,
  typeId: undefined,
  dictLabel: '',
  dictValue: '',
  dictSort: 0,
  cssClass: '',
  listClass: '',
  isDefault: false,
  status: true,
  remark: ''
});

const itemFormRules = {
  dictLabel: [{ required: true, message: '请输入字典项名称', trigger: 'blur' }],
  dictValue: [{ required: true, message: '请输入字典项值', trigger: 'blur' }],
};

// 打开新增字典项弹窗
const handleAddItem = () => {
  itemModalType.value = 'add';
  itemModalTitle.value = '新增字典项';
  currentItemRecord.value = null;
  
  // 重置表单数据
  Object.assign(itemFormState, {
    id: undefined,
    typeId: currentDictionary.value.id,
    dictLabel: '',
    dictValue: '',
    dictSort: itemDataSource.value.length + 1,
    cssClass: '',
    listClass: '',
    isDefault: false,
    status: true,
    remark: ''
  });
  
  itemModalVisible.value = true;
};

// 编辑字典项
const handleEditItem = async (record) => {
  itemModalType.value = 'edit';
  itemModalTitle.value = '编辑字典项';
  currentItemRecord.value = record;
  
  try {
    const res = await getDictionaryDataDetail(record.id);
    Object.assign(itemFormState, res);
    itemModalVisible.value = true;
  } catch (error) {
    console.error('获取字典数据详情失败:', error);
    message.error('获取字典数据详情失败');
  }
};

// 删除字典项
const handleDeleteItem = async (record) => {
  try {
    await deleteDictionaryData(record.id);
    message.success('删除成功');
    loadDictionaryItems(currentDictionary.value.id);
  } catch (error) {
    console.error('删除字典数据失败:', error);
  }
};

// 确认字典项弹窗
const handleItemModalOk = async () => {
  try {
    await itemFormRef.value.validate();
    
    if (itemModalType.value === 'add') {
      await addDictionaryData(itemFormState);
      message.success('新增成功');
    } else {
      await updateDictionaryData(itemFormState);
      message.success('修改成功');
    }
    
    itemModalVisible.value = false;
    loadDictionaryItems(currentDictionary.value.id);
  } catch (error) {
    console.error('提交字典项表单失败:', error);
  }
};

// 取消字典项弹窗
const handleItemModalCancel = () => {
  itemModalVisible.value = false;
};

// 搜索处理
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values);
  }
  resetPagination();
  loadData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.typeName = '';
  searchForm.typeCode = '';
  searchForm.status = undefined;
  searchForm.sortField = '';
  searchForm.sortOrder = '';
  resetPagination();
  loadData();
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.dictionary-container {
  width: 100%;
}

.operation-area {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
}

.dict-items-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
}

.dict-items-footer {
  margin-top: 16px;
  text-align: right;
}

.primary-button {
  background-color: #a18cd1;
  border-color: #a18cd1;
  min-width: 88px;
  color: #fff;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 32px;
  line-height: 32px;
}

.primary-button:hover {
  background-color: #8a65c9;
  border-color: #8a65c9;
  color: #fff;
}

.dict-action-btn {
  padding: 0 4px;
  margin: 0;
  height: 24px;
  color: #a18cd1;
}

/* 表格固定列样式优化 */
:deep(.ant-table-fixed-right) {
  background-color: #fff;
}
</style> 