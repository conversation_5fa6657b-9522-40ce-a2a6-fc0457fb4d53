const express = require('express');
const path = require('path');
const cors = require('cors');
const bodyParser = require('body-parser');
const morgan = require('morgan');
const dotenv = require('dotenv');
const http = require('http');
const WebSocket = require('ws');
const routes = require('./routes');
const { testConnection, closeConnection } = require('./config/database');
const { initializeAchievementSystem } = require('./utils/achievementEventListener');
const organizationRoutes = require('./routes/organization');
const userRouter = require('./routes/user');
const examRoutes = require('./routes/exam');
const knowledgeBaseRouter = require('./routes/knowledge-base');
const practiceRecordRoutes = require('./routes/practice-record-route');
const wechatExamRoutes = require('./routes/wechat-exam');
const infoConfigRoutes = require('./routes/infoConfig');
const certificateRoutes = require('./routes/certificate');
const restaurantConfigRoutes = require('./routes/restaurantConfigRoutes');
const knowledgeBaseRoutes = require('./routes/knowledge-base');
const kbQuestionsRoutes = require('./routes/kb-questions');
const restaurantRecordRoutes = require('./routes/restaurantRecordRoutes');
const simpleFileRoutes = require('./routes/simpleFileRoutes');
const wechatPracticeController = require('./controllers/weichat/wechatPracticeController');

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;
const API_PREFIX = process.env.API_PREFIX || '/api';

// 请求超时中间件
app.use((req, res, next) => {
  // 设置请求超时时间为30秒
  req.setTimeout(30000, () => {
    const err = new Error('Request Timeout');
    err.status = 408;
    next(err);
  });
  
  // 设置响应超时时间为30秒
  res.setTimeout(30000, () => {
    const err = new Error('Response Timeout');
    err.status = 408;
    next(err);
  });
  
  next();
});

// 中间件
app.use(cors());
app.use(bodyParser.json({ limit: '10mb' })); // 增加请求体大小限制
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('dev'));

// 健康检查端点
app.get('/health', async (req, res) => {
  try {
    // 检查数据库连接
    const dbStatus = await testConnection();
    
    const healthStatus = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbStatus ? 'connected' : 'disconnected',
      memory: process.memoryUsage(),
      version: process.version
    };
    
    res.status(dbStatus ? 200 : 503).json(healthStatus);
  } catch (error) {
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));
// 上传文件静态访问
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));
// 餐烤师头像访问
app.use('/public/uploads', express.static(path.join(__dirname, '../public/uploads')));

// 测试数据库连接
testConnection();

// 初始化成就系统
initializeAchievementSystem();

// 注册不需要认证的公共路由 - 放在其他路由和认证中间件之前
app.use('/api/simple-file', simpleFileRoutes);

// 路由
app.use(API_PREFIX, routes);
app.use('/api/organization', organizationRoutes);
app.use('/api/exam', examRoutes);
if (userRouter) app.use('/api/user', userRouter);

// 知识库题目路由 - 放在知识库路由前面
console.log('注册知识库题目路由');
app.use('/api/knowledge-base', kbQuestionsRoutes);

// 知识库路由
if (knowledgeBaseRouter) app.use('/api/knowledge-base', knowledgeBaseRouter);

app.use('/api', practiceRecordRoutes);
app.use('/api/wechat/exam', wechatExamRoutes);
app.use('/api/info-config', infoConfigRoutes);
app.use('/api', restaurantConfigRoutes);
app.use('/api/restaurantRecord', restaurantRecordRoutes);

// 证书模块路由，前端使用/api/certificate路径进行访问
app.use('/api/certificate', certificateRoutes);

// 注册路由
app.use('/organization', organizationRoutes);
app.use('/knowledge-base', knowledgeBaseRoutes);

// 默认路由
app.get('/', (req, res) => {
  res.json({
    message: '欢迎使用阿依来后台管理系统API',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// 超时错误处理
app.use((err, req, res, next) => {
  if (err.status === 408) {
    console.error(`Request timeout: ${req.method} ${req.url}`);
    return res.status(408).json({
      code: 408,
      message: '请求超时，请稍后重试',
      timestamp: new Date().toISOString()
    });
  }
  next(err);
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });
  
  res.status(err.status || 500).json({
    code: err.status || 500,
    message: err.status === 500 ? '服务器内部错误' : err.message,
    timestamp: new Date().toISOString(),
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    timestamp: new Date().toISOString()
  });
});

// 优雅关闭处理
const gracefulShutdown = async (signal) => {
  console.log(`收到 ${signal} 信号，开始优雅关闭...`);
  
  // 停止接受新的连接
  server.close(async () => {
    console.log('HTTP服务器已关闭');
    
    // 关闭数据库连接
    await closeConnection();
    
    console.log('应用程序已优雅关闭');
    process.exit(0);
  });
  
  // 如果10秒内没有完成关闭，强制退出
  setTimeout(() => {
    console.error('强制关闭应用程序');
    process.exit(1);
  }, 10000);
};

// 启动服务器
const server = http.createServer(app);

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
  server
});

// WebSocket连接处理
wss.on('connection', (ws, req) => {
  console.log('WebSocket客户端已连接, URL:', req.url);
  console.log('WebSocket请求头:', req.headers);
  
  // 从请求头中提取 openid 并保存到 WebSocket 连接对象上
  ws.openid = req.headers.openid || req.headers.openId || req.headers['openid'];
  console.log('WebSocket连接中提取的 openid:', ws.openid);
  
  // 发送连接成功消息
  try {
    ws.send(JSON.stringify({
      event: 'connected',
      data: {
        message: '连接成功',
        timestamp: new Date().toISOString(),
        url: req.url,
        openid: ws.openid  // 确认 openid 已正确获取
      }
    }));
  } catch (error) {
    console.error('发送连接消息失败:', error);
  }

  // 处理客户端消息
  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message);
      console.log('收到WebSocket消息:', data);
      
      // 根据消息类型处理
      switch (data.type) {
        case 'parse_answer':
          // 调用答案解析WebSocket版本
          await wechatPracticeController.parseAnswerWebSocket(ws, data.payload);
          break;
        case 'cankaoshi':
          // 调用餐烤师WebSocket版本
          const wechatController = require('./controllers/weichat/wechatController');
          await wechatController.callCankaoshiWebSocket(ws, data.payload);
          break;
        default:
          ws.send(JSON.stringify({
            event: 'error',
            data: {
              success: false,
              error: '未知的消息类型'
            }
          }));
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
      ws.send(JSON.stringify({
        event: 'error',
        data: {
          success: false,
          error: '消息格式错误'
        }
      }));
    }
  });

  // 处理连接关闭
  ws.on('close', () => {
    console.log('WebSocket客户端已断开连接');
  });

  // 处理连接错误
  ws.on('error', (error) => {
    console.error('WebSocket连接错误:', error);
  });
});

server.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log(`WebSocket服务运行在 ws://localhost:${PORT}`);
  console.log(`支持的WebSocket消息类型: parse_answer, cankaoshi`);
  console.log(`API接口前缀: ${API_PREFIX}`);
  console.log(`健康检查端点: http://localhost:${PORT}/health`);
  testConnection(); // 仅测试连接，不进行表字段同步
});

// 监听进程信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 监听未捕获的异常
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  gracefulShutdown('unhandledRejection');
});

module.exports = app;
