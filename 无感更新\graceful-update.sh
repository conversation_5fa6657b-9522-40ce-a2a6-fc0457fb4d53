#!/bin/bash

# 无感更新脚本 - 实现零停机部署
# 适用于cankao-admin项目

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 配置项
APP_DIR="/home/<USER>/enterprise/demo_enterprise"
HEALTH_CHECK_API_URL="http://localhost:${ENT_ADMIN_API_PORT}/api/health"
HEALTH_CHECK_WEB_URL="http://localhost:${ENT_ADMIN_WEB_PORT}"
HEALTH_CHECK_TIMEOUT=30  # 健康检查超时时间(秒)
BACKUP_DIR="${APP_DIR}/backups/$(date +%Y%m%d_%H%M%S)"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 健康检查函数
check_health() {
    local service=$1
    local url=$2
    local timeout=$3
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    
    log_info "正在检查 $service 健康状态..."
    
    while [ $(date +%s) -lt $end_time ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            log_info "$service 健康检查通过!"
            return 0
        fi
        log_warn "$service 尚未就绪，等待 3 秒后重试..."
        sleep 3
    done
    
    log_error "$service 健康检查失败，超过 $timeout 秒!"
    return 1
}

# 创建配置备份
backup_config() {
    log_info "创建配置文件备份..."
    mkdir -p "$BACKUP_DIR"
    cp "${APP_DIR}/.env" "${BACKUP_DIR}/.env" 2>/dev/null || true
    cp "${APP_DIR}/docker-compose.yaml" "${BACKUP_DIR}/docker-compose.yaml"
    log_info "备份已保存到 $BACKUP_DIR"
}

# 主更新流程
main() {
    cd "$APP_DIR" || { log_error "无法进入应用目录: $APP_DIR"; exit 1; }
    
    # 步骤1: 备份当前配置
    backup_config
    
    # 步骤2: 拉取最新镜像
    log_info "预拉取最新镜像..."
    sudo docker compose pull || { log_error "拉取镜像失败"; exit 1; }
    
    # 步骤3: 更新前端服务
    log_info "更新前端服务 (admin-web)..."
    sudo docker compose stop admin-web
    sudo docker compose up -d admin-web
    check_health "admin-web" "$HEALTH_CHECK_WEB_URL" "$HEALTH_CHECK_TIMEOUT" || { 
        log_error "前端服务更新失败，正在回滚..."; 
        sudo docker compose stop admin-web
        sudo docker compose up -d --no-recreate admin-web
        exit 1
    }
    
    # 步骤4: 更新后端服务
    log_info "更新后端服务 (admin-api)..."
    sudo docker compose stop admin-api
    sudo docker compose up -d admin-api
    check_health "admin-api" "$HEALTH_CHECK_API_URL" "$HEALTH_CHECK_TIMEOUT" || {
        log_error "后端服务更新失败，正在回滚...";
        sudo docker compose stop admin-api
        sudo docker compose up -d --no-recreate admin-api
        exit 1
    }
    
    # 步骤5: 清理旧镜像
    log_info "清理未使用的镜像..."
    sudo docker image prune -af --filter "until=24h" || log_warn "清理镜像时出现警告，但更新已完成"
    
    log_info "✅ 无感更新完成! 所有服务已成功更新且正常运行。"
}

# 执行主流程
main 