const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 用户成就记录模型
 */
const UserAchievement = sequelize.define('UserAchievement', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },

  enterpriseId: {
    type: DataTypes.BIGINT,
    field: 'enterprise_id',
    allowNull: false,
    comment: '企业ID'
  },

  userId: {
    type: DataTypes.BIGINT,
    field: 'user_id',
    allowNull: false,
    comment: '用户ID'
  },

  openId: {
    type: DataTypes.STRING(255),
    field: 'open_id',
    allowNull: true,
    comment: '微信openId'
  },

  templateId: {
    type: DataTypes.BIGINT,
    field: 'template_id',
    allowNull: false,
    comment: '成就模板ID'
  },

  achievementName: {
    type: DataTypes.STRING(100),
    field: 'achievement_name',
    allowNull: false,
    comment: '成就名称'
  },

  achievementIcon: {
    type: DataTypes.STRING(500),
    field: 'achievement_icon',
    allowNull: true,
    comment: '成就图标'
  },

  category: {
    type: DataTypes.STRING(50),
    field: 'category',
    allowNull: false,
    defaultValue: 'learning',
    comment: '成就类别：learning-学习,time-时间,exam-考试,practice-练习'
  },

  status: {
    type: DataTypes.STRING(20),
    field: 'status',
    allowNull: false,
    defaultValue: 'achieved',
    comment: '状态：achieved-已获得,revoked-已撤销'
  },

  achievedAt: {
    type: DataTypes.DATE,
    field: 'achieved_at',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '获得时间'
  },

  rewardPoints: {
    type: DataTypes.INTEGER,
    field: 'reward_points',
    allowNull: false,
    defaultValue: 0,
    comment: '获得积分'
  },

  triggerCondition: {
    type: DataTypes.TEXT,
    field: 'trigger_condition',
    allowNull: true,
    comment: '触发条件JSON'
  },

  achievementData: {
    type: DataTypes.JSON,
    field: 'achievement_data',
    allowNull: true,
    comment: '成就相关数据JSON，如学习时长、题目数量等'
  },

  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },

  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  },

  createBy: {
    type: DataTypes.STRING(50),
    field: 'create_by',
    allowNull: true,
    defaultValue: 'system',
    comment: '创建人'
  },

  updateBy: {
    type: DataTypes.STRING(50),
    field: 'update_by',
    allowNull: true,
    defaultValue: 'system',
    comment: '更新人'
  },

  isPop: {
    type: DataTypes.TINYINT(1),
    field: 'is_pop',
    allowNull: false,
    defaultValue: 0,
    comment: '是否弹出：0-未弹出，1-已弹出'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    field: 'is_active',
    allowNull: true,
    defaultValue: true,
    comment: '是否有效(1有效 0无效)'
  },
  careerRecordId: {
    type: DataTypes.INTEGER,
    field: 'career_record_id',
    allowNull: true,
    comment: '员工履历记录ID'
  }
}, {
  tableName: 'user_achievements',
  timestamps: true,
  createdAt: 'create_time',
  updatedAt: 'update_time',
  indexes: [
    {
      name: 'idx_enterprise_user',
      fields: ['enterprise_id', 'user_id']
    },
    {
      name: 'idx_enterprise_openid',
      fields: ['enterprise_id', 'open_id']
    },
    {
      name: 'idx_enterprise_template',
      fields: ['enterprise_id', 'template_id']
    },
    {
      name: 'idx_enterprise_category',
      fields: ['enterprise_id', 'category']
    },
    {
      name: 'idx_enterprise_status',
      fields: ['enterprise_id', 'status']
    },
    {
      name: 'idx_achieved_at',
      fields: ['achieved_at']
    },
    {
      // 复合唯一索引：确保同一用户不能重复获得同一成就
      name: 'uk_user_template',
      fields: ['enterprise_id', 'user_id', 'template_id'],
      unique: true
    }
  ]
});

module.exports = UserAchievement;

// 模型关联关系将在index.js中定义 