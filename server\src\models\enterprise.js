const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Enterprise = sequelize.define('enterprises', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '企业名称'
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '企业编码'
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '企业类型'
  },
  address: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '地址'
  },
  contact: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '联系人'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '联系电话'
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '邮箱'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态：0禁用，1正常'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'enterprises'
});

module.exports = Enterprise; 