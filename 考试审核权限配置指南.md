# 考试审核权限配置指南

## 概述

考试审核模块包含以下权限控制功能：
- **资格审核权限**：控制是否可以进行资格审核操作
- **成绩审核权限**：控制是否可以进行成绩审核操作

## 权限标识说明

| 权限标识 | 权限名称 | 功能说明 |
|---------|---------|----------|
| `exam.review.qualification` | 资格审核权限 | 控制单个和批量资格审核按钮的显示 |
| `exam.review.score` | 成绩审核权限 | 控制单个和批量成绩审核按钮的显示 |

## 在菜单管理中配置权限

### 1. 创建按钮权限菜单

需要在菜单管理中创建以下按钮权限：

#### 资格审核权限
- **菜单名称**：资格审核
- **菜单类型**：按钮
- **权限标识**：`exam.review.qualification`
- **父级菜单**：考试审核（对应的页面菜单）
- **排序**：1

#### 成绩审核权限
- **菜单名称**：成绩审核
- **菜单类型**：按钮
- **权限标识**：`exam.review.score`
- **父级菜单**：考试审核（对应的页面菜单）
- **排序**：2

### 2. 菜单配置示例（JSON格式）

```json
[
  {
    "name": "考试审核",
    "path": "/exam-manage/review",
    "type": 1,
    "permission": "exam.manage.review",
    "component": "exam-manage/review/index",
    "children": [
      {
        "name": "资格审核",
        "type": 2,
        "permission": "exam.review.qualification",
        "sort": 1
      },
      {
        "name": "成绩审核", 
        "type": 2,
        "permission": "exam.review.score",
        "sort": 2
      }
    ]
  }
]
```

## 权限控制效果

### 1. 页面顶部批量操作按钮
- **批量资格审核按钮**：需要 `exam.review.qualification` 权限
- **批量成绩审核按钮**：需要 `exam.review.score` 权限

### 2. 表格操作列按钮
- **资格审核按钮**：需要 `exam.review.qualification` 权限
- **成绩审核按钮**：需要 `exam.review.score` 权限

### 3. 权限检查逻辑
- 没有权限的按钮会被隐藏（使用 `v-permission` 指令）
- 批量操作按钮的禁用状态会考虑权限和数据状态
- 单个操作按钮的禁用状态会考虑权限和记录状态

## 角色权限分配示例

### 审核员角色（一般审核人员）
```json
{
  "roleName": "审核员",
  "permissions": [
    "exam.manage.review",           // 页面访问权限
    "exam.review.qualification"     // 只能进行资格审核
  ]
}
```

### 审核主管角色（高级审核人员）
```json
{
  "roleName": "审核主管", 
  "permissions": [
    "exam.manage.review",           // 页面访问权限
    "exam.review.qualification",    // 资格审核权限
    "exam.review.score"            // 成绩审核权限
  ]
}
```

### 超级管理员角色
```json
{
  "roleName": "超级管理员",
  "permissions": [
    "exam.manage.review",
    "exam.review.qualification", 
    "exam.review.score",
    "*"                            // 所有权限
  ]
}
```

## 使用效果演示

### 有资格审核权限的用户
- ✅ 可以看到"批量资格审核"按钮
- ✅ 可以看到表格中的"资格审核"按钮
- ❌ 看不到"批量成绩审核"按钮（如果没有成绩审核权限）
- ❌ 看不到表格中的"成绩审核"按钮（如果没有成绩审核权限）

### 有成绩审核权限的用户
- ✅ 可以看到"批量成绩审核"按钮
- ✅ 可以看到表格中的"成绩审核"按钮
- ❌ 看不到"批量资格审核"按钮（如果没有资格审核权限）
- ❌ 看不到表格中的"资格审核"按钮（如果没有资格审核权限）

### 无任何审核权限的用户
- ❌ 看不到任何审核相关按钮
- ✅ 只能查看数据，无法进行审核操作

## 实施步骤

1. **后端配置**
   - 在菜单表中添加按钮权限记录
   - 确保角色权限分配接口正常工作

2. **前端验证**
   - 登录不同角色的用户
   - 验证按钮显示/隐藏是否正确
   - 测试权限更新后的实时生效

3. **用户培训**
   - 向用户说明不同角色的权限范围
   - 提供权限申请流程

## 注意事项

1. **权限粒度**：当前设计为功能级权限，可根据需要进一步细分
2. **向后兼容**：确保现有用户的权限不受影响
3. **安全性**：前端权限控制主要用于UI展示，后端仍需进行权限验证
4. **用户体验**：权限不足时给予友好的提示信息

通过以上配置，您就可以完整地控制考试审核功能的权限访问了。 