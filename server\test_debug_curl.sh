#!/bin/bash

# 测试调试版证书生成接口的 curl 命令
echo "正在测试调试版证书生成接口..."
curl -X POST http://localhost:3000/api/wechat/certificate/generate-debug \
  -H "Content-Type: application/json" \
  -d '{
    "name": "姜子介",
    "courseName": "预定流程标准",
    "position": "服务员（p4级）",
    "certificateNo": "C2505493172",
    "issueDate": "2025-05-28",
    "validDate": "2025-08-26"
  }'

echo -e "\n\n调试版证书生成完成！"
echo "生成的证书文件在 uploads/certificates/ 目录下"
echo "文件名格式：certificate_debug_证书编号_时间戳.png"
echo "在调试版本中，你可以看到："
echo "- 红色网格线（每50像素一条）"
echo "- 坐标标注（每100像素标注X/Y坐标）"
echo "- 蓝色文字标注（显示每个文字元素的确切坐标）"
echo "- 画布尺寸信息（左上角显示）" 