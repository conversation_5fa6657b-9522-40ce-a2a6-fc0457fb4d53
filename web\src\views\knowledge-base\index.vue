<!-- 知识库管理模块 -->
<template>
  <div class="page-container">
    <!-- 全屏遮罩加载层 -->
    <div class="global-loading-mask" v-if="fullscreenLoading">
      <div class="loading-spinner">
        <a-spin size="large" />
        <div class="loading-text">段落加载中...</div>
      </div>
    </div>
    <page-header>
      <template #search>
        <!-- 搜索区域 -->
        <search-form-card
          :model-value="searchForm"
          :items="searchFormItems"
          :key="formKey"
          @search="handleSearch"
          @reset="resetSearch"
        />

      </template>
      <template #actions>
        <a-space>
          <a-button type="primary" @click="handleUpload">
            <upload-outlined /> 上传文档
          </a-button>
          <a-button danger :disabled="!selectedRowKeys.length" @click="handleBatchDelete">
            <delete-outlined /> 批量删除
          </a-button>
        </a-space>
      </template>
    </page-header>
      
      <!-- 表格区域 -->
       <base-table
        :columns="columns"
        :data-source="documentList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        :action-config="actionConfig"
        :delete-title="deleteTitle"
        @edit="handleEditDocument"
        @delete="handleDeleteDocument"
        :row-selection="rowSelection"
      >
        <!-- 证书名称列 -->
        <template #bodyCell="{ column, record }">

          <template v-if="column.key === 'fileName'">
            <div class="document-name">
              <div class="document-icon">
                <file-pdf-outlined v-if="record.fileType === 'pdf'" class="pdf-icon" />
                <file-word-outlined v-else-if="record.fileType === 'word'" class="word-icon" />
                <file-excel-outlined v-else-if="record.fileType === 'excel'" class="excel-icon" />
                <file-ppt-outlined v-else-if="record.fileType === 'ppt'" class="ppt-icon" />
                <file-image-outlined v-else-if="record.fileType === 'image'" class="image-icon" />
                <file-text-outlined v-else-if="record.fileType === 'text'" class="text-icon" />
                <file-outlined v-else class="file-icon" />
              </div>
              <span :title="record.fileName">{{ record.fileName }}</span>
            </div>
          </template>

          <!-- 文件归属列 -->
          <template v-else-if="column.key === 'category' || column.dataIndex === 'fileCategory'">
            <a-tag color="purple">{{ getCategoryById(record.fileCategory) }}</a-tag>
          </template>

          <!-- 所属岗位列 -->
          <template v-else-if="column.key === 'position' || column.dataIndex === 'position'">
            <a-tag color="green">{{ getPositionById(record.position) }}</a-tag>
          </template>

          <!-- 处理状态列 -->
          <template v-else-if="column.key === 'processStatus'">
            <doc-process-status 
              :key="`${record.id}-${processStatusKey}`"
              :docId="record.id" 
              :autoRefresh="shouldAutoRefresh(record)"
              :refreshInterval="5000" 
              :initialStatus="getInitialStatus(record)"
              @status-change="handleProcessStatusChange" 
            />
          </template>

          <!-- 文档状态列 -->
          <template v-else-if="column.key === 'status' || column.dataIndex === 'status'">
            <a-switch
              :checked="record.status === 'enable' || record.status === null || record.status === undefined"
              :loading="record.statusLoading"
              @change="(checked) => handleStatusChange(record, checked ? 'enable' : 'disable')"
            />
          </template>

          <!-- 文件大小列 -->
          <template v-else-if="column.key === 'fileSize'">
            <span>{{ formatFileSize(record.fileSize) }}</span>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'createdTime'">
            <span>{{ formatDate(record.createdTime) }}</span>
          </template>

          <!-- 更新时间列 -->
          <template v-else-if="column.key === 'updatedTime'">
            <span>{{ formatDate(record.updatedTime) }}</span>
          </template>

           <!-- 提示词 -->
        <template v-if="column.key === 'promote'">
          <a-tooltip>
            <template #title>{{ record.promote }}</template>
            {{ getToolTip(record.promote) }}
          </a-tooltip>
        </template>
                    <!-- 操作列 -->          <template v-else-if="column.key === 'action'">              <a-button type="link" size="small" class="custom-button" @click="handleManageParagraphs(record)">                <unordered-list-outlined /> 段落              </a-button>              <a-button 
                type="link" size="small" class="custom-button" @click="handleManageQuestions(record)">                <question-circle-outlined /> 题目              </a-button>              <a-button 
                type="link" size="small" class="custom-button" @click="handlePromptStrategy(record)">                <setting-outlined /> 出题策略              </a-button>          </template>
        </template>
      </base-table>

    <!-- 上传/编辑文档弹窗 -->
    <upload-modal
      v-model:visible="uploadModalVisible"
      :document-data="currentDocument"
      @success="handleUploadSuccess"
    />

    <!-- 段落管理抽屉 -->
    <paragraphs-drawer
      v-model:visible="paragraphsDrawerVisible"
      :document-id="currentDocumentForSegments && currentDocumentForSegments.id"
      :document-name="currentDocumentForSegments && currentDocumentForSegments.fileName"
      :initial-paragraphs="documentSegments"
      :fullscreenLoading="fullscreenLoading"
      @save="handleSaveSegments"
      @refresh="handleRefreshSegments"
      @delete="handleDeleteSegment"
      @setLoading="(val) => fullscreenLoading = val"
    />

    <!-- 题目管理抽屉 -->    <question-drawer      v-model:visible="questionDrawerVisible"      :knowledge-id="currentDocumentForQuestions && currentDocumentForQuestions.id"    />

    <!-- 出题策略弹窗 -->
    <a-modal
      v-model:visible="promptStrategyModalVisible"
      title="出题策略设置"
      :width="700"
      :footer="null"
      :maskClosable="false"
      :destroyOnClose="true"
    >
      <a-form 
        :model="promptStrategyForm" 
        layout="vertical"
        @finish="savePrompt"
      >
        <a-form-item
          label="提示词"
          name="prompt"
          :rules="[{ required: true, message: '请输入提示词' }]"
        >
          <a-textarea
            v-model:value="promptStrategyForm.prompt"
            :rows="6"
            placeholder="请输入提示词，用于指导系统如何生成题目"
          />
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 24 }" class="text-right">
          <a-space>
            <a-button @click="promptStrategyModalVisible = false">取消</a-button>
            <a-button type="primary" html-type="submit" :loading="promptStrategyLoading">保存</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useDocumentUtils } from './composables/useDocumentUtils';
import { useSegmentsManager } from './composables/useSegmentsManager';
import { useTablePagination, getToolTip } from '@/utils/common';
import UploadModal from './components/UploadModal.vue';
import ParagraphsDrawer from './components/ParagraphsDrawer.vue';
import { SearchFormCard } from '@/components/SearchForm';
import QuestionDrawer from './components/QuestionDrawer.vue';
import DocProcessStatus from '@/components/DocProcessStatus.vue';
import {
  getKnowledgeBaseList,
  deleteKnowledgeDocument,
  toggleDocumentStatus,
  getDocumentProcessStatus,
  savePromptStrategy,
  getPromptStrategy
} from '@/api/knowledge-base';
import { updateSegment } from '@/api/knowledge-base/segments';
import { getPositionTypeOptions, getPositionNameOptions } from '@/api/organization/position';
import { useRouter } from 'vue-router';
import { 
  UploadOutlined, 
  DeleteOutlined, 
  UnorderedListOutlined, 
  QuestionCircleOutlined, 
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FileOutlined,
  SettingOutlined 
} from '@ant-design/icons-vue';

// 使用文档工具函数
const { formatDate, getCategoryLabel, getPositionLabel } = useDocumentUtils();

// 使用段落管理
const { 
  documentSegments, 
  paragraphsDrawerVisible, 
  currentDocumentForSegments,
  openSegmentsDrawer, 
  saveSegments,
  removeSegment,
  fullscreenLoading
} = useSegmentsManager();

// 题目管理相关
const router = useRouter();
const questionDrawerVisible = ref(false);
const currentDocumentForQuestions = ref(null);

// 出题策略相关
const promptStrategyModalVisible = ref(false);
const promptStrategyLoading = ref(false);
const currentDocumentForPrompt = ref(null);
const promptStrategyForm = reactive({
  prompt: ''
});

// 添加用于控制DocProcessStatus组件重新渲染的变量
const processStatusKey = ref(0);

// 添加key值用于强制重新渲染搜索表单
const formKey = ref(0);

// 格式化文件大小（使用旧实现，确保与旧版本一致）
const formatFileSize = (bytes) => {
  if (bytes === 0 || !bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 分类选项和岗位选项
const categoryOptions = ref([]);
const positionOptions = ref([]);

// 表格操作配置
const actionConfig = {
  edit: true,
  delete: true
};

// 删除确认标题
const deleteTitle = '确定删除此文档吗?';

// 获取选项数据
const fetchOptions = async () => {
  try {
    const [typeRes, nameRes] = await Promise.all([
      getPositionTypeOptions(),
      getPositionNameOptions()
    ]);

    // 处理岗位类型数据为选项格式 - 确保是数组
    categoryOptions.value = Array.isArray(typeRes) ? typeRes : 
                           (typeRes && typeRes.rows ? typeRes.rows : []);

    // 处理岗位名称数据为选项格式 - 确保是数组
    positionOptions.value = Array.isArray(nameRes) ? nameRes : 
                           (nameRes && nameRes.rows ? nameRes.rows : []);
    
    console.log('文件分类选项:', categoryOptions.value);
    console.log('岗位名称选项:', positionOptions.value);
  } catch (error) {
    console.error('获取选项数据失败', error);
    message.error('获取选项数据失败');
    // 确保即使出错也将选项初始化为空数组
    categoryOptions.value = [];
    positionOptions.value = [];
  }
};



// 表格列定义
const columns = [
  {
    title: '证书名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    sorter: true,
    width: 180,
    minWidth: 150
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
    ellipsis: true,
    width: 200,
    minWidth: 180
  },
  {
    title: '文件归属',
    dataIndex: 'fileCategory',
    key: 'category',
    width: 120,
    minWidth: 100
  },
  {
    title: '所属岗位',
    dataIndex: 'position',
    key: 'position',
    width: 120,
    minWidth: 100
  },
  {
    title: '处理状态',
    key: 'processStatus',
    width: 250,
    minWidth: 220
  },
  {
    title: '文档状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 120,
    minWidth: 100
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    customRender: (size) => {
      return formatFileSize(size.text)
    },
    sorter: true,
    width: 120,
    minWidth: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    key: 'createdTime',
    sorter: true,
    width: 180,
    minWidth: 160
  },
  {
    title: '更新时间',
    dataIndex: 'updatedTime',
    key: 'updatedTime',
    sorter: true,
    width: 180,
    minWidth: 160
  },

  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 380,
    minWidth: 250
  }
];

// 复选框
const rowSelection = {
  onChange: (keys, selectedRows) => {
    selectedRowKeys.value = keys;
  },
};

// 查询参数
const searchForm = reactive({
  searchText: '',
  fileCategory: undefined,
  position: undefined,
  sortField: '',   // 排序字段
  sortOrder: ''    // 排序方向
});

// 状态变量
const documentList = ref([]);
const loading = ref(false);
const uploadModalVisible = ref(false);
const currentDocument = ref(null);
const selectedRowKeys = ref([]);

// 获取文档列表
const fetchDocumentList = async () => {
  loading.value = true;

  try {
    const params = {
      searchText: searchForm.searchText,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      fileCategory: searchForm.fileCategory,  // 文件归属筛选参数
      position: searchForm.position,       // 岗位名称筛选参数
      documentType: 'exam'  // 只获取练考类型的文档
    };
    
    // 添加排序参数
    if (searchForm.sortField) {
      params.sortField = searchForm.sortField;
      params.sortOrder = searchForm.sortOrder;
    }

    const response = await getKnowledgeBaseList(params);

    // 检查响应中是否有list字段
    if (response && response.list) {
      // 响应直接包含list (request工具已处理过data字段)
      documentList.value = response.list.map(item => {
        // 如果category为空，则使用fileCategory
        if (!item.category && item.fileCategory) {
          item.category = item.fileCategory;
        }

        // 确保字段映射正确
        const mappedItem = { ...item };

        // 处理文件大小字段
        // 可能的字段名：fileSize, size, filesize
        if (!mappedItem.fileSize && (mappedItem.size || mappedItem.filesize)) {
          mappedItem.fileSize = mappedItem.size || mappedItem.filesize;
        }

        // 处理创建时间和更新时间字段
        if (item.createTime && !item.createdTime) {
          mappedItem.createdTime = item.createTime;
        }

        if (item.updateTime && !item.updatedTime) {
          mappedItem.updatedTime = item.updateTime;
        }
        
        // 处理处理状态字段
        // 确保processStatus字段一致性
        if (!mappedItem.processStatus && mappedItem.process_status) {
          mappedItem.processStatus = mappedItem.process_status;
        }
        
        if (!mappedItem.processProgress && mappedItem.process_progress) {
          mappedItem.processProgress = mappedItem.process_progress;
        }
        
        if (!mappedItem.processMessage && mappedItem.process_message) {
          mappedItem.processMessage = mappedItem.process_message;
        }

        return mappedItem;
      });

      // 使用组合式函数的updatePagination方法更新分页信息
      updatePagination({
        total: response.total || documentList.value.length,
        pageNum: response.pageNum,
        pageSize: response.pageSize
      });
    } else {
      documentList.value = [];
      // 重置分页信息
      updatePagination({ total: 0, pageNum: 1 });
    }
  } catch (error) {
    console.error('获取知识库数据失败', error);
    message.error('获取知识库数据失败');
    documentList.value = [];
    // 发生错误时重置分页
    updatePagination({ total: 0, pageNum: 1 });
  } finally {
    loading.value = false;
  }
};

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchDocumentList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
});

// 搜索
const handleSearch = (values) => {
  if (values) {
    // 只更新搜索条件，保留排序设置
    const { sortField, sortOrder } = searchForm;
    Object.assign(searchForm, values, { sortField, sortOrder });
  }
  // 重置到第一页
  resetPagination();
  fetchDocumentList();
};

// 重置查询
const resetSearch = () => {
  searchForm.searchText = '';
  searchForm.fileCategory = undefined;
  searchForm.position = undefined;
  searchForm.sortField = '';
  searchForm.sortOrder = '';
  // 重置到第一页
  resetPagination();
  fetchDocumentList();
};

// 上传文档
const handleUpload = async () => {
  // 显示加载状态
  loading.value = true;
  
  try {
    // 检查所有文档的处理状态
    // const processingDocuments = [];
    //
    // // 创建检查文档状态的Promise数组
    // const checkPromises = documentList.value.map(async (doc) => {
    //   try {
    //     const status = await getDocumentProcessStatus(doc.id);
    //     // 如果状态存在且不是completed或failed，则加入处理中列表
    //     if (status && status.status &&
    //         status.status !== 'completed' &&
    //         status.status !== 'failed') {
    //       processingDocuments.push(doc);
    //     }
    //   } catch (error) {
    //     // 如果获取状态失败（如404），则假定文档已处理完成
    //     console.log(`获取文档 ${doc.id} 状态失败，视为已处理完成`);
    //   }
    // });
    //
    // // 等待所有检查完成
    // await Promise.all(checkPromises);
    //
    // if (processingDocuments.length > 0) {
    //   // 如果有正在处理中的文档，显示提示信息
    //   message.warning('有文件正在处理中，请等待文件处理完成后再上传，或刷新界面');
    //   return;
    // }
    
    // 没有处理中的文档，正常打开上传对话框
    currentDocument.value = null;
    uploadModalVisible.value = true;
  } catch (error) {
    console.error('检查文档处理状态失败', error);
    message.error('检查文档状态失败，请刷新页面后重试');
  } finally {
    loading.value = false;
  }
};

// 编辑文档
const handleEditDocument = (record) => {
  currentDocument.value = record;
  uploadModalVisible.value = true;
};

// 上传成功回调
const handleUploadSuccess = () => {
  uploadModalVisible.value = false;
  fetchDocumentList();
};

// 删除文档
const handleDeleteDocument = async (record) => {
  try {
    await deleteKnowledgeDocument(record.id);
    message.success('删除成功');
    fetchDocumentList(); // 重新加载数据
  } catch (error) {
    message.error('该文档已被练考配置关联，无法删除');
  }
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的文档');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个文档吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true;
        // 使用Promise.all逐个删除所选文档
        await Promise.all(
          selectedRowKeys.value.map(id => deleteKnowledgeDocument(id))
        );
        message.success(`已批量删除 ${selectedRowKeys.value.length} 个文档`);
        selectedRowKeys.value = [];
        fetchDocumentList();
      } catch (error) {
        message.error('批量删除失败,部分文档已被练考配置关联，无法删除');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 根据ID获取文件分类名称
const getCategoryById = (id) => {
  if (!id) {
    return '-';
  }
  
  
  // 首先尝试作为ID处理
  const numId = parseInt(id);
  // 确保categoryOptions.value是数组且不为空
  if (Array.isArray(categoryOptions.value) && categoryOptions.value.length > 0) {
    // 先尝试通过id完全匹配
    let category = categoryOptions.value.find(item => {
      const itemId = item.id || item.value || item.dictValue;
      return itemId == id; // 使用宽松比较，因为id可能是字符串或数字
    });
    
    // 如果没找到，尝试通过字符串包含来模糊匹配
    if (!category && typeof id === 'string') {
      category = categoryOptions.value.find(item => {
        const itemLabel = item.dictLabel || item.name || item.label;
        return itemLabel && itemLabel.includes(id);
      });
    }
    
    if (category) {
      const label = category.dictLabel || category.name || category.label;
      return label;
    } else {
      console.warn('未找到匹配的文件归属:', id);
    }
  } else {
    console.warn('文件归属选项为空或不是数组');
  }
  
  // 如果找不到匹配的ID或ID无效，则返回原始值
  return id;
};

// 根据ID获取岗位名称
const getPositionById = (id) => {
  if (!id) {
    return '-';
  }
  
  // 处理"通用"特殊值
  if (id === 'COMMON') {
    return '通用';
  }
  
  // 首先尝试作为ID处理
  const numId = parseInt(id);
  // 确保positionOptions.value是数组且不为空
  if (Array.isArray(positionOptions.value) && positionOptions.value.length > 0) {
    // 先尝试通过id完全匹配
    let position = positionOptions.value.find(item => {
      const itemId = item.id || item.value || item.dictValue;
      return itemId == id; // 使用宽松比较，因为id可能是字符串或数字
    });
    
    // 如果没找到，尝试通过字符串包含来模糊匹配
    if (!position && typeof id === 'string') {
      position = positionOptions.value.find(item => {
        const itemLabel = item.dictLabel || item.name || item.label;
        return itemLabel && itemLabel.includes(id);
      });
    }
    
    if (position) {
      const label = position.dictLabel || position.name || position.label;
      return label;
    } else {
      console.warn('未找到匹配的所属岗位:', id);
    }
  } else {
    console.warn('所属岗位选项为空或不是数组');
  }
  
  // 如果找不到匹配的ID或ID无效，则返回原始值
  return id;
};

// 段落管理
const handleManageParagraphs = (record) => {
  openSegmentsDrawer(record);
};

// 删除段落
const handleDeleteSegment = async (segmentId) => {
  if (!currentDocumentForSegments.value?.id) {
    message.error('删除段落失败：文档ID缺失');
    return;
  }
  
  try {
    // 调用删除段落API
    const documentId = currentDocumentForSegments.value.id;
    await removeSegment(documentId, segmentId);
    message.success('段落已删除');
    
    // 刷新段落列表
    await openSegmentsDrawer(currentDocumentForSegments.value);
  } catch (error) {
    message.error('删除段落失败：' + (error.message || '未知错误'));
  }
};

// 刷新段落数据
const handleRefreshSegments = async () => {
  if (!currentDocumentForSegments.value?.id) {
    message.error('刷新段落数据失败：文档ID缺失');
    return;
  }
  
  try {
    // 调用openSegmentsDrawer刷新数据，但不会关闭抽屉
    await openSegmentsDrawer(currentDocumentForSegments.value);
  } catch (error) {
    message.error('刷新段落数据失败');
  }
};

// 保存段落
const handleSaveSegments = async (segments, isSingleSegment = false, isNewSegment = false) => {
  
  // 确保currentDocumentForSegments已正确设置
  if (!currentDocumentForSegments.value?.id) {
    console.error('当前文档ID缺失');
    message.error('保存段落失败：文档ID缺失');
    return;
  }
  
  const documentId = currentDocumentForSegments.value.id;
  
  fullscreenLoading.value = true;
  try {
    // 如果是单个段落更新，并且有id，直接调用updateSegment更新单个段落
    if (isSingleSegment && segments.length === 1 && segments[0].id && !segments[0].id.startsWith('temp_')) {
      const segment = segments[0];
      const { id, ...updateData } = segment;
      await updateSegment(documentId, id, updateData);
      message.success('段落已更新');
    } else if (isNewSegment) {
      // 如果是新段落，只提交新段落数据
      await saveSegments(documentId, segments);
      // message.success('新段落已添加');
      // 重新加载所有段落数据，显示新添加的段落
      await openSegmentsDrawer(currentDocumentForSegments.value);
    } else {
      // 批量更新所有段落
      await saveSegments(documentId, segments);
      // 重新加载所有段落数据
      await openSegmentsDrawer(currentDocumentForSegments.value);
    }
    
    // 如果不是单个段落更新且不是新增段落，则重新加载所有段落数据
    if (!isSingleSegment && !isNewSegment) {
      await openSegmentsDrawer(currentDocumentForSegments.value);
      console.log('段落数据已刷新');
    }
  } catch (error) {
    console.error('保存段落数据失败', error);
    message.error('保存段落失败：' + (error.message || '未知错误'));
  } finally {
    fullscreenLoading.value = false;
  }
};

// 搜索表单配置
const searchFormItems = computed(() => {
  // 打印选项数据用于调试
  console.log('生成搜索表单时的分类选项:', categoryOptions.value);
  console.log('生成搜索表单时的岗位选项:', positionOptions.value);
  
  // 确保选项数据正确，即使属性名称不统一
  const enhancedCategoryOptions = Array.isArray(categoryOptions.value) 
    ? categoryOptions.value.map(item => ({
        ...item,
        id: item.id || item.value || item.dictValue,
        dictLabel: item.dictLabel || item.name || item.label || '未命名类型',
        // 添加label属性确保表单组件能正确显示
        label: item.dictLabel || item.name || item.label || '未命名类型',
        value: item.id || item.value || item.dictValue
      }))
    : [];
  
  const enhancedPositionOptions = Array.isArray(positionOptions.value)
    ? positionOptions.value.map(item => ({
        ...item,
        id: item.id || item.value || item.dictValue,
        dictLabel: item.dictLabel || item.name || item.label || '未命名岗位',
        // 添加label属性确保表单组件能正确显示
        label: item.dictLabel || item.name || item.label || '未命名岗位',
        value: item.id || item.value || item.dictValue
      }))
    : [];
  
  return [
    {
      label: '证书/文件名',
      field: 'searchText',
      type: 'input',
      placeholder: '搜索证书名称/文件名称',
      width: '220px'
    },
    {
      label: '文件归属',
      field: 'fileCategory',
      type: 'select',
      placeholder: '请选择归属',
      width: '200px',
      options: enhancedCategoryOptions,
      // 使用标准的label/value属性配置
      selectLabel: 'label',
      selectValue: 'value'
    },
    {
      label: '所属岗位',
      field: 'position',
      type: 'select',
      placeholder: '请选择岗位',
      width: '200px',
      options: enhancedPositionOptions,
      // 使用标准的label/value属性配置
      selectLabel: 'label',
      selectValue: 'value'
    }
  ]
})

// 处理题目管理
const handleManageQuestions = (record) => {
  router.push(`/kb-questions/${record.id}`);
};

// 处理出题策略
const handlePromptStrategy = async (record) => {
  currentDocumentForPrompt.value = record;
  promptStrategyModalVisible.value = true;
  
  try {
    // 获取已有的提示词
    promptStrategyForm.prompt = null
    const result = await getPromptStrategy(record.id);
    console.log("result",result)
    if (result && result.prompt) {
      promptStrategyForm.prompt = result.prompt;
    } else {
      // 默认提示词
      promptStrategyForm.prompt = '';
    }
  } catch (error) {
    console.error('获取提示词失败', error);
    // 设置默认提示词
    promptStrategyForm.prompt = '';
  }
};

// 保存提示词
const savePrompt = async () => {
  if (!promptStrategyForm.prompt.trim()) {
    message.error('提示词不能为空');
    return;
  }
  
  if (!currentDocumentForPrompt.value?.id) {
    message.error('文档ID不存在');
    return;
  }
  
  try {
    promptStrategyLoading.value = true;
    
    // 调用保存提示词API
    await savePromptStrategy(currentDocumentForPrompt.value.id, {
      prompt: promptStrategyForm.prompt
    });
    
    message.success('提示词保存成功，系统将根据新的策略生成题目');
    promptStrategyModalVisible.value = false;
    
    // 刷新文档列表数据，获取最新的处理状态
    await fetchDocumentList();
    
    // 强制重新渲染DocProcessStatus组件
    processStatusKey.value += 1;
  } catch (error) {
    message.error('保存提示词失败: ' + (error.message || '未知错误'));
  } finally {
    promptStrategyLoading.value = false;
  }
};

// 处理文档状态切换
const handleStatusChange = async (record, status) => {
  // 保存原始状态，如果为null或undefined，则默认为'enable'
  const originalStatus = record.status === null || record.status === undefined ? 'enable' : record.status;
  
  try {
    // 设置当前行的状态加载标志
    record.statusLoading = true;
    
    // 调用接口切换状态
    await toggleDocumentStatus(record.id, status);
    
    // 更新本地状态
    record.status = status;
    
    // 显示成功消息
    message.success(status === 'enable' ? '文档已启用' : '文档已禁用');
  } catch (error) {
    console.error('切换文档状态失败', error);
    message.error('切换状态失败: ' + (error.message || '未知错误'));
    
    // 发生错误时恢复原状态
    record.status = originalStatus;
  } finally {
    // 取消加载状态
    record.statusLoading = false;
  }
};

// 判断是否需要自动刷新状态
const shouldAutoRefresh = (record) => {
  // 如果记录中包含processStatus字段，且状态为completed或failed，则不需要自动刷新
  return !(record.processStatus === 'completed' || record.processStatus === 'failed');
};

// 获取初始状态对象
const getInitialStatus = (record) => {
  // 如果记录中包含processStatus字段，构建初始状态对象
  if (record.processStatus) {
    return {
      id: record.id,
      status: record.processStatus,
      progress: record.processProgress || 0,
      message: record.processMessage || '',
      updatedAt: record.updatedTime
    };
  }
  
  // 没有处理状态信息，返回null
  return null;
};

// 处理状态变化
const handleProcessStatusChange = (status) => {
  // 如果状态已完成或失败，更新本地记录
  if (status.status === 'completed' || status.status === 'failed') {
    // 查找并更新文档记录
    const doc = documentList.value.find(item => item.id === status.id);
    if (doc) {
      doc.processStatus = status.status;
      doc.processProgress = status.progress;
      doc.processMessage = status.message;
    }
  }
};

// 初始化
onMounted(() => {
  Promise.all([
    fetchOptions(),
    fetchDocumentList()
  ]).then(() => {
    formKey.value += 1;
  }).catch(error => {
  });
});
</script>

<style lang="scss" scoped>
  .document-name {
    display: flex;
    align-items: center;

    .document-icon {
      margin-right: 8px;
      font-size: 20px;

      .pdf-icon {
        color: #f56c6c;
      }

      .word-icon {
        color: #409eff;
      }

      .excel-icon {
        color: #67c23a;
      }

      .ppt-icon {
        color: #e6a23c;
      }

      .image-icon {
        color: #9c27b0;
      }

      .text-icon {
        color: #909399;
      }
    }
  }

  /* 移除了已在全局样式定义的custom-button和表格相关样式 */

// 添加全屏遮罩层样式，模拟v-loading效果
.global-loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: wait;
  user-select: none;
  pointer-events: auto; // 确保遮罩层能接收所有点击事件

  .loading-spinner {
    text-align: center;
  }

  .loading-text {
    color: #fff;
    margin-top: 12px;
    font-size: 16px;
  }

  :deep(.ant-spin-dot-item) {
    background-color: #fff;
  }
}
</style>
