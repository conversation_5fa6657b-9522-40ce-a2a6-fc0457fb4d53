const { ExamConfig, ExamGlobalConfig, Level} = require('../../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, addEnterpriseId } = require('../../utils/enterpriseFilter');

/**
 * 统一错误处理
 * @param {Object} res - 响应对象
 * @param {Error} error - 错误对象
 */
const handleError = (res, error) => {
  console.error('操作失败:', error);

  // 区分错误类型
  if (error.name === 'SequelizeValidationError' || error.name === 'SequelizeUniqueConstraintError') {
    // 数据验证错误
    return res.status(400).json({
      code: 400,
      message: '数据验证错误',
      errors: error.errors.map(err => ({
        field: err.path,
        message: err.message
      }))
    });
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    // 外键约束错误
    return res.status(400).json({
      code: 400,
      message: '数据关联错误，请检查关联数据是否存在'
    });
  } else if (error.name === 'SequelizeDatabaseError') {
    // 数据库错误
    return res.status(500).json({
      code: 500,
      message: '数据库操作错误',
      error: process.env.NODE_ENV === 'production' ? undefined : error.message
    });
  }

  // 默认错误处理
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'production' ? undefined : error.message
  });
};

/**
 * 获取练考配置列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getExamConfigList = async (req, res) => {
  try {
    console.log('获取练考配置列表');
    const { PositionType, PositionName, Level, KnowledgeBase } = require('../../models');

    // 获取分页参数
    const pageNum = parseInt(req.query.pageNum || 1);
    const pageSize = parseInt(req.query.pageSize || 10);

    // 获取筛选条件
    const { searchText, positionBelong, positionName, level, statusFilter } = req.query;
    console.log('查询参数:', { searchText, positionBelong, positionName, level, statusFilter, pageNum, pageSize });

    // 构建查询条件
    const whereConditions = {};

    // 处理搜索文本(匹配岗位名称、科目名称或科目ID)
    if (searchText) {
      whereConditions[Op.or] = [
        { positionName: { [Op.like]: `%${searchText}%` } },
        { examSubject: { [Op.like]: `%${searchText}%` } },
        // 使用Sequelize关联查询语法
        { '$subjectKnowledge.name$': { [Op.like]: `%${searchText}%` } }
      ];
    }

    // 处理岗位类型过滤
    if (positionBelong) {
      // 如果positionBelong是纯数字，则认为是ID
      if (/^\d+$/.test(positionBelong)) {
        whereConditions.positionBelong = positionBelong;
      } else {
        // 否则认为是名称，使用关联表的name字段查询
        whereConditions['$positionBelongDict.name$'] = positionBelong;
      }
    }

    // 处理岗位名称过滤 - 修改为使用关联查询，支持ID或名称匹配
    if (positionName) {
      // 如果positionName是纯数字，则认为是ID
      if (/^\d+$/.test(positionName)) {
        whereConditions.positionName = positionName;
      } else {
        // 否则认为是名称，使用关联表的name字段查询
        whereConditions['$positionDict.name$'] = positionName;
      }
    }

    // 处理岗位等级过滤 - 修改为使用关联查询，支持ID或名称匹配
    if (level) {
      // 如果level是纯数字，则认为是ID
      if (/^\d+$/.test(level)) {
        whereConditions.positionLevel = level;
      } else {
        // 否则认为是名称，使用关联表的name字段查询
        whereConditions['$level.name$'] = level;
      }
    }

    // 处理状态过滤
    if (statusFilter) {
      whereConditions.status = statusFilter;
    }

    // 计算分页偏移量
    const offset = (pageNum - 1) * pageSize;

    console.log('查询条件:', JSON.stringify(whereConditions));

    // 使用findAndCountAll进行分页查询
    const { rows: configs, count: total } = await ExamConfig.findAndCountAll(
      addEnterpriseFilter({
        where: whereConditions,
        order: [['created_time', 'DESC']],
        offset,
        limit: pageSize,
        include: [
          {
            model: PositionType,
            as: 'positionBelongDict',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: PositionName,
            as: 'positionDict',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: Level,
            as: 'level',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: KnowledgeBase,
            as: 'subjectKnowledge',
            attributes: ['id', 'name'],
            required: false
          }
        ],
        distinct: true // 确保count正确计算，避免关联表导致的重复计数
      })
    );

    // 处理返回数据，添加关联信息
    const formattedConfigs = configs.map(config => {
      const data = config.toJSON();
      return {
        ...data,
        departmentName: data.positionBelongDict?.name || data.positionBelong,
        positionFullName: data.positionDict?.name || data.positionName,
        levelFullName: data.level?.name || data.positionLevel,
        knowledgeBaseName: data.subjectKnowledge?.name
      };
    });

    return res.json({
      code: 200,
      message: 'success',
      data: {rows:formattedConfigs,total:total},
      total,
      pageNum,
      pageSize
    });
  } catch (error) {
    console.error('获取列表错误:', error);
    handleError(res, error);
  }
};

/**
 * 获取练考配置详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getExamConfigDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const config = await ExamConfig.findByPk(id);
    if (!config) {
      return res.status(404).json({
        code: 404,
        message: '配置不存在',
        data: null
      });
    }

    return res.json({
      code: 200,
      message: 'success',
      data: config
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 创建练考配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.createExamConfig = async (req, res) => {
  try {
    const configData = req.body;

    // 检查是否存在考试通用规则配置
    const globalConfig = await ExamGlobalConfig.findOne(addEnterpriseFilter());
    if (!globalConfig && configData.status === '必考') {
      return res.status(400).json({
        code: 400,
        message: '请先配置考试通用规则，再创建必考配置',
        data: null
      });
    }

    // 第一层规则：同一岗位同一等级下，练考科目不能重复
    const duplicateSubjectConfig = await ExamConfig.findOne(
      addEnterpriseFilter({
        where: {
          positionBelong: configData.positionBelong,
          positionName: configData.positionName,
          positionLevel: configData.positionLevel,
          examSubject: configData.examSubject
        }
      })
    );

    if (duplicateSubjectConfig) {
      return res.status(400).json({
        code: 400,
        message: '同一岗位同一等级下，练考科目不能重复',
        data: null
      });
    }

    // 第二层规则：同一岗位，同一个练考科目，只能有一个必考
    if (configData.status === '必考') {
      const duplicateRequiredConfig = await ExamConfig.findOne(
        addEnterpriseFilter({
          where: {
            positionBelong: configData.positionBelong,
            positionName: configData.positionName,
            examSubject: configData.examSubject,
            status: '必考'
          }
        })
      );

      if (duplicateRequiredConfig) {
        return res.status(400).json({
          code: 400,
          message: '同一岗位同一个练考科目只能有一个必考配置',
          data: null
        });
      }
    }

    // 设置练习相关字段的默认值
    if (globalConfig) {
      configData.questionMode =  globalConfig.questionMode ;
      configData.practiceDuration =  globalConfig.practiceDuration ;
      configData.practiceQuestionCount =  globalConfig.practiceQuestionCount ;
    } 

    // 根据状态设置默认值
    if (configData.status === '必练') {
      configData.examCount = 0;
      configData.questionCount = 0;
      configData.examDuration = 0;
      configData.needConfirmScore = null;
      configData.passScore = 0;
      configData.certificateValidDays = 0;
    } else if (configData.status === '必考' && globalConfig) {
      // 如果是必考模式，从全局配置获取默认值
      configData.examCount =  globalConfig.examCount ;
      configData.questionCount =  globalConfig.questionCount;
      configData.examDuration =  globalConfig.examDuration ;
      configData.needConfirmScore =   globalConfig.needConfirmScore;
      configData.passScore = globalConfig.passScore ;
      configData.certificateValidDays =  globalConfig.certificateValidDays ;
      configData.manualQuestionRatio =  globalConfig.manualQuestionRatio ;
      configData.intelligentQuestionRatio =  globalConfig.intelligentQuestionRatio ;
    }

    // 确保examSubject是字符串类型
    if (configData.examSubject && typeof configData.examSubject !== 'string') {
      // 如果是数组，只取第一个值（前端应该已经处理成每个科目一条记录）
      if (Array.isArray(configData.examSubject)) {
        configData.examSubject = String(configData.examSubject[0]);
      } else {
        configData.examSubject = String(configData.examSubject);
      }
    }

    console.log('创建练考配置:', JSON.stringify(configData));

    // 创建配置时添加企业ID
    const newConfig = await ExamConfig.create(addEnterpriseId(configData));

    return res.status(201).json({
      code: 200,
      message: 'success',
      data: newConfig
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 更新练考配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateExamConfig = async (req, res) => {
  try {
    const { id } = req.params;
    const configData = req.body;

    const config = await ExamConfig.findByPk(id);
    if (!config) {
      return res.status(404).json({
        code: 404,
        message: '配置不存在',
        data: null
      });
    }

    // 检查状态变化，处理必练必考状态切换时的数据同步
    const oldStatus = config.status;
    const newStatus = configData.status;
    
    if (oldStatus !== newStatus) {
      if (newStatus === '必考') {
        // 从必练改为必考时，检查是否存在考试通用规则配置
        const globalConfig = await ExamGlobalConfig.findOne(addEnterpriseFilter());
        if (!globalConfig) {
          return res.status(400).json({
            code: 400,
            message: '请先配置考试通用规则，再设置为必考',
            data: null
          });
        }
        
        // 从全局配置获取必考相关的默认值
        configData.examCount = globalConfig.examCount;
        configData.questionCount = globalConfig.questionCount;
        configData.examDuration = globalConfig.examDuration;
        configData.needConfirmScore = globalConfig.needConfirmScore;
        configData.passScore = globalConfig.passScore;
        configData.certificateValidDays = globalConfig.certificateValidDays;
        configData.manualQuestionRatio = globalConfig.manualQuestionRatio;
        configData.intelligentQuestionRatio = globalConfig.intelligentQuestionRatio;
        
        console.log('从必练改为必考，应用全局配置:', {
          examCount: configData.examCount,
          questionCount: configData.questionCount,
          examDuration: configData.examDuration,
          passScore: configData.passScore
        });
      } else if (newStatus === '必练') {
        // 从必考改为必练时，清空考试相关字段
        configData.examCount = 0;
        configData.questionCount = 0;
        configData.examDuration = 0;
        configData.needConfirmScore = null;
        configData.passScore = 0;
        configData.certificateValidDays = 0;
        
        console.log('从必考改为必练，清空考试相关配置');
      }
    }

    // 第一层规则：同一岗位同一等级下，练考科目不能重复
    // 更新时需要排除当前记录ID
    const duplicateSubjectConfig = await ExamConfig.findOne(
      addEnterpriseFilter({
        where: {
          positionBelong: configData.positionBelong || config.positionBelong,
          positionName: configData.positionName || config.positionName,
          positionLevel: configData.positionLevel || config.positionLevel,
          examSubject: configData.examSubject || config.examSubject,
          id: { [Op.ne]: id } // 排除当前记录
        }
      })
    );

    if (duplicateSubjectConfig) {
      return res.status(400).json({
        code: 400,
        message: '同一岗位同一等级下，练考科目不能重复',
        data: null
      });
    }

    // 第二层规则：同一岗位，同一个练考科目，只能有一个必考
    const updateStatus = configData.status || config.status;
    if (updateStatus === '必考') {
      const duplicateRequiredConfig = await ExamConfig.findOne(
        addEnterpriseFilter({
          where: {
            positionBelong: configData.positionBelong || config.positionBelong,
            positionName: configData.positionName || config.positionName,
            examSubject: configData.examSubject || config.examSubject,
            status: '必考',
            id: { [Op.ne]: id } // 排除当前记录
          }
        })
      );

      if (duplicateRequiredConfig) {
        return res.status(400).json({
          code: 400,
          message: '同一岗位同一个练考科目只能有一个必考配置',
          data: null
        });
      }
    }

    // 确保examSubject是字符串类型
    if (configData.examSubject && typeof configData.examSubject !== 'string') {
      // 如果是数组，只取第一个值（编辑模式下应该只有一个值）
      if (Array.isArray(configData.examSubject)) {
        configData.examSubject = String(configData.examSubject[0]);
      } else {
        configData.examSubject = String(configData.examSubject);
      }
    }

    console.log('更新练考配置:', JSON.stringify(configData));

    await config.update(configData);

    return res.json({
      code: 200,
      message: 'success',
      data: config
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 删除练考配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.deleteExamConfig = async (req, res) => {
  try {
    const { id } = req.params;

    const config = await ExamConfig.findByPk(id);
    if (!config) {
      return res.status(404).json({
        code: 404,
        message: '配置不存在',
        data: null
      });
    }

    await config.destroy();

    return res.json({
      code: 200,
      message: 'success',
      data: null
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 批量删除练考配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.batchDeleteExamConfig = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请选择要删除的配置',
        data: null
      });
    }

    // 删除时添加企业ID过滤
    await ExamConfig.destroy(
      addEnterpriseFilter({
        where: {
          id: ids
        }
      })
    );

    return res.json({
      code: 200,
      message: 'success',
      data: null
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 获取考试通用规则配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getExamGlobalConfig = async (req, res) => {
  try {
    const { id } = req.query;
    let where = addEnterpriseFilter({});

    // 如果指定了记录ID，则查询特定记录的规则
    if (id) {
      // 先查询该条记录
      const examConfig = await ExamConfig.findByPk(id);
      if (!examConfig) {
        return res.status(404).json({
          code: 404,
          message: '配置不存在',
          data: null
        });
      }

      // 构建记录特定的规则响应
      const ruleResponse = {
        id: examConfig.id,
        examCount: examConfig.examCount,
        examDuration: examConfig.examDuration,
        needConfirmScore: examConfig.needConfirmScore,
        passScore: examConfig.passScore,
        scoreReleaseRule: examConfig.scoreReleaseRule,
        questionDistribution: examConfig.questionDistribution,
        questionType: examConfig.questionType,
        difficultyLevel: examConfig.difficultyLevel,
        questionCount: examConfig.questionCount,
        questionMode: examConfig.questionMode,
        practiceDuration: examConfig.practiceDuration,
        practiceQuestionCount: examConfig.practiceQuestionCount,
        certificateValidDays: examConfig.certificateValidDays,
        manualQuestionRatio: examConfig.manualQuestionRatio,
        intelligentQuestionRatio: examConfig.intelligentQuestionRatio
      };

      return res.json({
        code: 200,
        message: 'success',
        data: ruleResponse
      });
    }

    // 查询全局规则
    let globalConfig = await ExamGlobalConfig.findOne(where);

    return res.json({
      code: 200,
      message: 'success',
      data: globalConfig
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 更新考试通用规则配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateExamGlobalConfig = async (req, res) => {
  try {
    const {
      examCount,
      examDuration,
      needConfirmScore,
      passScore,
      scoreReleaseRule,
      certificateValidDays,
      questionCount,
      manualQuestionRatio,
      intelligentQuestionRatio
    } = req.body;

    let globalConfig = await ExamGlobalConfig.findOne(addEnterpriseFilter({}));

    // 构建考试配置数据
    const examData = {};
    if (examCount !== undefined) examData.examCount = examCount;
    if (examDuration !== undefined) examData.examDuration = examDuration;
    if (needConfirmScore !== undefined) examData.needConfirmScore = needConfirmScore;
    if (passScore !== undefined) examData.passScore = passScore;
    if (scoreReleaseRule !== undefined) examData.scoreReleaseRule = scoreReleaseRule;
    if (certificateValidDays !== undefined) {
      examData.certificateValidDays = parseInt(certificateValidDays, 10) || 90;
    }
    if (questionCount !== undefined) examData.questionCount = questionCount;
    if (manualQuestionRatio !== undefined) examData.manualQuestionRatio = manualQuestionRatio;
    if (intelligentQuestionRatio !== undefined) examData.intelligentQuestionRatio = intelligentQuestionRatio;

    // 如果不存在，创建新配置
    if (!globalConfig) {
      // 设置默认值
      const defaultConfig = {
        questionMode: '时长',
        practiceDuration: 60,
        practiceQuestionCount: 20,
        examCount: examCount || 3,
        examDuration: examDuration || 90,
        needConfirmScore: needConfirmScore !== undefined ? needConfirmScore : true,
        passScore: passScore || 80,
        scoreReleaseRule: scoreReleaseRule || '自动发布',
        certificateValidDays: certificateValidDays ? parseInt(certificateValidDays, 10) : 90,
        questionCount: questionCount || 20,
        manualQuestionRatio: manualQuestionRatio || 50,
        intelligentQuestionRatio: intelligentQuestionRatio || 50
      };
      globalConfig = await ExamGlobalConfig.create(addEnterpriseId(defaultConfig));
    } else {
      // 更新现有配置
      await globalConfig.update(examData);
    }

    // 同步更新所有必考状态的配置
    const updateResult = await ExamConfig.update(
      examData,
      addEnterpriseFilter({
        where: {
          status: '必考'
        }
      })
    );

    return res.json({
      code: 200,
      message: 'success',
      data: {
        globalConfig,
        updatedCount: updateResult[0]
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 手动同步所有必考配置与通用规则
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.syncExamConfigs = async (req, res) => {
  try {
    // 获取通用规则配置
    const globalConfig = await ExamGlobalConfig.findOne(addEnterpriseFilter({}));

    if (!globalConfig) {
      return res.status(400).json({
        code: 400,
        message: '通用规则配置不存在，请先配置通用规则',
        data: null
      });
    }

    // 同步更新所有必考状态的配置
    const updateResult = await ExamConfig.update(
      {
        examCount: globalConfig.examCount,
        examDuration: globalConfig.examDuration,
        needConfirmScore: globalConfig.needConfirmScore,
        passScore: globalConfig.passScore,
        scoreReleaseRule: globalConfig.scoreReleaseRule,
        certificateValidDays: globalConfig.certificateValidDays,
        questionDistribution: globalConfig.questionDistribution,
        questionType: globalConfig.questionType,
        difficultyLevel: globalConfig.difficultyLevel,
        manualQuestionRatio: globalConfig.manualQuestionRatio,
        intelligentQuestionRatio: globalConfig.intelligentQuestionRatio
      },
      addEnterpriseFilter({
        where: {
          status: '必考'
        }
      })
    );

    return res.json({
      code: 200,
      message: 'success',
      data: {
        updatedCount: updateResult[0],
        globalConfig
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 更新通用练习配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updatePracticeGlobalConfig = async (req, res) => {
  try {
    const { questionMode, practiceDuration, practiceQuestionCount } = req.body;

    let globalConfig = await ExamGlobalConfig.findOne(addEnterpriseFilter({}));

    // 构建练习配置数据
    const practiceData = {};
    if (questionMode !== undefined) practiceData.questionMode = questionMode;
    if (practiceDuration !== undefined) practiceData.practiceDuration = practiceDuration;
    if (practiceQuestionCount !== undefined) practiceData.practiceQuestionCount = practiceQuestionCount;

    // 如果不存在，创建新配置
    if (!globalConfig) {
      // 设置默认值
      const defaultConfig = {
        questionMode: questionMode || '时长',
        practiceDuration: practiceDuration || 60,
        practiceQuestionCount: practiceQuestionCount || 20,
        examCount: 3,
        examDuration: 90,
        needConfirmScore: true,
        passScore: 80,
        scoreReleaseRule: '自动发布',
        certificateValidDays: 90,
        questionCount: 20,
        manualQuestionRatio: 50,
        intelligentQuestionRatio: 50
      };
      globalConfig = await ExamGlobalConfig.create(addEnterpriseId(defaultConfig));
    } else {
      // 更新现有配置
      await globalConfig.update(practiceData);
    }

    // 同步更新所有配置的练习相关字段
    const updateResult = await ExamConfig.update(
      practiceData,
      addEnterpriseFilter({
        where: {}  // 更新所有配置，包括必练和必考
      })
    );

    return res.json({
      code: 200,
      message: 'success',
      data: {
        globalConfig,
        updatedCount: updateResult[0]
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 更新单个配置的练习规则
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateConfigPracticeRules = async (req, res) => {
  try {
    const { id } = req.params;
    const { questionMode, practiceDuration, practiceQuestionCount } = req.body;

    const config = await ExamConfig.findByPk(id);
    if (!config) {
      return res.status(404).json({
        code: 404,
        message: '配置不存在',
        data: null
      });
    }

    // 构建练习配置数据
    const practiceData = {};
    if (questionMode !== undefined) practiceData.questionMode = questionMode;
    if (practiceDuration !== undefined) practiceData.practiceDuration = practiceDuration;
    if (practiceQuestionCount !== undefined) practiceData.practiceQuestionCount = practiceQuestionCount;

    await config.update(practiceData);

    return res.json({
      code: 200,
      message: 'success',
      data: config
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * 更新单个配置的考试规则
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateConfigExamRules = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      examCount,
      examDuration,
      needConfirmScore,
      passScore,
      scoreReleaseRule,
      certificateValidDays,
      questionCount,
      manualQuestionRatio,
      intelligentQuestionRatio
    } = req.body;

    const config = await ExamConfig.findByPk(id);
    if (!config) {
      return res.status(404).json({
        code: 404,
        message: '配置不存在',
        data: null
      });
    }

    // 只有必考配置才能更新考试规则
    if (config.status !== '必考') {
      return res.status(400).json({
        code: 400,
        message: '只有必考配置才能更新考试规则',
        data: null
      });
    }

    // 构建考试配置数据
    const examData = {};
    if (examCount !== undefined) examData.examCount = examCount;
    if (examDuration !== undefined) examData.examDuration = examDuration;
    if (needConfirmScore !== undefined) examData.needConfirmScore = needConfirmScore;
    if (passScore !== undefined) examData.passScore = passScore;
    if (scoreReleaseRule !== undefined) examData.scoreReleaseRule = scoreReleaseRule;
    if (certificateValidDays !== undefined) examData.certificateValidDays = certificateValidDays;
    if (questionCount !== undefined) examData.questionCount = questionCount;
    if (manualQuestionRatio !== undefined) examData.manualQuestionRatio = manualQuestionRatio;
    if (intelligentQuestionRatio !== undefined) examData.intelligentQuestionRatio = intelligentQuestionRatio;

    await config.update(examData);

    return res.json({
      code: 200,
      message: 'success',
      data: config
    });
  } catch (error) {
    handleError(res, error);
  }
};

