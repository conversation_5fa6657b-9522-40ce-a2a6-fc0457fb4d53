// 用户数据模型
let userData = [
  {
    id: 1,
    username: 'admin',
    nickname: '超级管理员',
    password: '123456', // 实际项目中应该存储加密后的密码
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    email: '<EMAIL>',
    phone: '13800138000',
    roleId: 1,
    status: true,
    createTime: '2024-03-20 10:00:00'
  },
  {
    id: 2,
    username: 'user',
    nickname: '普通用户',
    password: '123456',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    email: '<EMAIL>',
    phone: '13800138001',
    roleId: 2,
    status: true,
    createTime: '2024-03-20 10:00:00'
  },
  {
    id: 3,
    username: 'guest',
    nickname: '访客用户',
    password: '123456',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    email: '<EMAIL>',
    phone: '13800138002',
    roleId: 3,
    status: false,
    createTime: '2024-03-20 10:00:00'
  }
];

// 获取用户列表
exports.getUserList = (query = {}) => {
  let result = [...userData];
  
  // 支持按用户名过滤
  if (query.username) {
    result = result.filter(user => 
      user.username.toLowerCase().includes(query.username.toLowerCase())
    );
  }
  
  // 支持按昵称过滤
  if (query.nickname) {
    result = result.filter(user => 
      user.nickname.toLowerCase().includes(query.nickname.toLowerCase())
    );
  }
  
  // 支持按状态过滤
  if (query.status !== undefined) {
    const status = query.status === 'true' || query.status === true;
    result = result.filter(user => user.status === status);
  }
  
  // 计算总数
  const total = result.length;
  
  // 分页处理
  const pageNum = parseInt(query.pageNum) || 1;
  const pageSize = parseInt(query.pageSize) || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = pageNum * pageSize;
  const list = result.slice(startIndex, endIndex);
  
  // 返回结果时排除密码字段
  const safeList = list.map(user => {
    const { password, ...safeUser } = user;
    return safeUser;
  });
  
  return {
    list: safeList,
    total
  };
};

// 获取用户信息
exports.getUserInfo = (id) => {
  const user = userData.find(user => user.id === id);
  if (!user) return null;
  
  const { password, ...safeUser } = user;
  return safeUser;
};

// 用户登录
exports.login = (username, password) => {
  const user = userData.find(
    user => user.username === username && user.password === password
  );
  
  if (!user) return null;
  
  const { password: pwd, ...safeUser } = user;
  return {
    token: `token-${user.id}-${Date.now()}`,
    user: safeUser
  };
};

// 创建用户
exports.createUser = (user) => {
  const newUser = {
    ...user,
    id: Date.now(),
    createTime: new Date().toLocaleString()
  };
  
  userData.push(newUser);
  
  const { password, ...safeUser } = newUser;
  return safeUser;
};

// 更新用户
exports.updateUser = (user) => {
  const index = userData.findIndex(item => item.id === user.id);
  if (index > -1) {
    userData[index] = { ...userData[index], ...user };
    
    const { password, ...safeUser } = userData[index];
    return safeUser;
  }
  return null;
};

// 删除用户
exports.deleteUser = (id) => {
  const index = userData.findIndex(user => user.id === id);
  if (index > -1) {
    const { password, ...safeUser } = userData.splice(index, 1)[0];
    return safeUser;
  }
  return null;
}; 