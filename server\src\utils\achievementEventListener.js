const EventEmitter = require('events');
const { Op } = require('sequelize');

// 创建事件发射器实例
const achievementEventEmitter = new EventEmitter();

/**
 * 成就事件类型常量
 */
const ACHIEVEMENT_EVENTS = {
  // 首次学习进度事件（包括初入宝殿和知识探索）
  FIRST_COMPLETE: 'first_complete'
};

/**
 * 发射成就事件
 * @param {string} eventType - 事件类型
 * @param {Object} eventData - 事件数据
 */
const emitAchievementEvent = (eventType, eventData) => {
  try {
    console.log(`[成就事件] 发射事件: ${eventType}`, eventData);
    achievementEventEmitter.emit(eventType, eventData);
  } catch (error) {
    console.error('[成就事件] 发射事件失败:', error);
  }
};

/**
 * 监听所有成就相关事件（已弃用，改为直接调用）
 */
const setupAchievementEventListeners = () => {
  console.log('[成就系统] 初始化事件监听器...');
  console.log('[成就系统] 注意：初入宝殿和知识探索成就已改为直接调用方式，无需事件监听器');
  
  // 不再监听 FIRST_COMPLETE 事件，改为直接调用 achievementUtils 中的函数
  // achievementEventEmitter.on(ACHIEVEMENT_EVENTS.FIRST_COMPLETE, async (eventData) => {
  //   await handleFirstSubjectProgressEvent(eventData);
  // });

  console.log('[成就系统] 事件监听器初始化完成（实际已弃用事件机制）');
};

/**
 * 处理首次科目学习进度事件（已弃用，改为直接调用achievementUtils中的函数）
 */
const handleFirstSubjectProgressEvent = async (eventData) => {
  console.log('[成就检测] handleFirstSubjectProgressEvent 已弃用');
  console.log('[成就检测] 请使用 achievementUtils.js 中的直接调用方式');
  console.log('[成就检测] - processFirstEntryAchievement: 初入宝殿成就');
  console.log('[成就检测] - processKnowledgeExplorationAchievement: 知识探索成就');
  console.log('[成就检测] - processTimeMasterAchievement: 碎片时间大师成就');
};

/**
 * 检查所有科目的学习进度（已弃用，请使用achievementUtils中的函数）
 * @param {string} openId - 用户openId
 * @param {string} positionName - 岗位名称
 * @param {string} positionLevel - 岗位级别
 * @param {string} enterpriseId - 企业ID
 * @returns {Array} 所有科目的进度信息
 */
const checkAllSubjectsProgress = async (openId, positionName, positionLevel, enterpriseId) => {
  console.log('[成就检测] checkAllSubjectsProgress 已弃用');
  console.log('[成就检测] 请使用 achievementUtils.js 中的 checkAllSubjectsProgressWithoutRequired 函数');
  return [];
};

/**
 * 颁发成就
 */
const awardAchievement = async (template, userId, openId, enterpriseId, progress) => {
  try {
    const UserAchievement = require('../models/UserAchievement');
    const { addEnterpriseId } = require('./enterpriseFilter');

    // 创建成就记录
    const achievement = await UserAchievement.create(
      addEnterpriseId({
        userId,
        openId,
        templateId: template.id,
        achievementName: template.name,
        achievementIcon: template.icon,
        category: template.category,
        status: 'achieved',
        achievedAt: new Date(),
        rewardPoints: template.rewardPoints || 10,
        triggerCondition: template.triggerCondition,
        achievementData: {
          finalValue: progress.currentValue,
          targetValue: progress.targetValue,
          completedAt: progress.completedAt
        }
      }, enterpriseId)
    );

    console.log(`🎉 [成就获得] 用户${userId}获得成就: ${template.name}`);

    // 发送成就获得通知事件
    emitAchievementEvent('achievement_awarded', {
      userId,
      openId,
      enterpriseId,
      achievementId: achievement.id,
      achievementName: template.name,
      achievementIcon: template.icon,
      rewardPoints: template.rewardPoints || 10
    });

    return achievement;

  } catch (error) {
    console.error('[成就颁发] 颁发成就失败:', error);
    throw error;
  }
};

/**
 * 初始化成就系统（仅支持初入宝殿和知识探索成就）
 */
const initializeAchievementSystem = () => {
  console.log('[成就系统] 开始初始化...');
  
  try {
    // 启动事件监听器
    setupAchievementEventListeners();
    
    console.log('[成就系统] 初始化完成');
  } catch (error) {
    console.error('[成就系统] 初始化失败:', error);
  }
};

module.exports = {
  achievementEventEmitter,
  emitAchievementEvent,
  setupAchievementEventListeners,
  initializeAchievementSystem,
  ACHIEVEMENT_EVENTS,
  awardAchievement
};
