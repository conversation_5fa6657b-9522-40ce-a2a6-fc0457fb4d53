import request from '@/utils/request'

/**
 * 获取知识库题目列表
 * @param {String} id 知识库ID
 * @param {Object} params 查询参数
 * @returns {Promise} Promise对象
 */
export function getQuestionList(id, params = {}) {
  // 特殊处理all情况，使用专门的路由
  if (id === 'all') {
    return request({
      url: `/knowledge-base/all/questions`,
      method: 'get',
      params
    });
  }
  
  return request({
    url: `/knowledge-base/${id}/questions`,
    method: 'get',
    params
  });
}

/**
 * 添加题目
 * @param {String} id 知识库ID
 * @param {Object} data 题目数据
 * @returns {Promise} Promise对象
 */
export function addQuestion(id, data) {
  return request({
    url: `/knowledge-base/${id}/questions`,
    method: 'post',
    data
  })
}

/**
 * 更新题目
 * @param {String} id 知识库ID
 * @param {Object} data 题目数据
 * @returns {Promise} Promise对象
 */
export function updateQuestion(id, data) {
  return request({
    url: `/knowledge-base/${id}/questions`,
    method: 'put',
    data
  })
}

/**
 * 删除题目
 * @param {String} knowledgeId 知识库ID
 * @param {String} questionId 题目ID
 * @returns {Promise} Promise对象
 */
export function deleteQuestion(knowledgeId, questionId) {
  return request({
    url: `/knowledge-base/${knowledgeId}/questions/${questionId}`,
    method: 'delete'
  })
}

/**
 * 批量保存题目
 * @param {String} knowledgeId 知识库ID
 * @param {Array} questions 题目数组
 * @returns {Promise} Promise对象
 */
export function batchSaveQuestions(knowledgeId, questions) {
  return request({
    url: `/knowledge-base/${knowledgeId}/batch-save-questions`,
    method: 'post',
    data: { questions }
  })
}

/**
 * 获取知识库的详细信息
 * @param {String} id 知识库ID
 * @returns {Promise} Promise对象
 */
export function getKnowledgeBaseDetail(id) {
  return request({
    url: `/knowledge-base/detail/${id}`,
    method: 'get'
  })
} 

/**
 * 获取知识库选项列表（用于筛选）
 * @returns {Promise} Promise对象
 */
export function getKnowledgeOptions() {
  return request({
    url: `/knowledge-base/knowledge-options`,
    method: 'get',
    responseType: 'full'
  })
}

/**
 * 下载题目导入模板
 * @returns {Promise} Promise对象
 */
export function downloadTemplate() {
  return request({
    url: `/knowledge-base/download-template`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导入题目Excel文件
 * @param {File} file Excel文件
 * @returns {Promise} Promise对象
 */
export function importQuestions(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: `/knowledge-base/import`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出题目列表为Excel
 * @param {String} id 知识库ID
 * @param {Object} params 查询参数
 * @returns {Promise} Promise对象
 */
export function exportQuestions(id, params = {}) {
  // 特殊处理all情况，使用专门的路由
  if (id === 'all') {
    return request({
      url: `/knowledge-base/all/export`,
      method: 'get',
      params,
      responseType: 'blob'
    });
  }
  
  return request({
    url: `/knowledge-base/${id}/export`,
    method: 'get',
    params,
    responseType: 'blob'
  });
}
