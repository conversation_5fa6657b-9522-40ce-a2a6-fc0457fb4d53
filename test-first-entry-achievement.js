const { emitAchievementEvent, ACHIEVEMENT_EVENTS, setupAchievementEventListeners } = require('./server/src/utils/achievementEventListener');
const { processPracticeProgressAchievements } = require('./server/src/utils/achievementUtils');

/**
 * 测试初入宝殿成就功能
 */
async function testFirstEntryAchievement() {
  console.log('🎯 开始测试初入宝殿成就功能...\n');

  try {
    // 1. 启动成就事件监听器
    console.log('1️⃣ 启动成就事件监听器...');
    setupAchievementEventListeners();
    await delay(1000);
    console.log('✅ 事件监听器启动完成\n');

    // 2. 模拟用户首次学习不同进度的情况
    const testCases = [
      {
        userId: 1,
        openId: 'test_openid_001',
        enterpriseId: 1,
        subjectId: 1,
        positionName: '1',
        positionLevel: '1',
        description: '模拟首次学习进度检测'
      },
      {
        userId: 2,
        openId: 'test_openid_002',
        enterpriseId: 1,
        subjectId: 2,
        positionName: '2',
        positionLevel: '2',
        description: '模拟另一个用户的首次学习'
      }
    ];

    console.log('2️⃣ 开始测试不同用户的首次学习进度...\n');

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`测试用例 ${i + 1}: ${testCase.description}`);
      console.log(`  用户ID: ${testCase.userId}`);
      console.log(`  科目ID: ${testCase.subjectId}`);
      console.log(`  岗位: ${testCase.positionName}-${testCase.positionLevel}`);

      // 调用首次学习进度检测函数
      await processPracticeProgressAchievements(
        testCase.userId,
        testCase.openId,
        testCase.enterpriseId,
        testCase.subjectId,
        testCase.positionName,
        testCase.positionLevel
      );

      console.log(`  ✅ 检测完成\n`);
      await delay(2000); // 等待2秒让事件处理完成
    }

    // 3. 直接测试事件发射
    console.log('3️⃣ 直接测试首次学习进度事件发射...\n');

    const directTestEvent = {
      userId: 3,
      openId: 'test_openid_003',
      enterpriseId: 1,
      subjectId: 1,
      subjectName: '测试科目',
      positionName: '1',
      positionLevel: '1',
      actualProgress: 1.5  // 模拟超过1%的进度
    };

    console.log('发射首次学习进度事件:', directTestEvent);
    emitAchievementEvent(ACHIEVEMENT_EVENTS.FIRST_COMPLETE, directTestEvent);

    await delay(3000); // 等待3秒让事件处理完成

    console.log('🎉 初入宝殿成就测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }

  // 退出程序
  setTimeout(() => {
    console.log('测试结束，退出程序...');
    process.exit(0);
  }, 2000);
}

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
testFirstEntryAchievement().catch(console.error);
