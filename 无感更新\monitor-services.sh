#!/bin/bash

# 服务监控脚本
# 用于监控服务的运行状态和资源使用情况

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 配置项
APP_DIR="/home/<USER>/enterprise/demo_enterprise"
HEALTH_CHECK_API_URL="http://localhost:${ENT_ADMIN_API_PORT}/api/health"
HEALTH_CHECK_WEB_URL="http://localhost:${ENT_ADMIN_WEB_PORT}"
CHECK_INTERVAL=5  # 检查间隔(秒)

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}$1${NC}"
}

# 检查服务状态
check_service_status() {
    local service=$1
    local status=$(sudo docker compose ps --format json $service | grep -o '"State":"[^"]*"' | cut -d'"' -f4)
    
    if [ "$status" == "running" ]; then
        echo -e "${GREEN}运行中${NC}"
    else
        echo -e "${RED}$status${NC}"
    fi
}

# 获取服务资源使用情况
get_service_stats() {
    local service=$1
    local container_id=$(sudo docker compose ps -q $service)
    
    if [ -z "$container_id" ]; then
        echo "容器未运行"
        return
    fi
    
    # 获取CPU和内存使用情况
    local stats=$(sudo docker stats --no-stream --format "{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" $container_id)
    echo "$stats"
}

# 检查服务健康状态
check_health() {
    local service=$1
    local url=$2
    
    if curl -s -f "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}健康${NC}"
    else
        echo -e "${RED}不健康${NC}"
    fi
}

# 显示容器日志的最后几行
show_recent_logs() {
    local service=$1
    local lines=$2
    
    log_header "最近的 $lines 行日志 ($service):"
    sudo docker compose logs --tail=$lines $service
    echo
}

# 显示服务信息
show_service_info() {
    clear
    log_header "===== 服务监控 $(date '+%Y-%m-%d %H:%M:%S') ====="
    echo
    
    # 显示Docker容器版本
    log_header "Docker镜像版本:"
    echo -e "后端 API: $(sudo docker compose images admin-api --format '{{.Tag}}')"
    echo -e "前端 Web: $(sudo docker compose images admin-web --format '{{.Tag}}')"
    echo
    
    # 显示服务状态
    log_header "服务状态:"
    echo -e "后端 API: $(check_service_status admin-api)"
    echo -e "前端 Web: $(check_service_status admin-web)"
    echo
    
    # 显示健康状态
    log_header "健康状态:"
    echo -e "后端 API: $(check_health "admin-api" "$HEALTH_CHECK_API_URL")"
    echo -e "前端 Web: $(check_health "admin-web" "$HEALTH_CHECK_WEB_URL")"
    echo
    
    # 显示资源使用情况
    log_header "资源使用情况:"
    echo -e "后端 API: $(get_service_stats admin-api)"
    echo -e "前端 Web: $(get_service_stats admin-web)"
    echo
    
    # 显示最近的日志
    show_recent_logs "admin-api" 5
    show_recent_logs "admin-web" 5
    
    echo -e "${YELLOW}按 Ctrl+C 退出监控${NC}"
}

# 主函数
main() {
    cd "$APP_DIR" || { log_error "无法进入应用目录: $APP_DIR"; exit 1; }
    
    log_info "开始监控服务..."
    
    # 持续监控
    while true; do
        show_service_info
        sleep $CHECK_INTERVAL
    done
}

# 执行主函数
main 