# 忽略所有 node_modules 目录
node_modules
*/node_modules
**/node_modules

# 日志文件
logs
.specstory
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 测试覆盖率数据
coverage
.nyc_output

# 构建目录
dist
build
out

# 缓存目录
.cache
.npm
.yarn-cache

# 编辑器文件
.idea
.vscode
*.sublime-*
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# 环境变量文件
.env
.env.local
.env.*.local

/server/node_modules
/server/dist
/server/.env

# 依赖锁定文件 (可选择是否忽略)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
# SpecStory explanation file
.specstory/.what-is-this.md

# Added by Task Master AI
# Logs
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/
.cursor
.taskmaster
.roomodes
.windsurfrules
