const { ExamR<PERSON>ord, ExamReviewApplication, DictionaryData, Level, User } = require('../../models');
const { Op } = require('sequelize');
const { addEnterpriseFilter, DEFAULT_ENTERPRISE_ID } = require('../../utils/enterpriseFilter');
const logger = require('../../utils/logger');
const Employee = require('../../models/Employee');
const PositionName = require('../../models/PositionName');
const KnowledgeBase = require('../../models/knowledge-base');
const moment = require('moment');

/**
 * 获取考试记录列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getExamRecords = async (req, res) => {
  try {
    const { 
      examSubject, examinee, confirmStatus, 
      startDate, endDate, positionBelongId, positionId, levelId, categoryId,
      pageNum = 1, pageSize = 10 
    } = req.query;
    
    // 构建查询条件
    let where = {
      delFlag: 0
    };
    
    // 添加企业ID过滤
    const enterpriseId = DEFAULT_ENTERPRISE_ID;
    where.enterpriseId = enterpriseId;
    
    // 根据参数构建过滤条件
    if (examSubject) {
      where.examSubject = {
        [Op.like]: `%${examSubject}%`
      };
    }
    
    if (examinee) {
      where.examinee = {
        [Op.like]: `%${examinee}%`
      };
    }
    
    if (confirmStatus) {
      where.confirmStatus = confirmStatus;
    }
    
    if (startDate && endDate) {
      where.examTime = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }
    
    if (positionBelongId) {
      where.positionBelongId = positionBelongId;
    }
    
    if (positionId) {
      where.positionId = positionId;
    }
    
    if (levelId) {
      where.levelId = levelId;
    }
    
    if (categoryId) {
      where.categoryId = categoryId;
    }
    
    // 查询总数
    const total = await ExamRecord.count({ where });
    
    // 分页查询数据
    const offset = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);
    
    const examRecords = await ExamRecord.findAll({
      where,
      attributes: { 
        exclude: ['delFlag'],
        include: ['advantage', 'improve'] // 确保包含这两个字段
      },
      include: [
        {
          model: ExamReviewApplication,
          as: 'reviewApplication',
          attributes: ['id', 'status', 'scoreConfirmStatus', 'examConfigInfo'],
          required: false
        },
        {
          model: DictionaryData,
          as: 'positionBelongDict',
          attributes: ['id', 'dictLabel'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: DictionaryData,
          as: 'categoryDict',
          attributes: ['id', 'dictLabel'],
          required: false
        }
      ],
      order: [['examTime', 'DESC']],
      offset,
      limit
    });
    
    // 返回结果
    return res.json({
      code: 200,
      message: '获取考试记录列表成功',
      data: {
        total,
        records: examRecords,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      }
    });
    
  } catch (error) {
    logger.error('获取考试记录列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取考试记录列表失败',
      error: error.message
    });
  }
};

/**
 * 获取考试记录详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getExamRecordDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 构建查询条件
    const where = {
      id,
      delFlag: 0
    };
    
    // 添加企业ID过滤
    const enterpriseId = DEFAULT_ENTERPRISE_ID;
    where.enterpriseId = enterpriseId;
    
    // 查询数据
    const examRecord = await ExamRecord.findOne({
      where,
      attributes: {
        include: ['advantage', 'improve'] // 确保包含这两个字段
      },
      include: [
        {
          model: ExamReviewApplication,
          as: 'reviewApplication',
          attributes: ['id', 'status', 'scoreConfirmStatus', 'reviewInfo'],
          required: false
        },
        {
          model: DictionaryData,
          as: 'positionBelongDict',
          attributes: ['id', 'dictLabel'],
          required: false
        },
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: DictionaryData,
          as: 'categoryDict',
          attributes: ['id', 'dictLabel'],
          required: false
        },
        {
          model: User,
          as: 'examineeUser',
          attributes: ['id', 'username', 'realName', 'avatar'],
          required: false
        }
      ]
    });
    
    if (!examRecord) {
      return res.status(404).json({
        code: 404,
        message: '考试记录不存在'
      });
    }
    
    // 返回结果
    return res.json({
      code: 200,
      message: '获取考试记录详情成功',
      data: examRecord
    });
    
  } catch (error) {
    logger.error('获取考试记录详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取考试记录详情失败',
      error: error.message
    });
  }
};

/**
 * 创建考试记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createExamRecord = async (req, res) => {
  try {
    const { 
      examSubject, examTime, positionBelongId, positionBelong,
      positionId, positionName, levelId, positionLevel,
      categoryId, category, examineeId, examinee,
      score, usedDuration, confirmStatus, reviewApplicationId,
      examContent, passScore, examDuration, endTime, examStatus
    } = req.body;
    
    // 创建数据
    const examRecord = await ExamRecord.create({
      examSubject,
      examTime,
      positionBelongId,
      positionBelong,
      positionId,
      positionName,
      levelId,
      positionLevel,
      categoryId,
      category,
      examineeId,
      examinee,
      score,
      usedDuration,
      confirmStatus: confirmStatus || '1',
      reviewApplicationId,
      examContent,
      passScore: passScore ,
      examDuration,
      endTime,
      examStatus: examStatus || 'pending',
      enterpriseId: DEFAULT_ENTERPRISE_ID,
      createdBy: req.user.id,
      updatedBy: req.user.id
    });
    
    // 返回结果
    return res.status(201).json({
      code: 200,
      message: '创建考试记录成功',
      data: examRecord
    });
    
  } catch (error) {
    logger.error('创建考试记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建考试记录失败',
      error: error.message
    });
  }
};

/**
 * 更新考试记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateExamRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      examSubject, examTime, positionBelongId, positionBelong,
      positionId, positionName, levelId, positionLevel,
      categoryId, category, examineeId, examinee,
      score, usedDuration, confirmStatus, confirmPersonId,
      confirmPerson, confirmTime, examContent,
      passScore, examDuration, endTime, examStatus
    } = req.body;
    
    // 构建查询条件
    const where = {
      id,
      delFlag: 0
    };
    
    // 添加企业ID过滤
    const enterpriseId = DEFAULT_ENTERPRISE_ID;
    where.enterpriseId = enterpriseId;
    
    // 查询数据
    const examRecord = await ExamRecord.findOne({ where });
    
    if (!examRecord) {
      return res.status(404).json({
        code: 404,
        message: '考试记录不存在'
      });
    }
    
    // 更新数据
    const updateData = {
      examSubject,
      examTime,
      positionBelongId,
      positionBelong,
      positionId,
      positionName,
      levelId,
      positionLevel,
      categoryId,
      category,
      examineeId,
      examinee,
      score,
      usedDuration,
      confirmStatus,
      confirmPersonId,
      confirmPerson,
      confirmTime,
      examContent,
      passScore,
      examDuration,
      endTime,
      examStatus,
      updatedBy: req.user.id
    };
    
    // 过滤掉undefined值
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });
    
    // 更新数据
    await examRecord.update(updateData);
    
    // 返回结果
    return res.json({
      code: 200,
      message: '更新考试记录成功',
      data: examRecord
    });
    
  } catch (error) {
    logger.error('更新考试记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新考试记录失败',
      error: error.message
    });
  }
};

/**
 * 更新考试记录确认状态
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateConfirmStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { confirmStatus } = req.body;
    
    if (!confirmStatus) {
      return res.status(400).json({
        code: 400,
        message: '确认状态不能为空'
      });
    }
    
    // 构建查询条件
    const where = {
      id,
      delFlag: 0
    };
    
    // 添加企业ID过滤
    const enterpriseId = DEFAULT_ENTERPRISE_ID;
    where.enterpriseId = enterpriseId;
    
    // 查询数据
    const examRecord = await ExamRecord.findOne({ where });
    
    if (!examRecord) {
      return res.status(404).json({
        code: 404,
        message: '考试记录不存在'
      });
    }
    
    // 更新数据
    await examRecord.update({
      confirmStatus,
      confirmPersonId: req.user.id,
      confirmPerson: req.user.realName || req.user.username,
      confirmTime: new Date(),
      updatedBy: req.user.id
    });
    
    // 返回结果
    return res.json({
      code: 200,
      message: '更新考试记录确认状态成功',
      data: { confirmStatus }
    });
    
  } catch (error) {
    logger.error('更新考试记录确认状态失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新考试记录确认状态失败',
      error: error.message
    });
  }
};

/**
 * 删除考试记录(逻辑删除)
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteExamRecord = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 构建查询条件
    const where = {
      id,
      delFlag: 0
    };
    
    // 添加企业ID过滤
    const enterpriseId = DEFAULT_ENTERPRISE_ID;
    where.enterpriseId = enterpriseId;
    
    // 查询数据
    const examRecord = await ExamRecord.findOne({ where });
    
    if (!examRecord) {
      return res.status(404).json({
        code: 404,
        message: '考试记录不存在'
      });
    }
    
    // 更新数据(逻辑删除)
    await examRecord.update({
      delFlag: 1,
      updatedBy: req.user.id
    });
    
    // 返回结果
    return res.json({
      code: 200,
      message: '删除考试记录成功'
    });
    
  } catch (error) {
    logger.error('删除考试记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除考试记录失败',
      error: error.message
    });
  }
};

/**
 * 从小程序创建考试记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createExamRecordFromMiniapp = async (req, res) => {
  try {
    const { knowledgeBaseId } = req.body;
    const openId = req.headers.openid; // 从请求头获取openId
    
    if (!openId || !knowledgeBaseId) {
      return res.status(400).json({
        code: 400,
        message: '参数不完整'
      });
    }
    
    // 1. 根据openId查找用户
    const user = await User.findOne({
      where: { 
        openId, 
        status: true,
        enterpriseId: process.env.DEFAULT_ENTERPRISE_ID
      }
    });
    
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }
    
    const enterpriseId = DEFAULT_ENTERPRISE_ID;
    
    // 2. 查找员工信息
    const employee = await Employee.findOne({
      where: { 
        openId,
        status: '1', // 在职状态
        enterpriseId
      },
      include: [
        {
          model: PositionName,
          as: 'positionName',
          attributes: ['id', 'name']
        },
        {
          model: Level,
          as: 'level',
          attributes: ['id', 'name']
        }
      ]
    });
    
    if (!employee) {
      return res.status(404).json({
        code: 404,
        message: '员工信息不存在'
      });
    }
    
    // 3. 查询知识库数据
    const knowledgeBase = await KnowledgeBase.findOne({
      where: { 
        id: knowledgeBaseId,
        deleted: false,
        enterpriseId: String(enterpriseId)
      }
    });
    
    if (!knowledgeBase) {
      return res.status(404).json({
        code: 404,
        message: '知识库数据不存在'
      });
    }
    
    // 4. 检查考试审核申请状态
    const reviewApplication = await ExamReviewApplication.findOne({
      where: {
        enterpriseId,
        delFlag: 0,
        kbId: knowledgeBaseId,
        createdBy: user.id,
      }
    });
    
    // 如果没有申请记录，返回错误
    if (!reviewApplication) {
      return res.status(403).json({
        code: 403,
        message: '请先申请考试资格'
      });
    }
    
    // 如果申请状态不是2(已通过)，返回错误
    if (reviewApplication.status !== '2') {
      return res.status(403).json({
        code: 403,
        message: '考试资格尚未审核，请等待或联系管理人员'
      });
    }
    
    // 5. 创建考试记录
    const examRecord = await ExamRecord.create({
      examSubject: knowledgeBase.name, // 证书名称作为考试科目
      examTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 当前时间作为考试时间
      positionBelongId: employee.positionTypeId , // 非空默认值
      positionBelong: employee.positionName?.name || '未知',
      positionId: employee.positionId || 0, // 非空默认值
      positionName: employee.positionName?.name || '未知', // 补充缺少的positionName字段
      levelId: employee.levelId ? employee.levelId : 0, // 非空默认值
      positionLevel: employee.level?.name || '未知',
      categoryId: knowledgeBase.fileCategory || 0,
      category: knowledgeBase.fileCategory || '未分类',
      examineeId: user.id,
      examinee: user.realName || user.nickname || user.username,
      score: 0, // 非空默认值
      usedDuration: 0, // 非空默认值
      confirmStatus: '1', // 补充缺少的confirmStatus字段
      reviewApplicationId: reviewApplication.id, // 关联审核申请ID
      examContent: [], // 题目列表为空数组
      enterpriseId: enterpriseId,
      kbId: knowledgeBaseId, // 关联知识库ID
      createdBy: user.id,
      updatedBy: user.id,
      delFlag: 0
    });
    
    // 6. 返回结果
    return res.status(201).json({
      code: 200,
      message: '创建考试记录成功',
      data: examRecord
    });
    
  } catch (error) {
    logger.error('创建考试记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建考试记录失败',
      error: error.message
    });
  }
}; 