#!/bin/bash

# 无感更新脚本 - 一键式零停机部署
# 适用于cankao-admin项目

# 修复可能的Windows行尾符问题
fix_line_endings() {
    # 检测文件是否包含Windows行尾符(\r\n)
    if grep -q $'\r' "$1" 2>/dev/null; then
        echo "检测到Windows行尾符，正在修复..."
        # 创建临时文件
        TMP_FILE=$(mktemp)
        # 转换行尾符
        tr -d '\r' < "$1" > "$TMP_FILE"
        # 替换原文件
        mv "$TMP_FILE" "$1"
        echo "行尾符修复完成"
    fi
}

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 配置项
APP_DIR="/home/<USER>/enterprise/demo_enterprise"
ENV_FILE="${APP_DIR}/.env"
BACKUP_DIR="${APP_DIR}/backups/$(date +%Y%m%d_%H%M%S)"
HEALTH_CHECK_TIMEOUT=30  # 健康检查超时时间(秒)

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -a, --api VERSION    设置后端API版本"
    echo "  -w, --web VERSION    设置前端Web版本"
    echo "  -b, --both VERSION   同时设置API和Web版本"
    echo "  -h, --help           显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --api v1.2.3      # 更新API版本到v1.2.3并部署"
    echo "  $0 --web v1.2.3      # 更新Web版本到v1.2.3并部署"
    echo "  $0 --both v1.2.3     # 同时更新API和Web版本到v1.2.3并部署"
    echo "  $0                   # 使用当前版本进行部署"
}

# 备份配置
backup_config() {
    log_info "创建配置文件备份..."
    mkdir -p "$BACKUP_DIR"
    cp "${APP_DIR}/.env" "${BACKUP_DIR}/.env" 2>/dev/null || true
    cp "${APP_DIR}/docker-compose.yaml" "${BACKUP_DIR}/docker-compose.yaml"
    log_info "备份已保存到 $BACKUP_DIR"
}

# 更新版本号
update_version() {
    local component=$1
    local version=$2
    local var_name=""
    
    case "$component" in
        "api")
            var_name="ADMIN_DOCKER_VERSION"
            ;;
        "web")
            var_name="ADMIN_WEB_DOCKER_VERSION"
            ;;
        *)
            log_error "未知组件: $component"
            exit 1
            ;;
    esac
    
    # 检查.env文件是否存在
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境变量文件不存在: $ENV_FILE"
        exit 1
    fi
    
    # 修复可能的Windows行尾符问题
    fix_line_endings "$ENV_FILE"
    
    # 检查变量是否存在
    if grep -q "^${var_name}=" "$ENV_FILE"; then
        # 更新已存在的变量
        sed -i "s/^${var_name}=.*/${var_name}=${version}/" "$ENV_FILE"
    else
        # 添加新变量
        echo "${var_name}=${version}" >> "$ENV_FILE"
    fi
    
    log_info "${component} 版本已更新为 ${version}"
}

# 健康检查函数
check_health() {
    local service=$1
    local url=$2
    local timeout=$3
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    
    log_info "正在检查 $service 健康状态..."
    
    while [ $(date +%s) -lt $end_time ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            log_info "$service 健康检查通过!"
            return 0
        fi
        log_warn "$service 尚未就绪，等待 3 秒后重试..."
        sleep 3
    done
    
    log_error "$service 健康检查失败，超过 $timeout 秒!"
    return 1
}

# 获取容器ID
get_container_id() {
    local service=$1
    echo $(sudo docker compose ps -q $service)
}

# 等待容器停止
wait_for_container_stop() {
    local container_id=$1
    local service_name=$2
    local timeout=30
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    
    while [ $(date +%s) -lt $end_time ]; do
        if ! sudo docker ps -q | grep -q $container_id; then
            log_info "$service_name 容器已停止"
            return 0
        fi
        log_warn "$service_name 容器正在停止中，等待 1 秒..."
        sleep 1
    done
    
    log_error "$service_name 容器停止超时!"
    return 1
}

# 简化的更新服务函数 - 直接替换旧容器
update_service() {
    local service=$1
    local health_check_url=$2
    
    log_info "更新服务 $service..."
    
    # 停止服务
    sudo docker compose stop $service
    
    # 启动服务
    sudo docker compose up -d $service
    
    # 健康检查
    if [ -n "$health_check_url" ]; then
        if ! check_health "$service" "$health_check_url" "$HEALTH_CHECK_TIMEOUT"; then
            log_error "$service 更新失败，正在回滚..."
            sudo docker compose stop $service
            sudo docker compose up -d --no-recreate $service
            return 1
        fi
    else
        # 如果没有健康检查URL，等待10秒确保服务启动
        log_info "等待 $service 启动 (10秒)..."
        sleep 10
    fi
    
    log_info "$service 更新成功!"
    return 0
}

# 主更新流程
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -a|--api)
                API_VERSION="$2"
                shift 2
                ;;
            -w|--web)
                WEB_VERSION="$2"
                shift 2
                ;;
            -b|--both)
                API_VERSION="$2"
                WEB_VERSION="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    cd "$APP_DIR" || { log_error "无法进入应用目录: $APP_DIR"; exit 1; }
    
    # 获取环境变量
    if [ -f "$ENV_FILE" ]; then
        # 修复可能的Windows行尾符问题
        fix_line_endings "$ENV_FILE"
        source "$ENV_FILE"
    else
        log_warn "环境变量文件不存在，将使用系统环境变量"
    fi
    
    # 设置健康检查URL - 修正为正确的端点
    HEALTH_CHECK_API_URL="http://localhost:${ENT_ADMIN_API_PORT:-31494}/health"
    HEALTH_CHECK_WEB_URL="http://localhost:${ENT_ADMIN_WEB_PORT:-31495}"
    
    # 步骤1: 更新版本号(如果指定了)
    if [ -n "$API_VERSION" ]; then
        update_version "api" "$API_VERSION"
    fi
    
    if [ -n "$WEB_VERSION" ]; then
        update_version "web" "$WEB_VERSION"
    fi
    
    # 步骤2: 备份当前配置
    backup_config
    
    # 步骤3: 拉取最新镜像
    log_info "预拉取最新镜像..."
    sudo docker compose pull || { log_error "拉取镜像失败"; exit 1; }
    
    # 步骤4: 更新前端服务
    update_service "admin-web" "$HEALTH_CHECK_WEB_URL" || exit 1
    
    # 步骤5: 更新后端服务
    update_service "admin-api" "$HEALTH_CHECK_API_URL" || exit 1
    
    # 步骤6: 清理旧镜像
    log_info "清理未使用的镜像..."
    sudo docker image prune -af --filter "until=24h" || log_warn "清理镜像时出现警告，但更新已完成"
    
    log_info "✅ 无感更新完成! 所有服务已成功更新且正常运行。"
    
    # 显示当前版本
    log_header "当前版本信息:"
    echo -e "后端 API: $(sudo docker compose images admin-api --format '{{.Tag}}')"
    echo -e "前端 Web: $(sudo docker compose images admin-web --format '{{.Tag}}')"
}

# 执行主函数
main "$@" 