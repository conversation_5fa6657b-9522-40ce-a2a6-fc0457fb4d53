<template>
  <div class="page-container">
    <page-header
    >
      <template #search>
        <!-- 搜索和操作区域 -->
        <search-form-card
          :model-value="queryParams"
          :items="searchFormItems"
          @search="handleSearch"
          @reset="handleReset"
        />
      </template>
    <template #actions>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增用户
        </a-button>
      </template>
    </page-header>



    <!-- 表格区域 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      @edit="handleEdit"
      @delete="handleDelete"  
      :delete-title="deleteTitle"
      :show-default-action="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'platform'">
          <a-tag :color="record.platform === 'admin' ? 'blue' : 'green'">
            {{ record.platform === 'admin' ? '系统用户' : '小程序用户' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'success' : 'error'">
            {{ record.status ? '正常' : '禁用' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'roles'">
          <div>
            <a-tag 
              v-for="(role, index) in record.roles" 
              :key="index" 
              color="blue"
            >
              {{ role.roleName }}
            </a-tag>
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <div class="action-buttons">
            <template v-if="record.platform === 'admin'">
              <a-button 
                class="custom-button"
                type="link"
                size="small"
                @click="handleEdit(record)"
              >
                <template #icon><edit-outlined /></template>
                编辑
              </a-button>
              <a-button 
                class="custom-button"
                type="link"
                size="small"
                @click="handleResetPassword(record)"
              >
                <template #icon><key-outlined /></template>
                重置密码
              </a-button>
              <a-popconfirm
                :title="deleteTitle"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button 
                  class="custom-button"
                  type="link"
                  size="small"
                >
                  <template #icon><delete-outlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </template>
            <template v-else>
              
            </template>
          </div>
        </template>
      </template>
    </base-table>

    <!-- 用户表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="formData.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item
          v-if="!formData.id"
          label="密码"
          name="password"
          :rules="[{ required: !formData.id, message: '请输入密码' }]"
        >
          <a-input-password 
            v-model:value="formData.password" 
            placeholder="请输入密码" 
          />
        </a-form-item>
        <a-form-item label="昵称" name="nickname">
          <a-input v-model:value="formData.nickname" placeholder="请输入昵称" />
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item label="手机号" name="phone">
          <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
        </a-form-item>
        <a-form-item label="真实姓名" name="realName">
          <a-input v-model:value="formData.realName" placeholder="请输入真实姓名" />
        </a-form-item>
<!--        <a-form-item label="实名手机号" name="realPhone">-->
<!--          <a-input v-model:value="formData.realPhone" placeholder="请输入实名手机号" />-->
<!--        </a-form-item>-->
<!--        <a-form-item label="身份证号" name="idNumber">-->
<!--          <a-input v-model:value="formData.idNumber" placeholder="请输入身份证号" />-->
<!--        </a-form-item>-->
        <a-form-item label="角色" name="roleIds">
          <a-select
            v-model:value="formData.roleIds"
            mode="multiple"
            placeholder="请选择用户角色"
            style="width: 100%"
          >
            <a-select-option v-for="role in roleOptions" :key="role.id" :value="role.id">
              {{ role.roleName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formData.status" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal, Button, Space, Typography } from 'ant-design-vue'
import { EditOutlined, DeleteOutlined, KeyOutlined, CopyOutlined } from '@ant-design/icons-vue'
import { getUserList, createUser, updateUser, deleteUser, getRoleList } from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'
import { SearchFormCard } from '@/components/SearchForm'
import { useTablePagination } from '@/utils/common'

// 引入用户store
const userStore = useUserStore()

// 删除确认框标题
const deleteTitle = '确定要删除该用户吗？'

// 查询参数
const searchForm = reactive({
  username: '',
  nickname: '',
  platform: undefined,
  status: undefined
})

// 搜索表单配置
const searchFormItems = [
  {
    label: '用户名',
    field: 'username',
    type: 'input',
    placeholder: '请输入用户名'
  },
  {
    label: '昵称',
    field: 'nickname',
    type: 'input',
    placeholder: '请输入昵称'
  },
  {
    label: '用户类型',
    field: 'platform',
    type: 'select',
    placeholder: '请选择用户类型',
    width: '120px',
    options: [
      { label: '系统用户', value: 'admin' },
      { label: '小程序用户', value: 'miniapp' }
    ]
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    placeholder: '请选择状态',
    width: '120px',
    options: [
      { label: '正常', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
]

// 表格加载状态
const loading = ref(false)

// 表格列定义
const columns = [
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: '昵称',
    dataIndex: 'nickname',
    key: 'nickname'
  },
  {
    title: '用户类型',
    dataIndex: 'platform',
    key: 'platform'
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone'
  },
  {
    title: '真实姓名',
    dataIndex: 'realName',
    key: 'realName'
  },
  // {
  //   title: '实名手机号',
  //   dataIndex: 'realPhone',
  //   key: 'realPhone'
  // },
  // {
  //   title: '身份证号',
  //   dataIndex: 'idNumber',
  //   key: 'idNumber',
  //   customRender: ({ text }) => {
  //     // 隐藏部分身份证号，只显示前4位和后4位
  //     if (text && text.length > 8) {
  //       return text.substring(0, 4) + '********' + text.substring(text.length - 4);
  //     }
  //     return text;
  //   }
  // },
  {
    title: '角色',
    key: 'roles',
    dataIndex: 'roles'
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'action',
  }
]

// 表格数据
const tableData = ref([])


// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    
    // 使用延迟来减少网络请求的阻塞感
    setTimeout(async () => {
      try {
        const response = await getUserList(params)
        // 处理可能的数据格式问题
        const userData = response.list || response.rows || response.data || []
        
        // 确保每个用户对象的roles字段是数组
        tableData.value = userData.map(user => {
          // 确保roles字段存在并且是数组
          const userWithRoles = { ...user }
          if (!userWithRoles.roles) {
            userWithRoles.roles = user.Roles || []
          } else if (!Array.isArray(userWithRoles.roles)) {
            userWithRoles.roles = []
          }
          return userWithRoles
        })
        
        // 更新分页信息
        updatePagination({
          total: response.total || 0,
          pageNum: response.pageNum,
          pageSize: response.pageSize
        })
      } catch (error) {
        console.error('获取用户列表失败', error)
        message.error('获取用户列表失败')
        // 发生错误时重置分页
        updatePagination({ total: 0, pageNum: 1 })
      } finally {
        loading.value = false
      }
    }, 50)
  } catch (error) {
    console.error('参数处理失败', error)
    message.error('获取用户列表失败')
    loading.value = false
    // 发生错误时重置分页
    updatePagination({ total: 0, pageNum: 1 })
  }
}

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchUserList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
})

// 弹窗控制
const modalVisible = ref(false)
const modalTitle = ref('新增用户')
const formRef = ref()

// 角色选项
const roleOptions = ref([])

// 表单数据
const formData = reactive({
  id: undefined,
  username: '',
  password: '',
  nickname: '',
  email: '',
  phone: '',
  realName: '',
  realPhone: '',
  idNumber: '',
  roleIds: [],
  status: true
})

// 表单校验规则
const rules = {
  username: [
    { required: true, message: '请输入用户名' }
  ],
  nickname: [{ required: true, message: '请输入昵称' }],
  phone: [
    { required: true, message: '请输入手机号' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
  ],
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式' }
  ]
}

// 方法定义
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values)
  }
  // 重置到第一页
  resetPagination()
  fetchUserList()
}

const handleReset = () => {
  searchForm.username = ''
  searchForm.nickname = ''
  searchForm.platform = undefined
  searchForm.status = undefined
  // 重置到第一页
  resetPagination()
  fetchUserList()
}

const handleAdd = () => {
  modalTitle.value = '新增用户'
  formData.id = undefined
  formData.username = ''
  formData.password = ''
  formData.nickname = ''
  formData.email = ''
  formData.phone = ''
  formData.realName = ''
  formData.realPhone = ''
  formData.idNumber = ''
  formData.status = true
  formData.roleIds = [] // 清空角色选择

  // 打开弹窗前先获取最新角色列表
  fetchRoleOptions().then(() => {
    modalVisible.value = true
  })
}

const handleEdit = (record) => {
  modalTitle.value = '编辑用户'
  Object.assign(formData, record)
  formData.password = '' // 编辑时清空密码字段，用户可以选择性修改

  // 处理角色数据 - 修复回显问题
  if (record.roles && Array.isArray(record.roles)) {
    formData.roleIds = record.roles.map(role => role.id)
  } else if (record.Roles && Array.isArray(record.Roles)) {
    formData.roleIds = record.Roles.map(role => role.id)
  } else {
    formData.roleIds = []
  }

  // 打开弹窗前先获取最新角色列表
  fetchRoleOptions().then(() => {
    modalVisible.value = true
  })
}

const handleDelete = async (record) => {
  try {
    loading.value = true
    await deleteUser(record.id)
    message.success('删除成功')
    fetchUserList()
  } catch (error) {
    message.error('删除失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleModalOk = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    loading.value = true

    // 确保表单数据包含正确的字段格式
    const submitData = {
      ...formData,
      // 确保username字段被包含
      username: formData.username,
      // 确保roleIds是数组
      roleIds: Array.isArray(formData.roleIds) ? formData.roleIds : []
    }

    // 如果是编辑模式且密码为空，则删除password字段，不更新密码
    if (submitData.id && !submitData.password) {
      delete submitData.password
    }

    if (submitData.id) {
      // 编辑用户
      await updateUser(submitData)
      message.success('更新成功')
    } else {
      // 新增用户
      await createUser(submitData)
      message.success('创建成功')
    }

    modalVisible.value = false
    fetchUserList()
  } catch (error) {
    // 如果是表单验证错误，不显示错误提示，让表单自己展示验证信息
    if (!error?.errorFields) {
      // 只有在非表单验证错误时才显示错误提示
      const errorMsg = error?.response?.data?.message || error?.message || '请求失败，请稍后重试'
      message.error(errorMsg)
    }
  } finally {
    loading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}


// 获取角色选项列表
const fetchRoleOptions = async () => {
  try {
    // 直接调用API而不是通过store
    const response = await getRoleList();

    if (!response) {
      console.error('角色列表API响应为空');
      return;
    }

    // 从截图可以看到，返回的数据格式是 {list: Array(4), total: 4}
    if (response.list && Array.isArray(response.list)) {
      roleOptions.value = response.list;
    } else if (response.data && response.data.list && Array.isArray(response.data.list)) {
      roleOptions.value = response.data.list;
    } else if (response.data && Array.isArray(response.data)) {
      roleOptions.value = response.data;
    } else if (Array.isArray(response)) {
      roleOptions.value = response;
    } else {
      console.error('角色列表数据格式不符合预期:', response);
      roleOptions.value = [];
    }
  } catch (error) {
    console.error('获取角色选项失败:', error);
    message.error('获取角色选项失败');
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchUserList()
  fetchRoleOptions() // 获取角色选项
})

// 生成随机密码
const generateRandomPassword = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// 重置密码
const handleResetPassword = (record) => {
  // 先弹出确认框
  Modal.confirm({
    title: '确认重置密码',
    content: `确定要重置用户 "${record.username}" 的密码吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true;
        // 生成随机密码
        const newPassword = generateRandomPassword(10);
        
        // 调用更新用户API，只更新密码字段
        await updateUser({
          id: record.id,
          password: newPassword
        });
        
        // 显示成功消息
        message.success('密码重置成功');
        
        // 使用Modal显示新密码，使用h函数创建VNode而不是字符串
        Modal.success({
          title: '密码重置成功',
          content: h('div', [
            h('p', [
              '用户 ',
              h('strong', record.username),
              ' 的新密码是：'
            ]),
            h('div', {
              style: {
                display: 'flex',
                alignItems: 'center',
                background: '#f5f5f5',
                padding: '8px',
                borderRadius: '4px',
                marginBottom: '8px'
              }
            }, [
              h('span', {
                style: {
                  fontFamily: 'monospace',
                  marginRight: '10px',
                  flex: 1,
                  userSelect: 'all'
                }
              }, newPassword),
              h(Button, {
                type: 'primary',
                size: 'small',
                onClick: () => {
                  // 复制密码到剪贴板
                  navigator.clipboard.writeText(newPassword)
                    .then(() => message.success('密码已复制到剪贴板'))
                    .catch(() => message.error('复制失败，请手动复制'));
                }
              }, [
                h(CopyOutlined),
                '复制密码'
              ])
            ]),
            h('p', '请妥善保管此密码，此窗口关闭后将无法再次查看。')
          ]),
          // 自定义确认按钮文本和操作
          okText: '复制并关闭',
          onOk: () => {
            // 复制密码到剪贴板
            navigator.clipboard.writeText(newPassword)
              .then(() => message.success('密码已复制到剪贴板'))
              .catch(() => message.error('复制失败，请手动复制'));
          }
        });
      } catch (error) {
        message.error('密码重置失败: ' + error.message);
      } finally {
        loading.value = false;
      }
    }
  });
}

</script>

<style scoped>
.custom-button {
  color: #a18cd1;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.no-actions {
  color: #999;
  font-size: 12px;
}
</style>
