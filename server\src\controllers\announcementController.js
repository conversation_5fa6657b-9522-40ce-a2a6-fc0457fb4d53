const Announcement = require('../models/Announcement');
const { addEnterpriseFilter, addEnterpriseId } = require('../utils/enterpriseFilter');
const { Op } = require('sequelize');

// 创建公告
exports.createAnnouncement = async (req, res) => {
  try {
    const announcementData = req.body;
    
    // 创建公告
    const announcement = await Announcement.create(
      addEnterpriseId({
        title: announcementData.title,
        content: announcementData.content,
        status: announcementData.status ? 1 : 0,
        sort: announcementData.sort || 0
      })
    );

    return res.status(200).json({
      code: 200,
      message: '创建公告成功',
      data: announcement
    });
  } catch (error) {
    console.error('创建公告失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 更新公告
exports.updateAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 更新公告
    const [updated] = await Announcement.update(
      updateData,
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!updated) {
      return res.status(404).json({
        code: 404,
        message: '公告不存在或无权修改'
      });
    }

    // 获取更新后的公告
    const announcement = await Announcement.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    return res.status(200).json({
      code: 200,
      message: '更新公告成功',
      data: announcement
    });
  } catch (error) {
    console.error('更新公告失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 删除公告
exports.deleteAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;

    const deleted = await Announcement.destroy(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!deleted) {
      return res.status(404).json({
        code: 404,
        message: '公告不存在或无权删除'
      });
    }

    return res.status(200).json({
      code: 200,
      message: '删除公告成功'
    });
  } catch (error) {
    console.error('删除公告失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 获取公告详情
exports.getAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;

    const announcement = await Announcement.findOne(
      addEnterpriseFilter({
        where: { id }
      })
    );

    if (!announcement) {
      return res.status(404).json({
        code: 404,
        message: '公告不存在'
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取公告成功',
      data: announcement
    });
  } catch (error) {
    console.error('获取公告失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 获取公告列表
exports.getAnnouncementList = async (req, res) => {
  try {
    const { 
      pageNum = 1, 
      pageSize = 10, 
      status, 
      title
    } = req.query;

    // 构建查询条件
    const where = {};
    
    // 处理status参数
    if (status !== undefined && status !== '') {
      // 支持多种格式的status值
      if (status === '0' || status === 0 || status === 'false') {
        where.status = 0;
      } else if (status === '1' || status === 1 || status === 'true') {
        where.status = 1;
      }
      // 如果传入的值不是有效的状态值，则不添加status过滤条件
    }
    
    // 处理其他查询条件
    if (title) where.title = { [Op.like]: `%${title}%` };

    // 添加企业过滤
    const filter = addEnterpriseFilter({
      where,
      order: [['sort', 'ASC'], ['createTime', 'DESC']],
      offset: (pageNum - 1) * pageSize,
      limit: parseInt(pageSize)
    });

    const { count, rows } = await Announcement.findAndCountAll(filter);

    return res.status(200).json({
      code: 200,
      message: '获取公告列表成功',
      data: {
        total: count,
        list: rows,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取公告列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}; 