const express = require('express');
const router = express.Router();
const restaurantConfigController = require('../controllers/restaurantConfigController');
const { requireLogin } = require('../middleware/auth');
const { createUpload } = require('../utils/fileUpload');

// 创建专门的restaurant上传实例
const restaurantUpload = createUpload('restaurant');

// 所有路由都需要进行身份验证
router.use(requireLogin);

// 获取餐烤师配置列表
router.get('/restaurant-config', restaurantConfigController.getRestaurantConfigList);

// 获取餐烤师配置详情
router.get('/restaurant-config/:id', restaurantConfigController.getRestaurantConfigDetail);

// 创建餐烤师配置
router.post('/restaurant-config', restaurantConfigController.createRestaurantConfig);

// 更新餐烤师配置
router.put('/restaurant-config/:id', restaurantConfigController.updateRestaurantConfig);

// 删除餐烤师配置
router.delete('/restaurant-config/:id', restaurantConfigController.deleteRestaurantConfig);

// 上传餐烤师头像
router.post('/restaurant-config/upload', restaurantUpload.single('file'), restaurantConfigController.uploadAvatar);

// 获取岗位类型列表（用于下拉框选择）
router.get('/position-types', restaurantConfigController.getPositionTypeList);

module.exports = router; 