# 概述
- 这是 餐烤餐考 的后台管理系统的前端工程，位于项目根目录下的 web 文件夹
- 当前只有前端页面，没有任何后端交互逻辑，你可以在页面上放上适量的假数据(假数据请以json形式组织，统一存放在单独的文件夹中)
- 你是一位20年的资深全栈工程师，请根据下方任务安排中任务描述构思好功能需求和界面，然后设计 UI/UX。
    项目主色调：
  - 主色：#a18cd1 (紫色)
  - 辅助色：#fbc2eb (粉色)
  - 渐变色：linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%) (马卡龙渐变色)
  - 文本主色：#333333
  - 文本次要色：#666666
  - 背景色：#f8f9fa
  - 卡片背景色：#ffffff

# 任务安排
请按照以下的顺序开展你的编码工作，请注意：
- 最重要：编写前端页面最注重效果，样式一定不能丢！

- 当你完成每一步任务时，请并往changlog写入当前的工作总结, 同时请运行项目，让我进行阶段性测试

以下为具体的任务安排，总共分为以下几步：

- T10：餐考师：
  - 前厅餐考师：列表字段：岗位归属、岗位名称、岗位等级、员工名称、标题、聊天内容总结、创建时间、更新时间、消息数、详情
    - 查询条件：员工名称、创建时间、更新时间
    - 操作按钮：单条数据：详情      
    - 详情页表单字段： 展示员工和前厅餐考师的聊天对话详情
  - 后厨餐考师：列表字段：岗位归属、岗位名称、岗位等级、员工名称、标题、聊天内容总结、创建时间、更新时间、消息数、详情
    - 查询条件：员工名称、创建时间、更新时间
    - 操作按钮：单条数据：详情      
    - 详情页表单字段： 展示员工和后厨餐考师的聊天对话详情
- T11:意见反馈,描述：查看员工对话记录。
   查询条件：反馈类型、反馈内容、反馈时间
   列表字段：联系人、联系方式、反馈类型、反馈内容、图片、反馈时间
   操作按钮：顶部：无
   操作按钮：单条数据：详情
   详情页表单字段：展示员工反馈内容的详情

# 注意事项
-应用开发要求
--应用框架：html+vue+css
--最终呈现: web端
--1. 全部使用静态数据，要先看到效果
--2. UI样式要高级，要好看。
--3. 使用好看的组件库，比如ant-design-vue，vant，等
--4. 使用好看的图标库，比如iconfont，等
--5. 注重页面细节操作动效，增加用户体验
--6. 客户管理tab要按照上面功能说明来做
--7. 项目搭建不可缺少文件,如package.json，main.js，router.js，等





