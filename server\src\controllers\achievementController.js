const { Op } = require('sequelize');
const { addEnterpriseId, addEnterpriseFilter, createResponse } = require('../utils/responseHelper');
const AchievementTemplate = require('../models/AchievementTemplate');

/**
 * 获取成就模板列表req.user?.enterpriseId || 1
 */
const getTemplateList = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, name, category, isActive } = req.query;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID;

    console.log('分页参数:', { page, pageSize, total: '计算中' }); // 调试日志

    // 构建查询条件
    const whereCondition = { enterpriseId };

    if (name) {
      whereCondition.name = { [Op.like]: `%${name}%` };
    }
    if (category) {
      whereCondition.category = category;
    }
    if (isActive !== undefined) {
      whereCondition.isActive = isActive === 'true';
    }

    const pageNum = parseInt(page);
    const size = parseInt(pageSize);
    const offset = (pageNum - 1) * size;

    console.log('查询参数:', { pageNum, size, offset, whereCondition }); // 调试日志

    const { rows: templates, count: total } = await AchievementTemplate.findAndCountAll({
      where: whereCondition,
      order: [['createTime', 'DESC'], ['id', 'DESC']],
      offset: offset,
      limit: size
    });

    console.log('查询结果:', { total, listLength: templates.length }); // 调试日志

    res.json(createResponse(200, '获取成就模板列表成功', {
      list: templates,
      total,
      page: pageNum,
      pageSize: size
    }));
  } catch (error) {
    console.error('获取成就模板列表失败:', error);
    res.status(500).json(createResponse(500, '获取成就模板列表失败'));
  }
};

/**
 * 创建成就模板
 */
const createTemplate = async (req, res) => {
  try {
    const { name, description, icon, category, ruleType, triggerCondition } = req.body;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID;
    const createBy = req.user?.username || req.user?.name || 'system';

    // 验证必填字段
    if (!name || !ruleType || !triggerCondition) {
      return res.status(400).json(createResponse(400, '成就名称、规则类型和触发条件为必填项'));
    }

    // 解析并验证触发条件JSON
    let parsedCondition;
    try {
      parsedCondition = typeof triggerCondition === 'string' ? JSON.parse(triggerCondition) : triggerCondition;
    } catch (error) {
      return res.status(400).json(createResponse(400, '触发条件格式错误，必须是有效的JSON'));
    }

    const templateData = addEnterpriseId({
      name,
      description,
      icon,
      category: category || 'learning',
      ruleType,
      triggerCondition: JSON.stringify(parsedCondition),
      // 保持默认值用于向后兼容
      rewardPoints: 0,
      isActive: true,
      sort: 0,
      createBy
    }, enterpriseId);

    const template = await AchievementTemplate.create(templateData);

    res.json(createResponse(200, '创建成就模板成功', template));
  } catch (error) {
    console.error('创建成就模板失败:', error);
    res.status(500).json(createResponse(500, '创建成就模板失败'));
  }
};

/**
 * 更新成就模板
 */
const updateTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, icon, category, ruleType, triggerCondition } = req.body;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID;
    const updateBy = req.user?.username || req.user?.name || 'system';

    // 查找模板
    const template = await AchievementTemplate.findOne({
      where: { id, enterpriseId }
    });

    if (!template) {
      return res.status(404).json(createResponse(404, '成就模板不存在'));
    }

    // 解析并验证触发条件JSON（如果提供）
    let parsedCondition;
    if (triggerCondition) {
      try {
        parsedCondition = typeof triggerCondition === 'string' ? JSON.parse(triggerCondition) : triggerCondition;
      } catch (error) {
        return res.status(400).json(createResponse(400, '触发条件格式错误，必须是有效的JSON'));
      }
    }

    // 更新数据 - 只更新核心字段
    const updateData = {
      updateBy,
      updateTime: new Date()
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (icon !== undefined) updateData.icon = icon;
    if (category !== undefined) updateData.category = category;
    if (ruleType !== undefined) updateData.ruleType = ruleType;
    if (triggerCondition !== undefined) updateData.triggerCondition = JSON.stringify(parsedCondition);

    await template.update(updateData);

    res.json(createResponse(200, '更新成就模板成功', template));
  } catch (error) {
    console.error('更新成就模板失败:', error);
    res.status(500).json(createResponse(500, '更新成就模板失败'));
  }
};

/**
 * 删除成就模板
 */
const deleteTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID;

    // 查找模板
    const template = await AchievementTemplate.findOne({
      where: { id, enterpriseId }
    });

    if (!template) {
      return res.status(404).json(createResponse(404, '成就模板不存在'));
    }

    // 删除模板
    await template.destroy();

    res.json(createResponse(200, '删除成就模板成功'));
  } catch (error) {
    console.error('删除成就模板失败:', error);
    res.status(500).json(createResponse(500, '删除成就模板失败'));
  }
};

/**
 * 获取成就模板详情
 */
const getTemplateDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID;

    const template = await AchievementTemplate.findOne({
      where: { id, enterpriseId }
    });

    if (!template) {
      return res.status(404).json(createResponse(404, '成就模板不存在'));
    }

    // 解析触发条件JSON
    let triggerCondition = null;
    try {
      triggerCondition = JSON.parse(template.triggerCondition);
    } catch (error) {
      console.warn('解析触发条件JSON失败:', error);
    }

    res.json(createResponse(200, '获取成就模板详情成功', {
      ...template.toJSON(),
      triggerCondition
    }));
  } catch (error) {
    console.error('获取成就模板详情失败:', error);
    res.status(500).json(createResponse(500, '获取成就模板详情失败'));
  }
};

/**
 * 上传成就图标
 */
const uploadIcon = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json(createResponse(400, '请选择要上传的文件'));
    }

    const filePath = req.file.path.replace(/\\/g, '/'); // 标准化路径分隔符
    let relativePath = filePath.replace(/^.*\/uploads\//, '/uploads/'); // 获取相对路径
    
    // 确保路径以 / 开头
    if (!relativePath.startsWith('/')) {
      relativePath = '/' + relativePath;
    }

    res.json(createResponse(200, '图标上传成功', {
      filePath: relativePath,
      originalName: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    }));
  } catch (error) {
    console.error('上传成就图标失败:', error);
    res.status(500).json(createResponse(500, '上传成就图标失败'));
  }
};

/**
 * 初始化默认成就模板
 */
const initDefaultTemplates = async (req, res) => {
  try {
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID; // 从环境变量获取企业ID;
    const createBy = req.user?.username || req.user?.name || 'system';

    // 默认成就模板数据 - 简化版
    const defaultTemplates = [
      {
        name: '初入宝殿',
        description: '首次学习一门科目的进度达到100%',
        icon: '/icons/achievements/first-complete.svg',
        category: 'learning',
        ruleType: 'progress',
        triggerCondition: JSON.stringify({ type: 'subject_progress', target: 100, count: 1, operator: 'gte' })
      },
      {
        name: '知识探索',
        description: '完成5门科目的进度达到100%',
        icon: '/icons/achievements/knowledge-explorer.svg',
        category: 'learning',
        ruleType: 'progress',
        triggerCondition: JSON.stringify({ type: 'subject_progress', target: 100, count: 5, operator: 'gte' })
      },
      {
        name: '学霸模式',
        description: '连续学习超过10天',
        icon: '/icons/achievements/study-streak.svg',
        category: 'time',
        ruleType: 'streak',
        triggerCondition: JSON.stringify({ type: 'daily_study', target: 10, operator: 'gte' })
      },
      {
        name: '学无止境',
        description: '累计学习时长超过50小时',
        icon: '/icons/achievements/time-master.svg',
        category: 'time',
        ruleType: 'time',
        triggerCondition: JSON.stringify({ type: 'total_study_time', target: 50, unit: 'hours', operator: 'gte' })
      },
      {
        name: '碎片时间大师',
        description: '一天内0～12点、12～18点、18～24点都有练习记录',
        icon: '/icons/achievements/time-fragments.svg',
        category: 'time',
        ruleType: 'time',
        triggerCondition: JSON.stringify({ type: 'time_periods', periods: ['0-12', '12-18', '18-24'], operator: 'all' })
      }
    ];

    // 检查是否已有模板
    const existingCount = await AchievementTemplate.count({
      where: { enterpriseId }
    });

    if (existingCount > 0) {
      return res.status(400).json(createResponse(400, '已存在成就模板，无需重复初始化'));
    }

    // 批量创建默认模板
    const templates = defaultTemplates.map(template =>
      addEnterpriseId({
        ...template,
        createBy,
        // 添加默认值以保持数据库完整性
        rewardPoints: 0,
        isActive: true,
        sort: 0
      }, enterpriseId)
    );

    await AchievementTemplate.bulkCreate(templates);

    res.json(createResponse(200, '初始化默认成就模板成功', templates));
  } catch (error) {
    console.error('初始化默认成就模板失败:', error);
    res.status(500).json(createResponse(500, '初始化默认成就模板失败'));
  }
};

module.exports = {
  getTemplateList,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  getTemplateDetail,
  uploadIcon,
  initDefaultTemplates
};
