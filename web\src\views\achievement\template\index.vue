<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="searchFormItems"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <template #actions>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增
        </a-button>
      </template>
    </page-header>

    <!-- 表格区域 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      :action-config="actionConfig"
      :delete-title="deleteTitle"
      @edit="handleEdit"
      @delete="handleDelete"
    >
      <template #bodyCell="{ column, record }">
        <!-- 图标列 -->
        <template v-if="column.key === 'icon'">
          <div class="icon-preview" v-if="record.icon">
            <a-image
              :width="40"
              :height="40"
              :src="getFullImageUrl(record.icon)"
              :preview="{ src: getFullImageUrl(record.icon) }"
              style="border-radius: 4px; object-fit: cover;"
            />
          </div>
          <span v-else>无图标</span>
        </template>
      </template>
    </base-table>

    <!-- 成就模板表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="800px"
      :body-style="{ maxHeight: '70vh', overflowY: 'auto' }"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="成就名称" name="name">
          <a-select v-model:value="formData.name" placeholder="请选择成就名称" @change="handleNameChange">
            <a-select-option value="初入宝殿">初入宝殿</a-select-option>
            <a-select-option value="知识探索">知识探索</a-select-option>
            <a-select-option value="学霸模式">学霸模式</a-select-option>
            <a-select-option value="学无止境">学无止境</a-select-option>
            <a-select-option value="碎片时间大师">碎片时间大师</a-select-option>
            <a-select-option value="全能力者">全能力者</a-select-option>
            <a-select-option value="金牌毕业生">金牌毕业生</a-select-option>
            <a-select-option value="早起鸟">早起鸟</a-select-option>
            <a-select-option value="夜猫子">夜猫子</a-select-option>
            <a-select-option value="旗开得胜">旗开得胜</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 动态配置区域 -->
        <div v-if="formData.name">
          <!-- 初入宝殿 -->
          <template v-if="formData.name === '初入宝殿'">
            <a-form-item label="成就条件" name="progress">
              <div class="condition-config">
                <span>首次学习一门科目的进度达到</span>
                <a-input-number
                  v-model:value="formData.config.progress"
                  :min="1" :max="100" :precision="0"
                  style="width: 80px"
                  placeholder="50"
                />
                <span>%</span>
              </div>
            </a-form-item>
          </template>

          <!-- 知识探索 -->
          <template v-if="formData.name === '知识探索'">
            <a-form-item label="成就条件" name="subjectCount">
              <div class="condition-config">
                <span>完成</span>
                <a-input-number
                  v-model:value="formData.config.subjectCount"
                  :min="1" :precision="0"
                  style="width: 80px"
                  placeholder="5"
                />
                <span>门科目的进度达到</span>
                <a-input-number
                  v-model:value="formData.config.progress"
                  :min="1" :max="100" :precision="0"
                  style="width: 80px"
                  placeholder="80"
                />
                <span>%</span>
              </div>
            </a-form-item>
          </template>

          <!-- 学霸模式 -->
          <template v-if="formData.name === '学霸模式'">
            <a-form-item label="成就条件" name="days">
              <div class="condition-config">
                <span>连续学习超过</span>
                <a-input-number
                  v-model:value="formData.config.days"
                  :min="1" :precision="0"
                  style="width: 80px"
                  placeholder="7"
                />
                <span>天</span>
              </div>
            </a-form-item>
          </template>

          <!-- 学无止境 -->
          <template v-if="formData.name === '学无止境'">
            <a-form-item label="成就条件" name="hours">
              <div class="condition-config">
                <span>累计学习时长超过</span>
                <a-input-number
                  v-model:value="formData.config.hours"
                  :min="1" :precision="0"
                  style="width: 80px"
                  placeholder="50"
                />
                <span>小时</span>
              </div>
            </a-form-item>
          </template>

          <!-- 碎片时间大师 -->
          <template v-if="formData.name === '碎片时间大师'">
            <a-form-item label="成就条件" name="timeRanges">
              <div class="condition-config">
                <a-input-number
                  v-model:value="formData.config.startTime1"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="8"
                />
                <span>点～</span>
                <a-input-number
                  v-model:value="formData.config.endTime1"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="12"
                />
                <span>点、</span>
                <a-input-number
                  v-model:value="formData.config.startTime2"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="14"
                />
                <span>点～</span>
                <a-input-number
                  v-model:value="formData.config.endTime2"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="18"
                />
                <span>点、</span>
                <a-input-number
                  v-model:value="formData.config.startTime3"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="20"
                />
                <span>点～</span>
                <a-input-number
                  v-model:value="formData.config.endTime3"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="22"
                />
                <span>点三个时间范围都有练习记录</span>
              </div>
            </a-form-item>
          </template>

          <!-- 全能力者 -->
          <template v-if="formData.name === '全能力者'">
            <a-form-item label="成就条件">
              <div class="condition-config">
                <span>所有岗位都有练习记录</span>
              </div>
            </a-form-item>
          </template>

          <!-- 金牌毕业生 -->
          <template v-if="formData.name === '金牌毕业生'">
            <a-form-item label="成就条件" name="passRate">
              <div class="condition-config">
                <span>所有考试都是通过率</span>
                <a-input-number
                  v-model:value="formData.config.passRate"
                  :min="1" :max="100" :precision="0"
                  style="width: 80px"
                  placeholder="100"
                />
                <span>%</span>
              </div>
            </a-form-item>
          </template>

          <!-- 早起鸟 -->
          <template v-if="formData.name === '早起鸟'">
            <a-form-item label="成就条件" name="earlyBird">
              <div class="condition-config">
                <a-input-number
                  v-model:value="formData.config.startTime"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="6"
                />
                <span>点～</span>
                <a-input-number
                  v-model:value="formData.config.endTime"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="9"
                />
                <span>点之间有练习记录，连续</span>
                <a-input-number
                  v-model:value="formData.config.days"
                  :min="1" :precision="0"
                  style="width: 80px"
                  placeholder="7"
                />
                <span>天</span>
              </div>
            </a-form-item>
          </template>

          <!-- 夜猫子 -->
          <template v-if="formData.name === '夜猫子'">
            <a-form-item label="成就条件" name="nightOwl">
              <div class="condition-config">
                <a-input-number
                  v-model:value="formData.config.startTime"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="22"
                />
                <span>点～</span>
                <a-input-number
                  v-model:value="formData.config.endTime"
                  :min="0" :max="24" :precision="0"
                  style="width: 80px"
                  placeholder="2"
                />
                <span>点之间有练习记录，连续</span>
                <a-input-number
                  v-model:value="formData.config.days"
                  :min="1" :precision="0"
                  style="width: 80px"
                  placeholder="7"
                />
                <span>天</span>
              </div>
            </a-form-item>
          </template>

          <!-- 旗开得胜 -->
          <template v-if="formData.name === '旗开得胜'">
            <a-form-item label="成就条件">
              <div class="condition-config">
                <span>获得第一个考试满分</span>
              </div>
            </a-form-item>
          </template>
        </div>

        <a-form-item label="成就图标" name="icon">
          <a-upload
            name="file"
            list-type="picture-card"
            class="icon-uploader"
            :show-upload-list="false"
            :before-upload="beforeUpload"
            :custom-request="handleIconUpload"
          >
            <div v-if="formData.icon">
              <img :src="getFullImageUrl(formData.icon)" alt="icon" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;" />
            </div>
            <div v-else class="upload-placeholder">
              <plus-outlined />
              <div style="margin-top: 8px">上传图标</div>
            </div>
          </a-upload>
          <div class="upload-tip">
            <p>支持JPG、PNG、GIF格式，文件大小不超过2MB</p>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useTablePagination } from '@/utils/common'
import {
  getAchievementTemplates,
  createAchievementTemplate,
  updateAchievementTemplate,
  deleteAchievementTemplate,
  uploadAchievementIcon
} from '@/api/achievement'

// 图片基础路径
const VITE_APP_API_BASE_IMG_URL = import.meta.env.VITE_APP_API_BASE_IMG_URL || '';

// 成就类型映射
const achievementTypeMap = {
  'first_progress': '初入宝殿',
  'multiple_progress': '知识探索',
  'consecutive_days': '学霸模式',
  'study_time': '学无止境',
  'time_master': '碎片时间大师',
  'all_positions': '全能力者',
  'exam_pass_rate': '金牌毕业生',
  'early_bird': '早起鸟',
  'night_owl': '夜猫子',
  'first_perfect': '旗开得胜'
}

// 搜索表单
const searchForm = reactive({
  name: ''
})

// 搜索表单配置
const searchFormItems = [
  {
    label: '成就名称',
    field: 'name',
    type: 'input',
    placeholder: '请输入成就名称',
    width: '200px'
  }
]

// 表格配置
const columns = [
  { title: '成就名称', dataIndex: 'name', key: 'name', width: 200, ellipsis: true },
  { title: '成就图标', dataIndex: 'icon', key: 'icon', width: 100, align: 'center' },
  { title: '成就条件', dataIndex: 'description', key: 'description', width: 400, ellipsis: true },
  { title: '操作', key: 'action', width: 150, fixed: 'right', align: 'center' }
]

// 表格操作配置
const actionConfig = {
  edit: true,
  delete: true
}

// 删除确认标题
const deleteTitle = '确定删除该成就模板吗？'

// 表格数据和加载状态
const tableData = ref([])
const loading = ref(false)

// 弹窗状态
const modalVisible = ref(false)
const modalTitle = ref('')

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  config: {},
  icon: ''
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请选择成就名称', trigger: 'change' }]
}

const formRef = ref()

// 处理成就名称变化
const handleNameChange = (name) => {
  // 重置配置
  formData.config = {}

  // 根据名称设置默认值
  switch (name) {
    case '初入宝殿':
      formData.config = { progress: 50 }
      break
    case '知识探索':
      formData.config = { subjectCount: 5, progress: 80 }
      break
    case '学霸模式':
      formData.config = { days: 7 }
      break
    case '学无止境':
      formData.config = { hours: 50 }
      break
    case '碎片时间大师':
      formData.config = {
        startTime1: 8, endTime1: 12,
        startTime2: 14, endTime2: 18,
        startTime3: 20, endTime3: 22
      }
      break
    case '全能力者':
      formData.config = {}
      break
    case '金牌毕业生':
      formData.config = { passRate: 100 }
      break
    case '早起鸟':
      formData.config = { startTime: 6, endTime: 9, days: 7 }
      break
    case '夜猫子':
      formData.config = { startTime: 22, endTime: 2, days: 7 }
      break
    case '旗开得胜':
      formData.config = {}
      break
  }
}

// 生成成就描述
const generateDescription = (name, config) => {
  switch (name) {
    case '初入宝殿':
      return `首次学习一门科目的进度达到${config.progress}%`
    case '知识探索':
      return `完成${config.subjectCount}门科目的进度达到${config.progress}%`
    case '学霸模式':
      return `连续学习超过${config.days}天`
    case '学无止境':
      return `累计学习时长超过${config.hours}小时`
    case '碎片时间大师':
      if (config.startTime1 && config.endTime1 && config.startTime2 && config.endTime2 && config.startTime3 && config.endTime3) {
        return `一天内${config.startTime1}点～${config.endTime1}点、${config.startTime2}点～${config.endTime2}点、${config.startTime3}点～${config.endTime3}点三个时间范围都有练习记录`
      }
      return '一天内三个时间范围都有练习记录'
    case '全能力者':
      return '所有岗位都有练习记录'
    case '金牌毕业生':
      return `所有考试都是通过率${config.passRate}%`
    case '早起鸟':
      if (config.startTime && config.endTime && config.days) {
        return `${config.startTime}点～${config.endTime}点之间有练习记录，连续${config.days}天`
      }
      return '早晨时间段连续练习'
    case '夜猫子':
      if (config.startTime && config.endTime && config.days) {
        return `${config.startTime}点～${config.endTime}点之间有练习记录，连续${config.days}天`
      }
      return '夜晚时间段连续练习'
    case '旗开得胜':
      return '获得第一个考试满分'
    default:
      return ''
  }
}

// 获取完整图片URL
const getFullImageUrl = (imagePath) => {
  if (!imagePath) return ''
  if (imagePath.startsWith('http')) return imagePath
  return VITE_APP_API_BASE_IMG_URL + imagePath
}

// 图标上传处理
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif'
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG/GIF 格式的图片!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 自定义上传处理
const handleIconUpload = async ({ file, onSuccess, onError }) => {
  try {
    const response = await uploadAchievementIcon(file)
    if (response) {
      formData.icon = response.filePath || response.path || response.url
      message.success('图标上传成功')
      onSuccess(response)
    } else {
      message.error(response?.message || '图标上传失败')
      onError(new Error(response?.message || '图标上传失败'))
    }
  } catch (error) {
    console.error('图标上传失败:', error)
    message.error('图标上传失败')
    onError(error)
  }
}

// 获取列表数据
const fetchTableData = async () => {
  loading.value = true

  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    const response = await getAchievementTemplates(params)

    if (response && response.code === 200) {
      tableData.value = response.data?.list || response.list || []
      updatePagination({
        total: response.data?.total || response.total || 0,
        current: response.data?.page || response.page || pagination.current,
        pageSize: response.data?.pageSize || response.pageSize || pagination.pageSize
      })
    } else if (response) {
      tableData.value = response.list || []
      updatePagination({
        total: response.total || 0,
        current: response.page || pagination.current,
        pageSize: response.pageSize || pagination.pageSize
      })
    }
  } catch (error) {
    console.error('获取成就模板列表失败:', error)
    message.error('获取数据失败')
    tableData.value = []
    updatePagination({ total: 0, current: 1 })
  } finally {
    loading.value = false
  }
}

// 使用表格分页组合式函数
const {
  pagination,
  handleTableChange,
  updatePagination,
  resetPagination
} = useTablePagination({
  fetchData: fetchTableData,
  initialPagination: {
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条数据`
  },
  searchForm
})

// 事件处理
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values)
  }
  resetPagination()
  fetchTableData()
}

const resetSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values)
  } else {
    Object.assign(searchForm, {
      name: ''
    })
  }
  resetPagination()
  fetchTableData()
}

const handleAdd = () => {
  modalTitle.value = '新增成就模板'
  Object.assign(formData, {
    id: null,
    name: '',
    config: {},
    icon: ''
  })
  modalVisible.value = true
}

const handleEdit = (record) => {
  modalTitle.value = '编辑成就模板'

  // 解析现有数据
  let config = {}

  if (record.triggerCondition) {
    try {
      config = JSON.parse(record.triggerCondition)
    } catch (e) {
      console.warn('解析triggerCondition失败:', e)
    }
  }

  Object.assign(formData, {
    id: record.id,
    name: record.name,
    config: config,
    icon: record.icon
  })
  modalVisible.value = true
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()

    // 生成描述
    const description = generateDescription(formData.name, formData.config)

    // 根据成就名称设置rule_type和category
    let ruleType = 'progress'
    let category = 'learning'

    switch (formData.name) {
      case '初入宝殿':
      case '知识探索':
        ruleType = 'progress'
        category = 'learning'
        break
      case '全能力者':
        ruleType = 'all_positions'
        category = 'learning'
        break
      case '金牌毕业生':
        ruleType = 'exam_pass_rate'
        category = 'learning'
        break
      case '学霸模式':
        ruleType = 'consecutive_days'
        category = 'learning'
        break
      case '学无止境':
        ruleType = 'study_time'
        category = 'learning'
        break
      case '碎片时间大师':
      case '早起鸟':
      case '夜猫子':
        ruleType = 'time_based'
        category = 'learning'
        break
      case '旗开得胜':
        ruleType = 'exam_score'
        category = 'learning'
        break
    }

    const data = {
      name: formData.name,
      description: description,
      icon: formData.icon,
      category: category,
      ruleType: ruleType,
      triggerCondition: JSON.stringify({
        type: formData.name,
        rule: formData.name,
        ...formData.config
      }),
      rewardPoints: 10,
      isActive: true
    }

    if (formData.id) {
      await updateAchievementTemplate(formData.id, data)
      message.success('更新成就模板成功')
    } else {
      await createAchievementTemplate(data)
      message.success('创建成就模板成功')
    }

    modalVisible.value = false
    fetchTableData()
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
}

const handleDelete = async (record) => {
  try {
    await deleteAchievementTemplate(record.id)
    message.success('删除成就模板成功')
    fetchTableData()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

// 初始化
onMounted(() => {
  fetchTableData()
})
</script>

<style scoped>
.icon-uploader {
  width: 104px;
  height: 104px;
}

.icon-uploader :deep(.ant-upload) {
  width: 104px;
  height: 104px;
  border-radius: 4px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 14px;
}

.upload-tip {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.upload-tip p {
  margin: 0;
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-preview :deep(.ant-image) {
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.condition-config {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 32px;
  line-height: 1.5;
}

.condition-config span {
  white-space: nowrap;
  font-size: 14px;
  color: #333;
  flex-shrink: 0;
}

.condition-config .ant-input-number {
  flex-shrink: 0;
}

.condition-config .ant-picker {
  flex-shrink: 0;
}

/* 针对碎片时间大师的特殊样式 */
.condition-config:has(.ant-input-number:nth-of-type(6)) {
  flex-wrap: wrap;
  max-width: 100%;
}

/* 确保长文本能够正确换行 */
.condition-config span:last-child {
  flex: 1;
  min-width: 0;
  word-break: break-word;
}
</style>
