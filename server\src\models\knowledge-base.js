const { DataTypes, Model } = require('sequelize');
const sequelize = require('../config/database');

class KnowledgeBase extends Model {}

KnowledgeBase.init({
  id: {
    type: DataTypes.STRING(36),
    primaryKey: true,
    comment: '主键ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '知识库名称'
  },
  fileName: {
    field: 'file_name',
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '文件名称'
  },
  fileType: {
    field: 'file_type',
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '文件类型'
  },
  fileSize: {
    field: 'file_size',
    type: DataTypes.BIGINT,
    comment: '文件大小(字节)'
  },
  fileUrl: {
    field: 'file_url',
    type: DataTypes.STRING(500),
    comment: '文件访问路径'
  },
  category: {
    type: DataTypes.STRING(50),
    comment: '文件归属'
  },
  position: {
    type: DataTypes.STRING(50),
    comment: '所属岗位名称'
  },
  positionLevel: {
    field: 'position_level',
    type: DataTypes.STRING(50),
    comment: '所属岗位等级'
  },
  fileCategory: {
    field: 'file_category',
    type: DataTypes.STRING(50),
    comment: '文件分类'
  },
  certificateType: {
    field: 'certificate_type',
    type: DataTypes.STRING(50),
    comment: '证书类型'
  },
  documentType: {
    field: 'document_type',
    type: DataTypes.STRING(20),
    defaultValue: 'exam',
    comment: '文档类型(exam-练考类型 prompt-提示类型)'
  },
  status: {
    type: DataTypes.STRING(255),
    defaultValue: 'enable',
    comment: '文档状态(enable-正常 disable-禁用)'
  },
  processStatus: {
    field: 'process_status',
    type: DataTypes.STRING(20),
    defaultValue: 'preparing',
    comment: '文档处理状态(preparing-准备中 embedding-嵌入中 indexing-索引中 generating-出题中 completed-已完成 failed-失败)'
  },
  processProgress: {
    field: 'process_progress',
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '文档处理进度(0-100)'
  },
  processMessage: {
    field: 'process_message',
    type: DataTypes.STRING(255),
    comment: '处理状态消息'
  },
  remark: {
    type: DataTypes.STRING(500),
    comment: '备注'
  },
  createdBy: {
    field: 'created_by',
    type: DataTypes.STRING(50),
    comment: '创建者'
  },
  createdTime: {
    field: 'created_time',
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updatedBy: {
    field: 'updated_by',
    type: DataTypes.STRING(50),
    comment: '更新者'
  },
  updatedTime: {
    field: 'updated_time',
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  },
  enterpriseId: {
    field: 'enterprise_id',
    type: DataTypes.STRING(36),
    allowNull: false,
    comment: '企业ID'
  },
  deleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否删除(0-否 1-是)'
  },
  document_id: {
    field: 'document_id',
    type: DataTypes.STRING(255),
    comment: 'DIFY文档ID'
  },
  prompt: {
    type: DataTypes.TEXT,
    comment: '出题策略提示词'
  },
}, {
  sequelize,
  modelName: 'KnowledgeBase',
  tableName: 'kb_knowledge_base',
  timestamps: false
});

// 添加模型关联关系，在模型导出前设置
// 这些关联将在引入模型后生效
const PositionType = require('./PositionType');
const PositionName = require('./PositionName');

// 知识库与岗位类型的关联
KnowledgeBase.belongsTo(PositionType, {
  foreignKey: 'fileCategory',
  targetKey: 'id',
  as: 'positionType'
});

// 知识库与岗位名称的关联
KnowledgeBase.belongsTo(PositionName, {
  foreignKey: 'position',
  targetKey: 'id',
  as: 'positionName'
});

module.exports = KnowledgeBase;
