server {
    listen 80;
    server_name ck-demo.lingmiaoai.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name ck-demo.lingmiaoai.com;

    ssl_certificate     /etc/nginx/ssl/lingmiaoai.crt;
    ssl_certificate_key /etc/nginx/ssl/lingmiaoai.key;

    location / {
        proxy_pass http://***********:31495/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 关键修复项：处理分块传输编码
        proxy_http_version 1.1;       # 使用 HTTP/1.1 支持分块传输
        proxy_buffering off;          # 关闭代理缓冲，避免截断分块数据
        proxy_set_header Connection ""; # 清除 Connection 头，防止代理干扰
    }
}
