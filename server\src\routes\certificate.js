const express = require('express');
const router = express.Router();
const certificateController = require('../controllers/exam-manage/certificateController');

// 获取证书列表
router.get('/list', certificateController.getCertificateList);

// 获取证书详情
router.get('/detail/:id', certificateController.getCertificateDetail);

// 下载证书
router.get('/download/:id', certificateController.downloadCertificate);

// 创建证书记录
router.post('/create', certificateController.createCertificate);

// 更新证书记录
router.put('/update/:id', certificateController.updateCertificate);

// 删除证书记录
router.delete('/delete/:id', certificateController.deleteCertificate);

module.exports = router; 