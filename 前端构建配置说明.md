# 前端构建配置说明

## 当前问题

你发现的问题很对：**当前的nginx.conf确实没有在前端打包中被使用！**

### 现状分析
- 现在的`Dockerfile`使用`node:18-alpine`镜像
- 直接运行`npm run dev`（Vite开发服务器）
- 暴露5173端口，**没有使用nginx**
- 所以nginx.conf中的WebSocket代理配置实际上没有生效

## 解决方案

### 方案1：使用生产环境Dockerfile（推荐）

我已经创建了`Dockerfile.prod`，它会：
1. 构建项目（`npm run build`）
2. 使用nginx服务静态文件
3. 应用nginx.conf配置（包括WebSocket代理）

#### 使用方法：
```bash
# 生产环境构建（使用nginx）
./build-frontend.sh <password> yes prod

# 开发环境构建（使用Vite dev server）
./build-frontend.sh <password> yes dev
```

### 方案2：在Vite配置中添加WebSocket代理

如果你想继续使用开发模式，可以在`vite.config.js`中添加WebSocket代理：

```javascript
server: {
  // ... 现有配置
  proxy: {
    '/api': {
      target: env.VITE_APP_API_BASE_URL,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    },
    // 添加WebSocket代理
    '/ws': {
      target: env.VITE_APP_API_BASE_URL,
      ws: true,  // 启用WebSocket代理
      changeOrigin: true
    }
  }
}
```

### 方案3：修改现有Dockerfile使用nginx

替换现有的`Dockerfile`内容为生产构建模式。

## 推荐配置

### 生产环境推荐：
1. 使用`Dockerfile.prod`进行构建
2. 使用nginx服务静态文件
3. 通过nginx代理WebSocket连接
4. 前端连接地址：`ws://cankao-admin.dev.lingmiaoai.com/ws`

### 开发环境：
1. 继续使用现有的`Dockerfile`
2. 或者在`vite.config.js`中添加WebSocket代理
3. 前端连接地址：通过Vite代理访问

## 部署步骤

### 如果要解决生产环境的WebSocket问题：

1. **使用生产构建**：
   ```bash
   ./build-frontend.sh <password> yes prod
   ```

2. **更新前端WebSocket连接代码**：
   ```javascript
   // 更改连接地址为通过nginx代理
   const wsUrl = 'ws://cankao-admin.dev.lingmiaoai.com/ws';
   ```

3. **重新部署服务**

### 验证步骤：
1. 检查nginx配置是否生效
2. 测试WebSocket连接
3. 查看浏览器开发者工具的Network面板
4. 检查后端日志

## 总结

你的观察很准确！当前的nginx.conf确实没有被使用。要解决WebSocket连接问题，需要：

1. **要么**：使用生产环境构建（推荐）
2. **要么**：在Vite配置中添加WebSocket代理
3. **要么**：修改现有Dockerfile使用nginx

推荐使用方案1，因为这样更接近真实的生产环境配置。 