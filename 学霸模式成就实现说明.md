# 学霸模式成就实现说明

## 功能概述

学霸模式成就是一种连续学习类成就，用于奖励连续学习超过指定天数的用户。该成就属于学习类(category: 'learning')，规则类型为'consecutive_days'。

## 实现逻辑

### 1. 成就触发条件

- 用户连续学习天数达到配置的目标天数（默认为3天）
- 连续学习的定义：每天至少有一条练习记录，日期连续不间断

### 2. 检测流程

1. 从练习记录表(`practice_record`)中获取用户的所有练习记录
2. 按日期分组并去重，获取用户有练习记录的所有日期
3. 计算最大连续天数
4. 更新用户的成就进度记录
5. 当连续天数达到目标时，颁发成就

### 3. 核心代码

```javascript
/**
 * 处理学霸模式成就检测（连续学习天数）
 * @param {number} userId - 用户ID
 * @param {string} openId - 用户openId
 * @param {string} enterpriseId - 企业ID
 */
const processStudyStreakAchievement = async (userId, openId, enterpriseId) => {
  try {
    // 查找学霸模式成就模板
    const template = await AchievementTemplate.findOne({
      where: {
        name: '学霸模式',
        category: 'learning',
        ruleType: 'consecutive_days',
        isActive: true
      }
    });

    // 获取目标连续天数
    const triggerCondition = JSON.parse(template.triggerCondition);
    const targetDays = triggerCondition.days || 3;

    // 获取用户的练习记录
    const practiceRecords = await PracticeRecord.findAll({
      where: { openId },
      attributes: ['id', 'createTime'],
      order: [['createTime', 'DESC']]
    });

    // 获取用户练习的所有日期（按日期分组）
    const practiceDates = practiceRecords.map(record => {
      const date = new Date(record.createTime);
      return date.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
    });
    
    // 去重并排序
    const uniqueDates = [...new Set(practiceDates)].sort();

    // 计算最大连续天数
    let maxConsecutiveDays = 0;
    let currentStreak = 1;
    
    for (let i = 1; i < uniqueDates.length; i++) {
      const prevDate = new Date(uniqueDates[i-1]);
      const currDate = new Date(uniqueDates[i]);
      
      // 计算日期差
      const diffTime = Math.abs(currDate - prevDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) {
        // 连续天数
        currentStreak++;
      } else {
        // 重置连续天数
        currentStreak = 1;
      }
      
      // 更新最大连续天数
      maxConsecutiveDays = Math.max(maxConsecutiveDays, currentStreak);
    }

    // 如果达到目标连续天数，颁发成就
    if (maxConsecutiveDays >= targetDays) {
      await awardAchievement(template, userId, openId, enterpriseId, {
        currentValue: maxConsecutiveDays,
        targetValue: targetDays,
        practiceDates: uniqueDates
      });
    }
  } catch (error) {
    console.error('[学霸模式] 检查连续学习天数成就失败:', error);
  }
};
```

### 4. 触发时机

学霸模式成就在用户每次练习后触发检测，具体在 `processPracticeProgressAchievements` 函数中调用：

```javascript
// 分别调用独立的成就检测方法
await processFirstEntryAchievement(userId, openId, enterpriseId, subjectId, subjectName, progress);
await processKnowledgeExplorationAchievement(userId, openId, enterpriseId, positionName, positionLevel);
await processTimeMasterAchievement(userId, openId, enterpriseId, positionName, positionLevel);
await processStudyStreakAchievement(userId, openId, enterpriseId);
```

## 测试方法

### 1. 测试脚本

我们创建了一个测试脚本 `test-study-streak-achievement.js`，用于测试学霸模式成就的检测逻辑。该脚本模拟用户在不同天数的练习记录，并检测成就是否正确触发。

### 2. 测试场景

1. **连续学习2天（不足3天）**：
   - 预期结果：不触发成就，但更新进度记录

2. **连续学习4天（超过3天）**：
   - 预期结果：触发成就

3. **不连续学习5天**：
   - 预期结果：不触发成就，因为不满足连续条件

### 3. 手动测试

1. 登录微信小程序
2. 连续3天进行练习（每天至少一次）
3. 检查成就列表，查看是否获得"学霸模式"成就

## 配置说明

学霸模式成就的配置存储在 `achievement_templates` 表中，关键字段如下：

```json
{
  "id": 35,
  "enterprise_id": "8ecca795-c9a0-4cd4-9b82-bf4d190d3f32",
  "name": "学霸模式",
  "description": "连续学习超过3天",
  "icon": "/uploads/achievements/3.png",
  "category": "learning",
  "rule_type": "consecutive_days",
  "trigger_condition": "{\"type\":\"study_streak\",\"rule\":\"学霸模式\",\"days\":3}",
  "reward_points": 30,
  "is_active": 1
}
```

可以通过管理后台修改 `trigger_condition` 中的 `days` 参数来调整目标连续天数。

## 注意事项

1. 成就进度记录存储在 `achievement_progress` 表中，包含当前连续天数、目标天数等信息
2. 连续天数的计算基于 `practice_record` 表的 `create_time` 字段
3. 如果用户中断连续学习，连续天数会重置为1
4. 一旦用户获得该成就，将不再重复颁发 