const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const Position = require('./Position');
const Level = require('./Level');
const PositionType = require('./PositionType');
const Employee = require('./Employee');

/**
 * 员工晋升时间表模型
 */
const EmployeePromotion = sequelize.define('EmployeePromotion', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '员工ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  employeeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'employee_id',
    comment: '员工Id'
  },
  openId: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field: 'open_id',
    comment: '微信openId'
  },
  positionTypeId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'position_type_id',
    comment: '岗位类型ID'
  },
  positionId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'position_id',
    comment: '岗位ID'
  },
  levelId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'level_id',
    comment: '岗位等级ID'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active',
    comment: '是否有效(1有效 0无效)'
  },
  positionBelongName: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_belong_name',
    comment: '岗位归属名称'
  },
  positionNameCn: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_name_cn',
    comment: '岗位名称'
  },
  positionLevelName: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_level_name',
    comment: '岗位等级名称'
  },
  careerRecordId: {
    type: DataTypes.INTEGER,
    field: 'career_record_id',
    allowNull: true,
    comment: '员工履历记录ID'
  },
  createBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'create_by',
    comment: '创建者'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'update_by',
    comment: '更新者'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  },
  promotionTime: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'promotion_time',
    comment: '时间'
  },
  
}, {
  tableName: 'org_employee_promotion',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

// 关联岗位
EmployeePromotion.belongsTo(Position, {
  foreignKey: 'positionId',
  as: 'position'
});

// 关联岗位等级
EmployeePromotion.belongsTo(Level, {
  foreignKey: 'levelId',
  as: 'level'
});

// 关联岗位类型
EmployeePromotion.belongsTo(PositionType, {
  foreignKey: 'positionTypeId',
  as: 'positionType'
});

// 关联员工(通过openId)
EmployeePromotion.belongsTo(Employee, {
  foreignKey: 'openId',
  targetKey: 'openId',
  as: 'employee'
});

module.exports = EmployeePromotion; 