const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 餐烤师聊天记录详情模型
 */
const RestaurantRecordDetail = sequelize.define('RestaurantRecordDetail', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    field:'id',
    autoIncrement: true,
    comment: '主键ID'
  },
  restaurantRecordId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'restaurant_record_id',
    comment: '练习记录id'
  },
  enterpriseId: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field:'enterprise_id',
    comment: '企业ID'
  },
  positionBelongId: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field:'position_belong_id',
    comment: '前厅后厨分类ID'
  },
  positionName: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_name',
    comment: '岗位名称'
  },
  positionLevel: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'position_level',
    comment: '岗位等级'
  },
  openId: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field:'open_id',
    comment: 'open_id'
  },
  role: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field:'role',
    comment: '角色：系统，用户'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
    field:'content',
    comment: '聊天内容'
  },
  createdBy: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field:'created_by',
    comment: '创建人ID'
  },
  updatedBy: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field:'updated_by',
    comment: '更新人ID'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field:'created_at',
    defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
    comment: '创建时间'
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field:'updated_at',
    defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
    comment: '更新时间'
  }
}, {
  tableName: 'restaurant_record_detail',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = RestaurantRecordDetail;