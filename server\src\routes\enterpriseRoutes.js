const express = require('express');
const router = express.Router();
const enterpriseController = require('../controllers/enterpriseController');

// 获取企业列表
router.get('/list', enterpriseController.getEnterpriseList);

// 获取企业详情
router.get('/detail/:id', enterpriseController.getEnterpriseDetail);

// 创建企业
router.post('/', enterpriseController.createEnterprise);

// 更新企业
router.put('/', enterpriseController.updateEnterprise);

// 删除企业
router.delete('/:id', enterpriseController.deleteEnterprise);

module.exports = router; 