const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const Department = require('./Department');
const Position = require('./Position');
const PositionName = require('./PositionName');
const Level = require('./Level');
const PositionType = require('./PositionType');

/**
 * 员工模型
 */
const Employee = sequelize.define('Employee', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '员工ID'
  },
  positionTypeId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'position_type_id',
    comment: '岗位类型ID'
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'enterprise_id',
    comment: '企业ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '姓名'
  },
  departmentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'department_id',
    comment: '部门ID'
  },
  positionId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'position_id',
    comment: '岗位ID'
  },
  levelId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'level_id',
    comment: '岗位等级ID'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '手机号'
  },
  idCard: {
    type: DataTypes.STRING(20),
    allowNull: true,
    field: 'id_card',
    comment: '身份证号'
  },
  openId: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'open_id',
    comment: '微信openId'
  },
  gender: {
    type: DataTypes.STRING(10),
    allowNull: true,
    comment: '性别（male男 female女）'
  },
  entryTime: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    field: 'entry_time',
    comment: '入职时间'
  },
  status: {
    type: DataTypes.CHAR(1),
    defaultValue: '1',
    comment: '状态（1在职 0离职）'
  },
  isActivated: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_activated',
    comment: '小程序是否激活（1已激活 0未激活）'
  },
  remark: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '备注'
  },
  createBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'create_by',
    comment: '创建者'
  },
  updateBy: {
    type: DataTypes.STRING(64),
    allowNull: true,
    field: 'update_by',
    comment: '更新者'
  },
  createTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'org_employee',
  timestamps: false // 不使用默认的 createdAt 和 updatedAt
});

// 关联部门
Employee.belongsTo(Department, {
  foreignKey: 'departmentId',
  as: 'department'
});
Department.hasMany(Employee, {
  foreignKey: 'departmentId',
  as: 'employees'
});

// 关联岗位
Employee.belongsTo(Position, {
  foreignKey: 'positionId',
  as: 'position'
});
Position.hasMany(Employee, {
  foreignKey: 'positionId',
  as: 'employees'
});

// 关联岗位名称
Employee.belongsTo(PositionName, {
  foreignKey: 'positionId',
  as: 'positionName'
});
PositionName.hasMany(Employee, {
  foreignKey: 'positionId',
  as: 'employees'
});
// 关联岗位等级
Employee.belongsTo(Level, {
  foreignKey: 'levelId',
  as: 'level'
});
Level.hasMany(Employee, {
  foreignKey: 'levelId',
  as: 'employees'
});

// 关联岗位类型
Employee.belongsTo(PositionType, {
  foreignKey: 'positionTypeId',
  as: 'positionType'
});
PositionType.hasMany(Employee, {
  foreignKey: 'positionTypeId',
  as: 'employees'
});

// 延迟加载关联关系，避免循环依赖
let associationsSetup = false; // 添加标志防止重复设置

const setupEmployeePositionAssociations = () => {
  // 如果已经设置过关联，直接返回
  if (associationsSetup) {
    return;
  }

  const EmployeePosition = require('./EmployeePosition');

  // 员工与岗位关联的多对多关系
  Employee.hasMany(EmployeePosition, {
    foreignKey: 'employeeId',
    as: 'employeePositions'
  });

  EmployeePosition.belongsTo(Employee, {
    foreignKey: 'employeeId',
    as: 'employee'
  });

  // 通过关联表关联岗位名称（positionId实际上是PositionName的ID）
  EmployeePosition.belongsTo(PositionName, {
    foreignKey: 'positionId',
    as: 'positionName'
  });

  // 通过关联表关联岗位类型
  EmployeePosition.belongsTo(PositionType, {
    foreignKey: 'positionTypeId',
    as: 'positionType'
  });

  // 通过关联表关联等级
  EmployeePosition.belongsTo(Level, {
    foreignKey: 'levelId',
    as: 'level'
  });

  // 设置标志表示关联已设置
  associationsSetup = true;
};

// 导出设置关联的函数
Employee.setupEmployeePositionAssociations = setupEmployeePositionAssociations;

module.exports = Employee;
