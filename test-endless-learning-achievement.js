/**
 * 学无止境成就测试脚本
 * 该脚本用于测试累计学习时长成就
 */

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.DEFAULT_ENTERPRISE_ID = '8ecca795-c9a0-4cd4-9b82-bf4d190d3f32';

// 导入依赖
const { sequelize } = require('./server/src/config/database');
const { processEndlessLearningAchievement } = require('./server/src/utils/achievementUtils');
const PracticeRecord = require('./server/src/models/practice-record');
const User = require('./server/src/models/user');
const { addEnterpriseId } = require('./server/src/utils/enterpriseFilter');

// 测试用户信息
const TEST_OPEN_ID = 'oxiSG65bMvpNcF9TORr5mvW-HXo4'; // 替换为实际测试用户的openId
const TEST_POSITION_NAME = '1'; // 替换为实际岗位ID
const TEST_POSITION_LEVEL = '1'; // 替换为实际等级ID

/**
 * 创建模拟练习记录
 * @param {string} openId - 用户openId
 * @param {number} totalHours - 总学习时长（小时）
 * @param {number} recordCount - 要创建的记录数量
 */
async function createMockPracticeRecords(openId, totalHours, recordCount) {
  console.log(`开始创建${recordCount}条练习记录，总时长${totalHours}小时...`);
  
  const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
  const today = new Date();
  
  // 删除之前的测试记录
  await PracticeRecord.destroy(
    addEnterpriseId({
      where: { openId }
    }, enterpriseId)
  );
  
  // 计算每条记录的平均时长（秒）
  const totalSeconds = totalHours * 3600;
  const secondsPerRecord = Math.floor(totalSeconds / recordCount);
  
  // 创建练习记录
  for (let i = 0; i < recordCount; i++) {
    // 随机生成时长，但确保总和接近目标时长
    let recordSeconds = i === recordCount - 1 
      ? totalSeconds - (secondsPerRecord * (recordCount - 1)) // 最后一条记录补齐总时长
      : secondsPerRecord;
    
    // 将秒转换为 MM:SS 格式
    const minutes = Math.floor(recordSeconds / 60);
    const seconds = recordSeconds % 60;
    const durationString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    // 随机日期（过去30天内）
    const recordDate = new Date(today);
    recordDate.setDate(today.getDate() - Math.floor(Math.random() * 30));
    
    await PracticeRecord.create(
      addEnterpriseId({
        openId,
        questionNum: Math.floor(Math.random() * 20) + 5, // 随机题目数量
        totalDuration: durationString,
        positionBelong: '1',
        positionName: TEST_POSITION_NAME,
        positionLevel: TEST_POSITION_LEVEL,
        examSubject: Math.floor(Math.random() * 5) + 1, // 随机科目ID
        status: '必练',
        createTime: recordDate,
        updateTime: recordDate
      }, enterpriseId)
    );
    
    console.log(`创建练习记录: 时长=${durationString}, 日期=${recordDate.toISOString().split('T')[0]}`);
  }
  
  console.log(`成功创建${recordCount}条练习记录，总时长约${totalHours}小时`);
}

/**
 * 运行测试
 */
async function runTest() {
  try {
    console.log('开始测试学无止境成就...');
    
    // 获取测试用户
    const enterpriseId = process.env.DEFAULT_ENTERPRISE_ID;
    const user = await User.findOne(
      addEnterpriseId({
        where: { openId: TEST_OPEN_ID }
      }, enterpriseId)
    );
    
    if (!user) {
      console.error(`未找到测试用户: openId=${TEST_OPEN_ID}`);
      return;
    }
    
    console.log(`测试用户: id=${user.id}, username=${user.username}, openId=${user.openId}`);
    
    // 测试场景1: 累计学习20小时（不足50小时）
    await createMockPracticeRecords(TEST_OPEN_ID, 20, 10);
    console.log('测试场景1: 累计学习20小时（不足50小时）');
    await processEndlessLearningAchievement(user.id, TEST_OPEN_ID, enterpriseId);
    
    // 测试场景2: 累计学习60小时（超过50小时）
    await createMockPracticeRecords(TEST_OPEN_ID, 60, 15);
    console.log('测试场景2: 累计学习60小时（超过50小时）');
    await processEndlessLearningAchievement(user.id, TEST_OPEN_ID, enterpriseId);
    
    console.log('测试完成');
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  runTest().then(() => {
    console.log('测试脚本执行完毕');
    process.exit(0);
  }).catch(error => {
    console.error('测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runTest }; 