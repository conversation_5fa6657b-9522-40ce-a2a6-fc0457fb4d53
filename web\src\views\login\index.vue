<template>
  <div class="page-container login-page">
    <div class="login-box">
      <div class="login-header">
        <h1 class="logo">餐烤餐考后台管理系统</h1>
      </div>
      <div class="login-form">
        <a-form
          :model="loginForm"
          :rules="rules"
          ref="loginFormRef"
          @finish="handleSubmit"
        >
          <a-form-item name="username">
            <a-input 
              v-model:value="loginForm.username" 
              placeholder="请输入用户名"
              size="large"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item name="password">
            <a-input-password 
              v-model:value="loginForm.password" 
              placeholder="请输入密码"
              size="large"
            >
              <template #prefix>
                <LockOutlined />
              </template>
            </a-input-password>
          </a-form-item>
          <a-form-item name="remember">
            <a-checkbox v-model:checked="loginForm.remember">记住密码</a-checkbox>
            <!-- <a class="forgot-link">忘记密码？</a> -->
          </a-form-item>
          <a-form-item>
            <a-button 
              type="primary" 
              html-type="submit" 
              :loading="loading" 
              block
              size="large"
              class="login-button"
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user';

const router = useRouter();
const userStore = useUserStore();
const loginFormRef = ref(null);
const loading = ref(false);

const loginForm = reactive({
  username: '',
  password: '',
  remember: false
});

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
};

onMounted(() => {
  // 如果有保存的用户名和密码，自动填充
  const savedUsername = localStorage.getItem('savedUsername');
  const savedPassword = localStorage.getItem('savedPassword');
  if (savedUsername && savedPassword) {
    loginForm.username = savedUsername;
    loginForm.password = savedPassword;
    loginForm.remember = true;
  }
});

const handleSubmit = async () => {
  loading.value = true;
  try {
    // 调用store中的login方法
    const result = await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    });
    
    if (!result.success) {
      throw new Error(result.error || '登录失败');
    }
    
    // 处理记住密码功能
    if (loginForm.remember) {
      localStorage.setItem('savedUsername', loginForm.username);
      localStorage.setItem('savedPassword', loginForm.password);
    } else {
      localStorage.removeItem('savedUsername');
      localStorage.removeItem('savedPassword');
    }
    
    message.success('登录成功');
    router.push('/');
  } catch (error) {
    message.error(error.message || '登录失败，请检查用户名和密码');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.page-container.login-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  overflow: hidden;
  padding: 0; /* 覆盖page-container的默认内边距 */
}

.login-box {
  width: 400px;
  padding: 40px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 8px 24px rgba(149, 157, 165, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  font-size: 24px;
  color: #333;
  font-weight: bold;
  background-image: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.login-form {
  width: 100%;
}

.forgot-link {
  float: right;
  color: #a18cd1;
  cursor: pointer;
}

.login-button {
  height: 42px;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border: none;
  margin-top: 10px;
  transition: all 0.3s;
}

.login-button:hover {
  opacity: 0.9;
  box-shadow: 0 4px 12px rgba(161, 140, 209, 0.4);
}
</style> 