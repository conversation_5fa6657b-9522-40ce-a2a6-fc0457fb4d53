const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

/**
 * Excel导出工具类
 * 提供通用的Excel数据导出功能
 */
class ExcelExport {
  /**
   * 将数据导出为Excel并发送到响应
   * @param {Object} res - Express响应对象
   * @param {Array} data - 要导出的数据数组
   * @param {String} filename - 导出的文件名（不含后缀）
   * @param {Array} header - 表头字段列表
   * @param {Array} [colWidths] - 列宽配置，每列的宽度，如: [{ wpx: 100 }, { wpx: 120 }]
   * @param {String} [sheetName='数据'] - 工作表名称
   */
  static exportToResponse(res, data, filename, header, colWidths = [], sheetName = '数据') {
    try {
      // 创建工作簿
      const workbook = xlsx.utils.book_new();

      // 确保数据不为空，如果为空则创建一个只有表头的空行
      const exportData = data.length > 0 ? data : [this._createEmptyRow(header)];

      // 创建工作表
      const worksheet = xlsx.utils.json_to_sheet(exportData, { header });

      // 设置列宽
      if (colWidths && colWidths.length > 0) {
        worksheet['!cols'] = colWidths;
      } else {
        // 默认为每列设置合适的宽度
        worksheet['!cols'] = header.map(() => ({ wpx: 120 }));
      }

      // 添加工作表到工作簿
      xlsx.utils.book_append_sheet(workbook, worksheet, sheetName);

      // 生成Excel文件
      const excelBuffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(filename)}.xlsx`);

      // 发送Excel文件
      res.send(excelBuffer);
      
      return true;
    } catch (error) {
      console.error('导出Excel失败：', error);
      throw error;
    }
  }

  /**
   * 将数据导出为Excel文件保存到指定路径
   * @param {Array} data - 要导出的数据数组
   * @param {String} filePath - 文件保存路径（包含文件名和后缀）
   * @param {Array} header - 表头字段列表
   * @param {Array} [colWidths] - 列宽配置
   * @param {String} [sheetName='数据'] - 工作表名称
   * @returns {String} 保存的文件路径
   */
  static exportToFile(data, filePath, header, colWidths = [], sheetName = '数据') {
    try {
      // 创建工作簿
      const workbook = xlsx.utils.book_new();

      // 确保数据不为空
      const exportData = data.length > 0 ? data : [this._createEmptyRow(header)];

      // 创建工作表
      const worksheet = xlsx.utils.json_to_sheet(exportData, { header });

      // 设置列宽
      if (colWidths && colWidths.length > 0) {
        worksheet['!cols'] = colWidths;
      } else {
        // 默认为每列设置合适的宽度
        worksheet['!cols'] = header.map(() => ({ wpx: 120 }));
      }

      // 添加工作表到工作簿
      xlsx.utils.book_append_sheet(workbook, worksheet, sheetName);

      // 确保目录存在
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 写入文件
      xlsx.writeFile(workbook, filePath);

      return filePath;
    } catch (error) {
      console.error('导出Excel文件失败：', error);
      throw error;
    }
  }

  /**
   * 创建一个模板文件并返回模板路径
   * @param {Array} headers - 表头列表，如: ['姓名', '年龄', '性别']
   * @param {Array} descriptions - 字段说明列表，如: ['请填写姓名', '请填写年龄', '请选择性别']
   * @param {String} filePath - 文件保存路径
   * @param {String} [sheetName='模板'] - 工作表名称
   * @param {Array} [colWidths] - 列宽配置
   * @returns {String} 模板文件路径
   */
  static createTemplate(headers, descriptions, filePath, sheetName = '模板', colWidths = []) {
    try {
      // 创建工作簿
      const workbook = xlsx.utils.book_new();

      // 创建说明行数据
      const descriptionRow = {};
      headers.forEach((header, index) => {
        descriptionRow[header] = descriptions[index] || '';
      });

      // 创建空白行数据
      const emptyRow = {};
      headers.forEach(header => {
        emptyRow[header] = '';
      });

      // 创建工作表数据（只有说明行和一个空白行）
      const data = [descriptionRow, emptyRow];

      // 创建工作表
      const worksheet = xlsx.utils.json_to_sheet(data);

      // 设置列宽
      if (colWidths && colWidths.length > 0) {
        worksheet['!cols'] = colWidths;
      } else {
        // 默认为每列设置合适的宽度
        worksheet['!cols'] = headers.map(() => ({ wpx: 120 }));
      }

      // 添加工作表到工作簿
      xlsx.utils.book_append_sheet(workbook, worksheet, sheetName);

      // 确保目录存在
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 写入文件
      xlsx.writeFile(workbook, filePath);

      return filePath;
    } catch (error) {
      console.error('创建Excel模板失败：', error);
      throw error;
    }
  }

  /**
   * 向响应发送一个Excel模板
   * @param {Object} res - Express响应对象 
   * @param {String} templatePath - 模板文件路径
   * @param {String} [filename='template'] - 下载时的文件名（不含后缀）
   */
  static sendTemplate(res, templatePath, filename = 'template') {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(templatePath)) {
        throw new Error('模板文件不存在');
      }

      // 读取文件
      const fileBuffer = fs.readFileSync(templatePath);

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}.xlsx"`);
      res.setHeader('Content-Length', fileBuffer.length);

      // 发送文件
      res.send(fileBuffer);
      
      return true;
    } catch (error) {
      console.error('发送Excel模板失败：', error);
      throw error;
    }
  }

  /**
   * 创建一个空行对象，用于数据为空时显示表头
   * @param {Array} headers - 表头字段列表
   * @returns {Object} 空行对象
   * @private
   */
  static _createEmptyRow(headers) {
    const emptyRow = {};
    headers.forEach(header => {
      emptyRow[header] = '';
    });
    return emptyRow;
  }
}

module.exports = ExcelExport;
