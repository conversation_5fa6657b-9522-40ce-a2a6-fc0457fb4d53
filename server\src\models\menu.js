const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Menu = sequelize.define('menus', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  enterpriseId: {
    type: DataTypes.INTEGER,
    field: 'enterprise_id',
    allowNull: false,
    defaultValue: 1,
    comment: '企业ID'
  },
  parentId: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'parent_id',
    comment: '父菜单ID'
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '菜单名称'
  },
  path: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '路由路径'
  },
  component: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '组件路径',
    get() {
      const rawValue = this.getDataValue('component');
      return rawValue;
    },
    set(value) {
      console.log('设置组件路径:', value);
      this.setDataValue('component', value);
    }
  },
  redirect: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '重定向'
  },
  icon: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '图标'
  },
  sort: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '排序'
  },
  hidden: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否隐藏：0显示，1隐藏'
  },
  type: {
    type: DataTypes.TINYINT,
    defaultValue: 0,
    comment: '类型：0目录，1菜单，2按钮'
  },
  perms: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '权限标识'
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '状态：0禁用，1正常'
  },
  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'menus'
});

module.exports = Menu; 