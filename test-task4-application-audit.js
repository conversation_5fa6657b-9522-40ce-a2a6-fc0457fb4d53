/**
 * 任务4：员工申请通过功能测试脚本
 * 
 * 测试场景：
 * 输入：员工申请通过接口：/api/organization/employee/application/{员工id}/audit
 * 参数：{"id":18,"auditStatus":"通过","auditRemark":"","currentUser":{"id":1,"username":"admin"}}
 * 输出：当auditStatus为通过时，则employee_career_record 新增一条记录
 */

const axios = require('axios');

// 配置
const config = {
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    // 这里需要添加实际的认证token
    'Authorization': 'Bearer your-token-here'
  }
};

// 测试数据
const testData = {
  applicationId: 18,  // 测试申请ID
  auditData: {
    id: 18,
    auditStatus: "通过",
    auditRemark: "测试审核通过",
    currentUser: {
      id: 1,
      username: "admin"
    }
  }
};

/**
 * 测试员工申请审核通过功能
 */
async function testEmployeeApplicationAudit() {
  console.log('🚀 开始测试任务4：员工申请审核通过功能');
  console.log('='.repeat(60));
  
  try {
    console.log('\n📋 测试参数:');
    console.log(`申请ID: ${testData.applicationId}`);
    console.log(`审核状态: ${testData.auditData.auditStatus}`);
    console.log(`审核备注: ${testData.auditData.auditRemark}`);
    console.log(`审核人: ${testData.auditData.currentUser.username} (ID: ${testData.auditData.currentUser.id})`);
    
    console.log('\n📡 发送审核请求...');
    const response = await axios.put(
      `${config.baseURL}/organization/employee/application/${testData.applicationId}/audit`,
      testData.auditData,
      {
        headers: config.headers,
        timeout: config.timeout
      }
    );
    
    if (response.status === 200) {
      console.log('\n✅ 审核请求成功!');
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
      console.log('\n📊 预期结果:');
      console.log('1. ✅ 申请记录状态更新为"通过"');
      console.log('2. ✅ 创建或更新员工记录');
      console.log('3. ✅ employee_career_record 新增一条记录');
      console.log('   - status: 1 (在职)');
      console.log('   - entry_time: 申请中的入职时间');
      console.log('   - departure_time: null');
      console.log('   - departure_reason: null');
      console.log('4. ✅ 创建员工岗位关联记录');
      
      console.log('\n🔍 建议验证步骤:');
      console.log('1. 检查 employee_enterprise_application 表的审核状态');
      console.log('2. 验证 org_employee 表是否创建/更新员工记录');
      console.log('3. 确认 employee_career_record 表是否新增履历记录');
      console.log('4. 检查 org_employee_position 表的岗位关联记录');
      console.log('5. 验证所有字段值是否正确');
      
    } else {
      console.log('\n❌ 审核请求失败');
      console.log('状态码:', response.status);
      console.log('响应数据:', response.data);
    }
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', error.response.data);
    }
    
    console.log('\n🔧 可能的问题:');
    console.log('1. 申请ID不存在或已经审核过');
    console.log('2. 申请状态不是"审核中"');
    console.log('3. 认证token无效或过期');
    console.log('4. 服务器连接问题');
    console.log('5. 数据库连接或事务问题');
  }
}

/**
 * 测试批量审核功能
 */
async function testBatchEmployeeApplicationAudit() {
  console.log('\n🚀 开始测试批量审核功能');
  console.log('-'.repeat(40));
  
  const batchTestData = {
    ids: [18, 19, 20], // 多个申请ID
    auditStatus: "通过",
    auditRemark: "批量审核通过",
    currentUser: {
      id: 1,
      username: "admin"
    }
  };
  
  try {
    console.log('\n📋 批量测试参数:');
    console.log(`申请IDs: [${batchTestData.ids.join(', ')}]`);
    console.log(`审核状态: ${batchTestData.auditStatus}`);
    
    console.log('\n📡 发送批量审核请求...');
    const response = await axios.put(
      `${config.baseURL}/organization/employee/application/batch-audit`,
      batchTestData,
      {
        headers: config.headers,
        timeout: config.timeout
      }
    );
    
    if (response.status === 200) {
      console.log('\n✅ 批量审核请求成功!');
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
      console.log('\n📊 预期结果:');
      console.log('1. ✅ 所有申请记录状态更新为"通过"');
      console.log('2. ✅ 为每个申请创建或更新员工记录');
      console.log('3. ✅ 为每个新员工在 employee_career_record 新增记录');
      console.log('4. ✅ 为每个员工创建岗位关联记录');
      
    } else {
      console.log('\n❌ 批量审核请求失败');
      console.log('状态码:', response.status);
      console.log('响应数据:', response.data);
    }
    
  } catch (error) {
    console.error('\n💥 批量测试失败:', error.message);
    
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', error.response.data);
    }
  }
}

/**
 * 显示SQL验证查询
 */
function showVerificationQueries() {
  console.log('\n📝 数据库验证查询:');
  console.log(`
-- 检查申请记录状态
SELECT id, real_name, audit_status, audit_time, auditor_id 
FROM employee_enterprise_application 
WHERE id = ${testData.applicationId};

-- 检查员工记录
SELECT id, name, open_id, status, entry_time, is_activated 
FROM org_employee 
WHERE open_id = (
  SELECT open_id FROM employee_enterprise_application WHERE id = ${testData.applicationId}
);

-- 检查履历记录
SELECT ecr.*, e.name as employee_name
FROM employee_career_record ecr
JOIN org_employee e ON ecr.employee_id = e.id
WHERE e.open_id = (
  SELECT open_id FROM employee_enterprise_application WHERE id = ${testData.applicationId}
)
ORDER BY ecr.create_time DESC;

-- 检查岗位关联记录
SELECT ep.*, e.name as employee_name
FROM org_employee_position ep
JOIN org_employee e ON ep.employee_id = e.id
WHERE e.open_id = (
  SELECT open_id FROM employee_enterprise_application WHERE id = ${testData.applicationId}
);

-- 检查最新创建的履历记录
SELECT 
  ecr.id,
  ecr.employee_id,
  e.name as employee_name,
  ecr.status,
  ecr.entry_time,
  ecr.create_time,
  ecr.create_by
FROM employee_career_record ecr
JOIN org_employee e ON ecr.employee_id = e.id
ORDER BY ecr.create_time DESC 
LIMIT 5;
  `);
}

// 运行测试
async function runTest() {
  await testEmployeeApplicationAudit();
  await testBatchEmployeeApplicationAudit();
  showVerificationQueries();
  
  console.log('\n' + '='.repeat(60));
  console.log('📋 任务4测试完成');
  console.log('请根据上述验证查询检查数据库中的实际结果');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = {
  testEmployeeApplicationAudit,
  testBatchEmployeeApplicationAudit,
  showVerificationQueries,
  testData
};
