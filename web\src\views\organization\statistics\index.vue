<template>
  <div class="container">
      <!-- 搜索表单 -->
      <div class="search-area">
        <a-card :bordered="false">
          <a-form layout="inline" :model="searchForm">
            <a-form-item label="姓名">
              <a-input
                v-model:value="searchForm.name"
                placeholder="请输入姓名"
                style="width: 160px"
                allowClear
              />
            </a-form-item>
            <a-form-item label="岗位名称">
              <a-select
                v-model:value="searchForm.position"
                placeholder="请选择岗位"
                style="width: 180px"
                allowClear
              >
                <a-select-option v-for="option in positionOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="岗位等级">
              <a-select
                v-model:value="searchForm.level"
                placeholder="请选择等级"
                style="width: 180px"
                allowClear
              >
                <a-select-option v-for="option in levelOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleSearch" class="primary-button">
                  <template #icon><SearchOutlined /></template>
                  查询
                </a-button>
                <a-button @click="resetSearch">
                  <template #icon><RedoOutlined /></template>
                  重置
                </a-button>
                <a-button @click="handleExport" class="primary-button">
                  <template #icon><DownloadOutlined /></template>
                  导出Excel
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </div>

      <!-- 统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="5">
            <a-card class="stat-card" :bordered="false">
              <div class="stat-icon">
                <clock-circle-outlined />
              </div>
              <div class="stat-info">
                <div class="stat-title">总练习时长</div>
                <div class="stat-value">{{ Math.ceil(totalStatistics.totalTime) }} 分钟</div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="5">
            <a-card class="stat-card" :bordered="false">
              <div class="stat-icon">
                <question-circle-outlined />
              </div>
              <div class="stat-info">
                <div class="stat-title">总答题数</div>
                <div class="stat-value">{{ totalStatistics.totalQuestions }} 题</div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="4">
            <a-card class="stat-card" :bordered="false">
              <div class="stat-icon">
                <file-text-outlined />
              </div>
              <div class="stat-info">
                <div class="stat-title">总考试次数</div>
                <div class="stat-value">{{ totalStatistics.totalExams }} 次</div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="5">
            <a-card class="stat-card" :bordered="false">
              <div class="stat-icon">
                <trophy-outlined />
              </div>
              <div class="stat-info">
                <div class="stat-title">总证书数</div>
                <div class="stat-value">{{ totalStatistics.totalCertificates }} 个</div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="5">
            <a-card class="stat-card" :bordered="false">
              <div class="stat-icon">
                <user-outlined />
              </div>
              <div class="stat-info">
                <div class="stat-title">员工总数</div>
                <div class="stat-value">{{ totalStatistics.totalEmployees }} 人</div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
          bordered
          class="statistics-table"
          :scroll="{ x: 1500, y: tableScrollY }"
        >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="primary" size="small" @click="showPositionStats(record)" class="primary-button">
                晋升岗位统计
              </a-button>
              <a-button type="default" size="small" @click="showOtherPositionStats(record)" class="secondary-button">
                其他岗位统计
              </a-button>
            </a-space>
          </template>
        </template>
        </a-table>
      </div>

      <!-- 职位统计弹窗 -->
      <a-modal
        v-model:visible="positionModalVisible"
        title="晋升岗位统计详情"
        :footer="null"
        width="1200px"
        :destroyOnClose="true"
      >
        <div v-if="positionStats && positionStats.length > 0">
          <h3>{{ currentEmployee?.name }} - 晋升岗位统计</h3>
          <a-table
            :columns="positionStatsColumns"
            :data-source="positionStats"
            :pagination="false"
            rowKey="positionId"
            bordered
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'certificateProgress'">
                <a-progress 
                  :percent="record.certificateProgress" 
                  :format="(percent) => `${record.obtainedCertificates}/${record.requiredCertificates}`"
                  status="active"
                />
              </template>
            </template>
          </a-table>
        </div>
        <div v-else class="empty-state">
          <a-empty description="暂无职位统计数据" />
        </div>
      </a-modal>

      <!-- 其他岗位统计弹窗 -->
      <a-modal
        v-model:visible="otherPositionModalVisible"
        title="其他岗位统计详情"
        :footer="null"
        width="1000px"
        :destroyOnClose="true"
      >
        <div v-if="otherPositionStats && otherPositionStats.length > 0">
          <h3>{{ currentEmployee?.name }} - 其他岗位统计</h3>
          <a-table
            :columns="otherPositionStatsColumns"
            :data-source="otherPositionStats"
            :pagination="false"
            rowKey="positionId"
            bordered
            size="small"
          >
          </a-table>
        </div>
        <div v-else class="empty-state">
          <a-empty description="暂无其他岗位统计数据" />
        </div>
      </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, onBeforeUnmount } from 'vue';
import { 
  SearchOutlined, 
  RedoOutlined,
  DownloadOutlined,
  ClockCircleOutlined,
  QuestionCircleOutlined,
  FileTextOutlined,
  TrophyOutlined,
  UserOutlined
} from '@ant-design/icons-vue';
import { 
  getEmployeeStatistics, 
  getEmployeePositionStatistics, 
  exportEmployeeStatistics,
  getEmployeeOtherPositionStatistics
} from '@/api/organization/statistics';
import { getPositionNameOptions } from '@/api/organization/position';
import { getLevelOptions } from '@/api/organization/level';
import { message } from 'ant-design-vue';

// 搜索表单
const searchForm = reactive({
  name: '',
  position: undefined,
  level: undefined,
});

// 职位统计弹窗相关
const positionModalVisible = ref(false);
const positionStats = ref([]);
const currentEmployee = ref(null);

// 其他岗位统计弹窗相关
const otherPositionModalVisible = ref(false);
const otherPositionStats = ref([]);

// 监听searchForm变化
watch(searchForm, (newVal) => {
  // 表单数据变化监听
}, { deep: true });

// 重置搜索
const resetSearch = () => {
  searchForm.name = '';
  searchForm.position = undefined;
  searchForm.level = undefined;
  pagination.current = 1;
  loadData();
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

// 导出Excel
const handleExport = async () => {
  try {
    message.loading('正在导出...', 2);
    
    const params = {};
    
    // 添加搜索条件
    if (searchForm.name) {
      params.name = searchForm.name;
    }
    if (searchForm.position !== undefined) {
      params.position = searchForm.position;
    }
    if (searchForm.level !== undefined) {
      params.level = searchForm.level;
    }
    
    const response = await exportEmployeeStatistics(params);
    
    // 创建下载链接
    const blob = new Blob([response], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `员工统计数据_${new Date().toLocaleDateString()}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);
    
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
};

// 显示职位统计
const showPositionStats = async (record) => {
  try {
    const response = await getEmployeePositionStatistics(record.id);
    positionStats.value = response.positionStats || [];
    currentEmployee.value = record;
    positionModalVisible.value = true;
  } catch (error) {
    console.error('获取职位统计失败:', error);
    message.error('获取职位统计失败');
  }
};

// 显示其他岗位统计
const showOtherPositionStats = async (record) => {
  try {
    currentEmployee.value = record;
    otherPositionModalVisible.value = true;
    loading.value = true;
    
    const response = await getEmployeeOtherPositionStatistics(record.id);
    otherPositionStats.value = response.positionStats || [];
    
    if (otherPositionStats.value.length === 0) {
      message.info('未找到该员工的其他岗位练习记录');
    }
  } catch (error) {
    console.error('获取其他岗位统计失败:', error);
    message.error('获取其他岗位统计失败');
  } finally {
    loading.value = false;
  }
};

// 表格列定义
const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: '100px',
    minWidth: '100px',
    fixed: 'left'
  },
  {
    title: '所属门店',
    dataIndex: 'department',
    key: 'department',
    width: '150px',
    minWidth: '150px',
  },
  {
    title: '岗位名称(默认)',
    dataIndex: 'position',
    key: 'position',
    width: '130px',
    minWidth: '130px',
  },
  {
    title: '岗位等级(默认)',
    dataIndex: 'level',
    key: 'level',
    width: '130px',
    minWidth: '130px',
  },
  {
    title: '总练习时长(分钟)',
    dataIndex: 'totalTime',
    key: 'totalTime',
    width: '140px',
    minWidth: '140px',
    sorter: true,
    customRender: ({ record }) => `${Math.ceil(record.totalTime)}`,
  },
  {
    title: '总答题数',
    dataIndex: 'totalQuestions',
    key: 'totalQuestions',
    width: '100px',
    minWidth: '100px',
    sorter: true,
  },
  {
    title: '总考试次数',
    dataIndex: 'totalExams',
    key: 'totalExams',
    width: '110px',
    minWidth: '110px',
    sorter: true,
  },
  {
    title: '总证书数',
    dataIndex: 'totalCertificates',
    key: 'totalCertificates',
    width: '100px',
    minWidth: '100px',
    sorter: true,
  },
  {
    title: '总徽章数',
    dataIndex: 'totalBadges',
    key: 'totalBadges',
    width: '100px',
    minWidth: '100px',
    sorter: true,
  },
  {
    title: '操作',
    key: 'action',
    width: '220px',
    minWidth: '220px',
    fixed: 'right',
  },
];

// 职位统计表格列定义
const positionStatsColumns = [
  {
    title: '职位名称',
    dataIndex: 'positionName',
    key: 'positionName',
    width: '150px',
  },
  {
    title: '岗位类型',
    dataIndex: 'positionTypeName',
    key: 'positionTypeName',
    width: '120px',
  },
  {
    title: '职位等级',
    dataIndex: 'levelName',
    key: 'levelName',
    width: '120px',
  },
  {
    title: '练习时长(分钟)',
    dataIndex: 'currentPositionTime',
    key: 'currentPositionTime',
    width: '160px',
    customRender: ({ record }) => Math.ceil(record.currentPositionTime || 0),
  },
  {
    title: '答题数',
    dataIndex: 'currentPositionQuestions',
    key: 'currentPositionQuestions',
    width: '100px',
  },
  {
    title: '考试次数',
    dataIndex: 'currentPositionExams',
    key: 'currentPositionExams',
    width: '120px',
  },
  {
    title: '证书进度',
    dataIndex: 'certificateProgress',
    key: 'certificateProgress',
    width: '180px',
  },
  {
    title: '距离下一等级证书要求',
    dataIndex: 'nextLevelCertificates',
    key: 'nextLevelCertificates',
    width: '220px',
    customRender: ({ record }) => record.nextLevelCertificates || '已达最高等级',
  },
];

// 其他岗位统计表格列定义
const otherPositionStatsColumns = [
  {
    title: '岗位名称',
    dataIndex: 'positionName',
    key: 'positionName',
    width: '150px',
  },
  {
    title: '岗位类型',
    dataIndex: 'positionTypeName',
    key: 'positionTypeName',
    width: '120px',
  },
  {
    title: '岗位等级',
    dataIndex: 'levelName',
    key: 'levelName',
    width: '120px',
  },
  {
    title: '练习时长(分钟)',
    dataIndex: 'practiceTime',
    key: 'practiceTime',
    width: '130px',
    customRender: ({ record }) => Math.ceil(record.practiceTime || 0),
  },
  {
    title: '练习题数',
    dataIndex: 'practiceQuestions',
    key: 'practiceQuestions',
    width: '100px',
  },
];

// 岗位选项
const positionOptions = ref([]);

// 岗位等级选项
const levelOptions = ref([]);

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: (total) => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
});

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  
  // 处理排序
  if (sorter.field && sorter.order) {
    const order = sorter.order === 'ascend' ? 1 : -1;
    
    dataSource.value.sort((a, b) => {
      if (a[sorter.field] < b[sorter.field]) return -1 * order;
      if (a[sorter.field] > b[sorter.field]) return 1 * order;
      return 0;
    });
  } else {
    loadData();
  }
};

// 总体统计数据
const totalStatistics = reactive({
  totalTime: 0,
  totalQuestions: 0,
  totalExams: 0,
  totalCertificates: 0,
  totalBadges: 0,
  totalEmployees: 0,
});

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize
    };
    
    // 添加搜索条件
    if (searchForm.name) {
      params.name = searchForm.name;
    }
    if (searchForm.position !== undefined) {
      params.position = searchForm.position;
    }
    if (searchForm.level !== undefined) {
      params.level = searchForm.level;
    }
    
    const response = await getEmployeeStatistics(params);
    
    // response就是API返回的data部分，request.js已经提取了
    dataSource.value = response.list;
    pagination.total = response.pagination.total;
    
    // 更新总体统计
    Object.assign(totalStatistics, response.totalStatistics);
  } catch (error) {
    console.error('获取员工统计数据失败:', error);
    message.error('获取员工统计数据失败');
  } finally {
    loading.value = false;
  }
};

// 加载岗位选项
const loadPositionOptions = async () => {
  try {
    const response = await getPositionNameOptions();
    
    // request.js已经直接提取了data，检查数据结构
    if (response && response.rows && Array.isArray(response.rows)) {
      positionOptions.value = response.rows.map(item => ({
        value: item.id,
        label: item.name
      }));
    } else {
      positionOptions.value = [];
    }
  } catch (error) {
    positionOptions.value = [];
  }
};

// 加载等级选项
const loadLevelOptions = async () => {
  try {
    const response = await getLevelOptions();
    
    // 等级选项API直接返回数组，不像岗位选项有rows包装
    if (response && Array.isArray(response)) {
      levelOptions.value = response.map(item => ({
        value: item.id,
        label: item.name
      }));
    } else {
      levelOptions.value = [];
    }
  } catch (error) {
    levelOptions.value = [];
  }
};

// 计算表格滚动区域高度
const tableScrollY = ref(400); // 默认高度

// 计算表格高度的函数
const calculateTableHeight = () => {
  // 页面总高度减去其他组件的估计高度
  // 顶部导航高度(来自page-container的计算)
  const pageHeaderElement = document.querySelector('.page-header');
  const pageHeaderHeight = pageHeaderElement ? pageHeaderElement.offsetHeight : 80;

  // container高度
  const containerElement = document.querySelector('.content-wrapper');
  const containerHeight = containerElement ? containerElement.offsetHeight : 80;
  // 搜索区域高度估计
  const searchAreaElement = document.querySelector('.search-area');
  const searchAreaHeight = searchAreaElement ? searchAreaElement.offsetHeight : 80;
  // 统计卡片区域高度估计
  const statisticsCardsElement = document.querySelector('.statistics-cards');
  const statisticsCardsHeight = statisticsCardsElement ? statisticsCardsElement.offsetHeight : 80;
  // 表格容器高度估计
  const tableContainerElement = document.querySelector('.table-container');   
  const tableContainerHeight = tableContainerElement ? tableContainerElement.offsetHeight : 80;
  // 表格高度估计
  const tableElement = document.querySelector('.statistics-table');
  const tableHeight = tableElement ? tableElement.offsetHeight : 80;
// 93为组件的padding margin
  const otherComponentsHeight =  searchAreaHeight+statisticsCardsHeight+93;
  // 48未边框距离  70为分页高度
  const availableHeight = containerHeight - otherComponentsHeight-100;
  tableScrollY.value =availableHeight;
};

// 监听窗口大小变化
const handleResize = () => {
  calculateTableHeight();
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadPositionOptions();
  await loadLevelOptions();
  await loadData();
  calculateTableHeight();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.page-container {
  padding: 16px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.employee-statistics-container {
  width: 100%;
}

.search-area {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.statistics-cards {
  margin-bottom: 24px;
  margin-top: 20px;
  flex-shrink: 0;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.statistics-table {
  flex: 1;
}

.primary-button {
  background-color: #a18cd1;
  border-color: #a18cd1;
  min-width: 88px;
  color: #fff;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 32px;
  line-height: 32px;
}

.primary-button:hover {
  background-color: #8a65c9;
  border-color: #8a65c9;
  color: #fff;
}

.secondary-button {
  border-color: #a18cd1;
  color: #a18cd1;
  min-width: 88px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 32px;
  line-height: 32px;
}

.secondary-button:hover {
  border-color: #8a65c9;
  color: #8a65c9;
}

.position-link {
  color: #a18cd1;
  padding: 0;
}

.position-link:hover {
  color: #8a65c9;
}

.stat-card {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #fff;
  padding: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  height: 160px;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 64px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 12px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.text-gray {
  color: #999;
}

/* 表格样式优化 */
:deep(.ant-table-wrapper) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.ant-table) {
  flex: 1;
}

:deep(.ant-table-container) {
  height: 100%;
}

:deep(.ant-table-body) {
  overflow-y: auto !important;
  overflow-x: auto !important;
}

:deep(.ant-table-pagination) {
  margin-top: 16px;
  margin-bottom: 0;
  flex-shrink: 0;
}

:deep(.ant-table-fixed-right) {
  background-color: #fff;
}

:deep(.ant-table-fixed-column .ant-table-cell-fix-right) {
  background: #fff;
}

:deep(.ant-form-inline .ant-form-item) {
  margin-bottom: 16px;
}
</style> 