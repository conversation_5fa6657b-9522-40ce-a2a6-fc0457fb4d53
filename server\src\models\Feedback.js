const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const { DictionaryData } = require('./dictionary');

const Feedback = sequelize.define('feedback', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by',
    comment: '创建人ID'
  },
  open_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'open_id',
    comment: '用户openId'
  },
  contactPerson: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'contact_person',
    comment: '联系人'
  },
  contactInfo: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'contact_info',
    comment: '联系方式'
  },
  feedbackTypeId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'feedback_type_id',
    comment: '反馈类型ID'
  },
  feedbackContent: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'feedback_content',
    comment: '反馈内容'
  },
  enterpriseId: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'enterprise_id',
    comment: '反馈内容'
  },
  feedbackImgs: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'feedback_imgs',
    comment: '图片，多张图片使用,隔开'
  },
  feedbackTime: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'feedback_time',
    comment: '反馈时间'
  },
  replyContent: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'reply_content',
    comment: '回复内容'
  },
  replyTime: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'reply_time',
    comment: '回复时间'
  },
  replyBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'reply_by',
    comment: '回复人ID'
  },
  createTime: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'create_time',
    comment: '创建时间'
  },
  updateTime: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'update_time',
    comment: '更新时间'
  }
}, {
  tableName: 'feedback',
  timestamps: true,
  createdAt: 'createTime',
  updatedAt: 'updateTime',
  indexes: [
    {
      name: 'idx_feedback_type',
      fields: ['feedback_type_id']
    },
    {
      name: 'idx_feedback_time',
      fields: ['feedback_time']
    },
    {
      name: 'idx_created_by',
      fields: ['created_by']
    },
    {
      name: 'idx_reply_time',
      fields: ['reply_time']
    },
    {
      name: 'idx_open_id',
      fields: ['open_id']
    }
  ]
});

// 设置Feedback与DictionaryData的关联关系
Feedback.belongsTo(DictionaryData, { foreignKey: 'feedbackTypeId', as: 'feedbackType' });

module.exports = Feedback;
