/**
 * JSON工具函数
 * 提供JSON解析、修复等相关功能
 */

/**
 * 智能修复断裂JSON的辅助函数
 * @param {string} jsonStr - 可能断裂的JSON字符串
 * @returns {Object|null} - 解析成功返回对象，失败返回null
 */
function fixBrokenJson(jsonStr) {
    if (!jsonStr || jsonStr.trim() === '') {
        return null;
    }
    
    // 分析括号平衡情况，确定需要补充的结束符号
    function analyzeAndFix(str) {
        const stack = [];
        let inString = false;
        let escapeNext = false;
        
        // 分析当前字符串的括号状态
        for (let i = 0; i < str.length; i++) {
            const char = str[i];
            
            if (escapeNext) {
                escapeNext = false;
                continue;
            }
            
            if (char === '\\') {
                escapeNext = true;
                continue;
            }
            
            if (char === '"' && !escapeNext) {
                inString = !inString;
                continue;
            }
            
            if (!inString) {
                if (char === '{') {
                    stack.push('}');
                } else if (char === '[') {
                    stack.push(']');
                } else if (char === '}' || char === ']') {
                    if (stack.length > 0 && stack[stack.length - 1] === char) {
                        stack.pop();
                    }
                }
            }
        }
        
        return stack;
    }
    
    // 首先尝试找到最后一个逗号，舍弃后面的内容
    const lastCommaIndex = jsonStr.lastIndexOf(',');
    let baseStr = jsonStr;
    
    if (lastCommaIndex !== -1) {
        // 舍弃逗号后面的内容
        baseStr = jsonStr.substring(0, lastCommaIndex);
    }
    
    // 分析需要补充的括号
    const missingBrackets = analyzeAndFix(baseStr);
    
    // 尝试不同的修复策略
    const fixStrategies = [
        baseStr, // 原始字符串（去掉逗号后的）
        baseStr + missingBrackets.reverse().join(''), // 补充所有缺失的括号
    ];
    
    // 如果有逗号被截断，也尝试原始字符串的修复
    if (lastCommaIndex !== -1) {
        const originalMissingBrackets = analyzeAndFix(jsonStr);
        fixStrategies.push(jsonStr + originalMissingBrackets.reverse().join(''));
    }
    
    // 逐一尝试修复策略
    for (const fixedStr of fixStrategies) {
        try {
            const parsed = JSON.parse(fixedStr);
            return parsed;
        } catch (e) {
            continue;
        }
    }
    
    // 如果所有策略都失败，返回null
    return null;
}

/**
 * 安全的JSON解析函数，支持断裂JSON的智能修复
 * @param {string} jsonStr - JSON字符串
 * @returns {Object|null} - 解析成功返回对象，失败返回null
 */
function safeJsonParse(jsonStr) {
    try {
        return JSON.parse(jsonStr);
    } catch (parseError) {
        // 尝试智能修复
        return fixBrokenJson(jsonStr);
    }
}

/**
 * 测试函数 - 演示各种断裂情况的修复
 * 仅用于开发调试，生产环境不使用
 */
function testFixBrokenJson() {
    console.log('=== JSON修复功能测试 ===\n');
    
    const testCases = [
        {
            name: '数组被截断',
            input: '{"items": [1, 2, 3,',
            expected: '{"items": [1, 2, 3]}'
        },
        {
            name: '字符串被截断',
            input: '{"name": "用户名',
            expected: '{"name": "用户名"}'
        },
        {
            name: '复杂嵌套被截断',
            input: '{"users": [{"name": "张三", "data": {"score": 95,',
            expected: '{"users": [{"name": "张三", "data": {"score": 95}}]}'
        },
        {
            name: '多层对象被截断',
            input: '{"a": {"b": {"c": {"d": "value",',
            expected: '{"a": {"b": {"c": {"d": "value"}}}}'
        },
        {
            name: '数组中对象被截断',
            input: '{"list": [{"id": 1, "name": "test",',
            expected: '{"list": [{"id": 1, "name": "test"}]}'
        },
        {
            name: '字符串中包含转义字符',
            input: '{"message": "说话：\\"你好\\",',
            expected: '{"message": "说话：\\"你好\\""}'
        }
    ];
    
    testCases.forEach((testCase, index) => {
        console.log(`测试 ${index + 1}: ${testCase.name}`);
        console.log(`输入: ${testCase.input}`);
        
        const result = fixBrokenJson(testCase.input);
        if (result) {
            const resultStr = JSON.stringify(result);
            console.log(`修复结果: ${resultStr}`);
            console.log(`修复成功: ${resultStr === testCase.expected ? '✅' : '❌'}`);
        } else {
            console.log('修复失败: null');
        }
        console.log('---');
    });
}

module.exports = {
    fixBrokenJson,
    safeJsonParse,
    testFixBrokenJson  // 仅用于调试
}; 