import request from '@/utils/request';

/**
 * 获取岗位列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getPositionList(params) {
  return request({
    url: '/organization/position/list',
    method: 'get',
    params
  });
}

/**
 * 获取岗位选项（下拉列表用）
 * @returns {Promise} 请求结果
 */
export function getPositionOptions() {
  return request({
    url: '/organization/position/options',
    method: 'get'
  });
}

/**
 * 获取岗位名称选项（从字典获取，下拉列表用）
 * @returns {Promise} 请求结果
 */
export function getDictionaryPositionNameOptions() {
  return request({
    url: '/system/dict/data/type/position_name',
    method: 'get'
  });
}

/**
 * 获取岗位类别选项（从字典获取，下拉列表用）
 * @returns {Promise} 请求结果
 */
export function getDictionaryPositionTypeOptions() {
  return request({
    url: '/system/dict/data/type/position_type',
    method: 'get'
  });
}

/**
 * 获取岗位详情
 * @param {Number} id - 岗位ID
 * @returns {Promise} 请求结果
 */
export function getPositionDetail(id) {
  return request({
    url: `/organization/position/${id}`,
    method: 'get'
  });
}

/**
 * 新增岗位
 * @param {Object} data - 岗位数据
 * @returns {Promise} 请求结果
 */
export function addPosition(data) {
  return request({
    url: '/organization/position',
    method: 'post',
    data
  });
}

/**
 * 更新岗位
 * @param {Object} data - 岗位数据
 * @returns {Promise} 请求结果
 */
export function updatePosition(data) {
  return request({
    url: '/organization/position',
    method: 'put',
    data
  });
}

/**
 * 删除岗位
 * @param {Number} id - 岗位ID
 * @returns {Promise} 请求结果
 */
export function deletePosition(id) {
  return request({
    url: `/organization/position/${id}`,
    method: 'delete'
  });
}

/**
 * 获取岗位等级列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getLevelList(params) {
  return request({
    url: '/organization/level/list',
    method: 'get',
    params
  });
}

/**
 * 获取岗位等级选项（下拉列表用）
 * @returns {Promise} 请求结果
 */
export function getLevelOptions() {
  return request({
    url: '/organization/level/options',
    method: 'get'
  });
}

/**
 * 获取岗位等级详情
 * @param {Number} id - 岗位等级ID
 * @returns {Promise} 请求结果
 */
export function getLevelDetail(id) {
  return request({
    url: `/organization/level/${id}`,
    method: 'get'
  });
}

/**
 * 新增岗位等级
 * @param {Object} data - 岗位等级数据
 * @returns {Promise} 请求结果
 */
export function addLevel(data) {
  return request({
    url: '/organization/level',
    method: 'post',
    data
  });
}

/**
 * 更新岗位等级
 * @param {Object} data - 岗位等级数据
 * @returns {Promise} 请求结果
 */
export function updateLevel(data) {
  return request({
    url: '/organization/level',
    method: 'put',
    data
  });
}

/**
 * 删除岗位等级
 * @param {Number} id - 岗位等级ID
 * @returns {Promise} 请求结果
 */
export function deleteLevel(id) {
  return request({
    url: `/organization/level/${id}`,
    method: 'delete'
  });
}

/**
 * 获取岗位结构树
 * @returns {Promise} 请求结果
 */
export function getPositionStructureTree() {
  return request({
    url: '/organization/position/structure/tree',
    method: 'get'
  });
}

/**
 * 获取岗位类型列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getPositionTypeList(params) {
  return request({
    url: '/organization/position/type/list',
    method: 'get',
    params
  });
}

/**
 * 获取岗位类型选项
 * @returns {Promise} 请求结果
 */
export function getPositionTypeOptions() {
  return request({
    url: '/organization/position/type/options',
    method: 'get'
  });
}

/**
 * 新增岗位类型
 * @param {Object} data - 岗位类型数据
 * @returns {Promise} 请求结果
 */
export function addPositionType(data) {
  return request({
    url: '/organization/position/type',
    method: 'post',
    data
  });
}

/**
 * 更新岗位类型
 * @param {Object} data - 岗位类型数据
 * @returns {Promise} 请求结果
 */
export function updatePositionType(data) {
  return request({
    url: '/organization/position/type',
    method: 'put',
    data
  });
}

/**
 * 删除岗位类型
 * @param {Number} id - 岗位类型ID
 * @returns {Promise} 请求结果
 */
export function deletePositionType(id) {
  return request({
    url: `/organization/position/type/${id}`,
    method: 'delete'
  });
}

/**
 * 获取岗位名称列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getPositionNameList(params) {
  return request({
    url: '/organization/position/name/list',
    method: 'get',
    params
  });
}

/**
 * 获取岗位名称选项
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getPositionNameOptions(params) {
  return request({
    url: '/organization/position/name/options',
    method: 'get',
    params
  });
}

/**
 * 新增岗位名称
 * @param {Object} data - 岗位名称数据（包含levelIds数组）
 * @returns {Promise} 请求结果
 */
export function addPositionName(data) {
  return request({
    url: '/organization/position/name',
    method: 'post',
    data
  });
}

/**
 * 更新岗位名称
 * @param {Object} data - 岗位名称数据（包含levelIds数组）
 * @returns {Promise} 请求结果
 */
export function updatePositionName(data) {
  return request({
    url: '/organization/position/name',
    method: 'put',
    data
  });
}

/**
 * 删除岗位名称
 * @param {Number} id - 岗位名称ID
 * @returns {Promise} 请求结果
 */
export function deletePositionName(id) {
  return request({
    url: `/organization/position/name/${id}`,
    method: 'delete'
  });
}

/**
 * 根据岗位ID获取对应的等级选项
 * @param {Number} positionId - 岗位ID
 * @returns {Promise} 请求结果
 */
export function getLevelsByPosition(positionId) {
  return request({
    url: `/organization/position/${positionId}/levels`,
    method: 'get'
  });
}

/**
 * 获取岗位类型及相关信息（positionType -> positionName -> level）
 * @returns {Promise} 请求结果
 */
export function getPositionTypesWithNamesAndLevels() {
  return request({
    url: '/wechat/position/getPositionTypesWithNamesAndLevels',
    method: 'get'
  });
} 