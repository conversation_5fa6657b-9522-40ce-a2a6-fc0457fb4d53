{"name": "server", "version": "1.0.0", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "阿依来后台管理系统API服务", "dependencies": {"axios": "^1.8.4", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "canvas": "^2.11.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "form-data": "^4.0.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.0", "pdfkit": "^0.14.0", "redis": "^4.6.10", "sequelize": "^6.37.7", "uuid": "^9.0.1", "ws": "^8.18.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.9"}}