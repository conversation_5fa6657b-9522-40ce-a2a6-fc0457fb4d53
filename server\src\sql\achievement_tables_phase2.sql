-- 第二期成就系统数据库表结构
-- 用户成就记录表和成就进度表

-- 用户成就记录表
DROP TABLE IF EXISTS `user_achievements`;
CREATE TABLE `user_achievements` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `enterprise_id` bigint NOT NULL COMMENT '企业ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `open_id` varchar(255) DEFAULT NULL COMMENT '微信openId',
  `template_id` bigint NOT NULL COMMENT '成就模板ID',
  `achievement_name` varchar(100) NOT NULL COMMENT '成就名称',
  `achievement_icon` varchar(500) DEFAULT NULL COMMENT '成就图标',
  `category` varchar(50) NOT NULL DEFAULT 'learning' COMMENT '成就类别：learning-学习,time-时间,exam-考试,practice-练习',
  `status` varchar(20) NOT NULL DEFAULT 'achieved' COMMENT '状态：achieved-已获得,revoked-已撤销',
  `achieved_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `reward_points` int NOT NULL DEFAULT '0' COMMENT '获得积分',
  `trigger_condition` text COMMENT '触发条件JSON',
  `achievement_data` json DEFAULT NULL COMMENT '成就相关数据JSON，如学习时长、题目数量等',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT 'system' COMMENT '创建人',
  `update_by` varchar(50) DEFAULT 'system' COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_template` (`enterprise_id`,`user_id`,`template_id`),
  KEY `idx_enterprise_user` (`enterprise_id`,`user_id`),
  KEY `idx_enterprise_openid` (`enterprise_id`,`open_id`),
  KEY `idx_enterprise_template` (`enterprise_id`,`template_id`),
  KEY `idx_enterprise_category` (`enterprise_id`,`category`),
  KEY `idx_enterprise_status` (`enterprise_id`,`status`),
  KEY `idx_achieved_at` (`achieved_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户成就记录表';

-- 成就进度跟踪表
DROP TABLE IF EXISTS `achievement_progress`;
CREATE TABLE `achievement_progress` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `enterprise_id` bigint NOT NULL COMMENT '企业ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `open_id` varchar(255) DEFAULT NULL COMMENT '微信openId',
  `template_id` bigint NOT NULL COMMENT '成就模板ID',
  `achievement_name` varchar(100) NOT NULL COMMENT '成就名称',
  `rule_type` varchar(50) NOT NULL COMMENT '规则类型：progress-进度,time-时间,count-计数,streak-连续',
  `current_value` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '当前进度值',
  `target_value` decimal(15,2) NOT NULL COMMENT '目标值',
  `progress_percentage` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '完成百分比',
  `status` varchar(20) NOT NULL DEFAULT 'in_progress' COMMENT '状态：in_progress-进行中,completed-已完成,paused-已暂停',
  `is_completed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已完成',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `last_updated_data` json DEFAULT NULL COMMENT '最后更新的数据JSON，记录触发更新的具体数据',
  `progress_history` json DEFAULT NULL COMMENT '进度历史记录JSON数组',
  `streak_data` json DEFAULT NULL COMMENT '连续性数据JSON，如连续学习天数的详细记录',
  `time_data` json DEFAULT NULL COMMENT '时间相关数据JSON，如学习时间段记录',
  `last_activity_date` date DEFAULT NULL COMMENT '最后活动日期（用于连续性检测）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_template_progress` (`enterprise_id`,`user_id`,`template_id`),
  KEY `idx_enterprise_user` (`enterprise_id`,`user_id`),
  KEY `idx_enterprise_openid` (`enterprise_id`,`open_id`),
  KEY `idx_enterprise_template` (`enterprise_id`,`template_id`),
  KEY `idx_enterprise_status` (`enterprise_id`,`status`),
  KEY `idx_enterprise_completed` (`enterprise_id`,`is_completed`),
  KEY `idx_last_activity` (`last_activity_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='成就进度跟踪表';

-- 外键约束（如果需要）
-- ALTER TABLE `user_achievements` ADD CONSTRAINT `fk_user_achievement_template` FOREIGN KEY (`template_id`) REFERENCES `achievement_templates` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `achievement_progress` ADD CONSTRAINT `fk_achievement_progress_template` FOREIGN KEY (`template_id`) REFERENCES `achievement_templates` (`id`) ON DELETE CASCADE;

-- 插入示例数据（可选）
-- INSERT INTO `user_achievements` (`enterprise_id`, `user_id`, `open_id`, `template_id`, `achievement_name`, `achievement_icon`, `category`, `status`, `achieved_at`, `reward_points`, `trigger_condition`, `achievement_data`, `create_by`) VALUES
-- (1, 1, 'test_open_id', 1, '初入宝殿', '/icons/achievements/first-complete.svg', 'learning', 'achieved', NOW(), 10, '{"type":"first_complete","progress":100}', '{"finalValue":1,"targetValue":1,"completedAt":"2024-01-01T10:00:00.000Z"}', 'system'); 