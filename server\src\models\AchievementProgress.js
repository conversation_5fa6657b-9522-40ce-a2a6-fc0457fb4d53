const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 成就进度跟踪模型
 */
const AchievementProgress = sequelize.define('AchievementProgress', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },

  enterpriseId: {
    type: DataTypes.BIGINT,
    field: 'enterprise_id',
    allowNull: false,
    comment: '企业ID'
  },

  userId: {
    type: DataTypes.BIGINT,
    field: 'user_id',
    allowNull: false,
    comment: '用户ID'
  },

  openId: {
    type: DataTypes.STRING(255),
    field: 'open_id',
    allowNull: true,
    comment: '微信openId'
  },

  templateId: {
    type: DataTypes.BIGINT,
    field: 'template_id',
    allowNull: false,
    comment: '成就模板ID'
  },

  achievementName: {
    type: DataTypes.STRING(100),
    field: 'achievement_name',
    allowNull: false,
    comment: '成就名称'
  },

  ruleType: {
    type: DataTypes.STRING(50),
    field: 'rule_type',
    allowNull: false,
    comment: '规则类型：progress-进度,time-时间,count-计数,streak-连续'
  },

  currentValue: {
    type: DataTypes.DECIMAL(15, 2),
    field: 'current_value',
    allowNull: false,
    defaultValue: 0,
    comment: '当前进度值'
  },

  targetValue: {
    type: DataTypes.DECIMAL(15, 2),
    field: 'target_value',
    allowNull: false,
    comment: '目标值'
  },

  progressPercentage: {
    type: DataTypes.DECIMAL(5, 2),
    field: 'progress_percentage',
    allowNull: false,
    defaultValue: 0,
    comment: '完成百分比'
  },

  status: {
    type: DataTypes.STRING(20),
    field: 'status',
    allowNull: false,
    defaultValue: 'in_progress',
    comment: '状态：in_progress-进行中,completed-已完成,paused-已暂停'
  },

  isCompleted: {
    type: DataTypes.BOOLEAN,
    field: 'is_completed',
    allowNull: false,
    defaultValue: false,
    comment: '是否已完成'
  },

  completedAt: {
    type: DataTypes.DATE,
    field: 'completed_at',
    allowNull: true,
    comment: '完成时间'
  },

  lastUpdatedData: {
    type: DataTypes.JSON,
    field: 'last_updated_data',
    allowNull: true,
    comment: '最后更新的数据JSON，记录触发更新的具体数据'
  },

  progressHistory: {
    type: DataTypes.JSON,
    field: 'progress_history',
    allowNull: true,
    comment: '进度历史记录JSON数组'
  },

  streakData: {
    type: DataTypes.JSON,
    field: 'streak_data',
    allowNull: true,
    comment: '连续性数据JSON，如连续学习天数的详细记录'
  },

  timeData: {
    type: DataTypes.JSON,
    field: 'time_data',
    allowNull: true,
    comment: '时间相关数据JSON，如学习时间段记录'
  },

  lastActivityDate: {
    type: DataTypes.DATEONLY,
    field: 'last_activity_date',
    allowNull: true,
    comment: '最后活动日期（用于连续性检测）'
  },

  createTime: {
    type: DataTypes.DATE,
    field: 'create_time',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },

  updateTime: {
    type: DataTypes.DATE,
    field: 'update_time',
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '更新时间'
  }
}, {
  tableName: 'achievement_progress',
  timestamps: true,
  createdAt: 'create_time',
  updatedAt: 'update_time',
  indexes: [
    {
      name: 'idx_enterprise_user',
      fields: ['enterprise_id', 'user_id']
    },
    {
      name: 'idx_enterprise_openid',
      fields: ['enterprise_id', 'open_id']
    },
    {
      name: 'idx_enterprise_template',
      fields: ['enterprise_id', 'template_id']
    },
    {
      name: 'idx_enterprise_status',
      fields: ['enterprise_id', 'status']
    },
    {
      name: 'idx_enterprise_completed',
      fields: ['enterprise_id', 'is_completed']
    },
    {
      name: 'idx_last_activity',
      fields: ['last_activity_date']
    },
    {
      // 复合唯一索引：确保同一用户同一成就只有一条进度记录
      name: 'uk_user_template_progress',
      fields: ['enterprise_id', 'user_id', 'template_id'],
      unique: true
    }
  ]
});

module.exports = AchievementProgress;

// 模型关联关系将在index.js中定义 